#!/usr/bin/env python3
import json
import re

# Specifieke vertalingen voor resterende Engelse termen
final_translations = {
    # Veelvoorkomende fouten corrigeren
    "Aanroerend goed": "<PERSON><PERSON>ere<PERSON> goed",
    "Aantdek": "Ontdek", 
    "Aantgrendel": "Ontgrendel",
    "Aanbeperkt": "Onbeperkt",
    "Doenenwngrade": "Downgrade",
    "Doenenwnload": "Download",
    "Aanderhuur": "Onderhuur",
    "Naaregestane": "Toegestane",
    "Neemade": "Nomade",
    "Doenenor": "Door",
    "Naarekomstige": "Toekomstige",
    "Aantdekken": "Ontdekken",
    "Neetificaties": "Notificaties",
    "Allee": "Alle",
    "Naaro": "Te",
    "Neet": "Niet",
    "Elkething": "Alles",
    "Naarevoegened": "Toegevoegd",
    "Nieuwest": "Nieuwste",
    "Oudest": "<PERSON>uds<PERSON>",
    "Kleinest": "Kleinste",
    "Grootst": "<PERSON><PERSON><PERSON>",
    "Populairity": "Populariteit",
    "Uitzichted": "Bekeken",
    "Favorietd": "Favoriet",
    "Naartal": "Totaal",
    "Aangemeubileerd": "Ongemeubileerd",
    "Doenorgaan": "Doorgaan",
    "Voorgot": "Vergeten",
    "Bij least": "Tenminste",
    "Bijleast": "Tenminste",
    "Aane": "Een",
    "Naarevoegenress": "Adres",
    "Slechtkamer": "Badkamer",
    "Schooning": "Schoonmaak",
    "Naarevoegen": "Toevoegen",
    "Omhooggrade": "Upgrade",
    "Omhoogpercase": "Hoofdletter",
    "Aanmaken": "Maken",
    "Dichtbij": "Sluiten",
    "Zien": "Bekijk",
    "Gaan": "Ga",
    "Lezen": "Lees",
    "Volgen": "Volg",
    "Abonneren": "Abonneer",
    "Duidelijk": "Wis",
    "Naarnen": "Toon",
    "Opslaand": "Opgeslagen",
    "Doenenwngrade": "Downgrade",
    "Bewerken": "Bewerk",
    "Doenorgaan": "Ga door",
    "Verzenden": "Verstuur",
    "Snelly": "Snel",
    "Uitzicht": "Bekijk",
    "Vorige": "Vorige",
    "Volgende": "Volgende",
    "Opslaan": "Bewaar",
    "Verwijderen": "Verwijder",
    "Bijwerken": "Update",
    "Bevestig": "Bevestig",
    "Annuleer": "Annuleer",
    "Uitloggen": "Log uit",
    "Registreren": "Registreer",
    "Zoeken": "Zoek",
    "Bladeren": "Blader",
    "Vinden": "Vind",
    "Krijgen": "Krijg",
    "Startenen": "Begin",
    "Lezeny": "Klaar",
    "Naaruch": "Contact",
    "Taarnen": "Toon",
    "Deelnemen": "Word lid",
    "Bestee": "Beste",
    "Doenenn't": "Mis niet",
    "Vindening": "Vind",
    "Zienkers": "Zoekers",
    "Berichts": "Berichten",
    "Neetificatie": "Notificatie",
    "Waarschuwings": "Waarschuwingen",
    "Profiel": "Profiel",
    "Instellingen": "Instellingen",
    "Beveiligd": "Beveilig",
    "Voorwaarden": "Voorwaarden",
    "Beleid": "Beleid",
    "Gebruiker": "Gebruiker",
    "Bladeren": "Blader",
    "Eigenaars": "Eigenaren",
    "Eigenaar": "Eigenaar",
    "Klant": "Klant",
    "Aandersteuning": "Ondersteuning",
    "Terugend": "Backend",
    "Kantoor": "Kantoor",
    "Uurs": "Uren",
    "Veelgestelde vragen": "FAQ",
    "Rechtss": "Rechten",
    "Reserveererd": "Gereserveerd",
    "Veranderings": "Wijzigingen",
    "Succes": "Succesvol",
    "Voorget": "Vergeet",
    "Alsjeblieft": "Gelieve",
    "Invoeren": "Voer in",
    "Compleet": "Volledig",
    "Selecteer": "Selecteer",
    "Reserveerer": "Reserveer",
    "Doenor": "Door",
    "Bevestigation": "Bevestiging",
    "Aanze": "Onze",
    "Aans": "Ons",
    "Doenencument": "Document",
    
    # Engelse woorden die nog over zijn
    "Our expert visits the property to carry out verifications, depending on the package you choose. This includes for example: inspecting the property's structure, identifying potential hidden costs, taking photos or a video walkthrough, verifying the landlord's identity, and reviewing the rental or sales agreement.": "Onze expert bezoekt het eigendom om verificaties uit te voeren, afhankelijk van het pakket dat je kiest. Dit omvat bijvoorbeeld: inspecteren van de eigendom structuur, identificeren van potentiële verborgen kosten, foto's maken of een video rondleiding, verifiëren van de verhuurder identiteit, en beoordelen van de huur- of verkoopovereenkomst.",
    
    "Once the inspection is complete, you'll receive a personalized and detailed report via email or WhatsApp. With this comprehensive overview, you'll be empowered to make a safe and well-informed rental or purchase decision.": "Zodra de inspectie voltooid is, ontvang je een gepersonaliseerd en gedetailleerd rapport via e-mail of WhatsApp. Met dit uitgebreide overzicht ben je in staat om een veilige en goed geïnformeerde huur- of koopbeslissing te nemen.",
    
    "Why choose Property Plaza's Bali villa inspection?": "Waarom kiezen voor Property Plaza's Bali villa inspectie?",
    
    "Villa visit + landlord verification + photos + rental agreement draft": "Villa bezoek + verhuurder verificatie + foto's + huurovereenkomst concept",
    "Property inspection + written report + video walkthrough + consultation call": "Eigendom inspectie + schriftelijk rapport + video rondleiding + consultatie gesprek",
    "BPN ownership verification + long-term rental template + priority booking": "BPN eigendom verificatie + langetermijn huur sjabloon + prioriteit boeken",
    "Photos + voice summary + rental agreement draft": "Foto's + stem overzicht + huurovereenkomst concept",
    "Written inspection report + video + consultation call": "Schriftelijk inspectie rapport + video + consultatie gesprek",
    "Complete package + BPN verification + long-term rental template": "Compleet pakket + BPN verificatie + langetermijn huur sjabloon",
    
    # Overige Engelse zinnen
    "Enter your first name": "Voer je voornaam in",
    "Enter your last name": "Voer je achternaam in",
    "Please enter a valid email address": "Voer een geldig e-mailadres in",
    "Please enter a valid WhatsApp number": "Voer een geldig WhatsApp nummer in",
    "Please provide a complete villa address": "Geef een volledig villa adres op",
    "Email address is required": "E-mailadres is verplicht",
    "WhatsApp number is required": "WhatsApp nummer is verplicht",
    "Villa address is required": "Villa adres is verplicht",
    "First name is required": "Voornaam is verplicht",
    "Last name is required": "Achternaam is verplicht",
    "First name must be at least 2 characters": "Voornaam moet minimaal 2 karakters zijn",
    "Last name must be at least 2 characters": "Achternaam moet minimaal 2 karakters zijn",
    
    # Meer specifieke vertalingen
    "At least one number": "Tenminste één cijfer",
    "At least one uppercase": "Tenminste één hoofdletter", 
    "At least one lowercase": "Tenminste één kleine letter",
    "At least one symbol": "Tenminste één symbool",
    "Not common word": "Geen veelgebruikt woord",
    "Minimum 8 characters": "Minimaal 8 karakters",
    
    "we will send your request password to your email": "we sturen je wachtwoord verzoek naar je e-mail",
    "Password should have minimum 8 Characters, 1 Uppercase, 1 lowercase, 1 number and 1 Symbol": "Wachtwoord moet minimaal 8 karakters hebben, 1 hoofdletter, 1 kleine letter, 1 cijfer en 1 symbool",
    
    "Sign Up now": "Registreer nu",
    "Continue with": "Ga door met",
    "See translation": "Bekijk vertaling",
    "See original": "Bekijk origineel",
    "Need help? Chat with our Customer Support": "Hulp nodig? Chat met onze Klantenservice",
    "Create listing request": "Maak listing verzoek",
    "Create listing": "Maak listing",
    "Contact Account Manager": "Neem contact op met Account Manager",
    "Finish review": "Voltooi beoordeling",
    "Save as draft": "Bewaar als concept",
    "Add review": "Voeg beoordeling toe",
    "Join the waiting list": "Sluit je aan bij de wachtlijst",
    "Request change password": "Verzoek wachtwoord wijziging",
    "Contact us on Whatsapp": "Neem contact met ons op via WhatsApp",
    "Create your account": "Maak je account aan",
    "Go back": "Ga terug",
    "Remove search": "Verwijder zoekopdracht",
    "Send request": "Verstuur verzoek",
    "Close chat": "Sluit chat",
    "Quickly Sell or Rent Your Property": "Verkoop of verhuur snel je eigendom",
    "Activate listing": "Activeer listing",
    "Change status": "Wijzig status",
    "Change price": "Wijzig prijs",
    "top up": "bijladen",
    "View all property": "Bekijk alle eigendommen",
    "Read more": "Lees meer",
    "Change password": "Wijzig wachtwoord",
    "Clear all": "Wis alles",
    "Contact the Owner": "Neem contact op met de eigenaar",
    "Read less": "Lees minder",
    "Copy link": "Kopieer link",
    "Find other properties": "Vind andere eigendommen",
    "Find other packages": "Vind andere pakketten",
    "Follow us on Instagram": "Volg ons op Instagram",
    "Change Photo": "Wijzig foto",
    "Save Changes": "Bewaar wijzigingen",
    "View all": "Bekijk alles",
    "Sign out all device": "Log uit op alle apparaten",
    "Sign out device": "Log uit apparaat",
    "Add payment method": "Voeg betaalmethode toe",
    "Show all properties": "Toon alle eigendommen",
    "Generate 2FA code": "Genereer 2FA code",
    "Cancel subscription": "Annuleer abonnement",
    "Contact representative": "Neem contact op met vertegenwoordiger",
    "Continue to Payment": "Ga door naar betaling",
    
    # Meer UI termen
    "Total bathroom": "Totaal badkamers",
    "Total bedroom": "Totaal slaapkamers",
    "View of property": "Uitzicht van eigendom",
    "Village fee": "Dorpstaks",
    "Type living": "Type wonen",
    "Continue to your property": "Ga door naar je eigendom",
    "Create Account": "Maak account aan",
    "Forgot your": "Vergeten je",
    "do not match": "komen niet overeen",
    "Phone number": "Telefoonnummer",
    "Confirm password": "Bevestig wachtwoord",
    "First name": "Voornaam",
    "Last name": "Achternaam",
    "Property Type": "Eigendom type",
    "View Type": "Uitzicht type",
    "Land Area": "Grond oppervlakte",
    "Building Area": "Gebouw oppervlakte",
    "Garden/Outdoor Space": "Tuin/Buitenruimte",
    "# of Bedrooms": "# Slaapkamers",
    "# of Bathrooms": "# Badkamers",
    "total Cleaning": "totale schoonmaak",
    "Cleaning time": "Schoonmaak tijd",
    "Property address": "Eigendom adres",
    "Property Location": "Eigendom locatie",
    "Home address": "Thuis adres",
    "Property type": "Eigendom type",
    "Property View": "Eigendom uitzicht",
    "Property description": "Eigendom beschrijving",
    "Tell us a bit about your property": "Vertel ons iets over je eigendom",
    "Tell us about things you need to changes": "Vertel ons wat je wilt wijzigen",
    "Input your": "Voer je in",
    "Select": "Selecteer",
    "Set your": "Stel je in",
    "Earliest available contract date": "Vroegst beschikbare contractdatum",
    "Search FAQ": "Zoek FAQ",
    "Can I also contact you via WhatsApp?": "Kan ik jullie ook contacteren via WhatsApp?",
    "Villa with 4 bedrooms, 3 bathrooms, and a private pool located in Canggu, Badung": "Villa met 4 slaapkamers, 3 badkamers en een privé zwembad gelegen in Canggu, Badung",
    "I want to change the amount of bathroom": "Ik wil het aantal badkamers wijzigen",
    "Search for destination": "Zoek naar bestemming",
    
    # Meer specifieke termen
    "Email or Phone number": "E-mail of telefoonnummer",
    "Your language preferences": "Je taalvoorkeuren",
    "Casco property": "Casco eigendom",
    "Year Built": "Bouwjaar",
    "Property View": "Eigendom uitzicht",
    "Coordinates": "Coördinaten",
    "Entrance Road Size": "Toegangsweg grootte",
    "Postal code": "Postcode",
    "Contract type": "Contract type",
    "Available from": "Beschikbaar vanaf",
    "Minimum duration": "Minimale duur",
    "Asking Price": "Vraagprijs",
    "Maximum duration": "Maximale duur",
    "Electricity": "Elektriciteit",
    "Furnishing": "Meubilering",
    "Swimming Pool": "Zwembad",
    "Living": "Wonen",
    "Full name": "Volledige naam",
    "Purchase date": "Aankoopdatum",
    "Promo code": "Promocode",
    "Listing status": "Listing status",
    "User ID": "Gebruikers ID",
    
    # Meer algemene termen
    "Success request Forgot password": "Succesvol wachtwoord vergeten verzoek",
    "Please check your email to continue the process": "Controleer je e-mail om door te gaan",
    "Success update user detail": "Succesvol gebruikersgegevens bijgewerkt",
    "Success create new password": "Succesvol nieuw wachtwoord aangemaakt",
    "Please log in to continue": "Log in om door te gaan",
    "Follow us for Bali vibes, insider tips, and exclusive updates!": "Volg ons voor Bali vibes, insider tips en exclusieve updates!",
    "Get latest update from our team and owner": "Krijg de laatste updates van ons team en eigenaren",
    "Oops, Can't send verification mail": "Oeps, kan verificatie e-mail niet versturen",
    "Ascending order": "Oplopende volgorde",
    "Descending order": "Aflopende volgorde",
    "property-plaza user profile image": "property-plaza gebruiker profiel afbeelding",
    "Your Ideal Bali Property Is on the Way": "Je ideale Bali eigendom is onderweg",
    "We know how important it is to find the right space, whether it's a homestay, shop, or serene piece of land. That's why we're taking the time to source only listings that truly meet your needs.": "We weten hoe belangrijk het is om de juiste ruimte te vinden, of het nu een homestay, winkel of een rustig stuk land is. Daarom nemen we de tijd om alleen listings te vinden die echt aan je behoeften voldoen.",
    
    # Contact en andere pagina's
    "Contact Us | Property Plaza": "Neem contact op | Property Plaza",
    "Contact Property Plaza. Get in touch with our team for inquiries, support, or collaboration. We're ready to assist you with prompt, reliable responses.": "Neem contact op met Property Plaza. Neem contact op met ons team voor vragen, ondersteuning of samenwerking. We staan klaar om je te helpen met snelle, betrouwbare antwoorden.",
    "Contact Us": "Neem contact op",
    "Have questions or need assistance? We're here to help. Reach out to us using the form below.": "Heb je vragen of hulp nodig? We zijn er om te helpen. Neem contact met ons op via het onderstaande formulier.",
    "Send Us a Message": "Stuur ons een bericht",
    "Your Name": "Je naam",
    "Your Email": "Je e-mail",
    "Subject": "Onderwerp",
    "Your Message": "Je bericht",
    "Send Message": "Verstuur bericht",
    "Your message has been sent successfully. We'll get back to you soon!": "Je bericht is succesvol verzonden. We nemen binnenkort contact met je op!",
    
    # Verify sectie specifieke termen
    "Rental terms": "Huurvoorwaarden",
    "Owner property": "Eigenaar eigendom",
    "{count} people already contacted the owner": "{count} mensen hebben al contact opgenomen met de eigenaar",
    
    # Meer verify termen die nog Engels zijn
    "Book Your Inspection": "Boek je inspectie",
    "Fill out the form below and we'll contact you within 24 hours": "Vul het onderstaande formulier in en we nemen binnen 24 uur contact met je op",
    "Reserve Inspection": "Reserveer inspectie",
    "By submitting this form, you agree to be contacted via WhatsApp for inspection coordination.": "Door dit formulier in te dienen, ga je ermee akkoord dat we contact met je opnemen via WhatsApp voor inspectie coördinatie.",
    "We'll contact you within 24 hours to confirm your inspection.": "We nemen binnen 24 uur contact met je op om je inspectie te bevestigen.",
    "Failed to submit booking. Please try again.": "Boeking indienen mislukt. Probeer het opnieuw.",
    "Your villa inspection has been booked successfully.": "Je villa inspectie is succesvol geboekt.",
    "What happens next?": "Wat gebeurt er nu?",
    "Confirmation Email": "Bevestigings e-mail",
    "You'll receive a detailed confirmation email within 5 minutes.": "Je ontvangt binnen 5 minuten een gedetailleerde bevestigingsmail.",
    "Inspection Scheduling": "Inspectie inplannen",
    "Our team will contact you within 24 hours to confirm the exact time.": "Ons team neemt binnen 24 uur contact met je op om de exacte tijd te bevestigen.",
    "Direct Communication": "Directe communicatie",
    "We'll reach out via WhatsApp for any updates or questions.": "We nemen contact op via WhatsApp voor updates of vragen.",
    "Next Steps": "Volgende stappen",
    "Check your email for booking confirmation and preparation checklist": "Controleer je e-mail voor boekingsbevestiging en voorbereidingschecklist",
    "Ensure someone is available at the property during inspection": "Zorg ervoor dat er iemand beschikbaar is bij het eigendom tijdens de inspectie",
    "Prepare any relevant documents (if available)": "Bereid relevante documenten voor (indien beschikbaar)",
    "Our inspector will contact you 1 day before the visit": "Onze inspecteur neemt 1 dag voor het bezoek contact met je op",
    "Back to Home": "Terug naar home",
    "Contact Support": "Neem contact op met ondersteuning",
    "We act as an independent inspection service. Our reports are based on on-site observations and available documents. No rights can be derived from our findings. We do not provide legal guarantees regarding ownership or land title. Always consult a certified notary or legal advisor for legal due diligence.": "Wij fungeren als een onafhankelijke inspectieservice. Onze rapporten zijn gebaseerd op ter plaatse observaties en beschikbare documenten. Aan onze bevindingen kunnen geen rechten worden ontleend. Wij bieden geen juridische garanties betreffende eigendom of grondtitel. Raadpleeg altijd een gecertificeerde notaris of juridisch adviseur voor juridische due diligence.",
    "📋 Book Professional Villa Inspection Now": "📋 Boek nu professionele villa inspectie",
    "✅ Protect your investment • Avoid scams • Get peace of mind": "✅ Bescherm je investering • Vermijd oplichting • Krijg gemoedsrust"
}

def clean_translations(text):
    """Clean up translation artifacts and fix common issues"""
    if isinstance(text, str):
        # Apply all translations
        for en, nl in final_translations.items():
            text = text.replace(en, nl)
        return text
    return text

def clean_json_recursive(obj):
    """Recursively clean all string values in a JSON object"""
    if isinstance(obj, dict):
        return {key: clean_json_recursive(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [clean_json_recursive(item) for item in obj]
    elif isinstance(obj, str):
        return clean_translations(obj)
    else:
        return obj

# Load the JSON file
with open('property-plaza/locales/nl.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# Clean the data
cleaned_data = clean_json_recursive(data)

# Save the cleaned data
with open('property-plaza/locales/nl.json', 'w', encoding='utf-8') as f:
    json.dump(cleaned_data, f, ensure_ascii=False, indent=2)

print("Final cleanup completed!")
