{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "content-encoding": "gzip", "content-type": "application/json; charset=utf-8", "date": "Fri, 18 Jul 2025 08:11:17 GMT", "ratelimit-limit": "500", "ratelimit-remaining": "499", "ratelimit-reset": "1", "sanity-gateway": "k8s-gcp-as-s1-prod-ing-01", "sanity-query-hash": "nGr/ocMq7is /jrbFFWNuk8", "server-timing": "api;dur=11", "strict-transport-security": "max-age=63072000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding, origin", "via": "1.1 google", "x-ratelimit-limit-second": "500", "x-ratelimit-remaining-second": "499", "x-sanity-shard": "gcp-eu-w1-01-prod-1019", "x-served-by": "gradient-query-6bc987cbd5-hqcx6", "xkey": "project-r294h68q, project-r294h68q-production, s1:XbRIFA, project-r294h68q, project-r294h68q-production"}, "body": "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", "status": 200, "url": "https://r294h68q.api.sanity.io/v2024-01-05/data/query/production?query=*%5B_type+%3D%3D+%22seoContent%22+%26%26+language+%3D%3D+%24language%5D%7Btitle%2Cbody%7D&%24language=%22en%22&returnQuery=false&perspective=published"}, "revalidate": 3600, "tags": []}