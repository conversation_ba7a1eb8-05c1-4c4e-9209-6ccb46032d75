"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_app_locale_user_listings_faq_content_tsx";
exports.ids = ["_rsc_app_locale_user_listings_faq_content_tsx"];
exports.modules = {

/***/ "(rsc)/./app/[locale]/(user)/(listings)/faq/content.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/(user)/(listings)/faq/content.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SeekerFaQContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sidebar */ \"(rsc)/./app/[locale]/(user)/(listings)/faq/sidebar.tsx\");\n/* harmony import */ var _detail__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./detail */ \"(rsc)/./app/[locale]/(user)/(listings)/faq/detail.tsx\");\n/* harmony import */ var _components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seekers-content-layout/default-layout-content */ \"(rsc)/./components/seekers-content-layout/default-layout-content.tsx\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(rsc)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/useTranslations.js\");\n/* harmony import */ var _barrel_optimize_names_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Sparkles!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n\n\n\n\n\n\n\nfunction SeekerFaQContent() {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(\"seeker\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"faq\",\n        className: \"bg-seekers-foreground/50 text-black w-full py-12 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"inline-flex items-center gap-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"max-sm:hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, void 0),\n                        \" \",\n                        t(\"faq.title\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, void 0),\n                className: \"!mt-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex md:gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_detail__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n            lineNumber: 12,\n            columnNumber: 5\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\(listings)\\\\faq\\\\content.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/[locale]/(user)/(listings)/faq/content.tsx\n");

/***/ }),

/***/ "(rsc)/./app/[locale]/(user)/(listings)/faq/detail.tsx":
/*!*******************************************************!*\
  !*** ./app/[locale]/(user)/(listings)/faq/detail.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\detail.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/[locale]/(user)/(listings)/faq/sidebar.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/(user)/(listings)/faq/sidebar.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SidebarContent: () => (/* binding */ e0),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\sidebar.tsx#default`));

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\sidebar.tsx#SidebarContent`);


/***/ })

};
;