"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_components_footer_seeker-seo_tsx";
exports.ids = ["_rsc_components_footer_seeker-seo_tsx"];
exports.modules = {

/***/ "(rsc)/./components/footer/seeker-seo.tsx":
/*!******************************************!*\
  !*** ./components/footer/seeker-seo.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SeekerSeo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _core_services_sanity_services__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/core/services/sanity/services */ \"(rsc)/./core/services/sanity/services.ts\");\n/* harmony import */ var _seekers_seo_content__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./seekers-seo-content */ \"(rsc)/./components/footer/seekers-seo-content.tsx\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\nasync function SeekerSeo() {\n    const cookiesStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n    const locale = cookiesStore.get(\"NEXT_LOCALE\")?.value;\n    const seoContent = await (0,_core_services_sanity_services__WEBPACK_IMPORTED_MODULE_1__.getHomepageSeoContent)(locale?.toLowerCase() || \"en\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_seekers_seo_content__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        content: seoContent[0]\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\footer\\\\seeker-seo.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb21wb25lbnRzL2Zvb3Rlci9zZWVrZXItc2VvLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQXdFO0FBQ2xCO0FBQ2Y7QUFFeEIsZUFBZUc7SUFDNUIsTUFBTUMsZUFBZUYscURBQU9BO0lBQzVCLE1BQU1HLFNBQVNELGFBQWFFLEdBQUcsQ0FBQyxnQkFBZ0JDO0lBQ2hELE1BQU1DLGFBQWEsTUFBTVIscUZBQXFCQSxDQUFDSyxRQUFRSSxpQkFBaUI7SUFFeEUscUJBQU8sOERBQUNSLDREQUFpQkE7UUFBQ1MsU0FBU0YsVUFBVSxDQUFDLEVBQUU7Ozs7OztBQUNsRCIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vY29tcG9uZW50cy9mb290ZXIvc2Vla2VyLXNlby50c3g/YTE0YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRIb21lcGFnZVNlb0NvbnRlbnQgfSBmcm9tIFwiQC9jb3JlL3NlcnZpY2VzL3Nhbml0eS9zZXJ2aWNlc1wiO1xyXG5pbXBvcnQgU2Vla2Vyc1Nlb0NvbnRlbnQgZnJvbSBcIi4vc2Vla2Vycy1zZW8tY29udGVudFwiO1xyXG5pbXBvcnQgeyBjb29raWVzIH0gZnJvbSBcIm5leHQvaGVhZGVyc1wiO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gU2Vla2VyU2VvKCkge1xyXG4gIGNvbnN0IGNvb2tpZXNTdG9yZSA9IGNvb2tpZXMoKVxyXG4gIGNvbnN0IGxvY2FsZSA9IGNvb2tpZXNTdG9yZS5nZXQoJ05FWFRfTE9DQUxFJyk/LnZhbHVlXHJcbiAgY29uc3Qgc2VvQ29udGVudCA9IGF3YWl0IGdldEhvbWVwYWdlU2VvQ29udGVudChsb2NhbGU/LnRvTG93ZXJDYXNlKCkgfHwgXCJlblwiKVxyXG5cclxuICByZXR1cm4gPFNlZWtlcnNTZW9Db250ZW50IGNvbnRlbnQ9e3Nlb0NvbnRlbnRbMF19IC8+XHJcbn0gIl0sIm5hbWVzIjpbImdldEhvbWVwYWdlU2VvQ29udGVudCIsIlNlZWtlcnNTZW9Db250ZW50IiwiY29va2llcyIsIlNlZWtlclNlbyIsImNvb2tpZXNTdG9yZSIsImxvY2FsZSIsImdldCIsInZhbHVlIiwic2VvQ29udGVudCIsInRvTG93ZXJDYXNlIiwiY29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./components/footer/seeker-seo.tsx\n");

/***/ }),

/***/ "(rsc)/./components/footer/seekers-seo-content.tsx":
/*!***************************************************!*\
  !*** ./components/footer/seekers-seo-content.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\footer\seekers-seo-content.tsx#default`));


/***/ }),

/***/ "(rsc)/./core/services/sanity/client-config.ts":
/*!***********************************************!*\
  !*** ./core/services/sanity/client-config.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst config = {\n    projectId: \"r294h68q\",\n    dataset: \"production\",\n    apiVersion: \"2024-01-05\",\n    useCdn: false,\n    token: process.env.SANITY_API_KEY,\n    perspective: \"published\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9jb3JlL3NlcnZpY2VzL3Nhbml0eS9jbGllbnQtY29uZmlnLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFFQSxNQUFNQSxTQUFTO0lBQ2JDLFdBQVdDLFVBQXlDO0lBQ3BERyxTQUFTSCxZQUE0QztJQUNyREssWUFBWTtJQUNaQyxRQUFRO0lBQ1JDLE9BQU9QLFFBQVFDLEdBQUcsQ0FBQ08sY0FBYztJQUNqQ0MsYUFBYTtBQUNmO0FBRUEsaUVBQWVYLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL2NvcmUvc2VydmljZXMvc2FuaXR5L2NsaWVudC1jb25maWcudHM/MGYyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDbGllbnRQZXJzcGVjdGl2ZSB9IGZyb20gXCJuZXh0LXNhbml0eVwiO1xyXG5cclxuY29uc3QgY29uZmlnID0ge1xyXG4gIHByb2plY3RJZDogcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU0FOSVRZX1BST0pFQ1RfSUQgYXMgc3RyaW5nLFxyXG4gIGRhdGFzZXQ6IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NBTklUWV9QUk9KRUNUX1RJVExFIGFzIHN0cmluZyxcclxuICBhcGlWZXJzaW9uOiBcIjIwMjQtMDEtMDVcIixcclxuICB1c2VDZG46IGZhbHNlLFxyXG4gIHRva2VuOiBwcm9jZXNzLmVudi5TQU5JVFlfQVBJX0tFWSBhcyBzdHJpbmcsXHJcbiAgcGVyc3BlY3RpdmU6IFwicHVibGlzaGVkXCIgYXMgQ2xpZW50UGVyc3BlY3RpdmUsXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBjb25maWc7XHJcbiJdLCJuYW1lcyI6WyJjb25maWciLCJwcm9qZWN0SWQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU0FOSVRZX1BST0pFQ1RfSUQiLCJkYXRhc2V0IiwiTkVYVF9QVUJMSUNfU0FOSVRZX1BST0pFQ1RfVElUTEUiLCJhcGlWZXJzaW9uIiwidXNlQ2RuIiwidG9rZW4iLCJTQU5JVFlfQVBJX0tFWSIsInBlcnNwZWN0aXZlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./core/services/sanity/client-config.ts\n");

/***/ }),

/***/ "(rsc)/./core/services/sanity/query.ts":
/*!***************************************!*\
  !*** ./core/services/sanity/query.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TermsOfUseContent: () => (/* binding */ TermsOfUseContent),\n/* harmony export */   homepageSeoContent: () => (/* binding */ homepageSeoContent),\n/* harmony export */   morePostsBasedOnCategoryQuery: () => (/* binding */ morePostsBasedOnCategoryQuery),\n/* harmony export */   postQuery: () => (/* binding */ postQuery),\n/* harmony export */   postQueryByAuthor: () => (/* binding */ postQueryByAuthor),\n/* harmony export */   postQueryByCategory: () => (/* binding */ postQueryByCategory),\n/* harmony export */   postQueryBySlug: () => (/* binding */ postQueryBySlug),\n/* harmony export */   postQueryByTag: () => (/* binding */ postQueryByTag),\n/* harmony export */   postQueryHomepage: () => (/* binding */ postQueryHomepage),\n/* harmony export */   privacyPolicyContent: () => (/* binding */ privacyPolicyContent),\n/* harmony export */   userDataDeletionContent: () => (/* binding */ userDataDeletionContent)\n/* harmony export */ });\n/* harmony import */ var next_sanity__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-sanity */ \"(rsc)/./node_modules/groq/lib/groq.js\");\n\nconst postData = `{\r\n_id,\r\n  title,\r\n  metadata,\r\n  slug,\r\n  tags,\r\n  author->{\r\n    _id,\r\n    name,\r\n    slug,\r\n    image{asset -> {url}},\r\n    bio\r\n  },\r\n  coverImage{\r\n  asset->{url}},\r\n  mainImage{\r\n  asset->{url}},\r\n  publishedAt,\r\n  category -> {\r\n  _id,\r\n  title\r\n  },\r\n  body\r\n}`;\nconst postQuery = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && author != \"hidden\"] ${postData}`;\nconst postQueryHomepage = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && author != \"hidden\"][0...2] ${postData}`; // use this on homepage\nconst postQueryBySlug = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && slug.current == $slug][0]  ${postData}\r\n  \r\n\r\n`;\nconst postQueryByTag = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && $slug in tags[]->slug.current] ${postData}`;\nconst postQueryByAuthor = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && author->slug.current == $slug] ${postData}`;\nconst postQueryByCategory = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"post\" && category->slug.current == $slug] ${postData}`;\n// search by category and exclude id\nconst morePostsBasedOnCategoryQuery = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`\r\n  *[_type == \"post\" && _id != $id] | order(date asc, _updatedAt asc) [0...4] \r\n    ${postData}\r\n  `;\nconst homepageSeoContent = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"seoContent\" && language == $language]{title,body}`;\nconst TermsOfUseContent = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"termsOfUse\" && language == $language]{title,body}`;\nconst privacyPolicyContent = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"privacyPolicy\" && language == $language]{title,body}`;\nconst userDataDeletionContent = (0,next_sanity__WEBPACK_IMPORTED_MODULE_0__[\"default\"])`*[_type == \"userDataDeletion\" && language == $language]{title,body}`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./core/services/sanity/query.ts\n");

/***/ }),

/***/ "(rsc)/./core/services/sanity/services.ts":
/*!******************************************!*\
  !*** ./core/services/sanity/services.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   client: () => (/* binding */ client),\n/* harmony export */   getHomepagePosts: () => (/* binding */ getHomepagePosts),\n/* harmony export */   getHomepageSeoContent: () => (/* binding */ getHomepageSeoContent),\n/* harmony export */   getPostByCategory: () => (/* binding */ getPostByCategory),\n/* harmony export */   getPostBySlug: () => (/* binding */ getPostBySlug),\n/* harmony export */   getPosts: () => (/* binding */ getPosts),\n/* harmony export */   getPrivacyPolictContent: () => (/* binding */ getPrivacyPolictContent),\n/* harmony export */   getTermsOfUseContent: () => (/* binding */ getTermsOfUseContent),\n/* harmony export */   getUserDeletionContent: () => (/* binding */ getUserDeletionContent),\n/* harmony export */   imageBuilder: () => (/* binding */ imageBuilder),\n/* harmony export */   sanityFetch: () => (/* binding */ sanityFetch)\n/* harmony export */ });\n/* harmony import */ var _sanity_image_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @sanity/image-url */ \"(rsc)/./node_modules/@sanity/image-url/lib/node/index.js\");\n/* harmony import */ var _sanity_image_url__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_sanity_image_url__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_sanity__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-sanity */ \"(rsc)/./node_modules/@sanity/client/dist/index.browser.js\");\n/* harmony import */ var _client_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client-config */ \"(rsc)/./core/services/sanity/client-config.ts\");\n/* harmony import */ var _query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./query */ \"(rsc)/./core/services/sanity/query.ts\");\n\n\n\n\nconst client = (0,next_sanity__WEBPACK_IMPORTED_MODULE_2__.createClient)(_client_config__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\nfunction imageBuilder(source) {\n    return _sanity_image_url__WEBPACK_IMPORTED_MODULE_3___default()(_client_config__WEBPACK_IMPORTED_MODULE_0__[\"default\"]).image(source);\n}\nasync function sanityFetch({ query, qParams, tags }) {\n    return client.fetch(query, qParams, {\n        next: {\n            tags,\n            revalidate: 3600\n        }\n    });\n}\nconst getHomepagePosts = async ()=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.postQueryHomepage,\n        qParams: {},\n        tags: [\n            \"post\",\n            \"author\",\n            \"category\"\n        ]\n    });\n    return data;\n};\nconst getPosts = async ()=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.postQuery,\n        qParams: {},\n        tags: [\n            \"post\",\n            \"author\",\n            \"category\"\n        ]\n    });\n    return data;\n};\nconst getPostBySlug = async (slug)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.postQueryBySlug,\n        qParams: {\n            slug\n        },\n        tags: [\n            \"post\",\n            \"author\",\n            \"category\"\n        ]\n    });\n    return data;\n};\nconst getPostByCategory = async (slug, id)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.morePostsBasedOnCategoryQuery,\n        qParams: {\n            slug,\n            id\n        },\n        tags: []\n    });\n    return data;\n};\nconst getHomepageSeoContent = async (language)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.homepageSeoContent,\n        qParams: {\n            language\n        },\n        tags: []\n    });\n    return data;\n};\nconst getTermsOfUseContent = async (language)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.TermsOfUseContent,\n        qParams: {\n            language\n        },\n        tags: []\n    });\n    return data;\n};\nconst getPrivacyPolictContent = async (language)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.privacyPolicyContent,\n        qParams: {\n            language\n        },\n        tags: []\n    });\n    return data;\n};\nconst getUserDeletionContent = async (language)=>{\n    const data = await sanityFetch({\n        query: _query__WEBPACK_IMPORTED_MODULE_1__.userDataDeletionContent,\n        qParams: {\n            language\n        },\n        tags: []\n    });\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./core/services/sanity/services.ts\n");

/***/ })

};
;