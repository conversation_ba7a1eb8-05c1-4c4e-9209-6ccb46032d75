{"/_not-found/page": "app/_not-found/page.js", "/api/currency/route": "app/api/currency/route.js", "/[locale]/icon.ico/route": "app/[locale]/icon.ico/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/verify-booking/route": "app/api/verify-booking/route.js", "/api/translate/route": "app/api/translate/route.js", "/[locale]/create-password/page": "app/[locale]/create-password/page.js", "/[locale]/reset-password/page": "app/[locale]/reset-password/page.js", "/[locale]/(user)/page": "app/[locale]/(user)/page.js", "/[locale]/(user)/contact-us/page": "app/[locale]/(user)/contact-us/page.js", "/[locale]/(user)/s/[query]/page": "app/[locale]/(user)/s/[query]/page.js", "/[locale]/(user)/privacy-policy/page": "app/[locale]/(user)/privacy-policy/page.js", "/[locale]/(user)/about-us/page": "app/[locale]/(user)/about-us/page.js", "/[locale]/(user)/terms-of-use/page": "app/[locale]/(user)/terms-of-use/page.js", "/[locale]/(user)/plan/page": "app/[locale]/(user)/plan/page.js", "/[locale]/(user-profile)/notification/page": "app/[locale]/(user-profile)/notification/page.js", "/[locale]/(user)/user-data-deletion/page": "app/[locale]/(user)/user-data-deletion/page.js", "/[locale]/(user-profile)/favorites/page": "app/[locale]/(user-profile)/favorites/page.js", "/[locale]/(user-profile)/billing/page": "app/[locale]/(user-profile)/billing/page.js", "/[locale]/verify/page": "app/[locale]/verify/page.js", "/[locale]/(user-profile)/profile/page": "app/[locale]/(user-profile)/profile/page.js", "/[locale]/(user-profile)/security/page": "app/[locale]/(user-profile)/security/page.js", "/[locale]/(user-profile)/message/page": "app/[locale]/(user-profile)/message/page.js", "/[locale]/(user-profile)/subscription/page": "app/[locale]/(user-profile)/subscription/page.js", "/[locale]/(user)/[title]/page": "app/[locale]/(user)/[title]/page.js", "/[locale]/(user)/posts/[slug]/page": "app/[locale]/(user)/posts/[slug]/page.js"}