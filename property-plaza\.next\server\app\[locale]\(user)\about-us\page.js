(()=>{var a={};a.id=1417,a.ids=[1417],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},273:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}},1695:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\pop-up.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\pop-up.tsx","default")},2895:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["(user)",{children:["about-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,94142)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\about-us\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,35220)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,72952)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,41507))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,31455)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,16787)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\about-us\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/(user)/about-us/page",pathname:"/[locale]/about-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/(user)/about-us/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8303:(a,b,c)=>{Promise.resolve().then(c.bind(c,81295)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},16551:(a,b,c)=>{Promise.resolve().then(c.bind(c,46092)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19439:(a,b,c)=>{Promise.resolve().then(c.bind(c,1695)),Promise.resolve().then(c.bind(c,28504)),Promise.resolve().then(c.bind(c,67735)),Promise.resolve().then(c.bind(c,71810)),Promise.resolve().then(c.bind(c,18543)),Promise.resolve().then(c.bind(c,3203)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,49603,23)),Promise.resolve().then(c.bind(c,97094))},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28504:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\setup-seekers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\setup-seekers.tsx","default")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35220:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(37413),e=c(29666),f=c(67735),g=c(71810);c(61120);var h=c(28504),i=c(44999),j=c(1695),k=c(18543);async function l({children:a}){let b=await (0,i.UL)(),c=b.get("seekers-settings")?.value||"",l=c?JSON.parse(c):void 0,m=b.get("NEXT_LOCALE")?.value;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.default,{isSeeker:!0}),(0,d.jsx)(h.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:(0,d.jsx)(g.default,{currency_:l?.state?.currency,localeId:m})}),(0,d.jsx)("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:a}),(0,d.jsx)("div",{className:"!mt-0",children:(0,d.jsx)(e.A,{})}),(0,d.jsx)(j.default,{})]})}},40162:(a,b,c)=>{"use strict";c.d(b,{default:()=>o});var d=c(60687),e=c(85814),f=c.n(e),g=c(755),h=c(4e3),i=c(11976),j=c(24934),k=c(33213);function l({open:a,setOpen:b,trigger:c}){let e=(0,k.useTranslations)("universal");return(0,d.jsxs)(i.A,{open:a,setOpen:b,openTrigger:c,children:[(0,d.jsx)(h.A,{children:(0,d.jsx)("h3",{className:"text-base font-bold text-seekers-text",children:e("popup.followInstagram.title")})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:e("popup.followInstagram.description")})}),(0,d.jsx)(g.A,{children:(0,d.jsx)(j.$,{asChild:!0,className:"w-full",variant:"default-seekers",children:(0,d.jsx)(f(),{href:"https://www.instagram.com/join.propertyplaza/",children:e("cta.followUsOnInstagram")})})})]})}var m=c(56605),n=c(43210);function o(){let{successSignUp:a,setSuccessSignUp:b,loading:c}=(0,m.k)(),[e,f]=(0,n.useState)(!1),[g,h]=(0,n.useState)(!0);return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(l,{open:e,setOpen:a=>{b(a),f(a)},trigger:(0,d.jsx)(d.Fragment,{})})})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46092:(a,b,c)=>{"use strict";c.d(b,{default:()=>r});var d=c(60687),e=c(43210),f=c(33213),g=c(97992),h=c(62688);let i=(0,h.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),j=(0,h.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var k=c(5395),l=c(30474),m=c(24934),n=c(85814),o=c.n(n),p=c(273);let q={src:"/_next/static/media/office-building.73328fb0.webp",height:2560,width:1920,blurDataURL:"data:image/webp;base64,UklGRlIAAABXRUJQVlA4IEYAAADwAQCdASoGAAgAAkA4JYgCdAD0Y7s8OoAA/vj0FNF0Yy42grTicPZx5nQtif1x0Jkyvbx4Z5yYy2WdVGDoiwyt6c2QAAAA",blurWidth:6,blurHeight:8};function r(){let a=(0,f.useTranslations)("seeker"),[b,c]=(0,e.useState)("company");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"w-full bg-white",children:[(0,d.jsxs)("section",{"aria-label":"About Property Plaza Hero",className:"relative w-full h-[400px] md:h-[500px]",children:[(0,d.jsx)(l.default,{src:p.default,alt:"Property Plaza",fill:!0,className:"object-cover",style:{objectFit:"cover"},priority:!0}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center px-4",children:[(0,d.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:a("aboutUs.hero.title")}),(0,d.jsx)("p",{className:"text-xl text-white max-w-3xl mx-auto",children:a("aboutUs.hero.description")})]})})]}),(0,d.jsx)(k.A,{children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,d.jsxs)("div",{className:"flex flex-wrap border-b border-gray-200 mb-8",children:[(0,d.jsx)("button",{onClick:()=>c("company"),className:`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${"company"===b?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:a("aboutUs.tabs.company")}),(0,d.jsx)("button",{onClick:()=>c("team"),className:`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${"team"===b?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:a("aboutUs.tabs.team")}),(0,d.jsx)("button",{onClick:()=>c("mission"),className:`py-4 text-lg font-medium border-b-2 transition-colors ${"mission"===b?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:a("aboutUs.tabs.mission")})]}),"company"===b&&(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:a("aboutUs.story.companyTitle")}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a("aboutUs.story.paragraph1")}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a("aboutUs.story.paragraph2")}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:a("aboutUs.story.paragraph3")}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,d.jsx)(m.$,{asChild:!0,variant:"default-seekers",children:(0,d.jsx)(o(),{href:"/contact",children:a("aboutUs.hero.contactUs")})}),(0,d.jsx)(m.$,{asChild:!0,variant:"outline",children:(0,d.jsx)(o(),{href:"/s/all",children:a("aboutUs.hero.browseProperties")})})]})]}),(0,d.jsx)("div",{className:"relative h-[400px] rounded-lg overflow-hidden",children:(0,d.jsx)(l.default,{src:q,alt:"Property Plaza Office",fill:!0,className:"object-cover"})})]}),"team"===b&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:a("aboutUs.team.title")}),(0,d.jsx)("p",{className:"text-gray-600 max-w-3xl mx-auto",children:a("aboutUs.team.description")})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:a("aboutUs.team.members.rt.name"),position:a("aboutUs.team.roles.ceo"),bio:a("aboutUs.team.members.rt.bio"),image:"/team-member-ricardo-2.jpg"},{name:a("aboutUs.team.members.thijs.name"),position:a("aboutUs.team.roles.cto"),bio:a("aboutUs.team.members.thijs.bio"),image:"/team-member-thijs-2.jpg"},{name:a("aboutUs.team.members.joost.name"),position:a("aboutUs.team.roles.marketing"),bio:a("aboutUs.team.members.joost.bio"),image:"/team-member-joost.jpg"},{name:a("aboutUs.team.members.dennis.name"),position:a("aboutUs.team.roles.propertySpecialist"),bio:a("aboutUs.team.members.dennis.bio"),image:"/team-member-dennis.jpg"},{name:a("aboutUs.team.members.andrea.name"),position:a("aboutUs.team.roles.propertySpecialist"),bio:a("aboutUs.team.members.andrea.bio"),image:"/team-member-andrea.jpg"},{name:a("aboutUs.team.members.natha.name"),position:a("aboutUs.team.roles.propertySpecialist"),bio:a("aboutUs.team.members.natha.bio"),image:"/team-member-natha.jpg"},{name:a("aboutUs.team.members.aditya.name"),position:a("aboutUs.team.roles.frontend"),bio:a("aboutUs.team.members.aditya.bio"),image:"/team-member-aditya.jpg"},{name:a("aboutUs.team.members.anjas.name"),position:a("aboutUs.team.roles.backend"),bio:a("aboutUs.team.members.anjas.bio"),image:"/team-member-anjas.jpg"},{name:a("aboutUs.team.members.nuni.name"),position:a("aboutUs.team.roles.backend2"),bio:a("aboutUs.team.members.nuni.bio"),image:"/team-member-nuni.jpg"},{name:a("aboutUs.team.members.rizki.name"),position:a("aboutUs.team.roles.tester"),bio:a("aboutUs.team.members.rizki.bio"),image:"/team-member-rizki.jpg"}].map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow",children:[(0,d.jsx)("div",{className:"flex justify-center pt-6",children:(0,d.jsx)("div",{className:"relative !h-[180px] !w-[180px] rounded-full overflow-hidden border-4 border-seekers-primary/10",children:(0,d.jsx)(l.default,{src:a.image,alt:a.name,fill:!0})})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-1 text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-seekers-primary font-medium mb-3",children:a.position}),(0,d.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:a.bio})]})]},b))})]}),"mission"===b&&(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-12",children:[(0,d.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:(0,d.jsx)("path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"})})}),(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:a("aboutUs.mission.ourMission.title")}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a("aboutUs.mission.description")}),(0,d.jsx)("p",{className:"text-gray-600",children:a("aboutUs.mission.ourMission.additionalText")})]}),(0,d.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:(0,d.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:[(0,d.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,d.jsx)("path",{d:"m16 10-4 4-4-4"})]})}),(0,d.jsx)("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:a("aboutUs.mission.ourVision.title")}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a("aboutUs.mission.ourVision.description")}),(0,d.jsx)("p",{className:"text-gray-600",children:a("aboutUs.mission.ourVision.additionalText")})]}),(0,d.jsxs)("div",{className:"md:col-span-2 mt-8",children:[(0,d.jsx)("h3",{className:"text-2xl font-bold mb-6 text-gray-800",children:a("aboutUs.mission.ourCoreValues.title")}),(0,d.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:[{title:a("aboutUs.mission.values.global.title"),description:a("aboutUs.mission.values.global.description")},{title:a("aboutUs.mission.values.trust.title"),description:a("aboutUs.mission.values.trust.description")},{title:a("aboutUs.mission.values.quality.title"),description:a("aboutUs.mission.values.quality.description")},{title:a("aboutUs.mission.values.community.title"),description:a("aboutUs.mission.values.community.description")},{title:a("aboutUs.mission.values.innovation.title"),description:a("aboutUs.mission.values.innovation.description")},{title:a("aboutUs.mission.values.personalization.title"),description:a("aboutUs.mission.values.personalization.description")}].map((a,b)=>(0,d.jsxs)("div",{className:"border border-gray-200 p-6 rounded-lg hover:border-seekers-primary transition-colors",children:[(0,d.jsx)("h4",{className:"text-xl font-semibold mb-3 text-gray-800",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600",children:a.description})]},b))})]})]})]})}),(0,d.jsx)("div",{className:"bg-seekers-foreground/10 py-16 mt-16",children:(0,d.jsxs)(k.A,{children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:a("aboutUs.contact.title")}),(0,d.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:a("aboutUs.cta.description")})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(g.A,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:a("aboutUs.contact.visitUs.title")}),(0,d.jsx)("p",{className:"text-gray-600 whitespace-pre-line",children:a("aboutUs.contact.visitUs.address")})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(i,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:a("aboutUs.contact.emailUs.title")}),(0,d.jsx)("p",{className:"text-gray-600 mb-2",children:a("aboutUs.contact.emailUs.general")}),(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:a("aboutUs.contact.emailUs.generalEmail")}),(0,d.jsx)("p",{className:"text-gray-600 mt-2 mb-2",children:a("aboutUs.contact.emailUs.listings")}),(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:a("aboutUs.contact.emailUs.listingsEmail")})]}),(0,d.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(j,{className:"h-6 w-6 text-white"})}),(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:a("aboutUs.contact.callUs.title")}),(0,d.jsx)("p",{className:"text-gray-600 mb-2",children:a("aboutUs.contact.callUs.officeHours")}),(0,d.jsx)("p",{className:"text-gray-600 mt-4 mb-2",children:a("aboutUs.contact.callUs.whatsapp")}),(0,d.jsx)("a",{href:"https://wa.me/6281234567890",className:"text-seekers-primary hover:underline",children:a("aboutUs.contact.callUs.whatsappNumber")})]})]}),(0,d.jsx)("div",{className:"flex justify-center mt-12",children:(0,d.jsx)(m.$,{asChild:!0,variant:"default-seekers",size:"lg",children:(0,d.jsx)(o(),{href:"/s/all",children:a("aboutUs.cta.findProperty")})})})]})})]})})}},53258:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(77273),f=c(66835);function g(){let{setSeekers:a,setRole:b}=(0,f.k)(a=>a);return(0,e.H)(),(0,d.jsx)(d.Fragment,{})}c(43210)},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64784:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A});var d=c(81961)},72952:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(39916);function e(){(0,d.redirect)("/")}},74075:a=>{"use strict";a.exports=require("zlib")},75074:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(61120),e=c(67133),f=c(84757),g=(0,d.cache)(async function(a){let b,c;"string"==typeof a?b=a:a&&(c=a.locale,b=a.namespace);let d=await (0,f.A)(c);return(0,e.HM)({...d,namespace:b,messages:d.messages})})},79271:(a,b,c)=>{Promise.resolve().then(c.bind(c,40162)),Promise.resolve().then(c.bind(c,53258)),Promise.resolve().then(c.bind(c,62881)),Promise.resolve().then(c.bind(c,25842)),Promise.resolve().then(c.bind(c,65994)),Promise.resolve().then(c.bind(c,78377)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,46533,23)),Promise.resolve().then(c.bind(c,15246))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81295:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\about-us\\\\about-us-content.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\about-us\\about-us-content.tsx","default")},81630:a=>{"use strict";a.exports=require("http")},81961:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(91199);c(42087);var e=c(74208);async function f(a,b,c){let d=(0,e.UL)(),f=d.get("tkn")?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(33331).D)([f]),(0,d.A)(f,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94142:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,generateMetadata:()=>j});var d=c(37413),e=c(81295),f=c(75074),g=c(34708),h=c(98353),i=c(19491);async function j(){let a=await (0,f.A)("seeker"),b=await (0,g.A)()||i.DT.defaultLocale,c=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:a("metadata.aboutUs.title"),description:a("metadata.aboutUs.description"),keywords:a("metadata.aboutUs.keyword"),openGraph:{title:a("metadata.aboutUs.title"),description:a("metadata.aboutUs.description"),images:[{url:c+"og.png",width:1200,height:630,alt:"Property Plaza Team"}],type:"website",siteName:"Property Plaza",url:c+h.hT.replace("/","")},twitter:{card:"summary_large_image",title:a("metadata.aboutUs.title"),description:a("metadata.aboutUs.description"),images:[{url:c+"og.png",width:1200,height:630,alt:"Property Plaza"}],site:c+h.hT.replace("/","")},alternates:{canonical:c+b+h.hT,languages:{en:c+"en"+h.hT,id:c+"id"+h.hT,"x-default":c+h.hT.replace("/","")}},robots:{index:!0,follow:!0}}}function k(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(e.default,{})})}},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,7076,4999,648,4736,3226,3562,9202,1409,9737,2804],()=>b(b.s=2895));module.exports=c})();