(()=>{var a={};a.id=7472,a.ids=[7472],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10350:(a,b,c)=>{"use strict";c.d(b,{Vr:()=>g,_5:()=>f,cS:()=>e,lg:()=>h});var d=c(66595);let e=a=>d.apiClient.post("room-chats",a),f=a=>d.apiClient.get(`room-chats?search=${a.search}`),g=a=>d.apiClient.get(`room-chats/${a}`),h=a=>d.apiClient.put(`room-chats/${a}`)},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11986:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user-profile)\\\\message\\\\participant-detail.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\participant-detail.tsx","default")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22070:(a,b,c)=>{"use strict";c.d(b,{A$:()=>d,U8:()=>g,dK:()=>h,pL:()=>e});let d={accountManager:"LISTING",customerSupport:"CUSTOMER_SUPPORT",seekers:"SEEKER_OWNER"},e={customerSupport:"CUSTOMER_SUPPORT",owner:"SEEKER_OWNER"},f={waitingResponse:"WAITING_FOR_RESPONSE",endedResponse:"CONVERSATION_ENDED"},g=a=>a==f.waitingResponse||a==f.endedResponse,h=a=>a==f.endedResponse},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32954:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,q:()=>f});var d=c(78928),e=c(29494);let f="chat-list";function g(a){let{search:b,status:c}=a;return(0,e.I)({queryKey:[f,b,c],queryFn:async()=>await (0,d.QZ)({search:b||""}),retry:0})}},33121:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v,generateMetadata:()=>u});var d=c(37413),e=c(32401),f=c(16275),g=c(26246),h=c(66819),i=c(18898),j=c(57922),k=c(4536),l=c.n(k);function m(){let a=(0,j.A)("seeker");return(0,d.jsxs)(e.A,{className:(0,h.cn)("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0 w-full"),children:[(0,d.jsx)(g.SidebarTrigger,{className:"items-end -ml-2"}),(0,d.jsx)(f.Qp,{className:"",children:(0,d.jsxs)(f.AB,{className:"space-x-4 sm:gap-0",children:[(0,d.jsx)(f.J5,{className:"text-seekers-text font-medium text-sm",children:(0,d.jsxs)(l(),{href:"/",className:"flex gap-2.5 items-center",children:[(0,d.jsx)(i.A,{className:"w-4 h-4",strokeWidth:1}),a("misc.home")]})}),(0,d.jsx)(f.tH,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),(0,d.jsx)(f.J5,{className:"capitalize text-seekers-text font-medium text-sm",children:a("accountAndProfile.message")})]})})]})}var n=c(75074),o=c(34708),p=c(92675),q=c(83844),r=c(11986),s=c(98353),t=c(19491);async function u(){let a=await (0,n.A)("seeker"),b=await (0,o.A)()||t.DT.defaultLocale,c=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:a("metadata.message.title"),description:a("metadata.message.description"),alternates:{canonical:c+b+s.Nx,languages:{en:c+"en"+s.Nx,id:c+"id"+s.Nx,"x-default":c+s.Nx.replace("/","")}},openGraph:{title:a("metadata.message.title"),description:a("metadata.message.description"),images:[{url:c+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:c+b+s.Nx,countryName:"Indonesia",emails:"<EMAIL>",locale:b,alternateLocale:t.DT.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:a("metadata.message.title"),description:a("metadata.message.description"),images:[c+"og.jpg"]},robots:{index:!1,follow:!1,nocache:!1}}}function v(){return(0,d.jsxs)("div",{className:"h-full max-sm:space-y-6 md:flex md:flex-col items-start md:overflow-hidden max-h-full md:gap-6 ",children:[(0,d.jsx)(m,{}),(0,d.jsxs)(e.A,{className:"flex gap-8 space-y-0 w-full max-sm:pb-4 md:max-h-[calc(100%-68px-24px)] flex-grow max-md:px-0 md:pr-0",children:[" ",(0,d.jsx)(p.default,{}),(0,d.jsx)(q.ChatDetail,{}),(0,d.jsx)(r.default,{})]})]})}},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48539:(a,b,c)=>{"use strict";c.d(b,{p:()=>e});var d=c(60687);let e=({name:a,category:b})=>(0,d.jsx)("p",{className:"font-semibold flex-grow line-clamp-1",children:a})},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58047:(a,b,c)=>{Promise.resolve().then(c.bind(c,83844)),Promise.resolve().then(c.bind(c,92675)),Promise.resolve().then(c.bind(c,11986)),Promise.resolve().then(c.bind(c,26246)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,4536,23))},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(96241);let g=(0,e.F)("cursor-pointer inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs h-fit font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground  hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80",outline:"text-foreground",seekers:"border-transparent bg-seekers-primary/30 text-seekers-primary hover:bg-seekers-primary/80 rounded-full px-4 py-1.5"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a,"pointer-events-none"),...c})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64784:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A});var d=c(81961)},67429:(a,b,c)=>{"use strict";c.d(b,{ChatDetail:()=>D});var d=c(60687),e=c(24934),f=c(22070),g=c(72895),h=c(71702),i=c(58674),j=c(22104),k=c(89698),l=c(28559),m=c(33213),n=c(43210),o=c.n(n),p=c(48539),q=c(59821),r=c(36248),s=c.n(r),t=c(66595),u=c(54050),v=c(96241),w=c(97905);function x({createdAt:a,displayAs:b,isRead:c,isSent:f,text:g,code:i,isSeeker:j}){let k=(0,m.useTranslations)("component"),{toast:l}=(0,h.dj)(),[o,p]=(0,n.useState)(!1),[q,r]=(0,n.useState)(null),x=(0,u.n)({mutationFn:async a=>{let b;return await (b={text:a},t.B.post("/translate",b))}}),y=b==i,z=y?"items-end":"items-start",A=async()=>{if(null!==q)return void p(a=>!a);try{let a=await x.mutateAsync(g);p(a=>!a);let b=a.data.translatedText.translatedText;r(b)}catch(a){l({title:"there's issue with translations"})}};return(0,d.jsxs)(w.P.div,{initial:{opacity:0,translateY:20},animate:{opacity:1,translateY:0},transition:{duration:.2,ease:"easeInOut"},className:`w-full flex flex-col ${z} gap-2`,children:[(0,d.jsxs)("div",{className:(0,v.cn)("text-sm p-4 rounded-sm bg-background max-w-[256px] md:max-w-[348px]",j?"bg-seekers-primary/5":""),children:[(0,d.jsx)("div",{children:o&&q||g}),!y&&(0,d.jsx)(e.$,{variant:"ghost",className:"h-fit w-fit px-0 text-neutral-light font-normal",size:"sm",onClick:()=>A(),children:k(o?"cta.seeOriginal":"cta.seeTranslation")})]}),(0,d.jsx)("div",{className:`flex gap-1 ${z} text-xs text-neutral`,children:(0,d.jsx)("p",{children:s()(a).format("HH:mm")})})]})}var y=c(66835);function z({messages:a,chatEnded:b}){let c=(0,m.useTranslations)("seeker"),e="",{code:f}=(0,y.k)(a=>a.seekers);return(0,d.jsxs)(d.Fragment,{children:[a.map((a,b)=>{let g=a.createdAt,h=s()(),i=""===e||s()(e).isBefore(s()(g),"day"),j=s()(g).isSame(h,"day")?c("misc.today"):s()(g).format("DD-MMM-YY");return e=g,(0,d.jsxs)(o().Fragment,{children:[i&&(0,d.jsx)("div",{className:"w-full flex justify-center sticky top-0",children:(0,d.jsx)(q.E,{variant:"outline",className:"border-neutral-300 text-neutral-500 bg-seekers-primary-lighter min-w-[84px] text-center flex items-center justify-center",children:j})}),(0,d.jsx)("div",{className:"w-full space-y-2 my-2",children:(0,d.jsx)(x,{...a,code:f,isSeeker:!0})})]},b)}),b&&(0,d.jsx)(x,{code:"000",text:c("message.textChatEnded"),createdAt:a[a.length-1]?.createdAt||s()().format("DD-MM-YYYY"),displayAs:"",displayName:"",id:"000",isRead:!0,isSent:!0,status:""})]})}var A=c(69327),B=c(68988),C=c(70373);function D(){let a=(0,m.useTranslations)("seeker"),{currentLayout:b,roomId:c,chatDetail:o,participant:r,setlayout:s}=(0,j.R)(),t=(0,n.useRef)(null),u=(0,n.useRef)(null),w=(0,n.useRef)(null),[x,y]=(0,n.useState)(""),{toast:D}=(0,h.dj)(),E=(0,g.Y)(),F=()=>{c&&""!=x.trim()&&(x.length>i.Zu&&D({title:a("info.messageTooLong.title"),description:a("info.messageTooLong.description",{count:i.Zu})}),E.sendMessage(x,c),y(""))};return(0,d.jsx)("div",{className:` flex flex-col w-full max-sm:bg-white bg-seekers-primary-light/10 h-full pb-4 px-6 pr-3 md:max-h-full  md:rounded-lg relative 
  ${c||"detail-chat"==b?"max-sm:w-screen max-sm:left-0 max-sm:px-2 max-sm:bottom-0 max-sm:h-screen max-sm:fixed max-sm:inset-0 max-sm:z-20":"hidden"}`,children:c?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{ref:t,className:"flex bg-seekers-primary-light/0 w-full absolute gap-2 items-center left-0 md:px-6 pl-1 px-3 py-4 top-0 z-20",children:[(0,d.jsx)(e.$,{variant:"ghost",size:"icon",className:"lg:hidden",onClick:()=>E.leaveRoom(),children:(0,d.jsx)(l.A,{className:"h-5 w-5"})}),(0,d.jsxs)("div",{className:"flex h-10 w-fit cursor-pointer gap-2",onClick:()=>s("detail-user"),children:[(0,d.jsxs)(C.eu,{className:(0,v.cn)("w-10 h-10 rounded-full bg-seekers-text-lighter"),children:[(0,d.jsx)(C.BK,{src:r?.property?.image||r?.image||"",className:"border"}),(0,d.jsx)(C.q5,{className:"bg-transparent text-white",children:(0,d.jsxs)("span",{children:[r?.fullName[0][0],r?.fullName[r?.fullName.length/2]?.[0]||""]})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{children:(0,d.jsx)(p.p,{category:"",name:r?.property?.title||r?.fullName||""})}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 items-center",children:[r?.property?.title&&(0,d.jsx)("p",{className:"text-xs",children:r?.fullName}),(0,f.U8)(r?.status||"")?(0,d.jsx)(q.E,{variant:"outline",className:"border-seekers-text-lighter text-seekers-text-light mt-1",children:a("message.waitingResponse")}):(0,f.dK)(r?.status||"")?(0,d.jsx)(q.E,{variant:"outline",className:"border-seekers-text-lighter text-seekers-text-light mt-1",children:a("message.chatEnded")}):(0,d.jsx)(d.Fragment,{})]})]})]}),(0,d.jsx)("div",{className:"flex-grow"})]}),(0,d.jsx)("div",{className:"flex-1 mt-[90px] overflow-hidden",children:(0,d.jsxs)(A.F,{className:"h-full max-sm:px-3 overflow-y-auto pr-3",children:[(0,d.jsx)(z,{messages:o}),(0,d.jsx)("div",{ref:w,className:"h-10"})]})}),(0,d.jsx)("div",{ref:u,className:"bg-seekers-primary-light/0 w-full absolute bottom-0 left-0 max-sm:px-3 pt-2 px-6 py-4",children:(0,d.jsxs)("div",{className:"flex bg-background border rounded-sm focus-within:border-neutral-light items-end overflow-hidden",children:[(0,d.jsx)(B.p,{value:x,maxLength:i.Zu,onChange:a=>(a=>{if(a.length>=i.Zu)return void y(a.slice(0,i.Zu));y(a)})(a.target.value),onKeyDown:a=>"Enter"===a.key?F():()=>{},className:(0,v.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0"),disabled:(0,f.U8)(r?.status||""),placeholder:a("form.placeholder.basePlaceholder",{field:a("form.field.message").toLowerCase()})}),(0,d.jsxs)("p",{className:"text-[10px] text-seekers-text-light px-2",children:[x.length,"/",i.Zu]}),(0,d.jsx)(e.$,{type:"submit",variant:"default-seekers",className:"rounded-none text-white w-12",size:"icon",onClick:F,disabled:(0,f.U8)(r?.status||""),children:(0,d.jsx)(k.maG,{})})]})})]}):(0,d.jsx)(d.Fragment,{})})}},68640:(a,b,c)=>{"use strict";c.d(b,{default:()=>q});var d=c(60687),e=c(70373),f=c(24934),g=c(69327),h=c(96241),i=c(22104),j=c(28559),k=c(11860);let l=(0,c(62688).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);var m=c(33213),n=c(30474),o=c(85814),p=c.n(o);function q(){let a=(0,m.useTranslations)("seeker"),{currentLayout:b,setlayout:c}=(0,i.R)(),{participant:o}=(0,i.R)();return(0,d.jsxs)("div",{className:(0,h.cn)("relative","detail-user"==b?"md:rounded-lg w-full h-screen md:min-w-[300px] md:w-[300px] md:h-full bg-seekers-primary-light/10 flex flex-col items-center space-y-4 py-8 px-6 relative max-sm:fixed max-sm:top-0 max-sm:left-0 max-sm:z-50 max-sm:bg-background":"hidden"),children:[(0,d.jsx)(f.$,{variant:"ghost",className:"shadow-none absolute left-1 md:hidden top-[18px]",size:"icon",onClick:()=>c("detail-chat"),children:(0,d.jsx)(j.A,{})}),(0,d.jsx)(f.$,{variant:"ghost",className:"shadow-none absolute max-sm:hidden right-3 top-1",size:"icon",onClick:()=>c("detail-chat"),children:(0,d.jsx)(k.A,{})}),(0,d.jsxs)(g.F,{className:"w-full h-full ",children:[(0,d.jsxs)(e.eu,{className:(0,h.cn)("w-28 h-28 rounded-xl bg-seekers-text-lighter mx-auto"),children:[(0,d.jsx)(e.BK,{src:o?.property?.image||o?.image||"",className:"border"}),(0,d.jsx)(e.q5,{className:"bg-transparent text-2xl text-white",children:(0,d.jsxs)("span",{children:[o?.fullName[0][0],o?.fullName[o.fullName.length/2]?.[0]||""]})})]}),(0,d.jsx)(p(),{className:"flex justify-center !mt-2 items-center",href:`/${o?.property?.title?.replaceAll(" ","-")}?code=${o?.property?.id}`,target:"_blank",children:(0,d.jsx)(l,{className:"h-5 text-seekers-text w-5"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-base text-center font-semibold",children:o?.property?.title}),(0,d.jsx)("p",{className:"text-center text-neutral-600",children:o?.fullName}),o?.middleman?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("p",{className:"text-center text-seekers-text-lighter text-xs",children:a("misc.representedBy")}),(0,d.jsx)("p",{className:"text-center",children:o.middleman.name})]}):(0,d.jsx)(d.Fragment,{})]}),(0,d.jsxs)("div",{className:"h-fit w-full py-4 space-y-4",children:[(0,d.jsx)("h2",{className:"text-center text-seekers-text-light text-sm",children:a("misc.otherProperties")}),(0,d.jsx)("div",{className:"w-full space-y-2",children:o?.moreProperty?.map(a=>(0,d.jsxs)(p(),{className:"flex w-full gap-2",href:a.title+"?code="+a.id,target:"_blank",children:[(0,d.jsx)(n.default,{className:"h-10 rounded-full w-10 min-w-10",src:a.image||"",alt:"",width:48,height:48}),(0,d.jsx)("p",{className:"font-semibold line-clamp-2",children:a.title})]},a.id))})]})]})]})}},69327:(a,b,c)=>{"use strict";c.d(b,{$:()=>i,F:()=>h});var d=c(60687),e=c(43210),f=c(55161),g=c(96241);let h=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.bL,{ref:e,className:(0,g.cn)("relative overflow-hidden",a),...c,children:[(0,d.jsx)(f.LM,{className:"h-full w-full rounded-[inherit]",children:b}),(0,d.jsx)(i,{}),(0,d.jsx)(f.OK,{})]}));h.displayName=f.bL.displayName;let i=e.forwardRef(({className:a,orientation:b="vertical",...c},e)=>(0,d.jsx)(f.VM,{ref:e,orientation:b,className:(0,g.cn)("flex touch-none select-none transition-colors","vertical"===b&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===b&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",a),...c,children:(0,d.jsx)(f.lr,{className:"relative flex-1 rounded-full bg-border"})}));i.displayName=f.VM.displayName},72895:(a,b,c)=>{"use strict";c.d(b,{Y:()=>l});var d=c(22104),e=c(43210),f=c(1954),g=c(55503),h=c(10350),i=c(54050),j=c(78928),k=c(29494);let l=()=>{let{roomId:a,participant:b,updateSpecificAllChat:c,setchatDetail:l,setParticipant:m,updatechatDetail:n,setRoomId:o,setlayout:p}=(0,d.R)(a=>a),q=(0,i.n)({mutationFn:a=>(0,h.lg)(a)}),r=function(a,b=!0){return(0,k.I)({queryKey:["chat-detail",a],queryFn:()=>(0,j.Uy)(a),retry:0,enabled:b})}(a||"",void 0!==a);return(0,e.useEffect)(()=>(g.s.connected||g.s.connect(),()=>{g.s.disconnect()}),[]),(0,e.useEffect)(()=>{if(!a||!r.data?.data)return;let b=r.data.data;l(b.allMessages||[]),m(b.participant)},[r?.data?.data,a,m,l]),{sendMessage:(b,d)=>{g.s.connected||g.s.connect(),g.s.emit("sendMessage",{code:a,requested_by:"CLIENT",message:b,receiver:d},b=>{let d=(0,f.t8)(b);n(d),c(d,!1,a)})},createChatRoom:a=>{g.s.connected||g.s.connect(),g.s.emit("createRoomChat",{requested_by:"CLIENT",receiver:a},a=>{})},leaveRoom:()=>{o(""),l([]),m(),p("list"),g.s.emit("leaveRoomChat",{code:a},()=>{o("")})},ownerUpdateMessageStatus:async()=>{a&&b?.category==="SEEKER_OWNER"&&"WAITING_FOR_RESPONSE"===b.status&&await q.mutateAsync(a)},joinRoom:b=>{a&&g.s.emit("leaveRoomChat",{code:a}),g.s.emit("joinRoomChat",{code:b}),r.refetch()}}}},74075:a=>{"use strict";a.exports=require("zlib")},74896:(a,b,c)=>{"use strict";c.d(b,{A:()=>j});var d=c(60687),e=c(58164),f=c(43713),g=c(43210),h=c(96241);let i=g.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,h.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));function j({form:a,label:b,name:c,placeholder:g,description:h,inputProps:j}){return(0,d.jsx)(e.zB,{control:a.control,name:c,render:({field:a})=>(0,d.jsx)(f.A,{label:b,description:h,children:(0,d.jsx)(i,{placeholder:g,className:"resize-none",...a,...j,rows:10})})})}i.displayName="Textarea"},78452:(a,b,c)=>{"use strict";c.d(b,{m:()=>g});var d=c(10350),e=c(78928),f=c(54050);let g=()=>(0,f.n)({mutationFn:a=>(0,d.cS)(a),onSuccess:async a=>{let b=a.data.data;return await (0,e.Uy)(b.code)}})},78928:(a,b,c)=>{"use strict";c.d(b,{QZ:()=>g,Uy:()=>h});var d=c(27071),e=c(10350),f=c(1954);async function g(a){try{let b=(await (0,e._5)(a)).data.data;return{data:(0,f.aH)(b),meta:void 0}}catch(a){return console.log(a),{error:(0,d.Q)(a)}}}async function h(a){try{if(!a)return{error:"Id required"};let b=(await (0,e.Vr)(a)).data.data;return{data:(0,f.tP)(b),meta:void 0}}catch(a){return console.log(a),{error:(0,d.Q)(a)}}}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},81817:(a,b,c)=>{"use strict";c.d(b,{default:()=>O});var d=c(60687),e=c(32954),f=c(19743),g=c(22104),h=c(33213),i=c(43210),j=c(24934),k=c(55629),l=c(68988),m=c(22070),n=c(5698),o=c(89698),p=c(11976),q=c(37826),r=c(58674),s=c(45880),t=c(27605),u=c(63442),v=c(58164),w=c(74896),x=c(78452),y=c(71702),z=c(8693);function A({submitHandler:a}){let b=(0,h.useTranslations)("seeker"),c=(0,z.jE)(),f=function(){let a=(0,h.useTranslations)("seeker");return s.z.object({text:s.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.message")})}).min(r.Nr,{message:a("form.utility.minimumLength",{field:a("form.field.message"),length:r.Nr})}).max(r.Zu,{message:a("form.utility.maximumLength",{field:a("form.field.message"),length:r.Zu})})})}(),g=(0,x.m)(),i=(0,t.mN)({resolver:(0,u.u)(f),defaultValues:{text:""}}),{toast:k}=(0,y.dj)();async function l(d){if(d.text.trim().length<r.Nr)return void k({title:b("error.messageTooShort.title"),description:b("error.messageTooShort.description"),variant:"destructive"});let f={category:"CUSTOMER_SUPPORT",requested_by:"CLIENT",message:d.text};try{await g.mutateAsync(f),c.invalidateQueries({queryKey:[e.q]}),k({title:b("success.sendMessageToCs.title"),description:b("success.sendMessageToCs.description")}),a()}catch(a){k({title:b("error.failedSendMessage.title"),description:a.response?.data.message||"",variant:"destructive"})}}return(0,d.jsx)("div",{className:"w-full space-y-2",children:(0,d.jsxs)(v.lV,{...i,children:[(0,d.jsx)("form",{onSubmit:i.handleSubmit(l),className:"z-50",children:(0,d.jsx)(w.A,{form:i,label:"",name:"text",placeholder:b("form.placeholder.example.requestHelpToCs")})}),(0,d.jsx)(j.$,{variant:"default-seekers",loading:g.isPending,onClick:()=>l(i.getValues()),className:"min-w-40 max-sm:w-full",children:b("cta.sendRequest")})]})})}var B=c(4e3);function C({customTrigger:a}){let b=(0,h.useTranslations)("seeker"),[c,e]=(0,i.useState)(!1);return(0,d.jsxs)(p.A,{open:c,setOpen:e,openTrigger:a||(0,d.jsx)(j.$,{className:"w-full border-seekers-primary text-seekers-textz hover:bg-seekers-primary/30",size:"sm",variant:"outline",children:b("cta.chatCustomerService")}),children:[(0,d.jsx)(B.A,{className:"text-start px-0",children:(0,d.jsx)(q.L3,{className:"font-semibold",children:b("message.chatCs.title")})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("p",{children:b("message.chatCs.description")}),(0,d.jsx)(A,{submitHandler:()=>e(!1)})]})]})}var D=c(87616);function E(){let a=(0,h.useTranslations)("seeker"),b=m.pL,[c,e]=(0,i.useState)("");(0,n.d)(c);let{createQueryString:g,searchParams:p,removeQueryParam:q}=(0,f.A)();return(0,d.jsxs)("div",{className:"flex gap-2 justify-between w-full ",children:[(0,d.jsx)(l.p,{placeholder:a("message.searchChat"),className:"flex-grow h-8 border-seekers-text-lighter placeholder:text-seekers-text-lighter",value:c,onChange:a=>e(a.target.value)}),(0,d.jsxs)(k.rI,{children:[(0,d.jsx)(k.ty,{asChild:!0,children:(0,d.jsxs)(j.$,{variant:"outline",size:"sm",className:"ml-auto h-8 flex text-seekers-text border-seekers-text-lighter",children:[(0,d.jsx)(o.LMN,{className:"mr-2 h-4 w-4"}),a("cta.filter")]})}),(0,d.jsx)(k.SQ,{align:"end",className:"w-fit",children:Object.values(b).map(b=>(0,d.jsx)(k.hO,{className:"capitalize",checked:!p.get("filter")?.includes(b),onCheckedChange:()=>(a=>{let b=p.get("filter")||"";if(!b)return void g("filter",a);if(b?.includes(a)){let c=b.replaceAll(","," ").replace(a,"").trim(),d=c.replace(/\s+/g," ");if(c.length<1)q(["filter"]);else{let a=d.split(" ");a.filter(a=>""!==a),g("filter",a.toString())}}else{let c=b.split(",");c.push(a),g("filter",c.toString())}})(b),children:function(a,b){switch(a){case m.A$.accountManager:return b("message.category.accountManager");case m.A$.customerSupport:return b("message.category.customerSupport");case m.A$.seekers:return b("message.category.seekers");case m.pL.owner:return b("message.category.owner");default:return""}}(b,a)},b))})]}),(0,d.jsx)(C,{customTrigger:(0,d.jsx)("div",{className:"w-16 h-8",children:(0,d.jsx)(D.A,{trigger:(0,d.jsx)(j.$,{size:"icon",className:"w-full h-full border border-seekers-text-lighter",variant:"outline",children:(0,d.jsx)(o.JGH,{className:"!h-3 !w-3"})}),content:(0,d.jsx)("p",{className:"text-seekers-primary",children:a("misc.chatCustomerSupport")})})})})]})}var F=c(69327),G=c(71463),H=c(66835),I=c(48539),J=c(96241),K=c(72895),L=c(70373),M=c(55503);function N({participant:a,lastMessages:b,code:c,category:e}){let{setRoomId:f,setlayout:h,roomId:i,setParticipant:j}=(0,g.R)(a=>a),{leaveRoom:k}=(0,K.Y)(),{code:l}=(0,H.k)(a=>a.seekers),m=b.displayAs!==l,n=(a?.fullName||"").trim().split(" "),o=n.length;return b?(0,d.jsx)("div",{className:`rounded-lg w-full overscroll-none ${i==c?"bg-seekers-primary-lighter/30":""} hover:bg-seekers-primary-lighter/60 py-4 px-2`,onClick:()=>{c!=i&&(M.s.emit("joinRoomChat",{code:i},()=>{}),k(),f(c),h("detail-chat"),j({category:a?.category||"",email:a?.email||"",fullName:a?.fullName||"",id:a?.id||"",phoneNumber:a?.phoneNumber||"",status:a?.status||"",image:a?.image||"",property:{image:a?.property?.image||"",title:a?.property?.title||""}}))},children:(0,d.jsxs)("div",{className:"flex gap-2 ",children:[(0,d.jsx)("div",{className:"min-w-9 !w-9 !h-9 aspect-square rounded-full bg-neutral-200 relative overflow-hidden",children:(0,d.jsxs)(L.eu,{className:(0,J.cn)("w-full rounded-full bg-seekers-text-lighter"),children:[(0,d.jsx)(L.BK,{src:a?.property?.image||a?.image||"",className:"border"}),(0,d.jsx)(L.q5,{className:"bg-transparent text-white",children:(0,d.jsxs)("span",{children:[n[0][0],n[o/2]?.[0]||""]})})]})}),(0,d.jsxs)("div",{className:"flex-grow",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(I.p,{name:a?.property?.title||a?.fullName,category:""}),(0,d.jsx)("p",{className:"text-[10px] text-seekers-text-light",children:(0,J.yv)(b?.createdAt)})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("p",{className:"text-xs text-seekers-text-light line-clamp-1",children:b?.text}),!b.isRead&&m?(0,d.jsx)("div",{className:"w-2 aspect-square rounded-full bg-seekers-primary"}):(0,d.jsx)(d.Fragment,{})]})]})]})}):(0,d.jsx)(d.Fragment,{})}function O(){let a=(0,h.useTranslations)("seeker"),{currentLayout:b,allChat:c,setAllChat:j,setRoomId:k}=(0,g.R)(),{searchParams:l}=(0,f.A)(),m=l.get("search")||"",n=(0,e.A)({search:m});return(0,i.useEffect)(()=>{if(!n.data?.data)return;let a=l.get("filter")||"",b=n.data?.data;b.length<1&&k(""),a?j(b.filter(b=>!a||!a?.includes(b.category))):j(b)},[n.data?.data,l.get("search"),l.get("filter")]),(0,d.jsxs)("div",{className:`space-y-8 max-sm:space-y-4 md:max-w-xs flex flex-col max-sm:h-fit md:overflow-hidden ${"list"==b?"":"max-lg:hidden"} min-w-[300px] md:max-lg:min-w-full max-sm:w-full`,children:[(0,d.jsx)(E,{}),(0,d.jsxs)(F.F,{className:"overflow-y-auto md:flex-grow",children:[n.isPending?(0,d.jsx)("div",{className:"w-full space-y-2",children:[,,,].fill(0).map((a,b)=>(0,d.jsx)(G.E,{className:"w-full h-9"},b))}):(0,d.jsx)(d.Fragment,{children:c.length<1?(0,d.jsx)("p",{className:"text-center text-seekers-text-light",children:a("message.notMessageList")}):c.map((a,b)=>(0,d.jsx)(N,{...a},b))}),(0,d.jsx)("div",{className:"md:hidden h-20"})]})]})}},81881:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["(user-profile)",{children:["message",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,33121)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,65736)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,71772)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,41507))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,31455)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,16787)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/(user-profile)/message/page",pathname:"/[locale]/message",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/(user-profile)/message/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},81961:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(91199);c(42087);var e=c(74208);async function f(a,b,c){let d=(0,e.UL)(),f=d.get("tkn")?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(33331).D)([f]),(0,d.A)(f,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},83844:(a,b,c)=>{"use strict";c.d(b,{ChatDetail:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ChatDetail() from the server but ChatDetail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\chat-detail.tsx","ChatDetail")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},92675:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user-profile)\\\\message\\\\chat-list.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\chat-list.tsx","default")},94735:a=>{"use strict";a.exports=require("events")},94999:(a,b,c)=>{Promise.resolve().then(c.bind(c,67429)),Promise.resolve().then(c.bind(c,81817)),Promise.resolve().then(c.bind(c,68640)),Promise.resolve().then(c.bind(c,57452)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,85814,23))}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,7076,4999,648,4736,3226,4676,8268,1409,9737,1127],()=>b(b.s=81881));module.exports=c})();