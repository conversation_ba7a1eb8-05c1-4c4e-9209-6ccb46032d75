(()=>{var a={};a.id=2834,a.ids=[2834],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6986:(a,b,c)=>{"use strict";c.d(b,{n:()=>j});var d=c(16853),e=c(8693),f=c(54050),g=c(77273),h=c(71702),i=c(33213);function j(){let a=(0,e.jE)(),{toast:b}=(0,h.dj)(),c=(0,i.useTranslations)("universal");return(0,f.n)({mutationFn:a=>(0,d.yN)(a),onSuccess:()=>{a.invalidateQueries({queryKey:[g.g]}),b({title:c("success.updateUser")})},onError:a=>{let d=a.response.data;b({title:c("error.foundError"),description:d.message,variant:"destructive"})}})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},21907:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>s,generateMetadata:()=>r});var d=c(37413),e=c(75074),f=c(34708),g=c(32401),h=c(16275),i=c(26246),j=c(18898),k=c(57922),l=c(4536),m=c.n(l);function n(){let a=(0,k.A)("seeker");return(0,d.jsxs)(g.A,{className:"mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0",children:[(0,d.jsx)(i.SidebarTrigger,{className:"items-end -ml-2"}),(0,d.jsx)(h.Qp,{className:"",children:(0,d.jsxs)(h.AB,{className:"space-x-4 sm:gap-0",children:[(0,d.jsx)(h.J5,{className:"text-seekers-text font-medium text-sm",children:(0,d.jsxs)(m(),{href:"/",className:"flex gap-2.5 items-center",children:[(0,d.jsx)(j.A,{className:"w-4 h-4",strokeWidth:1}),a("misc.home")]})}),(0,d.jsx)(h.tH,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),(0,d.jsx)(h.J5,{className:"capitalize text-seekers-text font-medium text-sm",children:a("accountAndProfile.notification")})]})})]})}var o=c(84052),p=c(98353),q=c(19491);async function r(){let a=await (0,e.A)("seeker"),b=await (0,f.A)()||q.DT.defaultLocale,c=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:a("metadata.notificationSetting.title"),description:a("metadata.notificationSetting.description"),alternates:{canonical:c+b+"",languages:{en:c+"en"+p.VZ,id:c+"id"+p.VZ,"x-default":c+p.VZ.replace("/","")}},openGraph:{title:a("metadata.rootLayout.title"),description:a("metadata.rootLayout.description"),images:[{url:c+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:c+b,countryName:"Indonesia",emails:"<EMAIL>",locale:b,alternateLocale:q.DT.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:a("metadata.rootLayout.title"),description:a("metadata.rootLayout.description"),images:[c+"og.jpg"]},robots:{index:!1,follow:!1,nocache:!1}}}function s(){let a=(0,k.A)("seeker");return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(n,{}),(0,d.jsxs)(g.A,{className:"space-y-8 my-8 max-sm:px-0",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:a("setting.profile.notification.title")}),(0,d.jsx)("h2",{className:"text-muted-foreground mt-2",children:a("settings.profile.notification.description")})]}),(0,d.jsx)(o.default,{})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34126:(a,b,c)=>{Promise.resolve().then(c.bind(c,84052)),Promise.resolve().then(c.bind(c,26246)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,4536,23))},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48287:(a,b,c)=>{"use strict";c.d(b,{default:()=>p});var d=c(60687),e=c(33213),f=c(27605),g=c(45880);let h=g.z.object({soundNotifications:g.z.boolean(),emailNewMessages:g.z.boolean(),emailNewProperties:g.z.boolean(),priceChangeAlerts:g.z.boolean(),newsletters:g.z.boolean(),specialOffers:g.z.boolean(),surveys:g.z.boolean()});var i=c(63442),j=c(43210),k=c(58164),l=c(42902),m=c(6986),n=c(71702),o=c(66835);function p(){let a=(0,e.useTranslations)("seeker"),[b,c]=(0,j.useState)(!1),g=(0,o.k)(a=>a.seekers),p=(0,m.n)(),q=(0,f.mN)({resolver:(0,i.u)(h),defaultValues:{soundNotifications:g.setting.soundNotif,emailNewMessages:g.setting.messageNotif,emailNewProperties:g.setting.priceAlertNotif,priceChangeAlerts:g.setting.priceAlertNotif,newsletters:g.setting.newsletterNotif,specialOffers:g.setting.specialOfferNotif,surveys:g.setting.surveyNotif}}),{toast:r}=(0,n.dj)(),s=[{name:"soundNotifications",label:a("setting.notification.soundNotification.title"),description:a("setting.notification.soundNotification.description"),form:q,key:"sound_notif"},{name:"emailNewMessages",label:a("setting.notification.emailForNewMessages.title"),description:a("setting.notification.emailForNewMessages.description"),form:q,key:"message_notif"},{name:"emailNewProperties",label:a("setting.notification.emailForNewProperties.title"),description:a("setting.notification.emailForNewProperties.description"),form:q,key:"property_notif"},{name:"priceChangeAlerts",label:a("setting.notification.priceChangeAlert.title"),description:a("setting.notification.priceChangeAlert.description"),form:q,key:"price_alert_notif"},{name:"newsletters",label:a("setting.notification.newsletter.title"),description:a("setting.notification.newsletter.description"),form:q,key:"newsletter_notif"},{name:"specialOffers",label:a("setting.notification.specialOffer.title"),description:a("setting.notification.specialOffer.description"),form:q,key:"special_offer_notif"},{name:"surveys",label:a("setting.notification.surveys.title"),description:a("setting.notification.surveys.description"),form:q,key:"survey_notif"}],t=async(b,c,d,e)=>{try{await p.mutateAsync({settings:{[d]:e}}),r({title:a("success.updateNotification.title",{field:b})}),q.setValue(c.name,e)}catch(c){r({title:a("error.updateNotification.title",{field:b})})}};return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(k.lV,{...q,children:(0,d.jsx)("form",{onSubmit:q.handleSubmit(a=>{c(!0),setTimeout(()=>{c(!1)},1e3)}),className:"space-y-8 pb-8",children:(0,d.jsx)("div",{className:"space-y-4",children:s.map((a,b)=>(0,d.jsx)(k.zB,{control:q.control,name:a.name,render:({field:b})=>(0,d.jsxs)(k.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,d.jsxs)("div",{className:"space-y-0.5",children:[(0,d.jsx)(k.lR,{className:"text-base",children:a.label}),(0,d.jsx)(k.Rr,{children:a.description})]}),(0,d.jsx)(k.MJ,{children:(0,d.jsx)(l.d,{checked:b.value,className:"data-[state=checked]:bg-seekers-primary",onCheckedChange:b=>t(a.label,{form:q,name:a.name},a.key,b)})})]})},b))})})})})}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59575:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["(user-profile)",{children:["notification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,21907)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\notification\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,65736)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,71772)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,41507))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,31455)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,16787)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\notification\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/(user-profile)/notification/page",pathname:"/[locale]/notification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/(user-profile)/notification/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64784:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A});var d=c(81961)},74075:a=>{"use strict";a.exports=require("zlib")},75074:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(61120),e=c(67133),f=c(84757),g=(0,d.cache)(async function(a){let b,c;"string"==typeof a?b=a:a&&(c=a.locale,b=a.namespace);let d=await (0,f.A)(c);return(0,e.HM)({...d,namespace:b,messages:d.messages})})},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},81961:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(91199);c(42087);var e=c(74208);async function f(a,b,c){let d=(0,e.UL)(),f=d.get("tkn")?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(33331).D)([f]),(0,d.A)(f,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},83997:a=>{"use strict";a.exports=require("tty")},84052:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user-profile)\\\\notification\\\\form\\\\notification.form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\notification\\form\\notification.form.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},97174:(a,b,c)=>{Promise.resolve().then(c.bind(c,48287)),Promise.resolve().then(c.bind(c,57452)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,85814,23))}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,7076,4999,648,4736,3226,4676,1409,9737,1127],()=>b(b.s=59575));module.exports=c})();