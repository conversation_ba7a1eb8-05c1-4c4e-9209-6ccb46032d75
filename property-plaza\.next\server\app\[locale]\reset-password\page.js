(()=>{var a={};a.id=6437,a.ids=[6437],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6281:(a,b,c)=>{Promise.resolve().then(c.bind(c,48780)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19421:(a,b,c)=>{"use strict";c.d(b,{v:()=>f});var d=c(16648),e=c(54050);function f(){return(0,e.n)({mutationFn:a=>(0,d.aH)(a)})}},19743:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(16189),e=c(43210),f=c(61261);function g(){let a=(0,f.useRouter)(),b=(0,d.usePathname)(),c=(0,d.useSearchParams)(),g=(0,e.useCallback)(d=>{let e=new URLSearchParams(c.toString());d.forEach(a=>e.set(a.name,a.value)),a.push(b+"?"+e.toString())},[c,a,b]),h=(0,e.useCallback)((a,b)=>{let d=new URLSearchParams(c.toString());return d.set(a,b),d.toString()},[c]);return{searchParams:c,createQueryString:(d,e)=>{let f=new URLSearchParams(c.toString());f.set(d,e),a.push(b+"?"+f.toString())},generateQueryString:h,removeQueryParam:(b,d)=>{let e=new URLSearchParams(c.toString());b.forEach(a=>{e.delete(a)});let f=`${window.location.pathname}?${e.toString()}`;if(d)return window.location.href=f;a.push(f)},createMultipleQueryString:g,pathname:b,updateQuery:(d,e)=>{let f=new URLSearchParams(c.toString());f.set(d,e),a.push(b+"?"+f.toString())}}}},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},36866:(a,b,c)=>{"use strict";c.d(b,{h:()=>f});var d=c(33213),e=c(45880);function f(){let a=(0,d.useTranslations)("universal");return e.z.object({email:e.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.email")})}).email()})}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},48137:(a,b,c)=>{Promise.resolve().then(c.bind(c,65006)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995))},48780:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\reset-password\\\\content.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\reset-password\\content.tsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65006:(a,b,c)=>{"use strict";c.d(b,{default:()=>y});var d=c(60687),e=c(16189),f=c(71702),g=c(33213),h=c(58674),i=c(45880),j=c(16413),k=c(16648),l=c(54050);c(43210);var m=c(27605),n=c(63442),o=c(58164),p=c(73185),q=c(24934),r=c(19743),s=c(61261);function t({email:a,token:b,isSeeker:c=!0}){let e=(0,g.useTranslations)("universal"),{removeQueryParam:t}=(0,r.A)(),{toast:u}=(0,f.dj)(),v=(0,s.useRouter)(),w=function(){let a=(0,g.useTranslations)("universal");return i.z.object({password:i.z.string().min(1,{message:a("form.utility.fieldRequired",{field:a("form.field.password")})}).min(h.wz,{message:a("form.utility.minimumLength",{length:h.wz,field:a("form.field.password")})}).refine(a=>j.g.test(a),{message:a("form.utility.passwordWeak")}),confirmPassword:i.z.string().min(1,{message:a("form.utility.fieldRequired",{field:a("form.field.confirmPassword")})})}).refine(a=>a.password==a.confirmPassword,{message:a("form.utility.fieldNotMatch",{field:`${a("form.field.password")} ${a("conjuntion.and")} ${a("form.field.confirmPassword")}`}),path:["confirmPassword"]})}(),x=(0,l.n)({mutationFn:a=>(0,k.N$)(a)});(0,l.n)({mutationFn:a=>(0,k.Uu)(a)});let y=(0,m.mN)({resolver:(0,n.u)(w),defaultValues:{password:"",confirmPassword:""}});async function z(c){let d={email:a,token:b,password:c.password,confirm_password:c.confirmPassword};try{await x.mutateAsync(d)}catch(a){u({title:e("error.resetPassword.title"),description:a.response.data.message,variant:"destructive"})}v.push("/")}return(0,d.jsx)(o.lV,{...y,children:(0,d.jsxs)("form",{onSubmit:y.handleSubmit(z),className:"space-y-8",children:[(0,d.jsx)("div",{className:"space-y-2 text-center",children:(0,d.jsx)("h1",{className:"text-2xl font-semibold text-center",children:e("form.title.resetPassword")})}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(p.A,{form:y,name:"password",label:e("form.label.password"),placeholder:e("form.placeholder.basePlaceholder",{field:`${e("form.field.password")}`})}),(0,d.jsx)(p.A,{form:y,name:"confirmPassword",label:e("form.label.confirmPassword"),placeholder:e("form.placeholder.basePlaceholder",{field:`${e("form.field.confirmPassword")}`})})]}),(0,d.jsx)(q.$,{className:"w-full",variant:"default-seekers",loading:x.isPending,children:e("cta.changePassword")})]})})}var u=c(36866),v=c(67281),w=c(19421);function x({isDialog:a,isSeeker:b,onGoBack:c}){let h=(0,g.useTranslations)("universal"),{toast:i}=(0,f.dj)(),j=(0,u.h)(),k=(0,w.v)(),l=(0,e.useRouter)(),p=(0,m.mN)({resolver:(0,n.u)(j),defaultValues:{email:""}});async function r(a){let b={email:a.email};try{await k.mutateAsync(b)}catch(a){i({title:h("error.requestForgetPassword.title"),description:a.response.data.message,variant:"destructive"})}}return(0,d.jsx)("div",{className:"w-full space-y-6",children:k.isSuccess?(0,d.jsxs)("div",{className:"flex flex-col gap-6 items-center",children:[(0,d.jsxs)("div",{className:"space-y-2 text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-semibold ",children:h("success.requestForgotPassword.title")}),(0,d.jsx)("p",{className:"text-neutral-500",children:h("success.requestForgotPassword.description")})]}),(0,d.jsx)(q.$,{variant:"link",onClick:()=>l.push("/"),asChild:!0,children:h("cta.goBack")})]}):(0,d.jsx)(o.lV,{...p,children:(0,d.jsxs)("form",{onSubmit:p.handleSubmit(r),className:"space-y-8",children:[(0,d.jsxs)("div",{className:"space-y-2 text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-semibold text-center",children:h("form.title.resetPassword")}),(0,d.jsx)("p",{className:"text-neutral-500",children:h("form.description.resetPassword")})]}),(0,d.jsx)(v.A,{type:"email",form:p,name:"email",variant:"float",label:h("form.label.email"),labelClassName:"text-xs text-seekers-text-light font-normal",placeholder:""}),(0,d.jsxs)("div",{className:"space-y-2",children:[b?(0,d.jsx)(d.Fragment,{}):(0,d.jsx)(q.$,{className:"w-full",variant:"default-seekers",loading:k.isPending,children:h("cta.sendResetPassword")}),a?(0,d.jsx)(q.$,{type:"button",className:"w-full text-neutral-600",variant:"link",onClick:()=>c?.(),children:h("cta.goBack")}):(0,d.jsx)(q.$,{type:"button",variant:"link",onClick:()=>l.back(),className:"w-full text-neutral-600",children:h("cta.goBack")})]})]})})})}function y(){let a=(0,e.useSearchParams)(),b=a.get("email"),c=a.get("token");return b&&c?(0,d.jsx)(t,{email:b,token:c}):(0,d.jsx)(x,{})}},65234:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,generateMetadata:()=>g});var d=c(37413),e=c(75074),f=c(48780);async function g({params:a,searchParams:b}){let c=await (0,e.A)("seeker");return{title:c("metadata.rootLayout.title"),description:c("metadata.rootLayout.description"),alternates:{languages:{id:process.env.USER_DOMAIN+"/id",en:process.env.USER_DOMAIN+"/en","x-default":process.env.USER_DOMAIN+"/en"},canonical:{url:"https://property-plaza.com"}},robots:{index:!1,follow:!1}}}function h(){return(0,d.jsx)("div",{className:"container flex items-center justify-center min-h-screen py-10",children:(0,d.jsx)("div",{className:"max-w-sm max-sm:p-4",children:(0,d.jsx)(f.default,{})})})}},67281:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(58164),f=c(68988),g=c(43713),h=c(96241);function i({form:a,label:b,name:c,placeholder:i,description:j,type:k,inputProps:l,children:m,labelClassName:n,containerClassName:o,inputContainer:p,variant:q="default"}){return(0,d.jsx)(e.zB,{control:a.control,name:c,render:({field:a})=>(0,d.jsx)(g.A,{label:b,description:j,labelClassName:(0,h.cn)("float"==q?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",n),containerClassName:o,variant:q,children:(0,d.jsxs)("div",{className:(0,h.cn)("flex gap-2 w-full overflow-hidden","float"==q?"":"border rounded-sm focus-within:border-neutral-light",p),children:[(0,d.jsx)(f.p,{type:k,placeholder:i,...a,...l,className:(0,h.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==q?"px-0":"",l?.className)}),m]})})})}},74075:a=>{"use strict";a.exports=require("zlib")},75074:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(61120),e=c(67133),f=c(84757),g=(0,d.cache)(async function(a){let b,c;"string"==typeof a?b=a:a&&(c=a.locale,b=a.namespace);let d=await (0,f.A)(c);return(0,e.HM)({...d,namespace:b,messages:d.messages})})},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},80685:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,65234)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\reset-password\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,41507))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,31455)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,16787)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\reset-password\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/reset-password/page",pathname:"/[locale]/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/reset-password/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,7076,4999,648,1409],()=>b(b.s=80685));module.exports=c})();