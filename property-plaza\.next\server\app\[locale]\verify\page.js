(()=>{var a={};a.id=7975,a.ids=[7975],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5342:(a,b,c)=>{Promise.resolve().then(c.bind(c,67735)),Promise.resolve().then(c.bind(c,71810)),Promise.resolve().then(c.bind(c,18543)),Promise.resolve().then(c.bind(c,3203)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,49603,23)),Promise.resolve().then(c.bind(c,97094))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12451:(a,b,c)=>{Promise.resolve().then(c.bind(c,65483)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995))},13131:(a,b,c)=>{Promise.resolve().then(c.bind(c,29178)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},24019:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(37413),e=c(29666),f=c(67735),g=c(71810);c(61120);var h=c(18543);function i({children:a}){return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(h.default,{isSeeker:!0}),(0,d.jsx)(f.default,{}),(0,d.jsx)("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:(0,d.jsx)(g.default,{currency_:"IDR",localeId:"en"})}),(0,d.jsx)("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:a}),(0,d.jsx)("div",{className:"!mt-0",children:(0,d.jsx)(e.A,{})})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29178:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\verify-page-client.tsx","default")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},35534:(a,b,c)=>{"use strict";c.d(b,{c:()=>e});let d=process.env.CURRENCY_API+`latest?apikey=${process.env.CURRENCY_API_KEY}`,e=async a=>await fetch(d+`&currencies=EUR%2CUSD%2CAUD%2CIDR%2CGBP&base_currency=${a||"IDR"}`,{next:{revalidate:86400}}).then(a=>a.json())},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64784:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A});var d=c(81961)},65483:(a,b,c)=>{"use strict";c.d(b,{default:()=>C});var d=c(60687),e=c(43210),f=c(5395),g=c(24934),h=c(33213);function i(){let a=(0,h.useTranslations)("verify");return(0,d.jsx)("section",{className:"bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16","aria-labelledby":"hero-title",children:(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center",children:[(0,d.jsxs)("div",{className:"space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold",children:[(0,d.jsx)("span",{className:"text-xl md:text-2xl",children:"\uD83D\uDEA8"}),(0,d.jsx)("span",{className:"text-sm md:text-base",children:a("hero.badge")})]}),(0,d.jsx)("h1",{id:"hero-title",className:"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight",children:a("hero.title")}),(0,d.jsx)("p",{className:"text-base md:text-lg text-seekers-text-light leading-relaxed",children:a("hero.subtitle")}),(0,d.jsx)("div",{className:"space-y-3 md:space-y-4",children:a.raw("hero.benefits").map((a,b)=>(0,d.jsxs)("div",{className:"flex items-start lg:items-center gap-3 justify-center lg:justify-start",children:[(0,d.jsx)("span",{className:"text-seekers-primary text-lg mt-0.5 lg:mt-0",children:"•"}),(0,d.jsx)("span",{className:"text-sm md:text-base text-seekers-text font-medium text-left",children:a})]},b))}),(0,d.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 md:p-4",children:(0,d.jsxs)("p",{className:"text-red-800 font-medium text-sm md:text-base text-center lg:text-left",children:[a("hero.warning")," ",a("hero.cta")]})}),(0,d.jsxs)("div",{className:"pt-4 md:pt-6 text-center",children:[(0,d.jsx)(g.$,{size:"lg",className:"bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto",onClick:()=>{document.getElementById("booking-form")?.scrollIntoView({behavior:"smooth"})},children:"\uD83D\uDCCB Book Professional Villa Inspection Now"}),(0,d.jsx)("p",{className:"text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3",children:"✅ Protect your investment • Avoid scams • Get peace of mind"})]})]}),(0,d.jsx)("div",{className:"relative flex justify-center order-1 lg:order-2 w-full",children:(0,d.jsx)("div",{className:"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm bg-neutral-100 rounded-lg border-2 border-dashed border-neutral-300 flex items-center justify-center",style:{aspectRatio:"9/16"},children:(0,d.jsxs)("div",{className:"text-center space-y-2 p-4",children:[(0,d.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCF1"}),(0,d.jsx)("p",{className:"text-neutral-500 font-medium",children:"TikTok Video"}),(0,d.jsxs)("p",{className:"text-sm text-neutral-400 leading-relaxed",children:["Vertical video showing",(0,d.jsx)("br",{}),"villa inspection process",(0,d.jsx)("br",{}),"and red flags found"]})]})})})]})})})}var j=c(62688);let k=(0,j.A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var l=c(99270);let m=(0,j.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function n(){let a=(0,h.useTranslations)("verify"),b=[{icon:(0,d.jsx)(k,{className:"w-6 h-6"}),title:a("howItWorks.steps.book.title"),description:a("howItWorks.steps.book.description"),result:a("howItWorks.steps.book.result")},{icon:(0,d.jsx)(l.A,{className:"w-6 h-6"}),title:a("howItWorks.steps.inspect.title"),description:a("howItWorks.steps.inspect.description"),result:[a("howItWorks.steps.inspect.result.basic"),a("howItWorks.steps.inspect.result.smart"),a("howItWorks.steps.inspect.result.fullShield")]},{icon:(0,d.jsx)(m,{className:"w-6 h-6"}),title:a("howItWorks.steps.report.title"),description:a("howItWorks.steps.report.description"),result:[a("howItWorks.steps.report.result.basic"),a("howItWorks.steps.report.result.smart"),a("howItWorks.steps.report.result.fullShield")]}];return(0,d.jsx)("section",{className:"bg-seekers-foreground/50 py-12",children:(0,d.jsxs)(f.A,{children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:a("howItWorks.title")}),(0,d.jsx)("p",{className:"text-lg text-seekers-text-light",children:a("howItWorks.subtitle")})]}),(0,d.jsx)("div",{className:"relative max-w-[1200px] mx-auto px-4",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 relative",children:b.map((a,b)=>(0,d.jsxs)("div",{className:"group relative bg-white p-6 rounded-2xl border border-gray-100 hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md flex flex-col text-center",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,d.jsxs)("div",{className:"relative shrink-0",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center text-seekers-primary group-hover:scale-110 transition-transform duration-300",children:a.icon}),(0,d.jsx)("div",{className:"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full group-hover:blur-2xl transition-all duration-300"})]}),(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900 group-hover:text-seekers-primary transition-colors duration-300",children:a.title})]}),(0,d.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed flex-1",children:a.description}),(0,d.jsx)("div",{className:"absolute inset-0 border-2 border-transparent group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300"})]},b))})}),(0,d.jsxs)("div",{className:"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto",children:[(0,d.jsx)("h3",{className:"text-lg md:text-xl font-semibold text-seekers-text mb-4",children:a("howItWorks.whyChoose.title")}),(0,d.jsx)("p",{className:"text-base text-seekers-text-light",children:a("howItWorks.whyChoose.description")})]})]})})}var o=c(96241),p=c(43190);function q({conversions:a,onSelectTier:b}){let{currency:c,isLoading:i}=(0,p.M)(),[j,k]=(0,e.useState)("IDR"),l=(0,h.useLocale)(),m=(0,h.useTranslations)("verify"),n=[{id:"basic",name:m("pricing.tiers.basic.name"),price:45e5,features:m.raw("pricing.tiers.basic.features")},{id:"smart",name:m("pricing.tiers.smart.name"),price:6e6,popular:!0,features:m.raw("pricing.tiers.smart.features")},{id:"full-shield",name:m("pricing.tiers.fullShield.name"),price:85e5,features:m.raw("pricing.tiers.fullShield.features")}];return(0,d.jsx)("section",{className:"py-16 bg-white","aria-labelledby":"pricing-title",children:(0,d.jsxs)(f.A,{children:[(0,d.jsxs)("div",{className:"text-center mb-12",children:[(0,d.jsx)("h2",{id:"pricing-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:m("pricing.title")}),(0,d.jsx)("p",{className:"text-lg text-seekers-text-light",children:m("pricing.subtitle")})]}),(0,d.jsx)("div",{className:"grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto",children:n.map(c=>(0,d.jsxs)("div",{className:`relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg ${c.popular?"border-seekers-primary shadow-lg":"border-neutral-200"}`,children:[c.popular&&(0,d.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)("span",{className:"bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold",children:m("pricing.tiers.smart.popular")})}),(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("h4",{className:"text-xl font-bold text-seekers-text mb-2",children:c.name}),(0,d.jsx)("div",{className:"text-3xl font-bold text-seekers-primary",children:(b=>{let c=b*(a[j]||1);return(0,o.vv)(c,j,l)})(c.price)})]}),(0,d.jsx)("ul",{className:"space-y-3 mb-8",children:c.features.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start gap-3",children:[(0,d.jsx)("span",{className:"text-seekers-primary mt-1",children:"✓"}),(0,d.jsx)("span",{className:"text-seekers-text-light",children:a})]},b))}),(0,d.jsx)(g.$,{onClick:()=>b(c),className:`w-full py-3 font-semibold transition-all duration-200 ${c.popular?"bg-seekers-primary hover:bg-seekers-primary/90 text-white":"bg-neutral-100 hover:bg-neutral-200 text-seekers-text border border-neutral-300"}`,variant:c.popular?"default":"outline",children:m("pricing.cta",{tierName:c.name})})]},c.id))})]})})}var r=c(27605),s=c(63442),t=c(45880),u=c(19159),v=c.n(u),w=c(35239),x=c(58164),y=c(68988),z=c(63974),A=c(71702);function B({selectedTier:a,conversions:b}){let[c,i]=(0,e.useState)(!1),{executeRecaptcha:j}=(0,w.lw)(),{toast:k}=(0,A.dj)(),l=(0,h.useTranslations)("verify"),m=t.z.object({whatsappNumber:t.z.string().min(1,l("booking.form.whatsappNumber.required")).refine(a=>v()(a).isValid,l("booking.form.whatsappNumber.invalid")),villaAddress:t.z.string().min(1,l("booking.form.villaAddress.required")).min(10,l("booking.form.villaAddress.minLength")),preferredDate:t.z.string().min(1,l("booking.form.preferredDate.required")),tier:t.z.string().min(1,l("booking.form.tier.required"))}),n=[{id:"basic",name:l("booking.form.tier.options.basic"),price:45e5},{id:"smart",name:l("booking.form.tier.options.smart"),price:6e6},{id:"full-shield",name:l("booking.form.tier.options.fullShield"),price:85e5}],o=(0,r.mN)({resolver:(0,s.u)(m),defaultValues:{whatsappNumber:"",villaAddress:"",preferredDate:"",tier:a?.id||""}}),p=async a=>{i(!0);try{let b=await j("verify_booking");if((await fetch("/api/verify-booking",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,recaptchaToken:b})})).ok)k({title:l("booking.form.success.title"),description:l("booking.form.success.message"),variant:"default"}),o.reset();else throw Error("Failed to submit booking")}catch(a){k({title:l("booking.form.error.title"),description:l("booking.form.error.message"),variant:"destructive"})}finally{i(!1)}},q=new Date;q.setDate(q.getDate()+1);let u=q.toISOString().split("T")[0];return(0,d.jsx)("section",{id:"booking-form",className:"py-16 bg-white","aria-labelledby":"booking-title",children:(0,d.jsx)(f.A,{children:(0,d.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)("h2",{id:"booking-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:l("booking.title")}),(0,d.jsx)("p",{className:"text-lg text-seekers-text-light",children:l("booking.subtitle")})]}),(0,d.jsx)("div",{className:"bg-seekers-foreground/20 rounded-lg p-6 md:p-8",children:(0,d.jsx)(x.lV,{...o,children:(0,d.jsxs)("form",{onSubmit:o.handleSubmit(p),className:"space-y-6",children:[(0,d.jsx)(x.zB,{control:o.control,name:"whatsappNumber",render:({field:a})=>(0,d.jsxs)(x.eI,{children:[(0,d.jsxs)(x.lR,{className:"text-seekers-text font-semibold",children:[l("booking.form.whatsappNumber.label")," *"]}),(0,d.jsx)(x.MJ,{children:(0,d.jsx)(y.p,{type:"tel",placeholder:l("booking.form.whatsappNumber.placeholder"),...a,className:"border-neutral-300 focus:border-seekers-primary"})}),(0,d.jsx)(x.C5,{})]})}),(0,d.jsx)(x.zB,{control:o.control,name:"villaAddress",render:({field:a})=>(0,d.jsxs)(x.eI,{children:[(0,d.jsxs)(x.lR,{className:"text-seekers-text font-semibold",children:[l("booking.form.villaAddress.label")," *"]}),(0,d.jsx)(x.MJ,{children:(0,d.jsx)(y.p,{placeholder:l("booking.form.villaAddress.placeholder"),...a,className:"border-neutral-300 focus:border-seekers-primary"})}),(0,d.jsx)(x.C5,{})]})}),(0,d.jsx)(x.zB,{control:o.control,name:"preferredDate",render:({field:a})=>(0,d.jsxs)(x.eI,{children:[(0,d.jsxs)(x.lR,{className:"text-seekers-text font-semibold",children:[l("booking.form.preferredDate.label")," *"]}),(0,d.jsx)(x.MJ,{children:(0,d.jsx)(y.p,{type:"date",min:u,...a,className:"border-neutral-300 focus:border-seekers-primary"})}),(0,d.jsx)(x.C5,{})]})}),(0,d.jsx)(x.zB,{control:o.control,name:"tier",render:({field:a})=>(0,d.jsxs)(x.eI,{children:[(0,d.jsxs)(x.lR,{className:"text-seekers-text font-semibold",children:[l("booking.form.tier.label")," *"]}),(0,d.jsxs)(z.l6,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,d.jsx)(x.MJ,{children:(0,d.jsx)(z.bq,{className:"border-neutral-300 focus:border-seekers-primary",children:(0,d.jsx)(z.yv,{placeholder:l("booking.form.tier.placeholder")})})}),(0,d.jsx)(z.gC,{children:n.map(a=>(0,d.jsx)(z.eb,{value:a.id,children:a.name},a.id))})]}),(0,d.jsx)(x.C5,{})]})}),(0,d.jsx)(g.$,{type:"submit",disabled:c,className:"w-full bg-seekers-primary hover:bg-seekers-primary/90 text-white py-3 text-lg font-semibold",loading:c,children:c?l("booking.form.submitting"):l("booking.form.cta")}),(0,d.jsx)("p",{className:"text-xs text-seekers-text-light text-center",children:l("booking.form.disclaimer")})]})})})]})})})}function C({conversions:a}){let[b,c]=(0,e.useState)();return(0,d.jsxs)("main",{className:"min-h-screen",children:[(0,d.jsx)(i,{}),(0,d.jsx)(n,{}),(0,d.jsx)(q,{conversions:a,onSelectTier:a=>{c(a),setTimeout(()=>{document.getElementById("booking-form")?.scrollIntoView({behavior:"smooth",block:"start"})},100)}}),(0,d.jsx)(B,{selectedTier:b,conversions:a})]})}},74075:a=>{"use strict";a.exports=require("zlib")},74713:(a,b,c)=>{Promise.resolve().then(c.bind(c,62881)),Promise.resolve().then(c.bind(c,25842)),Promise.resolve().then(c.bind(c,65994)),Promise.resolve().then(c.bind(c,78377)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,46533,23)),Promise.resolve().then(c.bind(c,15246))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},81961:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(91199);c(42087);var e=c(74208);async function f(a,b,c){let d=(0,e.UL)(),f=d.get("tkn")?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(33331).D)([f]),(0,d.A)(f,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},83997:a=>{"use strict";a.exports=require("tty")},85561:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["[locale]",{children:["verify",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,96936)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,24019)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,41507))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,31455)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,16787)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/[locale]/verify/page",pathname:"/[locale]/verify",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/[locale]/verify/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96936:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,generateMetadata:()=>h});var d=c(37413),e=c(35534),f=c(29178),g=c(75074);async function h(){let a=await (0,g.A)("verify.seo");return{title:a("title"),description:a("description"),keywords:a("keywords"),openGraph:{title:a("title"),description:a("description"),type:"website"},twitter:{card:"summary_large_image",title:a("title"),description:a("description")}}}async function i(){let a=(await (0,e.c)()).data||{IDR:1,EUR:63e-6,USD:67e-6,GBP:53e-6,AUD:99e-6};return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Bali Villa Inspection Service",description:"Professional villa inspection service in Bali. We inspect legal/title red flags, structural issues & hidden costs, then deliver full video + risk report.",provider:{"@type":"Organization",name:"Property Plaza",url:"https://property-plaza.com"},areaServed:{"@type":"Place",name:"Bali, Indonesia"},serviceType:"Property Inspection",offers:[{"@type":"Offer",name:"Basic Villa Inspection",price:"4500000",priceCurrency:"IDR",description:"Video walkthrough, title deed validation, photo checklist, short voice summary"},{"@type":"Offer",name:"Smart Villa Inspection",price:"6000000",priceCurrency:"IDR",description:"Everything in Basic, detailed Risk-Score Report, highlight video, call with legal advisor"},{"@type":"Offer",name:"Full Shield Villa Inspection",price:"8500000",priceCurrency:"IDR",description:"Everything in Smart, contract review, BPN-certified endorsement, urgent booking priority"}]})}}),(0,d.jsx)(f.default,{conversions:a})]})}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,7076,4999,648,4736,3226,3562,9202,6384,1409,9737,2804],()=>b(b.s=85561));module.exports=c})();