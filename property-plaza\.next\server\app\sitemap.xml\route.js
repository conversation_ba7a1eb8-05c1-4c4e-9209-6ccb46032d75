(()=>{var a={};a.id=5475,a.ids=[5475],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1134:(a,b,c)=>{"use strict";c.d(b,{HF:()=>h,JX:()=>d,cz:()=>f,mZ:()=>e,tf:()=>g});let d=a=>a.toLowerCase().includes("day")?"DAY":a.toLowerCase().includes("week")?"WEEK":a.toLowerCase().includes("month")?"MONTH":a.toLowerCase().includes("year")?"YEAR":"MONTH",e=a=>a.includes("kbps")?"KBPS":a.includes("mbps")?"MBPS":a.includes("gbps")?"GBPS":"MBPS",f=a=>a.includes("day")?"DAY":a.includes("week")?"WEEK":a.includes("month")?"MONTH":"DAY",g=a=>a.includes("Seperated Room")?"SEPERATED_ROOM":"CONNECTED_ROOM",h={leasehold:"LEASEHOLD",freehold:"FREEHOLD",rent:"RENT"}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7980:(a,b,c)=>{"use strict";c.d(b,{fJ:()=>g,qH:()=>e,rU:()=>f});var d=c(1134);function e(a){let b=a.availability,c=a.detail,e=a.features,f=a.location,[g,i]=h(f.latitude,f.longitude),j=a.images.map(a=>({id:a.id,image:a.image,isHighlight:a.is_highlight,order:a.order,propertyId:a.property_id})),k=j.find(a=>a.isHighlight);k.order=1;let l=[k,...j.filter(a=>!a.isHighlight).map((a,b)=>({...a,order:b+2}))];return{availability:{availableAt:b.available_at,isNegotiable:b.is_negotiable,maxDuration:b?.duration_max||0,minDuration:b?.duration_min||0,price:b.price,type:b.type?.value||"",typeMaximumDuration:(0,d.JX)(b.duration_max_unit?.value||""),typeMinimumDuration:(0,d.JX)(b.duration_min_unit?.value||"")},description:a.description,detail:{bathroomTotal:+c.bathroom_total?.value||0,bedroomTotal:+c.bedroom_total?.value||0,buildingSize:+c.building_size||0,cascoStatus:c.casco_status,cleaningService:+c.cleaning_service?.value||0,garbageFee:c.garbage_fee,gardenSize:+c.garden_size||0,landSize:+c.land_size||0,propertyOfView:c.property_of_view,type:c.option.type,villageFee:c.village_fee,waterFee:c.water_fee,wifiService:+c.wifi_service?.value||0,typeWifiSpeed:(0,d.mZ)(c.wifi_service.suffix||""),yearsOfBuilding:c.years_of_building,typeBedRoom:(0,d.tf)(c.bedroom_total.suffix||""),typeCleaning:(0,d.cz)(c.cleaning_service.suffix||""),title:a.title,excerpt:a.excerpt},excerpt:a.excerpt,features:{amenities:(e.amenities||[]).map(a=>a.value),electricity:+e.electricity,furnishingOption:e.furnishing_option?.value,livingOption:e.living_option?.value||"",parkingOption:e.parking_option?.value||"",poolOption:e.pool_option?.value||"",sellingPoints:(e.selling_points||[]).map(a=>a.value)},id:a.id,images:l,location:{city:f.city,district:f.district,latitude:g,longitude:i,mainAddress:f.main_address,postalCode:f.postal_code,province:f.province,roadSize:+(f.road_size?.value||0),secondAddress:f.second_address,type:f.type.value,banjar:""},propertyId:c.property_id,status:a.status,title:a.title,owner:a.owner?{name:a.owner.full_name,image:a.owner.image,code:a.owner.user.id}:null,middleman:a.middleman?{code:a.middleman.user.id,image:a.middleman.image||"",name:a.middleman.full_name}:null,isFavorite:+(a?._count?.favorites||0)>0,chatCount:a.account?.user._count.chats||0}}function f(a){return a.map(a=>{var b;return{code:a.code,geolocation:h(a.location.latitude,a.location.longitude),location:a.location.district+", "+a.location.city+", "+a.location.province,price:a.availability.price,thumbnail:(b=a.code,a.images.map((a,c)=>({id:b+c,image:a.image,isHighlight:a.is_highlight})).sort((a,b)=>b.isHighlight-a.isHighlight)),title:a.title,listingDetail:{bathRoom:a.detail.bathroom_total,bedRoom:a.detail.bedroom_total,buildingSize:a.detail.building_size,landSize:a.detail.land_size,cascoStatus:a.detail.casco_status,gardenSize:a.detail.garden_size},availability:{availableAt:a.availability.available_at||"",maxDuration:a.availability.duration_max_unit?.value&&a.availability.duration_max?{value:a.availability.duration_max||1,suffix:a.availability.duration_max_unit?.value}:null,minDuration:a.availability.duration_min_unit?.value&&a.availability.duration_min?{value:a.availability.duration_min||1,suffix:a.availability.duration_min_unit?.value}:null,type:a.availability.type.value||""},sellingPoint:a.features.selling_points,category:a.detail.option.type,isFavorite:a?._count?.favorites>0,status:a.status}})}function g(a){return a.map(a=>({title:a.title.replaceAll(/[^a-zA-Z0-9]/g,"-"),id:a.code,updateAt:a.availability.updated_at}))}c(24684);let h=(a,b,c=10)=>{let d=1/111320*c;return[a+.4*d,b+.4*d]}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12127:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{resolveManifest:function(){return g},resolveRobots:function(){return e},resolveRouteData:function(){return h},resolveSitemap:function(){return f}});let d=c(77341);function e(a){let b="";for(let c of Array.isArray(a.rules)?a.rules:[a.rules]){for(let a of(0,d.resolveArray)(c.userAgent||["*"]))b+=`User-Agent: ${a}
`;if(c.allow)for(let a of(0,d.resolveArray)(c.allow))b+=`Allow: ${a}
`;if(c.disallow)for(let a of(0,d.resolveArray)(c.disallow))b+=`Disallow: ${a}
`;c.crawlDelay&&(b+=`Crawl-delay: ${c.crawlDelay}
`),b+="\n"}return a.host&&(b+=`Host: ${a.host}
`),a.sitemap&&(0,d.resolveArray)(a.sitemap).forEach(a=>{b+=`Sitemap: ${a}
`}),b}function f(a){let b=a.some(a=>Object.keys(a.alternates??{}).length>0),c=a.some(a=>{var b;return!!(null==(b=a.images)?void 0:b.length)}),d=a.some(a=>{var b;return!!(null==(b=a.videos)?void 0:b.length)}),e="";for(let i of(e+='<?xml version="1.0" encoding="UTF-8"?>\n',e+='<urlset xmlns="https://www.sitemaps.org/schemas/sitemap/0.9"',c&&(e+=' xmlns:image="https://www.google.com/schemas/sitemap-image/1.1"'),d&&(e+=' xmlns:video="https://www.google.com/schemas/sitemap-video/1.1"'),b?e+=' xmlns:xhtml="https://www.w3.org/1999/xhtml">\n':e+=">\n",a)){var f,g,h;e+="<url>\n",e+=`<loc>${i.url}</loc>
`;let a=null==(f=i.alternates)?void 0:f.languages;if(a&&Object.keys(a).length)for(let b in a)e+=`<xhtml:link rel="alternate" hreflang="${b}" href="${a[b]}" />
`;if(null==(g=i.images)?void 0:g.length)for(let a of i.images)e+=`<image:image>
<image:loc>${a}</image:loc>
</image:image>
`;if(null==(h=i.videos)?void 0:h.length)for(let a of i.videos)e+=["<video:video>",`<video:title>${a.title}</video:title>`,`<video:thumbnail_loc>${a.thumbnail_loc}</video:thumbnail_loc>`,`<video:description>${a.description}</video:description>`,a.content_loc&&`<video:content_loc>${a.content_loc}</video:content_loc>`,a.player_loc&&`<video:player_loc>${a.player_loc}</video:player_loc>`,a.duration&&`<video:duration>${a.duration}</video:duration>`,a.view_count&&`<video:view_count>${a.view_count}</video:view_count>`,a.tag&&`<video:tag>${a.tag}</video:tag>`,a.rating&&`<video:rating>${a.rating}</video:rating>`,a.expiration_date&&`<video:expiration_date>${a.expiration_date}</video:expiration_date>`,a.publication_date&&`<video:publication_date>${a.publication_date}</video:publication_date>`,a.family_friendly&&`<video:family_friendly>${a.family_friendly}</video:family_friendly>`,a.requires_subscription&&`<video:requires_subscription>${a.requires_subscription}</video:requires_subscription>`,a.live&&`<video:live>${a.live}</video:live>`,a.restriction&&`<video:restriction relationship="${a.restriction.relationship}">${a.restriction.content}</video:restriction>`,a.platform&&`<video:platform relationship="${a.platform.relationship}">${a.platform.content}</video:platform>`,a.uploader&&`<video:uploader${a.uploader.info&&` info="${a.uploader.info}"`}>${a.uploader.content}</video:uploader>`,`</video:video>
`].filter(Boolean).join("\n");if(i.lastModified){let a=i.lastModified instanceof Date?i.lastModified.toISOString():i.lastModified;e+=`<lastmod>${a}</lastmod>
`}i.changeFrequency&&(e+=`<changefreq>${i.changeFrequency}</changefreq>
`),"number"==typeof i.priority&&(e+=`<priority>${i.priority}</priority>
`),e+="</url>\n"}return e+"</urlset>\n"}function g(a){return JSON.stringify(a)}function h(a,b){return"robots"===b?e(a):"sitemap"===b?f(a):"manifest"===b?g(a):""}},13344:(a,b,c)=>{"use strict";c.d(b,{Ix:()=>e,Xh:()=>d});let d="tkn",e={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},26373:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(61120);let e=(...a)=>a.filter((a,b,c)=>!!a&&c.indexOf(a)===b).join(" ");var f={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let g=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...f,width:b,height:b,stroke:a,strokeWidth:g?24*Number(c)/Number(b):c,className:e("lucide",h),...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),h=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...f},h)=>(0,d.createElement)(g,{ref:h,iconNode:b,className:e(`lucide-${a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,c),...f}));return c.displayName=`${a}`,c}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38039:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},52837:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>J,patchFetch:()=>I,routeModule:()=>E,serverHooks:()=>H,workAsyncStorage:()=>F,workUnitAsyncStorage:()=>G});var d={};c.r(d),c.d(d,{default:()=>A,dynamic:()=>z});var e={};c.r(e),c.d(e,{GET:()=>D,dynamic:()=>z});var f=c(96559),g=c(48088),h=c(37719),i=c(26191),j=c(81289),k=c(261),l=c(92603),m=c(39893),n=c(14823),o=c(47220),p=c(66946),q=c(47912),r=c(99786),s=c(46143),t=c(86439),u=c(43365),v=c(32190),w=c(91920),x=c(90388),y=c(98353);let z="force-dynamic";async function A(){let a="https://www.property-plaza.com",b=new Date().toISOString(),c=await (0,x.K5)(),d=[];c&&c.forEach(c=>{d.push({url:`${a}/en/posts/${c.slug.current}`,priority:.7,changeFrequency:"monthly",lastModified:c.publishedAt?new Date(c.publishedAt).toISOString():b,alternates:{languages:{en:`${a}/en/posts/${c.slug.current}`,id:`${a}/id/posts/${c.slug.current}`,"x-default":`${a}/posts/${c.slug.current}`}}}),d.push({url:`${a}/id/posts/${c.slug.current}`,priority:.7,changeFrequency:"monthly",lastModified:c.publishedAt?new Date(c.publishedAt).toISOString():b,alternates:{languages:{en:`${a}/en/posts/${c.slug.current}`,id:`${a}/id/posts/${c.slug.current}`,"x-default":`${a}/posts/${c.slug.current}`}}}),d.push({url:`${a}/posts/${c.slug.current}`,priority:.7,changeFrequency:"monthly",lastModified:c.publishedAt?new Date(c.publishedAt).toISOString():b,alternates:{languages:{en:`${a}/en/posts/${c.slug.current}`,id:`${a}/id/posts/${c.slug.current}`,"x-default":`${a}/posts/${c.slug.current}`}}})});let e=await (0,w.qI)(),f=[];return e.data&&e.data.forEach(c=>{let d=c.title.toString().normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase().trim().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,"");f.push({url:`${a}/en/${d}?code=${c.id}`,priority:.9,changeFrequency:"monthly",lastModified:b,alternates:{languages:{en:`${a}/en/${d}?code=${c.id}`,id:`${a}/id/${d}?code=${c.id}`,"x-default":`${a}/${d}?code=${c.id}`}}}),f.push({url:`${a}/id/${d}?code=${c.id}`,priority:.9,changeFrequency:"monthly",lastModified:b,alternates:{languages:{en:`${a}/en/${d}?code=${c.id}`,id:`${a}/id/${d}?code=${c.id}`,"x-default":`${a}/${d}?code=${c.id}`}}}),f.push({url:`${a}/${d}?code=${c.id}`,priority:.9,changeFrequency:"monthly",lastModified:b,alternates:{languages:{en:`${a}/en/${d}?code=${c.id}`,id:`${a}/id/${d}?code=${c.id}`,"x-default":`${a}/${d}?code=${c.id}`}}})}),[{url:`${a}/en`,changeFrequency:"yearly",priority:1,lastModified:b,alternates:{languages:{en:`${a}/en`,id:`${a}/id`,"x-default":`${a}`}}},{url:`${a}/id`,changeFrequency:"yearly",priority:1,lastModified:b,alternates:{languages:{en:`${a}/en`,id:`${a}/id`,"x-default":`${a}`}}},{url:`${a}`,changeFrequency:"yearly",priority:1,lastModified:b,alternates:{languages:{en:`${a}/en`,id:`${a}/id`,"x-default":`${a}`}}},{url:`${a}/en${y.jd}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.jd}`,id:`${a}/id${y.jd}`,"x-default":`${a}${y.jd}`}}},{url:`${a}/id${y.jd}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.jd}`,id:`${a}/id${y.jd}`,"x-default":`${a}${y.jd}`}}},{url:`${a}${y.jd}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.jd}`,id:`${a}/id${y.jd}`,"x-default":`${a}${y.jd}`}}},{url:`${a}/en${y.hT}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.hT}`,id:`${a}/id${y.hT}`,"x-default":`${a}${y.hT}`}}},{url:`${a}/id${y.hT}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.hT}`,id:`${a}/id${y.hT}`,"x-default":`${a}${y.hT}`}}},{url:`${a}${y.hT}`,changeFrequency:"yearly",priority:.8,lastModified:b,alternates:{languages:{en:`${a}/en${y.hT}`,id:`${a}/id${y.hT}`,"x-default":`${a}${y.hT}`}}},{url:`${a}/en${y.ig}`,changeFrequency:"yearly",priority:.5,lastModified:b,alternates:{languages:{en:`${a}/en${y.ig}`,id:`${a}/id${y.ig}`,"x-default":`${a}${y.ig}`}}},{url:`${a}/id${y.ig}`,changeFrequency:"yearly",priority:.5,lastModified:b,alternates:{languages:{en:`${a}/en${y.ig}`,id:`${a}/id${y.ig}`,"x-default":`${a}${y.ig}`}}},{url:`${a}${y.ig}`,changeFrequency:"yearly",priority:.5,lastModified:b,alternates:{languages:{en:`${a}/en${y.ig}`,id:`${a}/id${y.ig}`,"x-default":`${a}${y.ig}`}}},{url:`${a}/en${y.Bu}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Bu}`,id:`${a}/id${y.Bu}`,"x-default":`${a}${y.Bu}`}}},{url:`${a}/id${y.Bu}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Bu}`,id:`${a}/id${y.Bu}`,"x-default":`${a}${y.Bu}`}}},{url:`${a}${y.Bu}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Bu}`,id:`${a}/id${y.Bu}`,"x-default":`${a}${y.Bu}`}}},{url:`${a}/en${y.fB}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.fB}`,id:`${a}/id${y.fB}`,"x-default":`${a}${y.fB}`}}},{url:`${a}/id${y.fB}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.fB}`,id:`${a}/id${y.fB}`,"x-default":`${a}${y.fB}`}}},{url:`${a}${y.fB}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.fB}`,id:`${a}/id${y.fB}`,"x-default":`${a}${y.fB}`}}},{url:`${a}/en${y.po}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.po}`,id:`${a}/id${y.po}`,"x-default":`${a}${y.po}`}}},{url:`${a}/id${y.po}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.po}`,id:`${a}/id${y.po}`,"x-default":`${a}${y.po}`}}},{url:`${a}${y.po}`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.po}`,id:`${a}/id${y.po}`,"x-default":`${a}${y.po}`}}},{url:`${a}/en${y.Eq}/all`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Eq}/all`,id:`${a}/id${y.Eq}/all`,"x-default":`${a}${y.Eq}/all`}}},{url:`${a}/id${y.Eq}/all`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Eq}/all`,id:`${a}/id${y.Eq}/all`,"x-default":`${a}${y.Eq}/all`}}},{url:`${a}${y.Eq}/all`,changeFrequency:"yearly",priority:.3,lastModified:b,alternates:{languages:{en:`${a}/en${y.Eq}/all`,id:`${a}/id${y.Eq}/all`,"x-default":`${a}${y.Eq}/all`}}},...f,...d]}var B=c(12127);let C={...d}.default;if("function"!=typeof C)throw Error('Default export is missing in "C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\sitemap.ts"');async function D(a,b){let{__metadata_id__:c,...d}=await b.params||{},e=!!c&&c.endsWith(".xml");if(c&&!e)return new v.NextResponse("Not Found",{status:404});let f=c&&e?c.slice(0,-4):void 0,g=await C({id:f}),h=(0,B.resolveRouteData)(g,"sitemap");return new v.NextResponse(h,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let E=new f.AppRouteRouteModule({definition:{kind:g.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},distDir:".next",projectDir:"",resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5C_PRIVATE%5CProperty%20Plaza%20-%20Seekers%5Cproperty-plaza%5Capp%5Csitemap.ts&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:e}),{workAsyncStorage:F,workUnitAsyncStorage:G,serverHooks:H}=E;function I(){return(0,h.patchFetch)({workAsyncStorage:F,workUnitAsyncStorage:G})}async function J(a,b,c){var d;let e="/sitemap.xml/route";"/index"===e&&(e="/");let f=await E.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!f)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:h,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=f,D=(0,k.normalizeAppPath)(e),F=!!(y.dynamicRoutes[D]||y.routes[C]);if(F&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new t.NoFallbackError}let G=null;!F||E.isDev||x||(G="/index"===(G=C)?"/":G);let H=!0===E.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,j.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,i.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>E.onRequestError(a,b,d,z)},sharedContext:{buildId:h}},N=new l.NodeNextRequest(a),O=new l.NodeNextResponse(b),P=m.NextRequestAdapter.fromNodeNextRequest(N,(0,m.signalFromNodeResponse)(b));try{let d=async c=>E.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==n.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),f=async f=>{var h,j;let k=async({previousCacheEntry:g})=>{try{if(!(0,i.getRequestMeta)(a,"minimalMode")&&A&&B&&!g)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(f);a.fetchMetrics=M.renderOpts.fetchMetrics;let h=M.renderOpts.pendingWaitUntil;h&&c.waitUntil&&(c.waitUntil(h),h=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,p.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,q.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[s.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=s.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=s.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:u.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==g?void 0:g.isStale)&&await E.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await E.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:g.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(h=l.value)?void 0:h.kind)!==u.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,i.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,q.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,i.getRequestMeta)(a,"minimalMode")&&F||m.delete(s.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,r.getCacheControlHeader)(l.cacheControl)),await (0,p.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await f(L):await K.withPropagatedContext(a.headers,()=>K.trace(n.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:j.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},f))}catch(b){if(L||await E.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,o.c)({isRevalidate:I,isOnDemandRevalidate:A})}),F)throw b;return await (0,p.I)(N,O,new Response(null,{status:500})),null}}},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77942:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("MessagesSquare",[["path",{d:"M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2z",key:"jj09z8"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1",key:"1cx29u"}]])},78335:()=>{},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86962:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(67218);c(79130);var e=c(13344),f=c(44999);async function g(a,b,c){let d=(0,f.UL)(),g=d.get(e.Xh)?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(17478).D)([g]),(0,d.A)(g,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},87468:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(26373).A)("ArrowDownUp",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"m21 8-4-4-4 4",key:"1c9v7m"}],["path",{d:"M17 4v16",key:"7dpous"}]])},90388:(a,b,c)=>{"use strict";c.d(b,{KP:()=>u,Bi:()=>y,gF:()=>x,N7:()=>w,K5:()=>v,xE:()=>A,Dc:()=>z,hD:()=>B,fx:()=>s});var d=c(23777),e=c.n(d),f=c(16664);let g={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var h=c(98810);let i=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`,j=(0,h.A)`*[_type == "post" && author != "hidden"] ${i}`,k=(0,h.A)`*[_type == "post" && author != "hidden"][0...2] ${i}`,l=(0,h.A)`*[_type == "post" && slug.current == $slug][0]  ${i}
  

`;(0,h.A)`*[_type == "post" && $slug in tags[]->slug.current] ${i}`,(0,h.A)`*[_type == "post" && author->slug.current == $slug] ${i}`,(0,h.A)`*[_type == "post" && category->slug.current == $slug] ${i}`;let m=(0,h.A)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${i}
  `,n=(0,h.A)`*[_type == "seoContent" && language == $language]{title,body}`,o=(0,h.A)`*[_type == "termsOfUse" && language == $language]{title,body}`,p=(0,h.A)`*[_type == "privacyPolicy" && language == $language]{title,body}`,q=(0,h.A)`*[_type == "userDataDeletion" && language == $language]{title,body}`,r=(0,f.UU)(g);function s(a){return e()(g).image(a)}async function t({query:a,qParams:b,tags:c}){return r.fetch(a,b,{next:{tags:c,revalidate:3600}})}let u=async()=>await t({query:k,qParams:{},tags:["post","author","category"]}),v=async()=>await t({query:j,qParams:{},tags:["post","author","category"]}),w=async a=>await t({query:l,qParams:{slug:a},tags:["post","author","category"]}),x=async(a,b)=>await t({query:m,qParams:{slug:a,id:b},tags:[]}),y=async a=>await t({query:n,qParams:{language:a},tags:[]}),z=async a=>await t({query:o,qParams:{language:a},tags:[]}),A=async a=>await t({query:p,qParams:{language:a},tags:[]}),B=async a=>await t({query:q,qParams:{language:a},tags:[]})},91527:(a,b,c)=>{"use strict";c.d(b,{w:()=>d});let d={get:"GET",post:"POST",put:"PUT",del:"DELETE",patch:"PATCH"}},91920:(a,b,c)=>{"use strict";c.d(b,{mD:()=>j,qI:()=>k}),c(94129);var d=c(91527),e=c(86962);let f="https://dev.property-plaza.id/api/v1",g=async(a,b)=>{let c=f+`/properties?${a.section?"&section="+a.section:""}${a.limit?"&limit="+a.limit:""}`;return await (0,e.A)(c,d.w.get,{next:{revalidate:900}})},h=async()=>await (0,e.A)(f+"/properties/filter",d.w.post,{next:{revalidate:86400},body:JSON.stringify({page:"1",per_page:"99000"})});var i=c(7980);async function j(){try{let a=await g({limit:8,section:"ALL"},"");if(a.error)throw Error(a.error.name,{cause:a.error.message});return{data:{newest:(0,i.rU)(a.data?.newest||[]),popular:(0,i.rU)(a.data?.popular||[]),featured:(0,i.rU)(a.data?.featured||[]),commercial:(0,i.rU)(a.data?.commercial||[])}}}catch(a){return{error:a.error??"An unknown error occurred"}}}async function k(){try{let a,b=await h();if(a=b?.data?.items||[],b.error)throw Error(b.error.name,{cause:b.error.message});return{data:(0,i.fJ)(a)}}catch(a){return{error:a.error??"An unknown error occurred"}}}},94129:(a,b,c)=>{"use strict";c.d(b,{apiClient:()=>e});var d=c(61369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call apiClient() from the server but apiClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\core\\client.ts","apiClient");(0,d.registerClientReference)(function(){throw Error("Attempted to call localApiClient() from the server but localApiClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\core\\client.ts","localApiClient")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{},98353:(a,b,c)=>{"use strict";c.d(b,{Bu:()=>q,DW:()=>h,Eq:()=>i,KI:()=>v,Nx:()=>k,Rd:()=>g,VZ:()=>o,Zs:()=>n,ch:()=>l,fB:()=>t,gA:()=>j,hT:()=>u,i1:()=>p,ig:()=>s,jd:()=>m,po:()=>r}),c(37413);var d=c(38039),e=c(77942),f=c(87468);let g="/",h="/profile",i="/s",j="/favorites",k="/message",l="/subscription",m="/plan",n="/billing",o="/notification",p="/security",q="/privacy-policy",r="/terms-of-use",s="/contact-us",t="/user-data-deletion",u="/about-us",v="/posts";d.A,e.A,f.A,d.A,e.A}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,5937,4999,4915,6055,9663],()=>b(b.s=52837));module.exports=c})();