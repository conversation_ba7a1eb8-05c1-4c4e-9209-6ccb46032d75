exports.id=1409,exports.ids=[1409],exports.modules={1954:(a,b,c)=>{"use strict";c.d(b,{aH:()=>f,t8:()=>g,tP:()=>h});var d=c(36248),e=c.n(d);function f(a){return a.map(a=>{let b=a.messages[0];if(b)return{code:a.code,category:a.category,roomId:a.participants.room_id,lastMessages:{createdAt:b?.created_at||a.created_at,displayAs:b?.display_as||"",displayName:b?.display_name||"",text:b?.text||"",isRead:b?.is_read||!1,isSent:b?.is_send||!1,id:b?.id||"",code:b?.code||""},participant:{email:a.participants.info?.email||"",fullName:a.participants.info?.display_name||"",phoneNumber:a.participants.info?.phone_number||"",image:a.participants.info.image||"",id:a.participants.info?.id||"",category:a.category,status:a.status,property:{title:a.ref_data?.title||void 0,image:a.ref_data?.images[0].image||void 0},otherProperty:[]},status:a.status,updatedAt:a.updated_at}}).filter(a=>void 0!==a).sort((a,b)=>e()(b.lastMessages.createdAt).unix()-e()(a.lastMessages.createdAt).unix())}function g(a){return{createdAt:a.created_at,displayAs:a.display_as,displayName:a.display_name,isRead:a.is_read,isSent:a.is_send,text:a.text,id:a.id,code:a.code}}function h(a){console.log(a.messages);let b=a.messages[a.messages?.length-1]||void 0,c=a.messages.map(a=>({createdAt:a.created_at,displayAs:a.display_as,displayName:a.display_name,isRead:a.is_read,isSent:a.is_send,text:a.text,id:a.id,code:a.code})),d=a.ref_data?.extended_list?.map(a=>({id:a.code,image:a.images[0]?.image||"",title:a.title}));return{code:a.code,category:a.category,roomId:a.participants.room_id,lastMessages:{createdAt:b.created_at,displayAs:b.display_as,displayName:b.display_name,text:b.text,isRead:b.is_read,isSent:b.is_send,id:b.id,code:b.code||""},participant:{email:a.participants.info?.email||"",fullName:a.participants.info?.display_name||"",phoneNumber:a.participants.info?.phone_number||"",image:a.participants.info?.image||"",id:a.participants.info?.id||"",category:a.category,status:a.status,property:{id:a.ref_data?.code||"",image:a.ref_data?.images[0]?.image||"",title:a.ref_data?.title||""},moreProperty:d||[]},allMessages:c,updatedAt:a.updated_at}}},3193:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(82902);function f({children:a}){return(0,d.jsx)(e.c4,{apiKey:"AIzaSyCOm6xsEL7MViTvzxhjmP6BRWPpCdCOtgM",children:a})}},5991:()=>{},6692:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\cookie-consent\\\\cookie-consent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\cookie-consent\\cookie-consent.tsx","default")},16413:(a,b,c)=>{"use strict";c.d(b,{Y:()=>h,g:()=>g});var d=c(58674),e=c(33213),f=c(45880);let g=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function h(){let a=(0,e.useTranslations)("seeker");return f.z.object({firstName:f.z.string().min(d.gF,{message:a("form.utility.minimumLength",{field:a("form.field.firstName"),length:d.gF})}).max(d.EM,{message:a("form.utility.maximumLength",{field:a("form.field.firstName"),length:d.EM})}),lastName:f.z.string().min(d.gF,{message:a("form.utility.minimumLength",{field:a("form.field.lastName"),length:d.gF})}).max(d.EM,{message:a("form.utility.maximumLength",{field:a("form.field.lastName"),length:d.EM})}),contact:f.z.string().email({message:a("form.utility.enterValidField",{field:` ${a("form.field.email")}`})}),password:f.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.password")})}),confirmPassword:f.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.confirmPassword")})})}).refine(a=>a.password==a.confirmPassword,{message:a("form.utility.fieldNotMatch",{field:`${a("form.field.password")}`}),path:["confirmPassword"]})}},16648:(a,b,c)=>{"use strict";c.d(b,{N$:()=>d.N$,Q0:()=>d.Q0,Uu:()=>d.Uu,_k:()=>d._k,aH:()=>d.aH,bH:()=>d.bH,eD:()=>d.eD,iD:()=>d.iD,ri:()=>d.ri,zp:()=>d.zp});var d=c(96256)},16787:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(39916);function e(){(0,d.redirect)("/")}},18543:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\providers\\\\notification-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\providers\\notification-provider.tsx","default")},19491:(a,b,c)=>{"use strict";c.d(b,{DT:()=>e,N_:()=>f});var d=c(91768);let e={locales:["en","id"],defaultLocale:"en"},{Link:f,redirect:g,usePathname:h,useRouter:i}=(0,d.A)(e)},22104:(a,b,c)=>{"use strict";c.d(b,{R:()=>h});var d=c(36248),e=c.n(d),f=c(85665),g=c(59350);let h=(0,f.vt)()((0,g.Zr)(a=>({currentLayout:"list",setlayout:b=>a({currentLayout:b}),roomId:void 0,setRoomId:b=>a({roomId:b}),chatDetail:[],setchatDetail:b=>a({chatDetail:b}),updatechatDetail:b=>a(a=>{let c=a.chatDetail.length;return a.chatDetail[c-1].id==b.id?{}:{chatDetail:[...a.chatDetail,b]}}),participant:void 0,setParticipant:b=>a({participant:b}),allChat:[],setAllChat:b=>a({allChat:b}),updateSpecificAllChat:(b,c,d)=>a(({allChat:a})=>{let f=a=>a.sort((a,b)=>e()(b.lastMessages.createdAt).unix()-e()(a.lastMessages.createdAt).unix());if(c)return{allChat:f([...a,b])};if(d){let c=a.findIndex(a=>a.code===d);if("roomId"in b)if(c<0)return{allChat:f([...a,b])};else{let d=[...a];return d[c]=b,{allChat:f(d)}}if("id"in b)if(c>=0)return{allChat:f(a.map((a,d)=>d===c?{...a,lastMessages:b}:a))};else return{allChat:a}}if("roomId"in b){let c=a.findIndex(a=>a.code===b.code);if(c<0)return{allChat:f([...a,b].sort((a,b)=>e()(b.lastMessages.createdAt).unix()-e()(a.lastMessages.createdAt).unix()))};{let d=[...a];return d[c]=b,{allChat:f(d)}}}if("id"in b){let c=a.findIndex(a=>a.code===b.code);if(c>=0)return{allChat:f(a.map((a,d)=>d===c?{...a,lastMessages:b}:a))}}return{allChat:a}}),updateParticipantStatus:b=>a(a=>a.participant?{participant:{...a.participant,status:b}}:{})}),{name:"messagingLayout",storage:(0,g.KU)(()=>sessionStorage)}))},22627:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>k});var d=c(60687),e=c(43210),f=c(11329),g=c(24224),h=c(96241),i=c(41862);let j=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),k=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,loading:g=!1,...k},l)=>{let m=e?f.DX:"button";return(0,d.jsx)(m,{className:(0,h.cn)(j({variant:b,size:c,className:a})),ref:l,disabled:g||k.disabled,...k,children:g?(0,d.jsx)(i.A,{className:(0,h.cn)("h-4 w-4 animate-spin")}):k.children})});k.displayName="Button"},30549:(a,b,c)=>{Promise.resolve().then(c.bind(c,86164)),Promise.resolve().then(c.bind(c,6692)),Promise.resolve().then(c.bind(c,63665)),Promise.resolve().then(c.bind(c,44259)),Promise.resolve().then(c.bind(c,18543)),Promise.resolve().then(c.bind(c,41900)),Promise.resolve().then(c.bind(c,52358)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.bind(c,71860)),Promise.resolve().then(c.bind(c,89153)),Promise.resolve().then(c.bind(c,96817)),Promise.resolve().then(c.bind(c,82920)),Promise.resolve().then(c.t.bind(c,51240,23))},31455:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>v,viewport:()=>u});var d=c(37413),e=c(90231),f=c.n(e),g=c(6429);c(85093);var h=c(21370),i=c(44259),j=c(41900),k=c(52358),l=c(51240),m=c.n(l),n=c(63665),o=c(86164),p=c(61120),q=c(18543),r=c(6692),s=c(22357);function t({children:a}){return(0,d.jsx)(s.L0,{useEnterprise:!0,reCaptchaKey:"6LfrGFUrAAAAAGJqQdr4HwH8y7lens_OJRrP9KJo",children:a})}let u={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover"};async function v({children:a,params:b}){let c=await (0,h.A)(),{locale:e}=await b;return(0,d.jsxs)("html",{lang:e,children:[(0,d.jsx)("meta",{name:"google-site-verification",content:"4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw"}),(0,d.jsx)(g.A,{messages:c,children:(0,d.jsx)(t,{children:(0,d.jsxs)("body",{className:`${f().className} relative`,children:[(0,d.jsx)(q.default,{}),(0,d.jsx)(p.Suspense,{fallback:"null",children:(0,d.jsx)(o.default,{})}),(0,d.jsx)(n.default,{}),(0,d.jsx)(m(),{showSpinner:!1}),(0,d.jsx)(j.default,{children:(0,d.jsxs)(i.default,{children:[a,(0,d.jsx)(k.Toaster,{})]})}),(0,d.jsx)(r.default,{})]})})})]})}},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41507:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"64x64",url:(0,d.fillMetadataSegment)("/[locale]",await a.params,"icon.ico")+"?8465a906383c07d0"}]},41662:(a,b,c)=>{var d={"./en.json":[4213,4213],"./id.json":[38163,8163]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,19))}e.keys=()=>Object.keys(d),e.id=41662,a.exports=e},41900:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\providers\\\\tanstack-query-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\providers\\tanstack-query-provider.tsx","default")},42902:(a,b,c)=>{"use strict";c.d(b,{d:()=>h});var d=c(60687),e=c(43210),f=c(90270),g=c(96241);let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.bL,{className:(0,g.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-seekers-primary data-[state=unchecked]:bg-input",a),...b,ref:c,children:(0,d.jsx)(f.zi,{className:(0,g.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));h.displayName=f.bL.displayName},43713:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687),e=c(58164),f=c(96241);function g({children:a,description:b,label:c,containerClassName:g,labelClassName:h,variant:i="default"}){return(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)(e.eI,{className:(0,f.cn)("w-full relative","float"==i?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",g),onClick:a=>a.stopPropagation(),children:[c&&(0,d.jsx)(e.lR,{className:h,children:c}),(0,d.jsx)(e.MJ,{className:"group relative w-full",children:a}),b&&(0,d.jsx)(e.Rr,{children:b}),"default"==i&&(0,d.jsx)(e.C5,{})]}),"float"==i&&(0,d.jsx)(e.C5,{})]})}},44259:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\providers\\\\google-maps-provider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\providers\\google-maps-provider.tsx","default")},52358:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\ui\\toaster.tsx","Toaster")},53014:(a,b,c)=>{"use strict";c.d(b,{default:()=>h});var d=c(60687),e=c(39091),f=c(8693);let g=new e.E;function h({children:a}){return(0,d.jsx)(f.Ht,{client:g,children:a})}},55503:(a,b,c)=>{"use strict";c.d(b,{s:()=>g});var d=c(40463),e=c(15659),f=c(58674);let g=(0,d.Ay)("https://dev.property-plaza.id/",{extraHeaders:{"auth-token":e.A.get(f.Xh)||""},autoConnect:!1})},58164:(a,b,c)=>{"use strict";c.d(b,{C5:()=>s,MJ:()=>q,Rr:()=>r,eI:()=>o,lR:()=>p,lV:()=>j,zB:()=>l});var d=c(60687),e=c(43210),f=c(11329),g=c(27605),h=c(96241),i=c(39390);let j=g.Op,k=e.createContext({}),l=({...a})=>(0,d.jsx)(k.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),m=()=>{let a=e.useContext(k),b=e.useContext(n),{getFieldState:c,formState:d}=(0,g.xW)(),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},n=e.createContext({}),o=e.forwardRef(({className:a,...b},c)=>{let f=e.useId();return(0,d.jsx)(n.Provider,{value:{id:f},children:(0,d.jsx)("div",{ref:c,className:(0,h.cn)("space-y-2",a),...b})})});o.displayName="FormItem";let p=e.forwardRef(({className:a,...b},c)=>{let{error:e,formItemId:f}=m();return(0,d.jsx)(i.J,{ref:c,className:(0,h.cn)(e&&"text-destructive",a),htmlFor:f,...b})});p.displayName="FormLabel";let q=e.forwardRef(({...a},b)=>{let{error:c,formItemId:e,formDescriptionId:g,formMessageId:h}=m();return(0,d.jsx)(f.DX,{ref:b,id:e,"aria-describedby":c?`${g} ${h}`:`${g}`,"aria-invalid":!!c,...a})});q.displayName="FormControl";let r=e.forwardRef(({className:a,...b},c)=>{let{formDescriptionId:e}=m();return(0,d.jsx)("p",{ref:c,id:e,className:(0,h.cn)("text-[0.8rem] text-muted-foreground",a),...b})});r.displayName="FormDescription";let s=e.forwardRef(({className:a,children:b,...c},e)=>{let{error:f,formMessageId:g}=m(),i=f?String(f?.message):b;return i?(0,d.jsx)("p",{ref:e,id:g,className:(0,h.cn)("text-[0.8rem] font-medium text-destructive",a),...c,children:i}):null});s.displayName="FormMessage"},58674:(a,b,c)=>{"use strict";c.d(b,{Dg:()=>e,Dj:()=>m,EM:()=>h,FN:()=>n,Ix:()=>p,Nr:()=>j,Xh:()=>d,Zu:()=>i,bV:()=>l,gF:()=>g,kj:()=>k,s7:()=>o,wz:()=>f});let d="tkn",e="SEEKER",f=8,g=1,h=30,i=300,j=10,k="cookies-collection-status",l="necessary-cookies-collection-status",m="functional-cookies-collection-status",n="analytic-cookies-collection-status",o="marketing-cookies-collection-status",p={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},62955:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},63665:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\locale\\\\moment-locale.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\locale\\moment-locale.tsx","default")},65994:(a,b,c)=>{"use strict";c.d(b,{default:()=>l});var d=c(60687);c(1954),c(55503);var e=c(71702),f=c(22104),g=c(85665),h=c(59350);let i=(0,g.vt)()((0,h.Zr)(a=>({hasNotificationSound:void 0,setNotificationSound:b=>a({hasNotificationSound:b}),isLoading:!0,setIsLoading:b=>a({isLoading:b}),editingStatus:[],setEditingStatus:(b,c)=>a(a=>{let d=a.editingStatus;if("add"==c)return d.includes(b)?{...a}:(d.push(b),{...a,editingStatus:d});if("remove"==c){let c=d.filter(a=>a!==b);return{...a,editingStatus:c}}return{...a}}),removeEditingStatus:()=>a({editingStatus:[]})}),{name:"settings",storage:(0,h.KU)(()=>localStorage),onRehydrateStorage:()=>a=>{a&&a.setIsLoading(!1)}}));var j=c(43210),k=c(33213);function l({isSeeker:a=!1}){(0,k.useTranslations)("universal");let{toast:b}=(0,e.dj)(),[c,g]=(0,j.useState)(!1),{updatechatDetail:h,updateSpecificAllChat:l}=(0,f.R)(a=>a),{hasNotificationSound:m,isLoading:n}=i(a=>a),{enableSoundNotification:o,playSound:p,popUpNotification:q}=(()=>{let{setNotificationSound:a}=i(a=>a),[b,c]=(0,j.useState)(null);return(0,j.useEffect)(()=>{let a=new Audio;a.src="/sounds/notification.mp3",c(a)},[]),{enableSoundNotification:b=>{a(b)},playSound:()=>{let a=new Audio;a.src="/sounds/notification.mp3",a.volume=1,a.play().then(()=>{}).catch(a=>{console.error("sound error",a)})},popUpNotification:(a,b)=>{if(!("Notification"in window))return void console.warn("This browser does not support desktop notifications.");"granted"===Notification.permission?new Notification(a,{body:b||""}):"default"===Notification.permission?Notification.requestPermission().then(c=>{"granted"===c?new Notification(a,{body:b||""}):console.warn("Notification permission denied.")}):"denied"===Notification.permission&&console.warn("Notifications are denied by the user.")}}})();return(0,d.jsx)(d.Fragment,{})}},66595:(a,b,c)=>{"use strict";c.d(b,{B:()=>j,apiClient:()=>i});var d=c(58674),e=c(51060),f=c(15659),g=c(55591);let h=new(c.n(g)()).Agent({rejectUnauthorized:!1}),i=e.A.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:f.A.get(d.Xh)?"Bearer "+f.A.get(d.Xh):""},httpsAgent:h}),j=e.A.create({baseURL:"/api/",httpsAgent:h})},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},69166:(a,b,c)=>{"use strict";c.d(b,{default:()=>n});var d=c(60687),e=c(58674),f=c(88920),g=c(97905),h=c(15659),i=c(47260),j=c(24934),k=c(16189),l=c(43210),m=c(42902);function n(){let a=(0,k.usePathname)();h.A.get(e.kj);let[b,c]=(0,l.useState)(!1),[m,n]=(0,l.useState)(!1),[p,q]=(0,l.useState)({necessary:!0,functional:!1,analytic:!1,marketing:!1}),r=a=>{["all","necessary","custom"].includes(a)&&(h.A.set(e.kj,"true"),"necessary"==a?q(a=>({...a,necessary:!0})):"all"==a&&q(a=>({analytic:!0,functional:!0,marketing:!0,necessary:!0})),h.A.set(e.bV,p.necessary.toString()),h.A.set(e.Dj,p.functional.toString()),h.A.set(e.FN,p.analytic.toString()),h.A.set(e.s7,p.marketing.toString()),c(!1))};return(0,d.jsx)(f.N,{children:!b||a.includes("create-password")&&a.includes("reset-password")?null:(0,d.jsxs)(g.P.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},exit:{y:20,opacity:0},transition:{duration:.7,ease:"easeInOut"},className:"fixed max-w-sm w-full bottom-4 left-4 bg-background p-8 z-20 shadow-md rounded-2xl space-y-4",children:[(0,d.jsxs)("p",{className:"inline-flex items-center gap-2 font-semibold",children:[(0,d.jsx)("span",{children:(0,d.jsx)(i.A,{})}),"Manage Cookie Preferences"]}),m?(0,d.jsxs)("section",{className:"space-y-4",children:[(0,d.jsx)(o,{title:"Necessary",onValueChange:a=>q(b=>({...b,necessary:a})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:p.necessary,disabled:!0}),(0,d.jsx)(o,{title:"Analytics",onValueChange:a=>q(b=>({...b,analytic:a})),description:"These cookies allow the website to remember your preferences and provide enhanced features like saved language settings or embedded videos.",value:p.analytic}),(0,d.jsx)(o,{title:"Functional",onValueChange:a=>q(b=>({...b,functional:a})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:p.functional}),(0,d.jsx)(o,{title:"Marketing",onValueChange:a=>q(b=>({...b,marketing:a})),description:"These cookies are used to deliver relevant ads and track your activity across websites to personalize your advertising experience.",value:p.marketing})]}):(0,d.jsx)("section",{children:(0,d.jsx)("p",{className:"text-seekers-text-light",children:"We use cookies to optimize your experience on our website. You can choose which categories of cookies to allow below."})}),(0,d.jsxs)("div",{className:"inline-flex gap-2",children:[(0,d.jsx)(j.$,{onClick:()=>r("all"),children:"Accept all"}),m?(0,d.jsx)(j.$,{variant:"ghost",onClick:()=>r("custom"),children:"Save preferences"}):(0,d.jsx)(j.$,{variant:"ghost",onClick:()=>n(!0),children:"Manage preferences"})]})]})})}function o({title:a,value:b,disabled:c,description:e,onValueChange:f}){return(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("p",{className:"font-semibold",children:a}),(0,d.jsx)(m.d,{checked:b,disabled:c,onCheckedChange:a=>f(a)})]}),(0,d.jsx)("p",{className:"text-xs",children:e})]})}},69794:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>r});var d=c(60687),e=c(43210),f=c(89698),g=c(39663),h=c(24224),i=c(96241);let j=g.Kq,k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.LM,{ref:c,className:(0,i.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4   sm:right-0  sm:flex-col md:max-w-[420px]",a),...b}));k.displayName=g.LM.displayName;let l=(0,h.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background",destructive:"destructive group border-destructive bg-destructive/20 backdrop-blur-sm text-destructive"}},defaultVariants:{variant:"default"}}),m=e.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)(g.bL,{ref:e,className:(0,i.cn)(l({variant:b}),a),...c}));m.displayName=g.bL.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.rc,{ref:c,className:(0,i.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",a),...b})).displayName=g.rc.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.bm,{ref:c,className:(0,i.cn)("absolute right-1 top-1 rounded-md p-1 text-primary-lighter opacity-0 transition-opacity hover:text-primary focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",a),"toast-close":"",...b,children:(0,d.jsx)(f.MKb,{className:"h-4 w-4"})}));n.displayName=g.bm.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.hE,{ref:c,className:(0,i.cn)("text-sm font-semibold [&+div]:text-xs",a),...b}));o.displayName=g.hE.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.VY,{ref:c,className:(0,i.cn)("text-sm opacity-90",a),...b}));p.displayName=g.VY.displayName;var q=c(71702);function r(){let{toasts:a}=(0,q.dj)();return(0,d.jsxs)(j,{children:[a.map(function({id:a,title:b,description:c,action:e,...f}){return(0,d.jsxs)(m,{...f,children:[(0,d.jsxs)("div",{className:"grid gap-1",children:[b&&(0,d.jsx)(o,{children:b}),c&&(0,d.jsx)(p,{children:c})]}),e,(0,d.jsx)(n,{})]},a)}),(0,d.jsx)(k,{})]})}},71702:(a,b,c)=>{"use strict";c.d(b,{dj:()=>l});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({...a}){let b=(e=(e+1)%Number.MAX_SAFE_INTEGER).toString(),c=()=>j({type:"DISMISS_TOAST",toastId:b});return j({type:"ADD_TOAST",toast:{...a,id:b,open:!0,onOpenChange:a=>{a||c()}}}),{id:b,dismiss:c,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:b}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}},72405:(a,b,c)=>{Promise.resolve().then(c.bind(c,93430)),Promise.resolve().then(c.bind(c,69166)),Promise.resolve().then(c.bind(c,73455)),Promise.resolve().then(c.bind(c,3193)),Promise.resolve().then(c.bind(c,65994)),Promise.resolve().then(c.bind(c,53014)),Promise.resolve().then(c.bind(c,69794)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.bind(c,20406)),Promise.resolve().then(c.bind(c,10346)),Promise.resolve().then(c.bind(c,87627)),Promise.resolve().then(c.bind(c,21022)),Promise.resolve().then(c.t.bind(c,76562,23))},73185:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(58164),f=c(68988),g=c(43713),h=c(43210),i=c(24934),j=c(12597),k=c(13861),l=c(96241);function m({form:a,label:b,name:c,placeholder:m,description:n,inputProps:o,labelClassName:p,containerClassName:q,inputContainer:r,variant:s="default"}){let[t,u]=(0,h.useState)(!1);return(0,d.jsx)(e.zB,{control:a.control,name:c,render:({field:a})=>(0,d.jsx)(g.A,{label:b,description:n,labelClassName:(0,l.cn)("float"==s?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:q,variant:s,children:(0,d.jsxs)("div",{className:(0,l.cn)("flex gap-2 w-full overflow-hidden","float"==s?"":"border rounded-sm focus-within:border-neutral-light",r),children:[(0,d.jsx)(f.p,{type:t?"text":"password",placeholder:m,...a,...o,className:(0,l.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==s?"px-0":"",o?.className)}),(0,d.jsx)(i.$,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:a=>{a.preventDefault(),a.stopPropagation(),u(a=>!a)},children:t?(0,d.jsx)(j.A,{className:"w-4 h-4"}):(0,d.jsx)(k.A,{className:"w-4 h-4"})})]})})})}},73455:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687);c(36248),c(99824);var e=c(33213);function f(){return(0,e.useLocale)(),(0,d.jsx)(d.Fragment,{})}c(43210)},78335:()=>{},80702:()=>{},85093:()=>{},86164:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\facebook-pixel.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\facebook-pixel.tsx","default")},93365:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>g,O4:()=>f});var d=c(74095),e=c(19491);let f=["en","id"],g=(0,d.A)(async({requestLocale:a})=>{let b=await a;return{locale:await a,messages:(await c(41662)(`./${b}.json`)).default,defaultLocale:e.DT.defaultLocale,locales:e.DT.locales}})},93430:(a,b,c)=>{"use strict";c.d(b,{default:()=>f});var d=c(60687),e=c(16189);function f(){return(0,e.usePathname)(),(0,e.useSearchParams)(),(0,d.jsx)(d.Fragment,{})}c(43210)},96241:(a,b,c)=>{"use strict";c.d(b,{ZV:()=>j,cn:()=>h,gT:()=>k,jW:()=>m,lF:()=>o,q7:()=>n,tT:()=>p,vv:()=>i,yv:()=>l});var d=c(49384),e=c(36248),f=c.n(e),g=c(82348);function h(...a){return(0,g.QP)((0,d.$)(a))}let i=(a,b="USD",c="en-US")=>new Intl.NumberFormat((a=>{switch(a){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}})(b),{style:"currency",currency:b,minimumFractionDigits:0,maximumFractionDigits:2*("IDR"!=b)}).format(a),j=(a,b="en-US")=>new Intl.NumberFormat(b,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(a);function k(a){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)}function l(a){let b=f()(a),c=f()();return b.isSame(c,"day")?b.format("HH:mm"):b.format("DD/MM/YY")}function m(a){return a.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let n=(a,b)=>a.includes(b)?a.filter(a=>a!==b):[...a,b];function o(a,b){return a.some(a=>b.includes(a))}let p=a=>a.charAt(0).toUpperCase()+a.slice(1)},96256:(a,b,c)=>{"use strict";c.d(b,{N$:()=>k,Q0:()=>l,Uu:()=>j,_k:()=>n,aH:()=>i,bH:()=>h,eD:()=>g,iD:()=>e,ri:()=>f,zp:()=>m});var d=c(66595);let e=(a,b)=>d.apiClient.post("auth/login",a,{headers:{"g-token":b||""}}),f=()=>d.apiClient.post("auth/logout"),g=a=>d.apiClient.post("notifications/email",a),h=a=>d.apiClient.post("auth/otp-verification",a),i=a=>d.apiClient.post("auth/forgot-password",a),j=a=>d.apiClient.get(`auth/verify-reset-password?email=${a.email}&token=${a.token}`),k=a=>d.apiClient.post("auth/reset-password",a),l=(a,b)=>d.apiClient.post("auth/create-password",a,b),m=a=>d.apiClient.post("users/security",a),n=a=>d.apiClient.post("auth/totp-verification",a)},96487:()=>{}};