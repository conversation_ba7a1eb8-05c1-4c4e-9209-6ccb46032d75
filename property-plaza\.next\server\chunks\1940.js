exports.id=1940,exports.ids=[1940],exports.modules={1695:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\pop-up.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\pop-up.tsx","default")},5471:(a,b,c)=>{"use strict";c.d(b,{Q:()=>e});var d=c(6475);let e=(0,d.createServerReference)("7fe793241b49d74d4b0baf527b96333153f48cf5a6",d.callServer,void 0,d.findSourceMapURL,"revalidateSyncTags")},19439:(a,b,c)=>{Promise.resolve().then(c.bind(c,1695)),Promise.resolve().then(c.bind(c,28504)),Promise.resolve().then(c.bind(c,67735)),Promise.resolve().then(c.bind(c,71810)),Promise.resolve().then(c.bind(c,18543)),Promise.resolve().then(c.bind(c,3203)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,49603,23)),Promise.resolve().then(c.bind(c,97094))},28504:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\setup-seekers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\setup-seekers.tsx","default")},34971:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A,"7f72133c10a72731b2d7b169807226a88ba6e85f39":()=>e.L,"7f721d2b9357e475fca6e07c844843ad51a70ab1ef":()=>f.q,"7fe793241b49d74d4b0baf527b96333153f48cf5a6":()=>e.Q});var d=c(81961),e=c(85769),f=c(95075)},35220:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(37413),e=c(29666),f=c(67735),g=c(71810);c(61120);var h=c(28504),i=c(44999),j=c(1695),k=c(18543);async function l({children:a}){let b=await (0,i.UL)(),c=b.get("seekers-settings")?.value||"",l=c?JSON.parse(c):void 0,m=b.get("NEXT_LOCALE")?.value;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.default,{isSeeker:!0}),(0,d.jsx)(h.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:(0,d.jsx)(g.default,{currency_:l?.state?.currency,localeId:m})}),(0,d.jsx)("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:a}),(0,d.jsx)("div",{className:"!mt-0",children:(0,d.jsx)(e.A,{})}),(0,d.jsx)(j.default,{})]})}},40162:(a,b,c)=>{"use strict";c.d(b,{default:()=>o});var d=c(60687),e=c(85814),f=c.n(e),g=c(755),h=c(4e3),i=c(11976),j=c(24934),k=c(33213);function l({open:a,setOpen:b,trigger:c}){let e=(0,k.useTranslations)("universal");return(0,d.jsxs)(i.A,{open:a,setOpen:b,openTrigger:c,children:[(0,d.jsx)(h.A,{children:(0,d.jsx)("h3",{className:"text-base font-bold text-seekers-text",children:e("popup.followInstagram.title")})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:e("popup.followInstagram.description")})}),(0,d.jsx)(g.A,{children:(0,d.jsx)(j.$,{asChild:!0,className:"w-full",variant:"default-seekers",children:(0,d.jsx)(f(),{href:"https://www.instagram.com/join.propertyplaza/",children:e("cta.followUsOnInstagram")})})})]})}var m=c(56605),n=c(43210);function o(){let{successSignUp:a,setSuccessSignUp:b,loading:c}=(0,m.k)(),[e,f]=(0,n.useState)(!1),[g,h]=(0,n.useState)(!0);return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(l,{open:e,setOpen:a=>{b(a),f(a)},trigger:(0,d.jsx)(d.Fragment,{})})})}},53258:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(77273),f=c(66835);function g(){let{setSeekers:a,setRole:b}=(0,f.k)(a=>a);return(0,e.H)(),(0,d.jsx)(d.Fragment,{})}c(43210)},72952:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(39916);function e(){(0,d.redirect)("/")}},79271:(a,b,c)=>{Promise.resolve().then(c.bind(c,40162)),Promise.resolve().then(c.bind(c,53258)),Promise.resolve().then(c.bind(c,62881)),Promise.resolve().then(c.bind(c,25842)),Promise.resolve().then(c.bind(c,65994)),Promise.resolve().then(c.bind(c,78377)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,46533,23)),Promise.resolve().then(c.bind(c,15246))},81961:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(91199);c(42087);var e=c(74208);async function f(a,b,c){let d=(0,e.UL)(),f=d.get("tkn")?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(33331).D)([f]),(0,d.A)(f,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},90388:(a,b,c)=>{"use strict";c.d(b,{KP:()=>u,Bi:()=>y,gF:()=>x,N7:()=>w,K5:()=>v,xE:()=>A,Dc:()=>z,hD:()=>B,fx:()=>s});var d=c(23777),e=c.n(d),f=c(16664);let g={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var h=c(98810);let i=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`,j=(0,h.A)`*[_type == "post" && author != "hidden"] ${i}`,k=(0,h.A)`*[_type == "post" && author != "hidden"][0...2] ${i}`,l=(0,h.A)`*[_type == "post" && slug.current == $slug][0]  ${i}
  

`;(0,h.A)`*[_type == "post" && $slug in tags[]->slug.current] ${i}`,(0,h.A)`*[_type == "post" && author->slug.current == $slug] ${i}`,(0,h.A)`*[_type == "post" && category->slug.current == $slug] ${i}`;let m=(0,h.A)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${i}
  `,n=(0,h.A)`*[_type == "seoContent" && language == $language]{title,body}`,o=(0,h.A)`*[_type == "termsOfUse" && language == $language]{title,body}`,p=(0,h.A)`*[_type == "privacyPolicy" && language == $language]{title,body}`,q=(0,h.A)`*[_type == "userDataDeletion" && language == $language]{title,body}`,r=(0,f.UU)(g);function s(a){return e()(g).image(a)}async function t({query:a,qParams:b,tags:c}){return r.fetch(a,b,{next:{tags:c,revalidate:3600}})}let u=async()=>await t({query:k,qParams:{},tags:["post","author","category"]}),v=async()=>await t({query:j,qParams:{},tags:["post","author","category"]}),w=async a=>await t({query:l,qParams:{slug:a},tags:["post","author","category"]}),x=async(a,b)=>await t({query:m,qParams:{slug:a,id:b},tags:[]}),y=async a=>await t({query:n,qParams:{language:a},tags:[]}),z=async a=>await t({query:o,qParams:{language:a},tags:[]}),A=async a=>await t({query:p,qParams:{language:a},tags:[]}),B=async a=>await t({query:q,qParams:{language:a},tags:[]})}};