"use strict";exports.id=2078,exports.ids=[2078],exports.modules={82078:(a,b,c)=>{c.r(b),c.d(b,{default:()=>o});var d=c(25630),e=c(6475);let f=(0,e.createServerReference)("7f72133c10a72731b2d7b169807226a88ba6e85f39",e.callServer,void 0,e.findSourceMapURL,"setPerspectiveCookie");var g=c(16189),h=c(43210),i=c(94219),j=c(16577);let k={"handshake/syn":d.Tx,"handshake/syn-ack":d.Rr,"handshake/ack":d.IM,"channel/response":d._K,"channel/heartbeat":d.vd,"channel/disconnect":d.FS,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},l={[d.Tx]:"handshake/syn",[d.Rr]:"handshake/syn-ack",[d.IM]:"handshake/ack",[d._K]:"channel/response",[d.vd]:"channel/heartbeat",[d.FS]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},m=a=>{let{data:b}=a;return b&&"object"==typeof b&&"domain"in b&&"type"in b&&"from"in b&&"to"in b&&("sanity/channels"===b.domain&&(b.domain=d.V2),"overlays"===b.to&&(b.to="visual-editing"),"overlays"===b.from&&(b.from="visual-editing"),b.channelId=b.connectionId,delete b.connectionId,b.type=k[b.type]??b.type),a},n=({context:a},b)=>{let{sources:c,targetOrigin:e}=a,f=(a=>{let{channelId:b,...c}=a,e={...c,connectionId:b};return e.domain===d.V2&&(e.domain="sanity/channels"),"visual-editing"===e.to&&(e.to="overlays"),"visual-editing"===e.from&&(e.from="overlays"),e.type=l[e.type]??e.type,"channel/response"===e.type&&e.responseTo&&!e.data&&(e.data={responseTo:e.responseTo}),("handshake/syn"===e.type||"handshake/syn-ack"===e.type||"handshake/ack"===e.type)&&(e.data={id:e.connectionId}),e})(b.message);c.forEach(a=>{a.postMessage(f,{targetOrigin:e})})};function o(a){let{draftModeEnabled:b,draftModePerspective:c}=a,e=(0,g.useRouter)(),k=(0,i.J)((a,d)=>{b&&a!==c&&f(a).then(()=>{d.aborted||e.refresh()}).catch(a=>console.error("Failed to set the preview perspective cookie",a))});return(0,h.useEffect)(()=>{let a,b=(0,d.RM)({name:"loaders",connectTo:"presentation"},(0,d.Uc)().provide({actors:{listen:(0,d.CC)(m),requestMachine:(0,d.tP)().provide({actions:{"send message":n}})}}));b.on("loader/perspective",b=>{a?.abort(),a=new AbortController,k(b.perspective,a.signal)});let c=b.start();return(0,j.El)(b),()=>{c()}},[k]),null}o.displayName="PresentationComlink"}};