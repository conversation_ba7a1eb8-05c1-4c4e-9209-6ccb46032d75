"use strict";exports.id=2604,exports.ids=[2604],exports.modules={45:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Plumbing.0ad0a3f8.svg",height:48,width:48,blurWidth:0,blurHeight:0}},1895:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Gazebo.fe6e9c2d.svg",height:48,width:48,blurWidth:0,blurHeight:0}},8384:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Pet allowed.7a5262d8.svg",height:48,width:48,blurWidth:0,blurHeight:0}},18117:(a,b,c)=>{c.d(b,{b:()=>d});let d="data:image/jpeg;base64,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"},32156:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Construction nearby-next to the location.c84c971d.svg",height:48,width:48,blurWidth:0,blurHeight:0}},33685:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Sublease allowed.0d58e5e4.svg",height:48,width:48,blurWidth:0,blurHeight:0}},35534:(a,b,c)=>{c.d(b,{c:()=>e});let d=process.env.CURRENCY_API+`latest?apikey=${process.env.CURRENCY_API_KEY}`,e=async a=>await fetch(d+`&currencies=EUR%2CUSD%2CAUD%2CIDR%2CGBP&base_currency=${a||"IDR"}`,{next:{revalidate:86400}}).then(a=>a.json())},42714:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Rooftop terrace.cb94448e.svg",height:48,width:48,blurWidth:0,blurHeight:0}},50017:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Terrace.7d093efa.svg",height:48,width:48,blurWidth:0,blurHeight:0}},59743:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Balcony.322dc8e4.svg",height:48,width:48,blurWidth:0,blurHeight:0}},73869:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Recently renovated.8d37cebc.svg",height:48,width:48,blurWidth:0,blurHeight:0}},76466:(a,b,c)=>{c.d(b,{_:()=>f});var d=c(11970),e=c(54050);function f(){return(0,e.n)({mutationFn:a=>(0,d.QS)(a)})}},80696:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Air Conditioning.211f8188.svg",height:48,width:48,blurWidth:0,blurHeight:0}},82659:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Bathtub.64872ead.svg",height:48,width:48,blurWidth:0,blurHeight:0}},97524:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Building Size.76edd524.svg",height:48,width:48,blurWidth:0,blurHeight:0}},99356:(a,b,c)=>{c.d(b,{yM:()=>_,kV:()=>ad,Ex:()=>ab,CQ:()=>ac,f1:()=>ae,LG:()=>aa,Iq:()=>Z,Ay:()=>Y});var d=c(60687),e=c(24934),f=c(84404),g=c(71463),h=c(76466),i=c(32737),j=c(18117),k=c(96241),l=c(43190),m=c(89698),n=c(67760),o=c(97992),p=c(17585),q=c(83923),r=c(33213),s=c(30474),t=c(43210),u=c(15659),v=c(58674),w=c(87616),x=c(45),y=c(1895),z=c(32156),A=c(8384),B=c(33685),C=c(73869),D=c(42714),E=c(82659),F=c(50017),G=c(80696),H=c(59743),I=c(99704),J=c(42017);function K({amenities:a,className:b,showText:c=!0}){let e=(0,r.useTranslations)("seeker");switch(a){case"PLUMBING":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:x.default||"",alt:e("listing.propertyCondition.optionFour.title"),"aria-label":e("listing.feature.additionalFeature.plumbing"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.propertyCondition.optionFour.title")]});case"GAZEBO":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:y.default||"",alt:e("listing.feature.additionalFeature.gazebo"),"aria-label":e("listing.feature.additionalFeature.gazebo"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.gazebo")]});case"CONSTRUCTION_NEARBY":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:z.default||"",alt:e("listing.feature.additionalFeature.constructionNearby"),"aria-label":e("listing.feature.additionalFeature.constructionNearby"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.constructionNearby")]});case"PET_ALLOWED":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:A.default||"",alt:e("listing.feature.additionalFeature.petAllowed"),"aria-label":e("listing.feature.additionalFeature.petAllowed"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.petAllowed")]});case"SUBLEASE_ALLOWED":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:B.default||"","aria-label":e("listing.feature.additionalFeature.subleaseAllowed"),alt:e("listing.feature.additionalFeature.subleaseAllowed"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.subleaseAllowed")]});case"RECENTLY_RENOVATED":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:C.default||"",alt:e("listing.feature.additionalFeature.recentlyRenovated"),"aria-label":e("listing.feature.additionalFeature.recentlyRenovated"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.recentlyRenovated")]});case"ROOFTOP_TERRACE":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:D.default||"",alt:e("listing.feature.additionalFeature.rooftopTerrace"),"aria-label":e("listing.feature.additionalFeature.rooftopTerrace"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.rooftopTerrace")]});case"GARDEN_BACKYARD":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:D.default||"","aria-label":e("listing.feature.additionalFeature.garden"),alt:e("listing.feature.additionalFeature.garden"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.garden")]});case"BATHUB":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:E.default||"",alt:e("listing.feature.additionalFeature.bathub"),"aria-label":e("listing.feature.additionalFeature.bathub"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.bathub")]});case"TERRACE":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:F.default||"",alt:e("listing.feature.additionalFeature.terrace"),"aria-label":e("listing.feature.additionalFeature.terrace"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.terrace")]});case"AIR_CONDITION":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:G.default||"","aria-label":e("listing.feature.additionalFeature.airCondition"),alt:e("listing.feature.additionalFeature.airCondition"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.airCondition")]});case"BALCONY":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:H.default||"",alt:e("listing.feature.additionalFeature.balcony"),"aria-label":e("listing.feature.additionalFeature.balcony"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.balcony")]});case"MUNICIPAL_WATERWORK":return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(s.default,{src:I.default||"",alt:e("listing.feature.additionalFeature.municipalWaterwork"),"aria-label":e("listing.feature.additionalFeature.municipalWaterwork"),className:(0,k.cn)("w-6 h-6",b),width:24,height:24}),c&&e("listing.feature.additionalFeature.municipalWaterwork")]});default:return(0,d.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,d.jsx)(J.A,{}),a]})}}let L={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"};function M({value:a}){let b=(0,r.useTranslations)("seeker");switch(a){case L.plumbing:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.plumbing,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.plumbing")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.airCondition:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.airCondition,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.airCondition")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.balcony:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.balcony,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.balcony")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.bathub:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.bathub,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.bathub")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.constructionNearby:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.constructionNearby,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.constructionNearby")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.garden:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.garden,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.garden")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.gazebo:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.gazebo,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.gazebo")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.petAllowed:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.petAllowed,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.petAllowed")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.recentlyRenovated:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.recentlyRenovated,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.recentlyRenovated")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.rooftopTerrace:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.rooftopTerrace,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.rooftopTerrace")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.subleaseAllowed:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.subleaseAllowed,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.subleaseAllowed")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.terrace:return(0,d.jsx)(w.A,{trigger:(0,d.jsx)("div",{className:"cursor-pointer",children:(0,d.jsx)(K,{amenities:L.terrace,className:"!w-4 !h-4",showText:!1})}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.terrace")}),contentClassName:"text-seekers-primary p-2 text-sm"});default:return(0,d.jsx)(d.Fragment,{})}}var N=c(66835),O=c(72820),P=c(53912),Q=c(97524),R=c(19791),S=c(85814),T=c.n(S),U=c(62112),V=c(71702);let W=(0,t.createContext)(void 0),X=()=>{let a=(0,t.useContext)(W);if(!a)throw Error("useListingContext must be used within a Listings");return a};function Y({data:a,maxImage:b,conversion:c,forceLazyloading:e,disabledSubscriptionAction:f}){return(0,d.jsxs)(Z,{data:{...a,thumbnail:b?a.thumbnail.slice(0,b):a.thumbnail},conversion:c,children:[(0,d.jsx)(_,{forceLazyloading:e,disableSubscriptionAction:f}),(0,d.jsxs)("div",{className:"space-y-2 px-0.5",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(aa,{className:"line-clamp-1"}),(0,d.jsx)(ab,{})]}),(0,d.jsx)(ac,{})]})]})}let Z=(0,t.forwardRef)(({children:a,data:b,className:c,conversion:e,...f},g)=>{let[h,i]=(0,t.useState)(b);return(0,t.useEffect)(()=>{i(b)},[b]),(0,d.jsx)(W.Provider,{value:{listing:h,setClientFavoriteListing:a=>{i(b=>({...b,isFavorite:a})),f.handleFavoriteListion?.(a)},handleOpenListing:()=>{window.open(`/${b.title.replace(/\W+/g,"-")}?code=${b.code}`)},conversion:e},children:(0,d.jsx)("div",{...f,ref:g,className:(0,k.cn)("relative w-full space-y-2 isolate cursor-pointer",c),children:a})})});function $({isFavorite:a,code:b,size:c="small",extraAction:f,updateClientFavorite:g,activeListing:i=!1,allowFavoritedWhileInactive:j=!1}){let l=(0,h._)(),m=(0,r.useTranslations)("seeker"),o=u.A.get(v.Xh),{role:p,seekers:q}=(0,N.k)(a=>a),s=(0,k.cn)("z-10  rounded-full h-[26px] w-[26px] hover:bg-transparent hover:scale-110 transition-transform duration-100 ease-linear","small"==c?"w-[24px] h-[24px]":"w-[26px] h-[26px]"),t=(0,k.cn)("text-white","small"==c?"!w-4 !h-4":"!w-5 !h-5"),{toast:w}=(0,V.dj)(),x=async()=>{if((o||"SEEKER"===p)&&(i||j)){if("Free"===q.accounts.membership)return void w({title:m("misc.subscibePropgram.favorite.title"),description:(0,d.jsxs)(d.Fragment,{children:[m("misc.subscibePropgram.favorite.description"),(0,d.jsx)(e.$,{asChild:!0,variant:"link",size:"sm",className:"p-0 text-seekers-primary h-fit w-fit underline",children:(0,d.jsx)(T(),{href:q.email?R.ch:R.jd,children:m("cta.subscribe")})})]})});try{g(!a),await l.mutateAsync({code:b,is_favorite:!a})}catch(a){}}};return(0,d.jsxs)("div",{className:"w-full py-3 px-2.5 pr-3 flex justify-end items-center gap-2",children:[o&&"SEEKER"==p?(0,d.jsx)(e.$,{size:"icon",onClick:a=>{a.stopPropagation(),x()},className:s,variant:"ghost",children:(0,d.jsx)(n.A,{className:t,fill:a?"red":"#707070",fillOpacity:a?1:.5})}):(0,d.jsx)(O.default,{customTrigger:(0,d.jsx)(e.$,{size:"icon",className:s,variant:"ghost",children:(0,d.jsx)(n.A,{className:t,fill:"#707070",fillOpacity:.5})})}),f]})}function _({heartSize:a="small",containerClassName:b,extraHeaderAction:c,allowFavoriteWhileInactive:g=!1,forceLazyloading:h=!1,disableSubscriptionAction:i}){let{listing:l,setClientFavoriteListing:m,handleOpenListing:n}=X(),o=(0,r.useTranslations)("seeker"),{seekers:p}=(0,N.k)(a=>a);return(0,d.jsxs)(f.FN,{opts:{loop:p.accounts.membership!=U.U$.free,active:(0,P.rJ)(l.status)&&l.thumbnail.length>1&&!i},className:(0,k.cn)("group isolate w-full aspect-[4/3] relative rounded-xl overflow-hidden",b),children:[(0,d.jsx)($,{updateClientFavorite:m,isFavorite:l.isFavorite,code:l.code,size:a,extraAction:c,activeListing:(0,P.rJ)(l.status),allowFavoritedWhileInactive:g}),!(0,P.rJ)(l.status)&&(0,d.jsx)("div",{onClick:()=>n(),className:" absolute top-0 left-0 rounded-xl w-full h-full -z-10 bg-slate-800/30 flex flex-col items-center justify-center",children:(0,d.jsx)("p",{className:"text-white font-semibold",children:o("misc.notAvailable")})}),(0,d.jsxs)(f.Wk,{className:"absolute top-0 left-0 w-full h-full ml-0 -z-20",children:[l.thumbnail.map((a,b)=>(0,d.jsxs)(f.A7,{className:"relative",onClick:a=>{a.stopPropagation(),n()},children:[(0,d.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none watermark-overlay"}),(0,d.jsx)(s.default,{src:a.image,alt:`${l.title}`,fill:!0,sizes:"300px",priority:0==b&&!h,loading:0!=b&&h?"lazy":"eager",style:{objectFit:"cover"},blurDataURL:j.b,placeholder:"blur",quality:10})]},a.id)),p.accounts.membership==U.U$.free&&!i&&(0,d.jsxs)(f.A7,{className:"relative isolate",onClick:a=>{a.stopPropagation()},children:[(0,d.jsx)(s.default,{className:"-z-10 brightness-50 blur-md",src:j.b,alt:"",fill:!0,sizes:"300px",loading:"lazy",blurDataURL:j.b,placeholder:"blur"}),(0,d.jsxs)("div",{className:"z-10 text-white absolute top-1/2 left-1/2 text-center flex flex-col items-center -translate-x-1/2 -translate-y-1/2 min-w-[200px]",children:[(0,d.jsxs)("p",{className:"text-center",children:[o("misc.subscibePropgram.detailPage.description")," "," "]}),(0,d.jsx)(e.$,{asChild:!0,variant:"link",size:"sm",className:"p-0 h-fit w-fit text-white underline",children:(0,d.jsx)(T(),{href:R.jd,children:o("cta.subscribe")})})]})]})]}),l.thumbnail.length<=1||!(0,P.rJ)(l.status)?(0,d.jsx)(d.Fragment,{}):(0,d.jsxs)("div",{className:"flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3",children:[(0,d.jsx)(f.Q8,{className:"left-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in"}),(0,d.jsx)(f.Oj,{className:"right-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in"})]}),(0,d.jsx)("div",{className:"flex absolute bottom-4 left-0 w-full items-center justify-center",children:(0,d.jsx)(f.ZZ,{carouselDotClassName:"hover:bg-seekers-primary",className:""})}),(0,d.jsx)("div",{className:"absolute w-full pointer-events-none h-full top-0 left-0 bg-gradient-to-b from-neutral-900/40 via-neutral-900/5 to-neutral-100/0 -z-10 group-hover:opacity-0  transition-all duration-100 ease-in-out"})]})}function aa({className:a}){let{listing:b,handleOpenListing:c}=X();return(0,d.jsx)("h3",{className:(0,k.cn)("font-semibold text-seekers-text text-base line-clamp-1",a),onClick:a=>{a.stopPropagation(),c()},children:b.title})}function ab({className:a}){let{listing:b,handleOpenListing:c}=X();return(0,d.jsxs)("div",{className:(0,k.cn)("flex items-center text-xs gap-1 text-seekers-text-light font-medium",a),onClick:a=>{a.stopPropagation(),c()},children:[(0,d.jsx)(o.A,{className:"w-4 h-4"})," ",b.location," "]})}function ac(){let{currency:a}=(0,l.M)(),{listing:b,handleOpenListing:c,conversion:e}=X(),{startWord:f,formattedPrice:g,suffix:h}=((a,b,c,d)=>{let e=(0,r.useTranslations)("seeker"),[f,g]=(0,t.useState)(""),[h,i]=(0,t.useState)(""),[j,k]=(0,t.useState)(0);return(0,t.useEffect)(()=>{let f=(0,P.JX)(c?.suffix||""),h=(0,P.JX)(d?.suffix||"");(()=>{switch(b){case"LEASEHOLD":let c="MONTH"==h?e("misc.month",{count:d?.value||1}):"YEAR"==h?e("misc.yearWithCount",{count:d?.value||1}):h;return i(e("listing.pricing.suffix.leasehold",{count:d?.value||1,durationType:c})),k(a),g("");case"FREEHOLD":return k(a),g(e("conjuntion.for"));case"RENT":k(a);let j="MONTH"==f?e("misc.month",{count:1}):"YEAR"==f?e("misc.yearWithCount",{count:1}):h;return i(`/ ${j}`),g(e("misc.startFrom"));default:return}})()},[b,d?.suffix,d?.value,c?.suffix,c?.value,a]),{startWord:f,suffix:h,formattedPrice:j}})(b.price,b.availability.type,b.availability.minDuration||void 0,b.availability.maxDuration||void 0);return(0,d.jsxs)("p",{className:" text-base text-seekers-text font-medium ",onClick:a=>{a.stopPropagation(),c()},children:[(0,d.jsx)("span",{className:"text-sm font-medium text-seekers-text-lighter",children:(0,k.tT)(f)})," ",(0,k.vv)(g*(e[a]||1),a,"en-US")," ",(0,d.jsx)("span",{className:"text-xs text-seekers-text-lighter",children:h})]})}function ad(){return(0,d.jsxs)("div",{className:"w-full space-y-2",children:[(0,d.jsx)(g.E,{className:"w-full aspect-[4/3]"}),(0,d.jsxs)("div",{className:"space-y-1 px-0.5",children:[(0,d.jsx)(g.E,{className:"w-full h-8"}),(0,d.jsx)(g.E,{className:"w-full h-4"}),(0,d.jsx)(g.E,{className:"w-full h-4"})]})]})}function ae({className:a}){let b=(0,r.useTranslations)("seeker"),{listing:c,handleOpenListing:e}=X(),f=[i.BT.rooms,i.BT.commercialSpace,i.BT.cafeOrRestaurants,i.BT.offices,i.BT.shops,i.BT.shellAndCore],g=[i.BT.villa,i.BT.apartment,i.BT.homestay,i.BT.guestHouse];return(0,d.jsxs)("div",{className:(0,k.cn)("flex gap-2 text-xs font-normal h-fit !mt-0 text-seekers-text",a),onClick:a=>{a.stopPropagation(),e()},children:[f.includes(c.category||"")&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(w.A,{trigger:(0,d.jsxs)("div",{className:"flex gap-1 items-end",children:[(0,d.jsx)(s.default,{loading:"lazy",src:Q.default||"",alt:"",width:16,height:16,className:"w-4 h-4","aria-label":b("listing.feature.additionalFeature.buildingSize")}),(0,d.jsx)("span",{children:c.listingDetail.buildingSize}),(0,d.jsxs)("span",{children:["m",(0,d.jsx)("span",{className:"align-super text-[10px]",children:"2"})]})]}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.buildingSize")}),contentClassName:"text-seekers-primary p-2 text-sm"})}),g.includes(c.category||"")&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(w.A,{trigger:(0,d.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,d.jsx)(p.A,{className:"w-4 h-4",strokeWidth:1}),(0,d.jsx)("span",{children:c.listingDetail.bedRoom.value})]}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.bedroom")}),contentClassName:"text-seekers-primary p-2 text-sm"}),(0,d.jsx)(w.A,{trigger:(0,d.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,d.jsx)(q.A,{className:"w-4 h-4",strokeWidth:1}),(0,d.jsx)("span",{children:c.listingDetail.bathRoom.value})]}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.bathroom")}),contentClassName:"text-seekers-primary p-2 text-sm"})]}),c.category!==i.BT.lands&&c.sellingPoint?.length>0&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("div",{className:"flex gap-1 items-end",children:(0,d.jsx)(M,{...c.sellingPoint[0]})})}),(0,d.jsx)(w.A,{trigger:(0,d.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,d.jsx)(m.hWu,{className:"w-4 h-4",strokeWidth:1.5}),(0,d.jsxs)("p",{children:[c.listingDetail.landSize||""," "," ",(0,d.jsxs)("span",{children:["m",(0,d.jsx)("span",{className:"align-super text-[10px]",children:"2"})]})]})]}),content:(0,d.jsx)("p",{children:b("listing.feature.additionalFeature.land")}),contentClassName:"text-seekers-primary p-2 text-sm"})]})}Z.displayName="ListingWrapper"},99704:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/Municipal Waterworks.2ab3dfd5.svg",height:48,width:48,blurWidth:0,blurHeight:0}}};