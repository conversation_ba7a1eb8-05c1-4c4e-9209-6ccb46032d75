"use strict";exports.id=2804,exports.ids=[2804],exports.modules={3203:(a,b,c)=>{c.d(b,{Separator:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\ui\\separator.tsx","Separator")},29666:(a,b,c)=>{c.d(b,{A:()=>o});var d=c(37413),e=c(57922),f=c(32401),g=c(53384),h=c(97094),i=c(66819),j=c(19491),k=c(3203),l=c(17328),m=c(98353),n=c(40263);function o(){let a=(0,e.A)("seeker"),b=[{id:"villas",url:`/s/all?t=${n.BT.villa}`,content:a("footer.tabsOne.content.optionOne.title")},{id:"apartment",url:`/s/all?t=${n.BT.apartment}`,content:a("footer.tabsOne.content.optionTwo.title")},{id:"guesthouse",url:`/s/all?t=${n.BT.rooms}`,content:a("footer.tabsOne.content.optionThree.title")},{id:"homestay",url:`/s/all?t=${n.BT.rooms}`,content:a("footer.tabsOne.content.optionFour.title")},{id:"shops",url:`/s/all?t=${n.BT.shops}`,content:a("footer.tabsOne.content.optionFive.title")},{id:"offices",url:`/s/all?t=${n.BT.offices}`,content:a("footer.tabsOne.content.optionSix.title")},{id:"restaurant",url:`/s/all?t=${n.BT.cafeOrRestaurants}`,content:a("footer.tabsOne.content.optionSeven.title")}],c=[{id:"get-your-property-listed",url:process.env.ADMIN_DOMAIN||"",content:a("footer.tabsTwo.content.optionOne.title")},{id:"faq-for-owner",url:(process.env.ADMIN_DOMAIN||"")+"/#faq",content:a("footer.tabsTwo.content.optionTwo.title")}],i=[{id:"faq",url:`${m.Rd}#faq`,content:a("footer.tabsFour.content.optionOne.title")},{id:"contact-support",url:m.ig,content:a("footer.tabsFour.content.optionTwo.title")},{id:"about-us",url:"/about-us",content:a("footer.tabsFour.content.optionThree.title")},{id:"terms-of-use",url:m.po,content:a("footer.tabsFour.content.optionFour.title"),target:"_blank"},{id:"privacy-policy",url:m.Bu,content:a("footer.tabsFour.content.optionFive.title"),target:"_blank"}];return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("footer",{className:" bg-seekers-foreground/50 space-y-8 w-full py-6",children:(0,d.jsxs)(f.A,{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"w-full grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-2 md:gap-x-6 gap-y-8  py-6",children:[(0,d.jsxs)("div",{className:"space-y-1  col-span-2 lg:col-span-4 xl:col-span-2 ",children:[(0,d.jsx)("div",{className:"flex gap-2 items-center",children:(0,d.jsx)(g.default,{src:h.default,alt:"Properti-Plaza",width:164,height:48})}),(0,d.jsx)("p",{className:"text-body-variant text-xs font-normal text-seekers-text-light leading-6 tracking-[0.06px]",children:a("footer.slogan")})]}),(0,d.jsx)("div",{className:"max-sm:hidden"}),(0,d.jsx)(p,{title:a("footer.exploreProperties.title"),children:b.map(a=>(0,d.jsx)(j.N_,{href:a.url,children:a.content},a.id))}),(0,d.jsx)(p,{title:a("footer.properyOwner.title"),children:c.map(a=>(0,d.jsx)(j.N_,{href:a.url,children:a.content},a.id))}),(0,d.jsx)(p,{title:a("footer.help.title"),children:i.map(a=>(0,d.jsx)(j.N_,{href:a.url,target:a.target||"",children:a.content},a.id))})]}),(0,d.jsx)(k.Separator,{}),(0,d.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 items-center gap-2 md:gap-x-6",children:[(0,d.jsxs)("p",{className:"text-xs font-semibold text-seekers-text-light lg:col-span-3 xl:col-span-5",children:["\xa9 ",a("footer.copyright")]}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)(j.N_,{href:"https://www.facebook.com/join.propertyplaza",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:(0,d.jsx)(l.iYk,{className:"w-5 h-5"})}),(0,d.jsx)(j.N_,{href:"https://www.instagram.com/join.propertyplaza/",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:(0,d.jsx)(l.ao$,{className:"w-5 h-5"})}),(0,d.jsx)(j.N_,{href:"https://www.tiktok.com/@join.propertyplaza",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:(0,d.jsx)(l.kkU,{className:"w-5 h-5"})}),(0,d.jsx)(j.N_,{href:"https://www.linkedin.com/company/property-plaza-indonesia",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:(0,d.jsx)(l.QEs,{className:"w-5 h-5"})})]})]})]})})})}function p({children:a,title:b,...c}){return(0,d.jsxs)("div",{className:(0,i.cn)("space-y-4 w-48",c),children:[(0,d.jsx)("h2",{className:"font-semibold  text-seekers-text",children:b}),(0,d.jsx)("div",{className:"flex flex-col gap-2 text-seekers-text-light text-xs",children:a})]})}},40263:(a,b,c)=>{c.d(b,{BT:()=>d});let d={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"}},62881:(a,b,c)=>{c.d(b,{default:()=>l});var d=c(60687),e=c(16189),f=c(5395),g=c(33213),h=c(84404),i=c(82001),j=c(69533),k=c(70742);function l(){let a=(0,i.A)({delay:3e3,stopOnInteraction:!1}),b=(0,g.useTranslations)("seeker"),c=(0,e.usePathname)();return(0,d.jsxs)(d.Fragment,{children:[(c.includes("/s/"),(0,d.jsx)(d.Fragment,{})),(0,d.jsxs)("div",{className:"w-full py-3 bg-[#F7ECDC]",children:[(0,d.jsx)(f.A,{className:"md:hidden",children:(0,d.jsx)("div",{className:"w-full md:hidden",children:(0,d.jsx)(h.FN,{opts:{active:!0,loop:!0},plugins:[a],children:(0,d.jsxs)(h.Wk,{className:"text-seekers-primary",children:[(0,d.jsxs)(h.A7,{className:"inline-flex gap-2 items-center",children:[(0,d.jsx)(j.A,{className:"!w-5 !h-5"}),(0,d.jsx)("p",{className:"font-semibold text-xs",children:b("banner.seekers.discoverDreamHome.title")})]}),(0,d.jsxs)(h.A7,{className:"inline-flex gap-2 items-center",children:[(0,d.jsx)(k.A,{className:"!w-5 !h-5"}),(0,d.jsx)("p",{className:" font-semibold text-xs",children:b("banner.seekers.connectToPropertyOwner.title")})]})]})})})}),(0,d.jsx)(f.A,{className:"hidden md:block text-seekers-primary",children:(0,d.jsxs)("div",{className:"hidden md:flex gap-4 md:justify-between",children:[(0,d.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,d.jsx)("p",{className:"font-semibold text-xs",children:b("banner.seekers.discoverDreamHome.title")}),(0,d.jsx)(j.A,{className:"!w-4 !h-4"})]}),(0,d.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,d.jsx)(k.A,{className:"!w-4 !h-4"}),(0,d.jsx)("p",{className:"font-semibold text-xs",children:b("banner.seekers.connectToPropertyOwner.title")})]})]})})]})]})}},67735:(a,b,c)=>{c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\navbar\\\\seekers-banner.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\navbar\\seekers-banner.tsx","default")},84404:(a,b,c)=>{c.d(b,{A7:()=>p,FN:()=>n,Oj:()=>r,Q8:()=>q,Wk:()=>o,ZZ:()=>s});var d=c(60687),e=c(43210),f=c(53562),g=c(96241),h=c(24934),i=c(47033),j=c(14952),k=c(33213);let l=e.createContext(null);function m(){let a=e.useContext(l);if(!a)throw Error("useCarousel must be used within a <Carousel />");return a}let n=e.forwardRef(({orientation:a="horizontal",opts:b,setApi:c,plugins:h,className:i,children:j,...k},m)=>{let[n,o]=(0,f.A)({...b,axis:"horizontal"===a?"x":"y"},h),[p,q]=e.useState(!1),[r,s]=e.useState(!1),[t,u]=e.useState(0),v=e.useCallback(a=>{a&&(q(a.canScrollPrev()),s(a.canScrollNext()),u(a.selectedScrollSnap()))},[]),w=e.useCallback(()=>{o?.scrollPrev()},[o]),x=e.useCallback(()=>{o?.scrollNext()},[o]),y=e.useCallback(a=>{"ArrowLeft"===a.key?(a.preventDefault(),w()):"ArrowRight"===a.key&&(a.preventDefault(),x())},[w,x]),z=e.useCallback(a=>{o?.scrollTo(a)},[o]);return e.useEffect(()=>{o&&c&&c(o)},[o,c]),e.useEffect(()=>{if(o)return v(o),o.on("reInit",v),o.on("select",v),()=>{o?.off("select",v)}},[o,v]),(0,d.jsx)(l.Provider,{value:{carouselRef:n,api:o,opts:b,orientation:a||(b?.axis==="y"?"vertical":"horizontal"),scrollPrev:w,scrollNext:x,canScrollPrev:p,canScrollNext:r,selectedIndex:t,scrollTo:z},children:(0,d.jsx)("div",{...k,ref:m,onKeyDownCapture:y,className:(0,g.cn)("relative",i),role:"region","aria-roledescription":"carousel",children:j})})});n.displayName="Carousel";let o=e.forwardRef(({className:a,...b},c)=>{let{carouselRef:e,orientation:f}=m();return(0,d.jsx)("div",{ref:e,className:"overflow-hidden",children:(0,d.jsx)("div",{...b,ref:c,className:(0,g.cn)("flex","horizontal"===f?"-ml-4":"-mt-4 flex-col",a)})})});o.displayName="CarouselContent";let p=e.forwardRef(({className:a,...b},c)=>{let{orientation:e}=m();return(0,d.jsx)("div",{...b,ref:c,role:"group","aria-roledescription":"slide",className:(0,g.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===e?"pl-4":"pt-4",a)})});p.displayName="CarouselItem";let q=e.forwardRef(({iconClassName:a,className:b,variant:c="outline",size:e="icon",...f},j)=>{let{orientation:l,scrollPrev:n,canScrollPrev:o}=m(),p=(0,k.useTranslations)("universal");return(0,d.jsxs)(h.$,{...f,ref:j,variant:c,size:e,className:(0,g.cn)("absolute  h-6 w-6 rounded-full","horizontal"===l?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",b),disabled:!o,onClick:n,children:[(0,d.jsx)(i.A,{className:(0,g.cn)("h-4 w-4",a)}),(0,d.jsx)("span",{className:"sr-only",children:p("cta.previous")})]})});q.displayName="CarouselPrevious";let r=e.forwardRef(({iconClassName:a,className:b,variant:c="outline",size:e="icon",...f},i)=>{let{orientation:l,scrollNext:n,canScrollNext:o}=m(),p=(0,k.useTranslations)("seeker");return(0,d.jsxs)(h.$,{...f,ref:i,variant:c,size:e,className:(0,g.cn)("absolute h-6 w-6 rounded-full","horizontal"===l?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",b),disabled:!o,onClick:n,children:[(0,d.jsx)(j.A,{className:(0,g.cn)("h-4 w-4",a)}),(0,d.jsx)("span",{className:"sr-only",children:p("cta.next")})]})});r.displayName="CarouselNext";let s=e.forwardRef(({className:a,carouselDotClassName:b,...c},e)=>{let{selectedIndex:f,scrollTo:i,api:j}=m();return(0,d.jsx)("div",{ref:e,className:(0,g.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",a),...c,children:j?.scrollSnapList().map((a,c)=>(0,d.jsx)(h.$,{size:"icon",className:(0,g.cn)(b,"embla__dot h-2 w-2 rounded-full ",c===f?"bg-white/90 ":"bg-black/10"),onClick:()=>i?.(c)},c))})});s.displayName="CarouselDots"},97094:(a,b,c)=>{c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SuiVK1jFWxjFVjqnREAAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}}};