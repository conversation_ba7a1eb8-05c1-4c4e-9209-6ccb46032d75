exports.id=3331,exports.ids=[3331],exports.modules={2657:(a,b,c)=>{var d=c(79551).parse,e=c(94735),f=c(55591),g=c(81630),h=c(28354),i=["pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","secureProtocol","servername","checkServerIdentity"],j=[239,187,191],k=/^(cookie|authorization)$/i;function l(a,b){var c,e=l.CONNECTING,h=b&&b.headers,o=!1;Object.defineProperty(this,"readyState",{get:function(){return e}}),Object.defineProperty(this,"url",{get:function(){return a}});var p=this;function q(b){e!==l.CLOSED&&(e=l.CONNECTING,x("error",new m("error",{message:b})),v&&(a=v,v=null,o=!1),setTimeout(function(){e!==l.CONNECTING||p.connectionInProgress||(p.connectionInProgress=!0,w())},p.reconnectInterval))}p.reconnectInterval=1e3,p.connectionInProgress=!1;var r="";h&&h["Last-Event-ID"]&&(r=h["Last-Event-ID"],delete h["Last-Event-ID"]);var s=!1,t="",u="",v=null;function w(){var y=d(a),z="https:"===y.protocol;if(y.headers={"Cache-Control":"no-cache",Accept:"text/event-stream"},r&&(y.headers["Last-Event-ID"]=r),h){var A=o?function(a){var b={};for(var c in a)k.test(c)||(b[c]=a[c]);return b}(h):h;for(var B in A){var C=A[B];C&&(y.headers[B]=C)}}if(y.rejectUnauthorized=!(b&&!b.rejectUnauthorized),b&&void 0!==b.createConnection&&(y.createConnection=b.createConnection),b&&b.proxy){var D=d(b.proxy);y.protocol=(z="https:"===D.protocol)?"https:":"http:",y.path=a,y.headers.Host=y.host,y.hostname=D.hostname,y.host=D.host,y.port=D.port}if(b&&b.https){for(var E in b.https)if(-1!==i.indexOf(E)){var F=b.https[E];void 0!==F&&(y[E]=F)}}b&&void 0!==b.withCredentials&&(y.withCredentials=b.withCredentials),(c=(z?f:g).request(y,function(b){if(p.connectionInProgress=!1,500===b.statusCode||502===b.statusCode||503===b.statusCode||504===b.statusCode){x("error",new m("error",{status:b.statusCode,message:b.statusMessage})),q();return}if(301===b.statusCode||302===b.statusCode||307===b.statusCode){var c,d,f=b.headers.location;return f?(o=new URL(a).origin!==new URL(f).origin,307===b.statusCode&&(v=a),a=f,void process.nextTick(w)):void x("error",new m("error",{status:b.statusCode,message:b.statusMessage}))}if(200!==b.statusCode)return x("error",new m("error",{status:b.statusCode,message:b.statusMessage})),p.close();e=l.OPEN,b.on("close",function(){b.removeAllListeners("close"),b.removeAllListeners("end"),q()}),b.on("end",function(){b.removeAllListeners("close"),b.removeAllListeners("end"),q()}),x("open",new m("open"));var g=0,h=-1,i=0,k=0;b.on("data",function(b){if(c)b.length>c.length-k&&((i=2*c.length+b.length)>262144&&(i=c.length+b.length+262144),d=Buffer.alloc(i),c.copy(d,0,0,k),c=d),b.copy(c,k),k+=b.length;else{var e;e=c=b,j.every(function(a,b){return e[b]===a})&&(c=c.slice(j.length)),k=c.length}for(var f=0,l=k;f<l;){s&&(10===c[f]&&++f,s=!1);for(var m,o=-1,q=h,v=g;o<0&&v<l;++v)58===(m=c[v])?q<0&&(q=v-f):13===m?(s=!0,o=v-f):10===m&&(o=v-f);if(o<0){g=l-f,h=q;break}g=0,h=-1,function(b,c,d,e){if(0===e){if(t.length>0){var f=u||"message";x(f,new n(f,{data:t.slice(0,-1),lastEventId:r,origin:new URL(a).origin})),t=""}u=void 0}else if(d>0){var g=d<0,h=0,i=b.slice(c,c+(g?e:d)).toString();h=g?e:32!==b[c+d+1]?d+1:d+2,c+=h;var j=e-h,k=b.slice(c,c+j).toString();if("data"===i)t+=k+"\n";else if("event"===i)u=k;else if("id"===i)r=k;else if("retry"===i){var l=parseInt(k,10);Number.isNaN(l)||(p.reconnectInterval=l)}}}(c,f,q,o),f+=o+1}f===l?(c=void 0,k=0):f>0&&(k=(c=c.slice(f,k)).length)})})).on("error",function(a){p.connectionInProgress=!1,q(a.message)}),c.setNoDelay&&c.setNoDelay(!0),c.end()}function x(){p.listeners(arguments[0]).length>0&&p.emit.apply(p,arguments)}w(),this._close=function(){e!==l.CLOSED&&(e=l.CLOSED,c.abort&&c.abort(),c.xhr&&c.xhr.abort&&c.xhr.abort())}}function m(a,b){if(Object.defineProperty(this,"type",{writable:!1,value:a,enumerable:!0}),b)for(var c in b)b.hasOwnProperty(c)&&Object.defineProperty(this,c,{writable:!1,value:b[c],enumerable:!0})}function n(a,b){for(var c in Object.defineProperty(this,"type",{writable:!1,value:a,enumerable:!0}),b)b.hasOwnProperty(c)&&Object.defineProperty(this,c,{writable:!1,value:b[c],enumerable:!0})}a.exports=l,h.inherits(l,e.EventEmitter),l.prototype.constructor=l,["open","error","message"].forEach(function(a){Object.defineProperty(l.prototype,"on"+a,{get:function(){var b=this.listeners(a)[0];return b?b._listener?b._listener:b:void 0},set:function(b){this.removeAllListeners(a),this.addEventListener(a,b)}})}),Object.defineProperty(l,"CONNECTING",{enumerable:!0,value:0}),Object.defineProperty(l,"OPEN",{enumerable:!0,value:1}),Object.defineProperty(l,"CLOSED",{enumerable:!0,value:2}),l.prototype.CONNECTING=0,l.prototype.OPEN=1,l.prototype.CLOSED=2,l.prototype.close=function(){this._close()},l.prototype.addEventListener=function(a,b){"function"==typeof b&&(b._listener=b,this.on(a,b))},l.prototype.dispatchEvent=function(a){if(!a.type)throw Error("UNSPECIFIED_EVENT_TYPE_ERR");this.emit(a.type,a.detail)},l.prototype.removeEventListener=function(a,b){"function"==typeof b&&(b._listener=void 0,this.removeListener(a,b))}},93331:(a,b,c)=>{a.exports=c(2657)}};