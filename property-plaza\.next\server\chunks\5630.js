"use strict";exports.id=5630,exports.ids=[5630],exports.modules={10958:(a,b,c)=>{c.d(b,{$:()=>C,B:()=>Z,C:()=>av,D:()=>U,E:()=>aB,F:()=>X,G:()=>as,H:()=>function a(b,c,d,e){if("string"==typeof c){let a=ag(b,c).next(d,e);return a&&a.length?a:b.next(d,e)}if(1===Object.keys(c).length){let f=Object.keys(c),g=a(ag(b,f[0]),c[f[0]],d,e);return g&&g.length?g:b.next(d,e)}let f=[];for(let g of Object.keys(c)){let h=c[g];if(!h)continue;let i=a(ag(b,g),h,d,e);i&&f.push(...i)}return f.length?f:b.next(d,e)},I:()=>ar,J:()=>l,K:()=>ao,L:()=>af,M:()=>o,N:()=>f,O:()=>Y,P:()=>ah,Q:()=>aD,R:()=>y,S:()=>e,T:()=>h,U:()=>k,V:()=>D,W:()=>aC,X:()=>i,c:()=>G,f:()=>J,g:()=>ai,h:()=>M,k:()=>P,l:()=>q,q:()=>aa,r:()=>aG,t:()=>r,u:()=>u,v:()=>_,w:()=>Q,x:()=>x,y:()=>$,z:()=>ab});class d{constructor(a){this._process=a,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(a){let b={value:a,next:null};if(this._current){this._last.next=b,this._last=b;return}this._current=b,this._last=b,this._active&&this.flush()}flush(){for(;this._current;){let a=this._current;this._process(a.value),this._current=a.next}this._last=null}}let e=".",f="",g="xstate.init",h="xstate.error",i="xstate.stop";function j(a,b){return{type:`xstate.done.state.${a}`,output:b}}function k(a,b){return{type:`xstate.error.actor.${a}`,error:b,actorId:a}}function l(a){return{type:g,input:a}}function m(a){setTimeout(()=>{throw a})}let n="function"==typeof Symbol&&Symbol.observable||"@@observable";function o(a){if(t(a))return a;let b=[],c="";for(let d=0;d<a.length;d++){switch(a.charCodeAt(d)){case 92:c+=a[d+1],d++;continue;case 46:b.push(c),c="";continue}c+=a[d]}return b.push(c),b}function p(a){var b;return(b=a)&&"object"==typeof b&&"machine"in b&&"value"in b?a.value:"string"!=typeof a?a:function(a){if(1===a.length)return a[0];let b={},c=b;for(let b=0;b<a.length-1;b++)if(b===a.length-2)c[a[b]]=a[b+1];else{let d=c;c={},d[a[b]]=c}return b}(o(a))}function q(a,b){let c={},d=Object.keys(a);for(let e=0;e<d.length;e++){let f=d[e];c[f]=b(a[f],f,a,e)}return c}function r(a){var b;return void 0===a?[]:t(b=a)?b:[b]}function s(a,b,c,d){return"function"==typeof a?a({context:b,event:c,self:d}):a}function t(a){return Array.isArray(a)}function u(a){var b;return(t(b=a)?b:[b]).map(a=>void 0===a||"string"==typeof a?{target:a}:a)}function v(a){if(void 0!==a&&""!==a)return r(a)}function w(a,b,c){let d="object"==typeof a,e=d?a:void 0;return{next:(d?a.next:a)?.bind(e),error:(d?a.error:b)?.bind(e),complete:(d?a.complete:c)?.bind(e)}}function x(a,b){return`${b}.${a}`}function y(a,b){let c=b.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!c)return a.implementations.actors[b];let[,d,e]=c,f=a.getStateNodeById(e).config.invoke;return(Array.isArray(f)?f[d]:f).src}function z(a,b){return`${a.sessionId}.${b}`}let A=0,B=!1,C=1,D=function(a){return a[a.NotStarted=0]="NotStarted",a[a.Running=1]="Running",a[a.Stopped=2]="Stopped",a}({}),E={clock:{setTimeout:(a,b)=>setTimeout(a,b),clearTimeout:a=>clearTimeout(a)},logger:console.log.bind(console),devTools:!1};class F{constructor(a,b){this.logic=a,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new d(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=D.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];let c={...E,...b},{clock:e,logger:f,parent:g,syncSnapshot:h,id:i,systemId:j,inspect:k}=c;this.system=g?g.system:function(a,b){let c=new Map,d=new Map,e=new WeakMap,f=new Set,g={},{clock:h,logger:i}=b,j={schedule:(a,b,c,d,e=Math.random().toString(36).slice(2))=>{let f={source:a,target:b,event:c,delay:d,id:e,startedAt:Date.now()},i=z(a,e);k._snapshot._scheduledEvents[i]=f;let j=h.setTimeout(()=>{delete g[i],delete k._snapshot._scheduledEvents[i],k._relay(a,b,c)},d);g[i]=j},cancel:(a,b)=>{let c=z(a,b),d=g[c];delete g[c],delete k._snapshot._scheduledEvents[c],void 0!==d&&h.clearTimeout(d)},cancelAll:a=>{for(let b in k._snapshot._scheduledEvents){let c=k._snapshot._scheduledEvents[b];c.source===a&&j.cancel(a,c.id)}}},k={_snapshot:{_scheduledEvents:(b?.snapshot&&b.snapshot.scheduler)??{}},_bookId:()=>`x:${A++}`,_register:(a,b)=>(c.set(a,b),a),_unregister:a=>{c.delete(a.sessionId);let b=e.get(a);void 0!==b&&(d.delete(b),e.delete(a))},get:a=>d.get(a),_set:(a,b)=>{let c=d.get(a);if(c&&c!==b)throw Error(`Actor with system ID '${a}' already exists.`);d.set(a,b),e.set(b,a)},inspect:a=>{let b=w(a);return f.add(b),{unsubscribe(){f.delete(b)}}},_sendInspectionEvent:b=>{if(!f.size)return;let c={...b,rootId:a.sessionId};f.forEach(a=>a.next?.(c))},_relay:(a,b,c)=>{k._sendInspectionEvent({type:"@xstate.event",sourceRef:a,actorRef:b,event:c}),b._send(c)},scheduler:j,getSnapshot:()=>({_scheduledEvents:{...k._snapshot._scheduledEvents}}),start:()=>{let a=k._snapshot._scheduledEvents;for(let b in k._snapshot._scheduledEvents={},a){let{source:c,target:d,event:e,delay:f,id:g}=a[b];j.schedule(c,d,e,f,g)}},_clock:h,_logger:i};return k}(this,{clock:e,logger:f}),k&&!g&&this.system.inspect(w(k)),this.sessionId=this.system._bookId(),this.id=i??this.sessionId,this.logger=b?.logger??this.system._logger,this.clock=b?.clock??this.system._clock,this._parent=g,this._syncSnapshot=h,this.options=c,this.src=c.src??a,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:a=>{this._deferred.push(a)},system:this.system,stopChild:a=>{if(a._parent!==this)throw Error(`Cannot stop child actor ${a.id} of ${this.id} because it is not a child`);a._stop()},emit:a=>{let b=this.eventListeners.get(a.type),c=this.eventListeners.get("*");if(b||c)for(let d of[...b?b.values():[],...c?c.values():[]])d(a)},actionExecutor:a=>{let b=()=>{if(this._actorScope.system._sendInspectionEvent({type:"@xstate.action",actorRef:this,action:{type:a.type,params:a.params}}),!a.exec)return;let b=B;try{B=!0,a.exec(a.info,a.params)}finally{B=b}};this._processingStatus===D.Running?b():this._deferred.push(b)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),j&&(this._systemId=j,this.system._set(j,this)),this._initState(b?.snapshot??b?.state),j&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(a){try{this._snapshot=a?this.logic.restoreSnapshot?this.logic.restoreSnapshot(a,this._actorScope):a:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(a){this._snapshot={status:"error",output:void 0,error:a}}}update(a,b){let c;for(this._snapshot=a;c=this._deferred.shift();)try{c()}catch(b){this._deferred.length=0,this._snapshot={...a,status:"error",error:b}}switch(this._snapshot.status){case"active":for(let b of this.observers)try{b.next?.(a)}catch(a){m(a)}break;case"done":var d;for(let b of this.observers)try{b.next?.(a)}catch(a){m(a)}this._stopProcedure(),this._complete(),this._doneEvent=(d=this.id,{type:`xstate.done.actor.${d}`,output:this._snapshot.output,actorId:d}),this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:b,snapshot:a})}subscribe(a,b,c){let d=w(a,b,c);if(this._processingStatus!==D.Stopped)this.observers.add(d);else switch(this._snapshot.status){case"done":try{d.complete?.()}catch(a){m(a)}break;case"error":{let a=this._snapshot.error;if(d.error)try{d.error(a)}catch(a){m(a)}else m(a)}}return{unsubscribe:()=>{this.observers.delete(d)}}}on(a,b){let c=this.eventListeners.get(a);c||(c=new Set,this.eventListeners.set(a,c));let d=b.bind(void 0);return c.add(d),{unsubscribe:()=>{c.delete(d)}}}start(){if(this._processingStatus===D.Running)return this;this._syncSnapshot&&this.subscribe({next:a=>{"active"===a.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:a})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=D.Running;let a=l(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:a}),this._snapshot.status){case"done":return this.update(this._snapshot,a),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(a){return this._snapshot={...this._snapshot,status:"error",error:a},this._error(a),this}return this.update(this._snapshot,a),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(a){let b,c;try{b=this.logic.transition(this._snapshot,a,this._actorScope)}catch(a){c={err:a}}if(c){let{err:a}=c;this._snapshot={...this._snapshot,status:"error",error:a},this._error(a);return}this.update(b,a),a.type===i&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===D.Stopped||((this.mailbox.clear(),this._processingStatus===D.NotStarted)?this._processingStatus=D.Stopped:this.mailbox.enqueue({type:i})),this}stop(){if(this._parent)throw Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(let a of this.observers)try{a.complete?.()}catch(a){m(a)}this.observers.clear()}_reportError(a){if(!this.observers.size){this._parent||m(a);return}let b=!1;for(let c of this.observers){let d=c.error;b||=!d;try{d?.(a)}catch(a){m(a)}}this.observers.clear(),b&&m(a)}_error(a){this._stopProcedure(),this._reportError(a),this._parent&&this.system._relay(this,this._parent,k(this.id,a))}_stopProcedure(){return this._processingStatus!==D.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new d(this._process.bind(this)),this._processingStatus=D.Stopped,this.system._unregister(this)),this}_send(a){this._processingStatus!==D.Stopped&&this.mailbox.enqueue(a)}send(a){this.system._relay(void 0,this,a)}attachDevTools(){let{devTools:a}=this.options;a&&("function"==typeof a?a:a=>{if("undefined"==typeof window)return;let b=function(){let a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;if(a.__xstate__)return a.__xstate__}();b&&b.register(a)})(this)}toJSON(){return{xstate$$type:C,id:this.id}}getPersistedSnapshot(a){return this.logic.getPersistedSnapshot(this._snapshot,a)}[n](){return this}getSnapshot(){return this._snapshot}}function G(a,...[b]){return new F(a,b)}function H(a,b,c,d,{sendId:e}){return[b,{sendId:"function"==typeof e?e(c,d):e},void 0]}function I(a,b){a.defer(()=>{a.system.scheduler.cancel(a.self,b.sendId)})}function J(a){function b(a,b){}return b.type="xstate.cancel",b.sendId=a,b.resolve=H,b.execute=I,b}function K(a,b,c,d,{id:e,systemId:f,src:g,input:h,syncSnapshot:i}){let j,k,l="string"==typeof g?y(b.machine,g):g,m="function"==typeof e?e(c):e;return l&&(k="function"==typeof h?h({context:b.context,event:c.event,self:a.self}):h,j=G(l,{id:m,src:g,parent:a.self,syncSnapshot:i,systemId:f,input:k})),[aC(b,{children:{...b.children,[m]:j}}),{id:e,systemId:f,actorRef:j,src:g,input:k},void 0]}function L(a,{actorRef:b}){b&&a.defer(()=>{b._processingStatus!==D.Stopped&&b.start()})}function M(...[a,{id:b,systemId:c,input:d,syncSnapshot:e=!1}={}]){function f(a,b){}return f.type="xstate.spawnChild",f.id=b,f.systemId=c,f.src=a,f.input=d,f.syncSnapshot=e,f.resolve=K,f.execute=L,f}function N(a,b,c,d,{actorRef:e}){let f="function"==typeof e?e(c,d):e,g="string"==typeof f?b.children[f]:f,h=b.children;return g&&(h={...h},delete h[g.id]),[aC(b,{children:h}),g,void 0]}function O(a,b){if(b){if(a.system._unregister(b),b._processingStatus!==D.Running)return void a.stopChild(b);a.defer(()=>{a.stopChild(b)})}}function P(a){function b(a,b){}return b.type="xstate.stopChild",b.actorRef=a,b.resolve=N,b.execute=O,b}function Q(a,b,c,d){let{machine:e}=d,f="function"==typeof a,g=f?a:e.implementations.guards["string"==typeof a?a:a.type];if(!f&&!g)throw Error(`Guard '${"string"==typeof a?a:a.type}' is not implemented.'.`);if("function"!=typeof g)return Q(g,b,c,d);let h={context:b,event:c},i=f||"string"==typeof a?void 0:"params"in a?"function"==typeof a.params?a.params({context:b,event:c}):a.params:void 0;return"check"in g?g.check(d,h,g):g(h,i)}let R=a=>"atomic"===a.type||"final"===a.type;function S(a){return Object.values(a.states).filter(a=>"history"!==a.type)}function T(a,b){let c=[];if(b===a)return c;let d=a.parent;for(;d&&d!==b;)c.push(d),d=d.parent;return c}function U(a){let b=new Set(a),c=V(b);for(let a of b)if("compound"!==a.type||c.get(a)&&c.get(a).length){if("parallel"===a.type){for(let c of S(a))if("history"!==c.type&&!b.has(c))for(let a of ae(c))b.add(a)}}else ae(a).forEach(a=>b.add(a));for(let a of b){let c=a.parent;for(;c;)b.add(c),c=c.parent}return b}function V(a){let b=new Map;for(let c of a)b.has(c)||b.set(c,[]),c.parent&&(b.has(c.parent)||b.set(c.parent,[]),b.get(c.parent).push(c));return b}function W(a,b){return function a(b,c){let d=c.get(b);if(!d)return{};if("compound"===b.type){let a=d[0];if(!a)return{};if(R(a))return a.key}let e={};for(let b of d)e[b.key]=a(b,c);return e}(a,V(U(b)))}function X(a,b){return"compound"===b.type?S(b).some(b=>"final"===b.type&&a.has(b)):"parallel"===b.type?S(b).every(b=>X(a,b)):"final"===b.type}let Y=a=>"#"===a[0];function Z(a,b){return a.transitions.get(b)||[...a.transitions.keys()].filter(a=>{if("*"===a)return!0;if(!a.endsWith(".*"))return!1;let c=a.split("."),d=b.split(".");for(let a=0;a<c.length;a++){let b=c[a],e=d[a];if("*"===b)return a===c.length-1;if(b!==e)return!1}return!0}).sort((a,b)=>b.length-a.length).flatMap(b=>a.transitions.get(b))}function $(a){let b=a.config.after;return b?Object.keys(b).flatMap(c=>{let d=b[c],e=Number.isNaN(+c)?c:+c,f=(b=>{var c;let d=(c=a.id,{type:`xstate.after.${b}.${c}`}),e=d.type;return a.entry.push(aG(d,{id:e,delay:b})),a.exit.push(J(e)),e})(e);return r("string"==typeof d?{target:d}:d).map(a=>({...a,event:f,delay:e}))}).map(b=>{let{delay:c}=b;return{..._(a,b.event,b),delay:c}}):[]}function _(a,b,c){let d=v(c.target),f=c.reenter??!1,g=function(a,b){if(void 0!==b)return b.map(b=>{if("string"!=typeof b)return b;if(Y(b))return a.machine.getStateNodeById(b);let c=b[0]===e;if(c&&!a.parent)return ah(a,b.slice(1));let d=c?a.key+b:b;if(a.parent)try{return ah(a.parent,d)}catch(b){throw Error(`Invalid transition definition for state node '${a.id}':
${b.message}`)}throw Error(`Invalid target: "${b}" is not a valid target from the root node. Did you mean ".${b}"?`)})}(a,d),h={...c,actions:r(c.actions),guard:c.guard,target:g,source:a,reenter:f,eventType:b,toJSON:()=>({...h,source:`#${a.id}`,target:g?g.map(a=>`#${a.id}`):void 0})};return h}function aa(a){let b=new Map;if(a.config.on)for(let c of Object.keys(a.config.on)){if(c===f)throw Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');let d=a.config.on[c];b.set(c,u(d).map(b=>_(a,c,b)))}if(a.config.onDone){let c=`xstate.done.state.${a.id}`;b.set(c,u(a.config.onDone).map(b=>_(a,c,b)))}for(let c of a.invoke){if(c.onDone){let d=`xstate.done.actor.${c.id}`;b.set(d,u(c.onDone).map(b=>_(a,d,b)))}if(c.onError){let d=`xstate.error.actor.${c.id}`;b.set(d,u(c.onError).map(b=>_(a,d,b)))}if(c.onSnapshot){let d=`xstate.snapshot.${c.id}`;b.set(d,u(c.onSnapshot).map(b=>_(a,d,b)))}}for(let c of a.after){let a=b.get(c.eventType);a||(a=[],b.set(c.eventType,a)),a.push(c)}return b}function ab(a,b){let c="string"==typeof b?a.states[b]:b?a.states[b.target]:void 0;if(!c&&b)throw Error(`Initial state node "${b}" not found on parent state node #${a.id}`);let d={source:a,actions:b&&"string"!=typeof b?r(b.actions):[],eventType:null,reenter:!1,target:c?[c]:[],toJSON:()=>({...d,source:`#${a.id}`,target:c?[`#${c.id}`]:[]})};return d}function ac(a){let b=v(a.config.target);return b?{target:b.map(b=>"string"==typeof b?ah(a.parent,b):b)}:a.parent.initial}function ad(a){return"history"===a.type}function ae(a){let b=af(a);for(let c of b)for(let d of T(c,a))b.add(d);return b}function af(a){let b=new Set;return!function a(c){if(!b.has(c)){if(b.add(c),"compound"===c.type)a(c.initial.target[0]);else if("parallel"===c.type)for(let b of S(c))a(b)}}(a),b}function ag(a,b){if(Y(b))return a.machine.getStateNodeById(b);if(!a.states)throw Error(`Unable to retrieve child state '${b}' from '${a.id}'; no child states exist.`);let c=a.states[b];if(!c)throw Error(`Child state '${b}' does not exist on '${a.id}'`);return c}function ah(a,b){if("string"==typeof b&&Y(b))try{return a.machine.getStateNodeById(b)}catch{}let c=o(b).slice(),d=a;for(;c.length;){let a=c.shift();if(!a.length)break;d=ag(d,a)}return d}function ai(a,b){if("string"==typeof b){let c=a.states[b];if(!c)throw Error(`State '${b}' does not exist on '${a.id}'`);return[a,c]}let c=Object.keys(b),d=c.map(b=>ag(a,b)).filter(Boolean);return[a.machine.root,a].concat(d,c.reduce((c,d)=>{let e=ag(a,d);if(!e)return c;let f=ai(e,b[d]);return c.concat(f)},[]))}function aj(a,b){let c=a;for(;c.parent&&c.parent!==b;)c=c.parent;return c.parent===b}function ak(a,b,c){let d=new Set;for(let e of a){let a=!1,f=new Set;for(let g of d)if(function(a,b){let c=new Set(a),d=new Set(b);for(let a of c)if(d.has(a))return!0;for(let a of d)if(c.has(a))return!0;return!1}(an([e],b,c),an([g],b,c)))if(aj(e.source,g.source))f.add(g);else{a=!0;break}if(!a){for(let a of f)d.delete(a);d.add(e)}}return Array.from(d)}function al(a,b){if(!a.target)return[];let c=new Set;for(let d of a.target)if(ad(d))if(b[d.id])for(let a of b[d.id])c.add(a);else for(let a of al(ac(d),b))c.add(a);else c.add(d);return[...c]}function am(a,b){let c=al(a,b);if(!c)return;if(!a.reenter&&c.every(b=>b===a.source||aj(b,a.source)))return a.source;let d=function(a){let[b,...c]=a;for(let a of T(b,void 0))if(c.every(b=>aj(b,a)))return a}(c.concat(a.source));return d||(a.reenter?void 0:a.source.machine.root)}function an(a,b,c){let d=new Set;for(let e of a)if(e.target?.length){let a=am(e,c);for(let c of(e.reenter&&e.source===a&&d.add(a),b))aj(c,a)&&d.add(c)}return[...d]}function ao(a,b,c,d,e,f){if(!a.length)return b;let g=new Set(b._nodes),h=b.historyValue,i=ak(a,g,h),k=b;e||([k,h]=function(a,b,c,d,e,f,g,h){let i,j=a,k=an(d,e,f);for(let a of(k.sort((a,b)=>b.order-a.order),k))for(let b of function(a){return Object.keys(a.states).map(b=>a.states[b]).filter(a=>"history"===a.type)}(a)){let c;c="deep"===b.history?b=>R(b)&&aj(b,a):b=>b.parent===a,(i??={...f})[b.id]=Array.from(e).filter(c)}for(let a of k)j=ar(j,b,c,[...a.exit,...a.invoke.map(a=>P(a.id))],g,void 0),e.delete(a);return[j,i||f]}(k,d,c,i,g,h,f,c.actionExecutor)),k=function(a,b,c,d,e,f,g,h){let i=a,k=new Set,l=new Set;(function(a,b,c,d){for(let e of a){let a=am(e,b);for(let f of e.target||[])!ad(f)&&(e.source!==f||e.source!==a||e.reenter)&&(d.add(f),c.add(f)),ap(f,b,c,d);for(let f of al(e,b)){let g=T(f,a);a?.type==="parallel"&&g.push(a),aq(d,b,c,g,!e.source.parent&&e.reenter?void 0:a)}}})(d,g,l,k),h&&l.add(a.machine.root);let m=new Set;for(let a of[...k].sort((a,b)=>a.order-b.order)){e.add(a);let d=[];for(let b of(d.push(...a.entry),a.invoke))d.push(M(b.src,{...b,syncSnapshot:!!b.onSnapshot}));if(l.has(a)){let b=a.initial.actions;d.push(...b)}if(i=ar(i,b,c,d,f,a.invoke.map(a=>a.id)),"final"===a.type){let d=a.parent,g=d?.type==="parallel"?d:d?.parent,h=g||a;for(d?.type==="compound"&&f.push(j(d.id,void 0!==a.output?s(a.output,i.context,b,c.self):void 0));g?.type==="parallel"&&!m.has(g)&&X(e,g);)m.add(g),f.push(j(g.id)),h=g,g=g.parent;if(g)continue;i=aC(i,{status:"done",output:function(a,b,c,d,e){if(void 0===d.output)return;let f=j(e.id,void 0!==e.output&&e.parent?s(e.output,a.context,b,c.self):void 0);return s(d.output,a.context,f,c.self)}(i,b,c,i.machine.root,h)})}}return i}(k=ar(k,d,c,i.flatMap(a=>a.actions),f,void 0),d,c,i,g,f,h,e);let l=[...g];"done"===k.status&&(k=ar(k,d,c,l.sort((a,b)=>b.order-a.order).flatMap(a=>a.exit),f,void 0));try{if(h===b.historyValue&&function(a,b){if(a.length!==b.size)return!1;for(let c of a)if(!b.has(c))return!1;return!0}(b._nodes,g))return k;return aC(k,{_nodes:l,historyValue:h})}catch(a){throw a}}function ap(a,b,c,d){var e,f,g,h;if(ad(a))if(b[a.id]){let g=b[a.id];for(let a of g)d.add(a),ap(a,b,c,d);for(let h of g){e=h,f=a.parent,aq(d,b,c,T(e,f))}}else{let e=ac(a);for(let f of e.target)d.add(f),e===a.parent?.initial&&c.add(a.parent),ap(f,b,c,d);for(let f of e.target){g=f,h=a.parent,aq(d,b,c,T(g,h))}}else if("compound"===a.type){let[e]=a.initial.target;ad(e)||(d.add(e),c.add(e)),ap(e,b,c,d),aq(d,b,c,T(e,a))}else if("parallel"===a.type)for(let e of S(a).filter(a=>!ad(a)))[...d].some(a=>aj(a,e))||(ad(e)||(d.add(e),c.add(e)),ap(e,b,c,d))}function aq(a,b,c,d,e){for(let f of d)if((!e||aj(f,e))&&a.add(f),"parallel"===f.type)for(let d of S(f).filter(a=>!ad(a)))[...a].some(a=>aj(a,d))||(a.add(d),ap(d,b,c,a))}function ar(a,b,c,d,e,f){let g=f?[]:void 0,h=function a(b,c,d,e,f,g){let{machine:h}=b,i=b;for(let b of e){var j;let e="function"==typeof b,k=e?b:(j="string"==typeof b?b:b.type,h.implementations.actions[j]),l={context:i.context,event:c,self:d.self,system:d.system},m=e||"string"==typeof b?void 0:"params"in b?"function"==typeof b.params?b.params({context:i.context,event:c}):b.params:void 0;if(!k||!("resolve"in k)){d.actionExecutor({type:"string"==typeof b?b:"object"==typeof b?b.type:b.name||"(anonymous)",info:l,params:m,exec:k});continue}let[n,o,p]=k.resolve(d,i,l,m,k,f);i=n,"retryResolve"in k&&g?.push([k,o]),"execute"in k&&d.actionExecutor({type:k.type,info:l,params:o,exec:k.execute.bind(null,d,o)}),p&&(i=a(i,c,d,p,f,g))}return i}(a,b,c,d,{internalQueue:e,deferredActorIds:f},g);return g?.forEach(([a,b])=>{a.retryResolve(c,h,b)}),h}function as(a,b,c,d){let e=a,f=[];function h(a,b,d){c.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:c.self,event:b,snapshot:a,_transitions:d}),f.push(a)}if(b.type===i)return h(e=aC(at(e,b,c),{status:"stopped"}),b,[]),{snapshot:e,microstates:f};let j=b;if(j.type!==g){let b=j,g=b.type.startsWith("xstate.error.actor"),i=au(b,e);if(g&&!i.length)return h(e=aC(a,{status:"error",error:b.error}),b,[]),{snapshot:e,microstates:f};h(e=ao(i,a,c,j,!1,d),b,i)}let k=!0;for(;"active"===e.status;){let a=k?function(a,b){let c=new Set;for(let d of a._nodes.filter(R))a:for(let e of[d].concat(T(d,void 0)))if(e.always){for(let d of e.always)if(void 0===d.guard||Q(d.guard,a.context,b,a)){c.add(d);break a}}return ak(Array.from(c),new Set(a._nodes),a.historyValue)}(e,j):[],b=a.length?e:void 0;if(!a.length){if(!d.length)break;a=au(j=d.shift(),e)}k=(e=ao(a,e,c,j,!1,d))!==b,h(e,j,a)}return"active"!==e.status&&at(e,j,c),{snapshot:e,microstates:f}}function at(a,b,c){return ar(a,b,c,Object.values(a.children).map(a=>P(a)),[],void 0)}function au(a,b){return b.machine.getTransitionData(b,a)}function av(a,b){let c=U(ai(a,b));return W(a,[...c])}let aw=function(a){return function a(b,c){let d=p(b),e=p(c);return"string"==typeof e?"string"==typeof d&&e===d:"string"==typeof d?d in e:Object.keys(d).every(b=>b in e&&a(d[b],e[b]))}(a,this.value)},ax=function(a){return this.tags.has(a)},ay=function(a){let b=this.machine.getTransitionData(this,a);return!!b?.length&&b.some(a=>void 0!==a.target||a.actions.length)},az=function(){let{_nodes:a,tags:b,machine:c,getMeta:d,toJSON:e,can:f,hasTag:g,matches:h,...i}=this;return{...i,tags:Array.from(b)}},aA=function(){return this._nodes.reduce((a,b)=>(void 0!==b.meta&&(a[b.id]=b.meta),a),{})};function aB(a,b){return{status:a.status,output:a.output,error:a.error,machine:b,context:a.context,_nodes:a._nodes,value:W(b.root,a._nodes),tags:new Set(a._nodes.flatMap(a=>a.tags)),children:a.children,historyValue:a.historyValue||{},matches:aw,hasTag:ax,can:ay,getMeta:aA,toJSON:az}}function aC(a,b={}){return aB({...a,...b},a.machine)}function aD(a,b){let{_nodes:c,tags:d,machine:e,children:f,context:g,can:h,hasTag:i,matches:j,getMeta:k,toJSON:l,...m}=a,n={};for(let a in f){let c=f[a];n[a]={snapshot:c.getPersistedSnapshot(b),src:c.src,systemId:c._systemId,syncSnapshot:c._syncSnapshot}}return{...m,context:function a(b){let c;for(let d in b){let e=b[d];if(e&&"object"==typeof e)if("sessionId"in e&&"send"in e&&"ref"in e)(c??=Array.isArray(b)?b.slice():{...b})[d]={xstate$$type:C,id:e.id};else{let f=a(e);f!==e&&((c??=Array.isArray(b)?b.slice():{...b})[d]=f)}}return c??b}(g),children:n,historyValue:function(a){if("object"!=typeof a||null===a)return{};let b={};for(let c in a){let d=a[c];Array.isArray(d)&&(b[c]=d.map(a=>({id:a.id})))}return b}(m.historyValue)}}function aE(a,b,c,d,{event:e,id:f,delay:g},{internalQueue:h}){let i,j=b.machine.implementations.delays;if("string"==typeof e)throw Error(`Only event objects may be used with raise; use raise({ type: "${e}" }) instead`);let k="function"==typeof e?e(c,d):e;if("string"==typeof g){let a=j&&j[g];i="function"==typeof a?a(c,d):a}else i="function"==typeof g?g(c,d):g;return"number"!=typeof i&&h.push(k),[b,{event:k,id:f,delay:i},void 0]}function aF(a,b){let{event:c,delay:d,id:e}=b;if("number"==typeof d)return void a.defer(()=>{let b=a.self;a.system.scheduler.schedule(b,b,c,d,e)})}function aG(a,b){function c(a,b){}return c.type="xstate.raise",c.event=a,c.id=b?.id,c.delay=b?.delay,c.resolve=aE,c.execute=aF,c}},22286:(a,b,c)=>{c.d(b,{DT:()=>l,mj:()=>m}),c(44856);var d=c(10958),e=c(74103);let f=new WeakMap;function g(a,b,c){let d=f.get(a);return d?b in d||(d[b]=c()):(d={[b]:c()},f.set(a,d)),d[b]}let h={},i=a=>"string"==typeof a?{type:a}:"function"==typeof a?"resolve"in a?{type:a.type}:{type:a.name}:a;class j{constructor(a,b){if(this.config=a,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=b._parent,this.key=b._key,this.machine=b._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(d.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,d.l)(this.config.states,(a,b)=>new j(a,{_parent:this,_key:b,_machine:this.machine})):h,"compound"===this.type&&!this.config.initial)throw Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,d.t)(this.config.entry).slice(),this.exit=(0,d.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,d.t)(a.tags).slice()}_initialize(){this.transitions=(0,d.q)(this),this.config.always&&(this.always=(0,d.u)(this.config.always).map(a=>(0,d.v)(this,d.N,a))),Object.keys(this.states).forEach(a=>{this.states[a]._initialize()})}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(i),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map(a=>`#${a.id}`),source:`#${this.id}`,actions:this.initial.actions.map(i),eventType:null})}:void 0,history:this.history,states:(0,d.l)(this.states,a=>a.definition),on:this.on,transitions:[...this.transitions.values()].flat().map(a=>({...a,actions:a.actions.map(i)})),entry:this.entry.map(i),exit:this.exit.map(i),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return g(this,"invoke",()=>(0,d.t)(this.config.invoke).map((a,b)=>{let{src:c,systemId:e}=a,f=a.id??(0,d.x)(this.id,b),g="string"==typeof c?c:`xstate.invoke.${(0,d.x)(this.id,b)}`;return{...a,src:g,id:f,systemId:e,toJSON(){let{onDone:b,onError:c,...d}=a;return{...d,type:"xstate.invoke",src:g,id:f}}}}))}get on(){return g(this,"on",()=>[...this.transitions].flatMap(([a,b])=>b.map(b=>[a,b])).reduce((a,[b,c])=>(a[b]=a[b]||[],a[b].push(c),a),{}))}get after(){return g(this,"delayedTransitions",()=>(0,d.y)(this))}get initial(){return g(this,"initial",()=>(0,d.z)(this,this.config.initial))}next(a,b){let c,e=b.type,f=[];for(let h of g(this,`candidates-${e}`,()=>(0,d.B)(this,e))){let{guard:g}=h,i=a.context,j=!1;try{j=!g||(0,d.w)(g,i,b,a)}catch(b){let a="string"==typeof g?g:"object"==typeof g?g.type:void 0;throw Error(`Unable to evaluate guard ${a?`'${a}' `:""}in transition for event '${e}' in state node '${this.id}':
${b.message}`)}if(j){f.push(...h.actions),c=h;break}}return c?[c]:void 0}get events(){return g(this,"events",()=>{let{states:a}=this,b=new Set(this.ownEvents);if(a)for(let c of Object.keys(a)){let d=a[c];if(d.states)for(let a of d.events)b.add(`${a}`)}return Array.from(b)})}get ownEvents(){return Array.from(new Set([...this.transitions.keys()].filter(a=>this.transitions.get(a).some(a=>!(!a.target&&!a.actions.length&&!a.reenter)))))}}class k{constructor(a,b){this.config=a,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.id=a.id||"(machine)",this.implementations={actors:b?.actors??{},actions:b?.actions??{},delays:b?.delays??{},guards:b?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new j(a,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(a){let{actions:b,guards:c,actors:d,delays:e}=this.implementations;return new k(this.config,{actions:{...b,...a.actions},guards:{...c,...a.guards},actors:{...d,...a.actors},delays:{...e,...a.delays}})}resolveState(a){let b=(0,d.C)(this.root,a.value),c=(0,d.D)((0,d.g)(this.root,b));return(0,d.E)({_nodes:[...c],context:a.context||{},children:{},status:(0,d.F)(c,this.root)?"done":a.status||"active",output:a.output,error:a.error,historyValue:a.historyValue},this)}transition(a,b,c){return(0,d.G)(a,b,c,[]).snapshot}microstep(a,b,c){return(0,d.G)(a,b,c,[]).microstates}getTransitionData(a,b){return(0,d.H)(this.root,a.value,a,b)||[]}getPreInitialState(a,b,c){let{context:f}=this.config,g=(0,d.E)({context:"function"!=typeof f&&f?f:{},_nodes:[this.root],children:{},status:"active"},this);return"function"==typeof f?(0,d.I)(g,b,a,[(0,e.a)(({spawn:a,event:b,self:c})=>f({spawn:a,input:b.input,self:c}))],c,void 0):g}getInitialSnapshot(a,b){let c=(0,d.J)(b),e=[],f=this.getPreInitialState(a,c,e),g=(0,d.K)([{target:[...(0,d.L)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],f,a,c,!0,e),{snapshot:h}=(0,d.G)(g,c,a,e);return h}start(a){Object.values(a.children).forEach(a=>{"active"===a.getSnapshot().status&&a.start()})}getStateNodeById(a){let b=(0,d.M)(a),c=b.slice(1),e=(0,d.O)(b[0])?b[0].slice(1):b[0],f=this.idMap.get(e);if(!f)throw Error(`Child state node '#${e}' does not exist on machine '${this.id}'`);return(0,d.P)(f,c)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(a,b){return(0,d.Q)(a,b)}restoreSnapshot(a,b){let c={},e=a.children;Object.keys(e).forEach(a=>{let f=e[a],g=f.snapshot,h=f.src,i="string"==typeof h?(0,d.R)(this,h):h;if(!i)return;let j=(0,d.c)(i,{id:a,parent:b.self,syncSnapshot:f.syncSnapshot,snapshot:g,src:h,systemId:f.systemId});c[a]=j});let f=function(a,b){if(!b||"object"!=typeof b)return{};let c={};for(let d in b)for(let e of b[d]){let b=function(a,b){if(b instanceof j)return b;try{return a.machine.getStateNodeById(b.id)}catch{}}(a,e);b&&(c[d]??=[],c[d].push(b))}return c}(this.root,a.historyValue),g=(0,d.E)({...a,children:c,_nodes:Array.from((0,d.D)((0,d.g)(this.root,a.value))),historyValue:f},this),h=new Set;return!function a(b,c){if(!h.has(b))for(let e in h.add(b),b){let f=b[e];if(f&&"object"==typeof f){if("xstate$$type"in f&&f.xstate$$type===d.$){b[e]=c[f.id];continue}a(f,c)}}}(g.context,c),g}}function l(a,b){let c=(0,d.t)(b);if(!c.includes(a.type)){let b=1===c.length?`type "${c[0]}"`:`one of types "${c.join('", "')}"`;throw Error(`Expected event ${JSON.stringify(a)} to have ${b}`)}}function m({schemas:a,actors:b,actions:c,guards:d,delays:e}){return{createMachine:f=>new k({...f,schemas:a},{actors:b,actions:c,guards:d,delays:e})}}},25630:(a,b,c)=>{c.d(b,{V2:()=>s,FS:()=>v,IM:()=>y,Tx:()=>w,Rr:()=>x,vd:()=>u,_K:()=>t,CC:()=>r,RM:()=>B,Uc:()=>A,tP:()=>z});var d=c(55511);let e={randomUUID:d.randomUUID},f=new Uint8Array(256),g=f.length,h=[];for(let a=0;a<256;++a)h.push((a+256).toString(16).slice(1));let i=function(a,b,c){if(e.randomUUID&&!b&&!a)return e.randomUUID();let i=(a=a||{}).random||(a.rng||function(){return g>f.length-16&&((0,d.randomFillSync)(f),g=0),f.slice(g,g+=16)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,b){c=c||0;for(let a=0;a<16;++a)b[c+a]=i[a];return b}return function(a,b=0){return(h[a[b+0]]+h[a[b+1]]+h[a[b+2]]+h[a[b+3]]+"-"+h[a[b+4]]+h[a[b+5]]+"-"+h[a[b+6]]+h[a[b+7]]+"-"+h[a[b+8]]+h[a[b+9]]+"-"+h[a[b+10]]+h[a[b+11]]+h[a[b+12]]+h[a[b+13]]+h[a[b+14]]+h[a[b+15]]).toLowerCase()}(i)};var j=c(44856),k=c(22286),l=c(53662),m=c(74103),n=c(10958),o=c(49849);let p=a=>({context:b})=>{let{count:c,include:d,exclude:e,responseType:f="message.received"}=a;return{count:c,domain:b.domain,from:b.connectTo,include:d?Array.isArray(d)?d:[d]:[],exclude:e?Array.isArray(e)?e:[e]:[],responseType:f,target:b.target,to:b.name}},q=(0,o.defer)(()=>(0,o.fromEvent)(window,"message")),r=a=>(0,j.OX)(({input:b})=>{let c;return q.pipe(a?(0,o.map)(a):(0,o.pipe)(),(0,o.filter)((a=>b=>{let{data:c}=b;return(!a.include.length||a.include.includes(c.type))&&(!a.exclude.length||!a.exclude.includes(c.type))&&c.domain===a.domain&&c.from===a.from&&c.to===a.to&&(!a.target||b.source===a.target)})(b)),(0,o.map)((c=b.responseType,a=>({type:c,message:a}))),b.count?(0,o.pipe)((0,o.bufferCount)(b.count),(0,o.concatMap)(a=>a),(0,o.take)(b.count)):(0,o.pipe)())}),s="sanity/comlink",t="comlink/response",u="comlink/heartbeat",v="comlink/disconnect",w="comlink/handshake/syn",x="comlink/handshake/syn-ack",y="comlink/handshake/ack",z=()=>(0,k.mj)({types:{},actors:{listen:(0,j.OX)(({input:a})=>{let b,c=a.signal?(0,o.fromEvent)(a.signal,"abort").pipe((b=`Request ${a.requestId} aborted`,a=>a.pipe((0,o.take)(1),(0,o.map)(()=>{throw Error(b)})))):o.EMPTY;return(0,o.fromEvent)(window,"message").pipe((0,o.filter)(b=>b.data?.type===t&&b.data?.responseTo===a.requestId&&!!b.source&&a.sources.has(b.source)),(0,o.take)(a.sources.size),(0,o.takeUntil)(c))})},actions:{"send message":({context:a},b)=>{let{sources:c,targetOrigin:d}=a,{message:e}=b;c.forEach(a=>{a.postMessage(e,{targetOrigin:d})})},"on success":(0,l.b)(({context:a})=>a.parentRef,({context:a,self:b})=>(a.response&&a.resolvable?.resolve(a.response),{type:"request.success",requestId:b.id,response:a.response,responseTo:a.responseTo})),"on fail":(0,l.b)(({context:a})=>a.parentRef,({context:a,self:b})=>(a.suppressWarnings||console.warn(`[@sanity/comlink] Received no response to message '${a.type}' on client '${a.from}' (ID: '${a.id}').`),a.resolvable?.reject(Error("No response received")),{type:"request.failed",requestId:b.id})),"on abort":(0,l.b)(({context:a})=>a.parentRef,({context:a,self:b})=>(a.resolvable?.reject(Error("Request aborted")),{type:"request.aborted",requestId:b.id}))},guards:{expectsResponse:({context:a})=>a.expectResponse},delays:{initialTimeout:0,responseTimeout:({context:a})=>a.responseTimeout??3e3}}).createMachine({context:({input:a})=>({channelId:a.channelId,data:a.data,domain:a.domain,expectResponse:a.expectResponse??!1,from:a.from,id:`msg-${i()}`,parentRef:a.parentRef,resolvable:a.resolvable,response:null,responseTimeout:a.responseTimeout,responseTo:a.responseTo,signal:a.signal,sources:a.sources instanceof Set?a.sources:new Set([a.sources]),suppressWarnings:a.suppressWarnings,targetOrigin:a.targetOrigin,to:a.to,type:a.type}),initial:"idle",on:{abort:".aborted"},states:{idle:{after:{initialTimeout:[{target:"sending"}]}},sending:{entry:{type:"send message",params:({context:a})=>{let{channelId:b,data:c,domain:d,from:e,id:f,responseTo:g,to:h,type:i}=a;return{message:{channelId:b,data:c,domain:d,from:e,id:f,to:h,type:i,responseTo:g}}}},always:[{guard:"expectsResponse",target:"awaiting"},"success"]},awaiting:{invoke:{id:"listen for response",src:"listen",input:({context:a})=>({requestId:a.id,sources:a.sources,signal:a.signal}),onError:"aborted"},after:{responseTimeout:"failed"},on:{message:{actions:(0,m.a)({response:({event:a})=>a.data.data,responseTo:({event:a})=>a.data.responseTo}),target:"success"}}},failed:{type:"final",entry:"on fail"},success:{type:"final",entry:"on success"},aborted:{type:"final",entry:"on abort"}},output:({context:a,self:b})=>({requestId:b.id,response:a.response,responseTo:a.responseTo})}),A=((0,j.SP)(({sendBack:a,input:b})=>{let c=()=>{a(b.event)};b.immediate&&c();let d=setInterval(c,b.interval);return()=>{clearInterval(d)}}),()=>(0,k.mj)({types:{},actors:{requestMachine:z(),listen:r()},actions:{"buffer incoming message":(0,m.a)({handshakeBuffer:({event:a,context:b})=>((0,k.DT)(a,"message.received"),[...b.handshakeBuffer,a])}),"buffer message":(0,l.a)(({enqueue:a})=>{a.assign({buffer:({event:a,context:b})=>((0,k.DT)(a,"post"),[...b.buffer,{data:a.data,resolvable:a.resolvable,options:a.options}])}),a.emit(({event:a})=>((0,k.DT)(a,"post"),{type:"_buffer.added",message:a.data}))}),"create request":(0,m.a)({requests:({context:a,event:b,self:c,spawn:d})=>{(0,k.DT)(b,"request");let e=(Array.isArray(b.data)?b.data:[b.data]).map(b=>d("requestMachine",{id:`req-${i()}`,input:{channelId:a.channelId,data:b.data,domain:a.domain,expectResponse:b.expectResponse,from:a.name,parentRef:c,resolvable:b.resolvable,responseTimeout:b.options?.responseTimeout,responseTo:b.responseTo,signal:b.options?.signal,sources:a.target,suppressWarnings:b.options?.suppressWarnings,targetOrigin:a.targetOrigin,to:a.connectTo,type:b.type}}));return[...a.requests,...e]}}),"emit heartbeat":(0,l.e)(()=>({type:"_heartbeat"})),"emit received message":(0,l.a)(({enqueue:a})=>{a.emit(({event:a})=>((0,k.DT)(a,"message.received"),{type:"_message",message:a.message.data})),a.emit(({event:a})=>((0,k.DT)(a,"message.received"),{type:a.message.data.type,message:a.message.data}))}),"emit status":(0,l.e)((a,b)=>({type:"_status",status:b.status})),"flush buffer":(0,l.a)(({enqueue:a})=>{a.raise(({context:a})=>({type:"request",data:a.buffer.map(({data:a,resolvable:b,options:c})=>({data:a.data,type:a.type,expectResponse:!!b,resolvable:b,options:c}))})),a.emit(({context:a})=>({type:"_buffer.flushed",messages:a.buffer.map(({data:a})=>a)})),a.assign({buffer:[]})}),"flush handshake buffer":(0,l.a)(({context:a,enqueue:b})=>{a.handshakeBuffer.forEach(a=>b.raise(a)),b.assign({handshakeBuffer:[]})}),post:(0,n.r)(({event:a})=>((0,k.DT)(a,"post"),{type:"request",data:{data:a.data.data,expectResponse:!!a.resolvable,type:a.data.type,resolvable:a.resolvable,options:a.options}})),"remove request":(0,l.a)(({context:a,enqueue:b,event:c})=>{(0,k.DT)(c,["request.success","request.failed","request.aborted"]),(0,n.k)(c.requestId),b.assign({requests:a.requests.filter(({id:a})=>a!==c.requestId)})}),"send response":(0,n.r)(({event:a})=>((0,k.DT)(a,["message.received","heartbeat.received"]),{type:"request",data:{type:t,responseTo:a.message.data.id,data:void 0}})),"send handshake syn ack":(0,n.r)({type:"request",data:{type:x}}),"set connection config":(0,m.a)({channelId:({event:a})=>((0,k.DT)(a,"handshake.syn"),a.message.data.channelId),target:({event:a})=>((0,k.DT)(a,"handshake.syn"),a.message.source||void 0),targetOrigin:({event:a})=>((0,k.DT)(a,"handshake.syn"),a.message.origin)})},guards:{hasSource:({context:a})=>null!==a.target}}).createMachine({id:"node",context:({input:a})=>({buffer:[],channelId:null,connectTo:a.connectTo,domain:a.domain??s,handshakeBuffer:[],name:a.name,requests:[],target:void 0,targetOrigin:null}),invoke:{id:"listen for handshake syn",src:"listen",input:p({include:w,responseType:"handshake.syn"})},on:{"request.success":{actions:"remove request"},"request.failed":{actions:"remove request"},"request.aborted":{actions:"remove request"},"handshake.syn":{actions:"set connection config",target:".handshaking"}},initial:"idle",states:{idle:{entry:[{type:"emit status",params:{status:"idle"}}],on:{post:{actions:"buffer message"}}},handshaking:{guard:"hasSource",entry:["send handshake syn ack",{type:"emit status",params:{status:"handshaking"}}],invoke:[{id:"listen for handshake ack",src:"listen",input:p({include:y,count:1,responseType:"handshake.complete"}),onDone:"connected"},{id:"listen for disconnect",src:"listen",input:p({include:v,count:1,responseType:"disconnect"})},{id:"listen for messages",src:"listen",input:p({exclude:[v,w,y,u,t]})}],on:{request:{actions:"create request"},post:{actions:"buffer message"},"message.received":{actions:"buffer incoming message"},disconnect:{target:"idle"}}},connected:{entry:["flush handshake buffer","flush buffer",{type:"emit status",params:{status:"connected"}}],invoke:[{id:"listen for messages",src:"listen",input:p({exclude:[v,w,y,u,t]})},{id:"listen for heartbeat",src:"listen",input:p({include:u,responseType:"heartbeat.received"})},{id:"listen for disconnect",src:"listen",input:p({include:v,count:1,responseType:"disconnect"})}],on:{request:{actions:"create request"},post:{actions:"post"},disconnect:{target:"idle"},"message.received":{actions:["send response","emit received message"]},"heartbeat.received":{actions:["send response","emit heartbeat"]}}}}})),B=(a,b=A())=>{let c,d=(0,n.c)(b,{input:a}),e=()=>{d.stop()};return{actor:d,fetch:(a,b,c)=>{let{responseTimeout:e=1e4,signal:f,suppressWarnings:g}=c||{},h=Promise.withResolvers();return d.send({type:"post",data:{type:a,data:b},resolvable:h,options:{responseTimeout:e,signal:f,suppressWarnings:g}}),h.promise},machine:b,on:(a,b)=>{let{unsubscribe:c}=d.on(a,a=>{b(a.message.data)});return c},onStatus:(a,b)=>{let{unsubscribe:e}=d.on("_status",d=>{c=d.status,b&&d.status!==b||a(d.status)});return c&&a(c),e},post:(a,b)=>{d.send({type:"post",data:{type:a,data:b}})},start:()=>(d.start(),e),stop:e}}},44856:(a,b,c)=>{c.d(b,{H6:()=>o,OX:()=>i,SP:()=>f,Sx:()=>m});var d=c(10958);let e=new WeakMap;function f(a){return{config:a,start:(b,c)=>{let{self:d,system:f,emit:g}=c,h={receivers:void 0,dispose:void 0};e.set(d,h),h.dispose=a({input:b.input,system:f,self:d,sendBack:a=>{"stopped"!==d.getSnapshot().status&&d._parent&&f._relay(d,d._parent,a)},receive:a=>{h.receivers??=new Set,h.receivers.add(a)},emit:g})},transition:(a,b,c)=>{let f=e.get(c.self);return b.type===d.X?(a={...a,status:"stopped",error:void 0},f.dispose?.()):f.receivers?.forEach(a=>a(b)),a},getInitialSnapshot:(a,b)=>({status:"active",output:void 0,error:void 0,input:b}),getPersistedSnapshot:a=>a,restoreSnapshot:a=>a}}let g="xstate.observable.error",h="xstate.observable.complete";function i(a){return{config:a,transition:(a,b)=>{if("active"!==a.status)return a;switch(b.type){case g:return{...a,status:"error",error:b.data,input:void 0,_subscription:void 0};case h:return{...a,status:"done",input:void 0,_subscription:void 0};case d.X:return a._subscription.unsubscribe(),{...a,status:"stopped",input:void 0,_subscription:void 0};default:return a}},getInitialSnapshot:(a,b)=>({status:"active",output:void 0,error:void 0,context:void 0,input:b,_subscription:void 0}),start:(b,{self:c,system:d,emit:e})=>{"done"!==b.status&&(b._subscription=a({input:b.input,system:d,self:c,emit:e}).subscribe({next:a=>{c._parent&&d._relay(c,c._parent,a)},error:a=>{d._relay(c,c,{type:g,data:a})},complete:()=>{d._relay(c,c,{type:h})}}))},getPersistedSnapshot:({_subscription:a,...b})=>b,restoreSnapshot:a=>({...a,_subscription:void 0})}}let j="xstate.promise.resolve",k="xstate.promise.reject",l=new WeakMap;function m(a){return{config:a,transition:(a,b,c)=>{if("active"!==a.status)return a;switch(b.type){case j:{let c=b.data;return{...a,status:"done",output:c,input:void 0}}case k:return{...a,status:"error",error:b.data,input:void 0};case d.X:return l.get(c.self)?.abort(),{...a,status:"stopped",input:void 0};default:return a}},start:(b,{self:c,system:d,emit:e})=>{if("active"!==b.status)return;let f=new AbortController;l.set(c,f),Promise.resolve(a({input:b.input,system:d,self:c,signal:f.signal,emit:e})).then(a=>{"active"===c.getSnapshot().status&&(l.delete(c),d._relay(c,c,{type:j,data:a}))},a=>{"active"===c.getSnapshot().status&&(l.delete(c),d._relay(c,c,{type:k,data:a}))})},getInitialSnapshot:(a,b)=>({status:"active",output:void 0,error:void 0,input:b}),getPersistedSnapshot:a=>a,restoreSnapshot:a=>a}}let n=function(a,b){return{config:a,transition:(b,c,d)=>({...b,context:a(b.context,c,d)}),getInitialSnapshot:(a,c)=>({status:"active",output:void 0,error:void 0,context:"function"==typeof b?b({input:c}):b}),getPersistedSnapshot:a=>a,restoreSnapshot:a=>a}}(a=>void 0,void 0);function o(){return(0,d.c)(n)}},53662:(a,b,c)=>{c.d(b,{a:()=>p,b:()=>m,e:()=>h,s:()=>n});var d=c(10958),e=c(74103);function f(a,b,c,d,{event:e}){return[b,{event:"function"==typeof e?e(c,d):e},void 0]}function g(a,{event:b}){a.defer(()=>a.emit(b))}function h(a){function b(a,b){}return b.type="xstate.emit",b.event=a,b.resolve=f,b.execute=g,b}let i=function(a){return a.Parent="#_parent",a.Internal="#_internal",a}({});function j(a,b,c,d,{to:e,event:f,id:g,delay:h},j){let k,l,m=b.machine.implementations.delays;if("string"==typeof f)throw Error(`Only event objects may be used with sendTo; use sendTo({ type: "${f}" }) instead`);let n="function"==typeof f?f(c,d):f;if("string"==typeof h){let a=m&&m[h];k="function"==typeof a?a(c,d):a}else k="function"==typeof h?h(c,d):h;let o="function"==typeof e?e(c,d):e;if("string"==typeof o){if(!(l=o===i.Parent?a.self._parent:o===i.Internal?a.self:o.startsWith("#_")?b.children[o.slice(2)]:j.deferredActorIds?.includes(o)?o:b.children[o]))throw Error(`Unable to send event to actor '${o}' from machine '${b.machine.id}'.`)}else l=o||a.self;return[b,{to:l,targetId:"string"==typeof o?o:void 0,event:n,id:g,delay:k},void 0]}function k(a,b,c){"string"==typeof c.to&&(c.to=b.children[c.to])}function l(a,b){a.defer(()=>{let{to:c,event:e,delay:f,id:g}=b;if("number"==typeof f)return void a.system.scheduler.schedule(a.self,c,e,f,g);a.system._relay(a.self,c,e.type===d.T?(0,d.U)(a.self.id,e.data):e)})}function m(a,b,c){function d(a,b){}return d.type="xstate.sendTo",d.to=a,d.event=b,d.id=c?.id,d.delay=c?.delay,d.resolve=j,d.retryResolve=k,d.execute=l,d}function n(a,b){return m(i.Parent,a,b)}function o(a,b,c,f,{collect:g}){let i=[],j=function(a){i.push(a)};return j.assign=(...a)=>{i.push((0,e.a)(...a))},j.cancel=(...a)=>{i.push((0,d.f)(...a))},j.raise=(...a)=>{i.push((0,d.r)(...a))},j.sendTo=(...a)=>{i.push(m(...a))},j.sendParent=(...a)=>{i.push(n(...a))},j.spawnChild=(...a)=>{i.push((0,d.h)(...a))},j.stopChild=(...a)=>{i.push((0,d.k)(...a))},j.emit=(...a)=>{i.push(h(...a))},g({context:c.context,event:c.event,enqueue:j,check:a=>(0,d.w)(a,b.context,c.event,b),self:a.self,system:a.system},f),[b,void 0,i]}function p(a){function b(a,b){}return b.type="xstate.enqueueActions",b.collect=a,b.resolve=o,b}},74103:(a,b,c)=>{c.d(b,{a:()=>f});var d=c(10958);function e(a,b,c,e,{assignment:f}){if(!b.context)throw Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");let g={},h={context:b.context,event:c.event,spawn:function(a,{machine:b,context:c},e,f){return(g,h)=>{let i=((g,h)=>{if("string"!=typeof g)return(0,d.c)(g,{id:h?.id,parent:a.self,syncSnapshot:h?.syncSnapshot,input:h?.input,src:g,systemId:h?.systemId});{let i=(0,d.R)(b,g);if(!i)throw Error(`Actor logic '${g}' not implemented in machine '${b.id}'`);let j=(0,d.c)(i,{id:h?.id,parent:a.self,syncSnapshot:h?.syncSnapshot,input:"function"==typeof h?.input?h.input({context:c,event:e,self:a.self}):h?.input,src:g,systemId:h?.systemId});return f[j.id]=j,j}})(g,h);return f[i.id]=i,a.defer(()=>{i._processingStatus!==d.V.Stopped&&i.start()}),i}}(a,b,c.event,g),self:a.self,system:a.system},i={};if("function"==typeof f)i=f(h,e);else for(let a of Object.keys(f)){let b=f[a];i[a]="function"==typeof b?b(h,e):b}let j=Object.assign({},b.context,i);return[(0,d.W)(b,{context:j,children:Object.keys(g).length?{...b.children,...g}:b.children}),void 0,void 0]}function f(a){function b(a,b){}return b.type="xstate.assign",b.assignment=a,b.resolve=e,b}}};