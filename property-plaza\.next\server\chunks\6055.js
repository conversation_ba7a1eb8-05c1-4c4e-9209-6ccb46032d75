exports.id=6055,exports.ids=[6055],exports.modules={4525:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{ImageResponse:function(){return d.ImageResponse},NextRequest:function(){return e.NextRequest},NextResponse:function(){return f.NextResponse},URLPattern:function(){return h.URLPattern},after:function(){return i.after},connection:function(){return j.connection},unstable_rootParams:function(){return k.unstable_rootParams},userAgent:function(){return g.userAgent},userAgentFromString:function(){return g.userAgentFromString}});let d=c(42174),e=c(76268),f=c(93426),g=c(53182),h=c(11243),i=c(93381),j=c(12944),k=c(72079)},11243:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"URLPattern",{enumerable:!0,get:function(){return c}});let c="undefined"==typeof URLPattern?void 0:URLPattern},12944:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"connection",{enumerable:!0,get:function(){return j}});let d=c(29294),e=c(63033),f=c(84971),g=c(80023),h=c(68388),i=c(8719);function j(){let a=d.workAsyncStorage.getStore(),b=e.workUnitAsyncStorage.getStore();if(a){if(b&&"after"===b.phase&&!(0,i.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside "after(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but "after(...)" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E186",enumerable:!1,configurable:!0});if(a.forceStatic)return Promise.resolve(void 0);if(b){if("cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside "use cache". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E111",enumerable:!1,configurable:!0});else if("unstable-cache"===b.type)throw Object.defineProperty(Error(`Route ${a.route} used "connection" inside a function cached with "unstable_cache(...)". The \`connection()\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E1",enumerable:!1,configurable:!0})}if(a.dynamicShouldError)throw Object.defineProperty(new g.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`connection\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E562",enumerable:!1,configurable:!0});if(b)if("prerender"===b.type||"prerender-client"===b.type)return(0,h.makeHangingPromise)(b.renderSignal,"`connection()`");else"prerender-ppr"===b.type?(0,f.postponeWithTracking)(a.route,"connection",b.dynamicTracking):"prerender-legacy"===b.type&&(0,f.throwToInterruptStaticGeneration)("connection",a,b);(0,f.trackDynamicDataInDynamicRender)(a,b)}return Promise.resolve(void 0)}},14871:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"after",{enumerable:!0,get:function(){return e}});let d=c(29294);function e(a){let b=d.workAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error("`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context"),"__NEXT_ERROR_CODE",{value:"E468",enumerable:!1,configurable:!0});let{afterContext:c}=b;return c.after(a)}},32190:(a,b,c)=>{"use strict";var d=c(4525);c.o(d,"NextResponse")&&c.d(b,{NextResponse:function(){return d.NextResponse}})},42174:(a,b)=>{"use strict";function c(){throw Object.defineProperty(Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead'),"__NEXT_ERROR_CODE",{value:"E183",enumerable:!1,configurable:!0})}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"ImageResponse",{enumerable:!0,get:function(){return c}})},53182:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{isBot:function(){return e},userAgent:function(){return g},userAgentFromString:function(){return f}});let d=function(a){return a&&a.__esModule?a:{default:a}}(c(70397));function e(a){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(a)}function f(a){return{...(0,d.default)(a),isBot:void 0!==a&&e(a)}}function g({headers:a}){return f(a.get("user-agent")||void 0)}},66946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(30898),e=c(42471),f=c(47912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},70397:(a,b,c)=>{var d;(()=>{var e={226:function(e,f){!function(g,h){"use strict";var i="function",j="undefined",k="object",l="string",m="major",n="model",o="name",p="type",q="vendor",r="version",s="architecture",t="console",u="mobile",v="tablet",w="smarttv",x="wearable",y="embedded",z="Amazon",A="Apple",B="ASUS",C="BlackBerry",D="Browser",E="Chrome",F="Firefox",G="Google",H="Huawei",I="Microsoft",J="Motorola",K="Opera",L="Samsung",M="Sharp",N="Sony",O="Xiaomi",P="Zebra",Q="Facebook",R="Chromium OS",S="Mac OS",T=function(a,b){var c={};for(var d in a)b[d]&&b[d].length%2==0?c[d]=b[d].concat(a[d]):c[d]=a[d];return c},U=function(a){for(var b={},c=0;c<a.length;c++)b[a[c].toUpperCase()]=a[c];return b},V=function(a,b){return typeof a===l&&-1!==W(b).indexOf(W(a))},W=function(a){return a.toLowerCase()},X=function(a,b){if(typeof a===l)return a=a.replace(/^\s\s*/,""),typeof b===j?a:a.substring(0,350)},Y=function(a,b){for(var c,d,e,f,g,j,l=0;l<b.length&&!g;){var m=b[l],n=b[l+1];for(c=d=0;c<m.length&&!g&&m[c];)if(g=m[c++].exec(a))for(e=0;e<n.length;e++)j=g[++d],typeof(f=n[e])===k&&f.length>0?2===f.length?typeof f[1]==i?this[f[0]]=f[1].call(this,j):this[f[0]]=f[1]:3===f.length?typeof f[1]!==i||f[1].exec&&f[1].test?this[f[0]]=j?j.replace(f[1],f[2]):void 0:this[f[0]]=j?f[1].call(this,j,f[2]):void 0:4===f.length&&(this[f[0]]=j?f[3].call(this,j.replace(f[1],f[2])):h):this[f]=j||h;l+=2}},Z=function(a,b){for(var c in b)if(typeof b[c]===k&&b[c].length>0){for(var d=0;d<b[c].length;d++)if(V(b[c][d],a))return"?"===c?h:c}else if(V(b[c],a))return"?"===c?h:c;return a},$={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},_={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,r],[/opios[\/ ]+([\w\.]+)/i],[r,[o,K+" Mini"]],[/\bopr\/([\w\.]+)/i],[r,[o,K]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[o,r],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[r,[o,"UC"+D]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[r,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[r,[o,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[r,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure "+D],r],[/\bfocus\/([\w\.]+)/i],[r,[o,F+" Focus"]],[/\bopt\/([\w\.]+)/i],[r,[o,K+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[r,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[r,[o,K+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[r,[o,"MIUI "+D]],[/fxios\/([-\w\.]+)/i],[r,[o,F]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 "+D]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 "+D],r],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],r],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,r],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,Q],r],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,r],[/\bgsa\/([\w\.]+) .*safari\//i],[r,[o,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[r,[o,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[r,[o,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,E+" WebView"],r],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[r,[o,"Android "+D]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,r],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[r,[o,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[r,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[r,Z,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,r],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],r],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[r,[o,F+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[o,r],[/(cobalt)\/([\w\.]+)/i],[o,[r,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[s,"amd64"]],[/(ia32(?=;))/i],[[s,W]],[/((?:i[346]|x)86)[;\)]/i],[[s,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[s,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[s,"armhf"]],[/windows (ce|mobile); ppc;/i],[[s,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[s,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[s,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[s,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[n,[q,L],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[n,[q,L],[p,u]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[n,[q,A],[p,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[n,[q,A],[p,v]],[/(macintosh);/i],[n,[q,A]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[n,[q,M],[p,u]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[n,[q,H],[p,v]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[n,[q,H],[p,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[n,/_/g," "],[q,O],[p,v]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[n,[q,"OPPO"],[p,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[n,[q,"Vivo"],[p,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[n,[q,"Realme"],[p,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[n,[q,J],[p,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[n,[q,J],[p,v]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[n,[q,"LG"],[p,v]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[n,[q,"LG"],[p,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[n,[q,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[n,/_/g," "],[q,"Nokia"],[p,u]],[/(pixel c)\b/i],[n,[q,G],[p,v]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[n,[q,G],[p,u]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[n,[q,N],[p,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[n,"Xperia Tablet"],[q,N],[p,v]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[n,[q,"OnePlus"],[p,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[n,[q,z],[p,v]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[n,/(.+)/g,"Fire Phone $1"],[q,z],[p,u]],[/(playbook);[-\w\),; ]+(rim)/i],[n,q,[p,v]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[n,[q,C],[p,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[n,[q,B],[p,v]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[n,[q,B],[p,u]],[/(nexus 9)/i],[n,[q,"HTC"],[p,v]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[q,[n,/_/g," "],[p,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[n,[q,"Acer"],[p,v]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[n,[q,"Meizu"],[p,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[q,n,[p,u]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[q,n,[p,v]],[/(surface duo)/i],[n,[q,I],[p,v]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[n,[q,"Fairphone"],[p,u]],[/(u304aa)/i],[n,[q,"AT&T"],[p,u]],[/\bsie-(\w*)/i],[n,[q,"Siemens"],[p,u]],[/\b(rct\w+) b/i],[n,[q,"RCA"],[p,v]],[/\b(venue[\d ]{2,7}) b/i],[n,[q,"Dell"],[p,v]],[/\b(q(?:mv|ta)\w+) b/i],[n,[q,"Verizon"],[p,v]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[n,[q,"Barnes & Noble"],[p,v]],[/\b(tm\d{3}\w+) b/i],[n,[q,"NuVision"],[p,v]],[/\b(k88) b/i],[n,[q,"ZTE"],[p,v]],[/\b(nx\d{3}j) b/i],[n,[q,"ZTE"],[p,u]],[/\b(gen\d{3}) b.+49h/i],[n,[q,"Swiss"],[p,u]],[/\b(zur\d{3}) b/i],[n,[q,"Swiss"],[p,v]],[/\b((zeki)?tb.*\b) b/i],[n,[q,"Zeki"],[p,v]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[q,"Dragon Touch"],n,[p,v]],[/\b(ns-?\w{0,9}) b/i],[n,[q,"Insignia"],[p,v]],[/\b((nxa|next)-?\w{0,9}) b/i],[n,[q,"NextBook"],[p,v]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[q,"Voice"],n,[p,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[q,"LvTel"],n,[p,u]],[/\b(ph-1) /i],[n,[q,"Essential"],[p,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[n,[q,"Envizen"],[p,v]],[/\b(trio[-\w\. ]+) b/i],[n,[q,"MachSpeed"],[p,v]],[/\btu_(1491) b/i],[n,[q,"Rotor"],[p,v]],[/(shield[\w ]+) b/i],[n,[q,"Nvidia"],[p,v]],[/(sprint) (\w+)/i],[q,n,[p,u]],[/(kin\.[onetw]{3})/i],[[n,/\./g," "],[q,I],[p,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[n,[q,P],[p,v]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[n,[q,P],[p,u]],[/smart-tv.+(samsung)/i],[q,[p,w]],[/hbbtv.+maple;(\d+)/i],[[n,/^/,"SmartTV"],[q,L],[p,w]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[q,"LG"],[p,w]],[/(apple) ?tv/i],[q,[n,A+" TV"],[p,w]],[/crkey/i],[[n,E+"cast"],[q,G],[p,w]],[/droid.+aft(\w)( bui|\))/i],[n,[q,z],[p,w]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[n,[q,M],[p,w]],[/(bravia[\w ]+)( bui|\))/i],[n,[q,N],[p,w]],[/(mitv-\w{5}) bui/i],[n,[q,O],[p,w]],[/Hbbtv.*(technisat) (.*);/i],[q,n,[p,w]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[q,X],[n,X],[p,w]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[p,w]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[q,n,[p,t]],[/droid.+; (shield) bui/i],[n,[q,"Nvidia"],[p,t]],[/(playstation [345portablevi]+)/i],[n,[q,N],[p,t]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[n,[q,I],[p,t]],[/((pebble))app/i],[q,n,[p,x]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[n,[q,A],[p,x]],[/droid.+; (glass) \d/i],[n,[q,G],[p,x]],[/droid.+; (wt63?0{2,3})\)/i],[n,[q,P],[p,x]],[/(quest( 2| pro)?)/i],[n,[q,Q],[p,x]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[q,[p,y]],[/(aeobc)\b/i],[n,[q,z],[p,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[n,[p,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[n,[p,v]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[p,v]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[p,u]],[/(android[-\w\. ]{0,9});.+buil/i],[n,[q,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[r,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[o,r],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,r],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[r,Z,$]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[r,Z,$]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[r,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,S],[r,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,r],[/\(bb(10);/i],[r,[o,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[r,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[r,[o,F+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r,[o,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[r,[o,"watchOS"]],[/crkey\/([\d\.]+)/i],[r,[o,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[o,R],r],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,r],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],r],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[o,r]]},aa=function(a,b){if(typeof a===k&&(b=a,a=h),!(this instanceof aa))return new aa(a,b).getResult();var c=typeof g!==j&&g.navigator?g.navigator:h,d=a||(c&&c.userAgent?c.userAgent:""),e=c&&c.userAgentData?c.userAgentData:h,f=b?T(_,b):_,t=c&&c.userAgent==d;return this.getBrowser=function(){var a,b={};return b[o]=h,b[r]=h,Y.call(b,d,f.browser),b[m]=typeof(a=b[r])===l?a.replace(/[^\d\.]/g,"").split(".")[0]:h,t&&c&&c.brave&&typeof c.brave.isBrave==i&&(b[o]="Brave"),b},this.getCPU=function(){var a={};return a[s]=h,Y.call(a,d,f.cpu),a},this.getDevice=function(){var a={};return a[q]=h,a[n]=h,a[p]=h,Y.call(a,d,f.device),t&&!a[p]&&e&&e.mobile&&(a[p]=u),t&&"Macintosh"==a[n]&&c&&typeof c.standalone!==j&&c.maxTouchPoints&&c.maxTouchPoints>2&&(a[n]="iPad",a[p]=v),a},this.getEngine=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.engine),a},this.getOS=function(){var a={};return a[o]=h,a[r]=h,Y.call(a,d,f.os),t&&!a[o]&&e&&"Unknown"!=e.platform&&(a[o]=e.platform.replace(/chrome os/i,R).replace(/macos/i,S)),a},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return d},this.setUA=function(a){return d=typeof a===l&&a.length>350?X(a,350):a,this},this.setUA(d),this};aa.VERSION="1.0.35",aa.BROWSER=U([o,r,m]),aa.CPU=U([s]),aa.DEVICE=U([n,q,p,t,u,w,v,x,y]),aa.ENGINE=aa.OS=U([o,r]),typeof f!==j?(e.exports&&(f=e.exports=aa),f.UAParser=aa):c.amdO?void 0===(d=(function(){return aa}).call(b,c,b,a))||(a.exports=d):typeof g!==j&&(g.UAParser=aa);var ab=typeof g!==j&&(g.jQuery||g.Zepto);if(ab&&!ab.ua){var ac=new aa;ab.ua=ac.getResult(),ab.ua.get=function(){return ac.getUA()},ab.ua.set=function(a){ac.setUA(a);var b=ac.getResult();for(var c in b)ab.ua[c]=b[c]}}}("object"==typeof window?window:this)}},f={};function g(a){var b=f[a];if(void 0!==b)return b.exports;var c=f[a]={exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,g),d=!1}finally{d&&delete f[a]}return c.exports}g.ab=__dirname+"/",a.exports=g(226)})()},72079:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"unstable_rootParams",{enumerable:!0,get:function(){return k}});let d=c(71617),e=c(84971),f=c(29294),g=c(63033),h=c(68388),i=c(72609),j=new WeakMap;async function k(){let a=f.workAsyncStorage.getStore();if(!a)throw Object.defineProperty(new d.InvariantError("Missing workStore in unstable_rootParams"),"__NEXT_ERROR_CODE",{value:"E615",enumerable:!1,configurable:!0});let b=g.workUnitAsyncStorage.getStore();if(!b)throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` in Pages Router. This API is only available within App Router.`),"__NEXT_ERROR_CODE",{value:"E641",enumerable:!1,configurable:!0});switch(b.type){case"unstable-cache":case"cache":throw Object.defineProperty(Error(`Route ${a.route} used \`unstable_rootParams()\` inside \`"use cache"\` or \`unstable_cache\`. Support for this API inside cache scopes is planned for a future version of Next.js.`),"__NEXT_ERROR_CODE",{value:"E642",enumerable:!1,configurable:!0});case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return function(a,b,c){let f=b.fallbackRouteParams;if(f){let n=!1;for(let b in a)if(f.has(b)){n=!0;break}if(n)switch(c.type){case"prerender":let o=j.get(a);if(o)return o;let p=(0,h.makeHangingPromise)(c.renderSignal,"`unstable_rootParams`");return j.set(a,p),p;case"prerender-client":let q="`unstable_rootParams`";throw Object.defineProperty(new d.InvariantError(`${q} must not be used within a client component. Next.js should be preventing ${q} from being included in client components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E693",enumerable:!1,configurable:!0});default:var g=a,k=f,l=b,m=c;let r=j.get(g);if(r)return r;let s={...g},t=Promise.resolve(s);return j.set(g,t),Object.keys(g).forEach(a=>{i.wellKnownProperties.has(a)||(k.has(a)?Object.defineProperty(s,a,{get(){let b=(0,i.describeStringPropertyAccess)("unstable_rootParams",a);"prerender-ppr"===m.type?(0,e.postponeWithTracking)(l.route,b,m.dynamicTracking):(0,e.throwToInterruptStaticGeneration)(b,l,m)},enumerable:!0}):t[a]=g[a])}),t}}return Promise.resolve(a)}(b.rootParams,a,b);default:return Promise.resolve(b.rootParams)}}},93381:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),function(a,b){Object.keys(a).forEach(function(c){"default"===c||Object.prototype.hasOwnProperty.call(b,c)||Object.defineProperty(b,c,{enumerable:!0,get:function(){return a[c]}})})}(c(14871),b)},93426:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"NextResponse",{enumerable:!0,get:function(){return l}});let d=c(23158),e=c(66608),f=c(47912),g=c(43763),h=c(23158),i=Symbol("internal response"),j=new Set([301,302,303,307,308]);function k(a,b){var c;if(null==a||null==(c=a.request)?void 0:c.headers){if(!(a.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let c=[];for(let[d,e]of a.request.headers)b.set("x-middleware-request-"+d,e),c.push(d);b.set("x-middleware-override-headers",c.join(","))}}class l extends Response{constructor(a,b={}){super(a,b);let c=this.headers,j=new Proxy(new h.ResponseCookies(c),{get(a,e,f){switch(e){case"delete":case"set":return(...f)=>{let g=Reflect.apply(a[e],a,f),i=new Headers(c);return g instanceof h.ResponseCookies&&c.set("x-middleware-set-cookie",g.getAll().map(a=>(0,d.stringifyCookie)(a)).join(",")),k(b,i),g};default:return g.ReflectAdapter.get(a,e,f)}}});this[i]={cookies:j,url:b.url?new e.NextURL(b.url,{headers:(0,f.toNodeOutgoingHttpHeaders)(c),nextConfig:b.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[i].cookies}static json(a,b){let c=Response.json(a,b);return new l(c.body,c)}static redirect(a,b){let c="number"==typeof b?b:(null==b?void 0:b.status)??307;if(!j.has(c))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let d="object"==typeof b?b:{},e=new Headers(null==d?void 0:d.headers);return e.set("Location",(0,f.validateURL)(a)),new l(null,{...d,headers:e,status:c})}static rewrite(a,b){let c=new Headers(null==b?void 0:b.headers);return c.set("x-middleware-rewrite",(0,f.validateURL)(a)),k(b,c),new l(null,{...b,headers:c})}static next(a){let b=new Headers(null==a?void 0:a.headers);return b.set("x-middleware-next","1"),k(a,b),new l(null,{...a,headers:b})}}},96559:(a,b,c)=>{"use strict";a.exports=c(44870)}};