"use strict";exports.id=6060,exports.ids=[1687,6060],exports.modules={56060:(a,b,c)=>{c.r(b),c.d(b,{default:()=>n});var d,e,f,g=c(60687),h=c(91687),i=c(43210),j=c(94219),k=c(16577),l=function(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}((f||(f=1,e=function a(b,c){if(b===c)return!0;if(b&&c&&"object"==typeof b&&"object"==typeof c){if(b.constructor!==c.constructor)return!1;if(Array.isArray(b)){if((d=b.length)!=c.length)return!1;for(e=d;0!=e--;)if(!a(b[e],c[e]))return!1;return!0}if(b.constructor===RegExp)return b.source===c.source&&b.flags===c.flags;if(b.valueOf!==Object.prototype.valueOf)return b.valueOf()===c.valueOf();if(b.toString!==Object.prototype.toString)return b.toString()===c.toString();if((d=(f=Object.keys(b)).length)!==Object.keys(c).length)return!1;for(e=d;0!=e--;)if(!Object.prototype.hasOwnProperty.call(c,f[e]))return!1;for(e=d;0!=e--;){var d,e,f,g=f[e];if(!a(b[g],c[g]))return!1}return!0}return b!=b&&c!=c}),e));let m="use"in(d||(d=c.t(i,2)))?i.use:()=>{throw TypeError("SanityLiveStream requires a React version with React.use()")};function n(a){let{query:b,dataset:c,params:d={},perspective:e,projectId:f,stega:n}=a,o=(0,i.useCallback)(a=>(k.Hi.add(a),()=>k.Hi.delete(a)),[]),p=(0,i.useSyncExternalStore)(o,()=>k.sq,()=>null),[q,r]=(0,i.useState)(void 0),s=(0,j.J)(a=>{a.post("loader/query-listen",{projectId:f,dataset:c,perspective:e,query:b,params:d,heartbeat:1e3})}),t=(0,j.J)(e=>{if(l({projectId:f,dataset:c,query:b,params:d},{projectId:e.projectId,dataset:e.dataset,query:e.query,params:e.params})){let{result:b,resultSourceMap:c,tags:d}=e,f=n?(0,h.stegaEncodeSourceMap)(b,c,{enabled:!0,studioUrl:"/"}):b;console.groupCollapsed("rendering with server action"),a.children({data:f,sourceMap:c,tags:d||[]}).then(a=>{console.log("setChildren(children)"),r(a)},a=>{console.error("rendering with server action: render children error",a)}).finally(()=>console.groupEnd())}});return(0,i.useEffect)(()=>{if(!p)return;let a=p.on("loader/query-change",t),b=setInterval(()=>s(p),1e3);return()=>{clearInterval(b),a()}},[p,t,s]),p&&void 0!==q?(0,g.jsx)(g.Fragment,{children:q}):m(a.initial)}n.displayName="SanityLiveStream"},91687:(a,b,c)=>{c.r(b),c.d(b,{encodeIntoResult:()=>l,stegaEncodeSourceMap:()=>q,stegaEncodeSourceMap$1:()=>r});var d=c(27713);let e=/_key\s*==\s*['"](.*)['"]/;function f(a){if(!Array.isArray(a))throw Error("Path is not an array");return a.reduce((a,b,c)=>{let d=typeof b;if("number"===d)return`${a}[${b}]`;if("string"===d)return`${a}${0===c?"":"."}${b}`;if(("string"==typeof b?e.test(b.trim()):"object"==typeof b&&"_key"in b)&&b._key)return`${a}[_key=="${b._key}"]`;if(Array.isArray(b)){let[c,d]=b;return`${a}[${c}:${d}]`}throw Error(`Unsupported path segment \`${JSON.stringify(b)}\``)},"")}let g={"\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","'":"\\'","\\":"\\\\"},h={"\\f":"\f","\\n":`
`,"\\r":"\r","\\t":"	","\\'":"'","\\\\":"\\"};function i(a){let b,c=[],d=/\['(.*?)'\]|\[(\d+)\]|\[\?\(@\._key=='(.*?)'\)\]/g;for(;null!==(b=d.exec(a));){if(void 0!==b[1]){let a=b[1].replace(/\\(\\|f|n|r|t|')/g,a=>h[a]);c.push(a);continue}if(void 0!==b[2]){c.push(parseInt(b[2],10));continue}if(void 0!==b[3]){let a=b[3].replace(/\\(\\')/g,a=>h[a]);c.push({_key:a,_index:-1});continue}}return c}function j(a){return a.map(a=>{if("string"==typeof a||"number"==typeof a)return a;if(""!==a._key)return{_key:a._key};if(-1!==a._index)return a._index;throw Error(`invalid segment:${JSON.stringify(a)}`)})}function k(a){return"object"==typeof a&&null!==a}function l(a,b,c){return function a(b,c,d=[]){if(null!==b&&Array.isArray(b))return b.map((b,e)=>{if(k(b)){let f=b._key;if("string"==typeof f)return a(b,c,d.concat({_key:f,_index:e}))}return a(b,c,d.concat(e))});if(k(b)){if("block"===b._type||"span"===b._type){let e={...b};return"block"===b._type?e.children=a(b.children,c,d.concat("children")):"span"===b._type&&(e.text=a(b.text,c,d.concat("text"))),e}return Object.fromEntries(Object.entries(b).map(([b,e])=>[b,a(e,c,d.concat(b))]))}return c(b,d)}(a,(a,d)=>{if("string"!=typeof a)return a;let e=function(a,b){var c;if(!b?.mappings)return;let d=(c=a.map(a=>{if("string"==typeof a||"number"==typeof a)return a;if(-1!==a._index)return a._index;throw Error(`invalid segment:${JSON.stringify(a)}`)}),`$${c.map(a=>"string"==typeof a?`['${a.replace(/[\f\n\r\t'\\]/g,a=>g[a])}']`:"number"==typeof a?`[${a}]`:""!==a._key?`[?(@._key=='${a._key.replace(/['\\]/g,a=>g[a])}')]`:`[${a._index}]`).join("")}`);if(void 0!==b.mappings[d])return{mapping:b.mappings[d],matchedPath:d,pathSuffix:""};let e=Object.entries(b.mappings).filter(([a])=>d.startsWith(a)).sort(([a],[b])=>b.length-a.length);if(0==e.length)return;let[f,h]=e[0],i=d.substring(f.length);return{mapping:h,matchedPath:f,pathSuffix:i}}(d,b);if(!e)return a;let{mapping:f,matchedPath:h}=e;if("value"!==f.type||"documentValue"!==f.source.type)return a;let j=b.documents[f.source.document],k=b.paths[f.source.path],l=i(h);return c({sourcePath:i(k).concat(d.slice(l.length)),sourceDocument:j,resultPath:d,value:a})})}let m="drafts.",n=({sourcePath:a,resultPath:b,value:c})=>{if(function(a){return!!/^\d{4}-\d{2}-\d{2}/.test(a)&&!!Date.parse(a)}(c)||function(a){try{new URL(a,a.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(c))return!1;let d=a.at(-1);return!("slug"===a.at(-2)&&"current"===d||"string"==typeof d&&(d.startsWith("_")||d.endsWith("Id"))||a.some(a=>"meta"===a||"metadata"===a||"openGraph"===a||"seo"===a)||p(a)||p(b)||"string"==typeof d&&o.has(d))},o=new Set(["color","colour","currency","email","format","gid","hex","href","hsl","hsla","icon","id","index","key","language","layout","link","linkAction","locale","lqip","page","path","ref","rgb","rgba","route","secret","slug","status","tag","template","theme","type","textTheme","unit","url","username","variant","website"]);function p(a){return a.some(a=>"string"==typeof a&&null!==a.match(/type/i))}function q(a,b,c){let{filter:g,logger:h,enabled:i}=c;if(!i){let d="config.enabled must be true, don't call this function otherwise";throw h?.error?.(`[@sanity/client]: ${d}`,{result:a,resultSourceMap:b,config:c}),TypeError(d)}if(!b)return h?.error?.("[@sanity/client]: Missing Content Source Map from response body",{result:a,resultSourceMap:b,config:c}),a;if(!c.studioUrl){let d="config.studioUrl must be defined";throw h?.error?.(`[@sanity/client]: ${d}`,{result:a,resultSourceMap:b,config:c}),TypeError(d)}let k=[],o=[],p=l(a,b,({sourcePath:a,sourceDocument:b,resultPath:e,value:i})=>{var l;let p;if(("function"==typeof g?g({sourcePath:a,resultPath:e,filterDefault:n,sourceDocument:b,value:i}):n({sourcePath:a,resultPath:e,filterDefault:n,sourceDocument:b,value:i}))===!1)return h&&o.push({path:f(j(a)),value:`${i.slice(0,20)}${i.length>20?"...":""}`,length:i.length}),i;h&&k.push({path:f(j(a)),value:`${i.slice(0,20)}${i.length>20?"...":""}`,length:i.length});let{baseUrl:q,workspace:r,tool:s}=("/"!==(p="string"==typeof(l="function"==typeof c.studioUrl?c.studioUrl(b):c.studioUrl)?l:l.baseUrl)&&(p=p.replace(/\/$/,"")),"string"==typeof l?{baseUrl:p}:{...l,baseUrl:p});if(!q)return i;let{_id:t,_type:u,_projectId:v,_dataset:w}=b;return(0,d.C)(i,{origin:"sanity.io",href:function(a){let{baseUrl:b,workspace:c="default",tool:d="default",id:e,type:g,path:h,projectId:i,dataset:k}=a;if(!b)throw Error("baseUrl is required");if(!h)throw Error("path is required");if(!e)throw Error("id is required");if("/"!==b&&b.endsWith("/"))throw Error("baseUrl must not end with a slash");let l="default"===c?void 0:c,n="default"===d?void 0:d,o=e.startsWith(m)?e.slice(m.length):e,p=Array.isArray(h)?f(j(h)):h,q=new URLSearchParams({baseUrl:b,id:o,type:g,path:p});l&&q.set("workspace",l),n&&q.set("tool",n),i&&q.set("projectId",i),k&&q.set("dataset",k),e.startsWith(m)&&q.set("isDraft","");let r=["/"===b?"":b];l&&r.push(l);let s=["mode=presentation",`id=${o}`,`type=${g}`,`path=${encodeURIComponent(p)}`];return n&&s.push(`tool=${n}`),r.push("intent","edit",`${s.join(";")}?${q}`),r.join("/")}({baseUrl:q,workspace:r,tool:s,id:t,type:u,path:a,...!c.omitCrossDatasetReferenceData&&{dataset:w,projectId:v}})},!1)});if(h){let a=o.length,b=k.length;if((a||b)&&((h?.groupCollapsed||h.log)?.("[@sanity/client]: Encoding source map into result"),h.log?.(`[@sanity/client]: Paths encoded: ${k.length}, skipped: ${o.length}`)),k.length>0&&(h?.log?.("[@sanity/client]: Table of encoded paths"),(h?.table||h.log)?.(k)),o.length>0){let a=new Set;for(let{path:b}of o)a.add(b.replace(e,"0").replace(/\[\d+\]/g,"[]"));h?.log?.("[@sanity/client]: List of skipped paths",[...a.values()])}(a||b)&&h?.groupEnd?.()}return p}var r=Object.freeze({__proto__:null,stegaEncodeSourceMap:q})}};