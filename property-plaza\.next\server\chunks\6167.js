"use strict";exports.id=6167,exports.ids=[6167],exports.modules={36167:(a,b,c)=>{c.r(b),c.d(b,{default:()=>l});var d=c(37413),e=c(21201),f=c(95804),g=c(18060),h=c(32401),i=c(71891),j=c(90388),k=c(75074);async function l(){let a=await (0,j.KP)(),b=await (0,k.A)("seeker");return(0,d.jsx)("article",{className:"bg-seekers-foreground/50 py-12",children:(0,d.jsx)(h.A,{children:(0,d.jsx)(e.default,{title:(0,d.jsxs)("span",{className:"inline-flex items-center gap-1",children:[(0,d.jsx)(i.A,{className:"max-sm:hidden"})," ",b("blog.sectionTitle")]}),className:"!mt-2",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map((a,b)=>(0,d.jsx)(f.default,{date:a.publishedAt,content:a.metadata,title:a.title,image:a?.mainImage?.asset?.url||g.default,url:`/posts/${a.slug.current}`},b))})})})})}},71891:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(26373).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},90388:(a,b,c)=>{c.d(b,{KP:()=>u,Bi:()=>y,gF:()=>x,N7:()=>w,K5:()=>v,xE:()=>A,Dc:()=>z,hD:()=>B,fx:()=>s});var d=c(23777),e=c.n(d),f=c(16664);let g={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var h=c(98810);let i=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`,j=(0,h.A)`*[_type == "post" && author != "hidden"] ${i}`,k=(0,h.A)`*[_type == "post" && author != "hidden"][0...2] ${i}`,l=(0,h.A)`*[_type == "post" && slug.current == $slug][0]  ${i}
  

`;(0,h.A)`*[_type == "post" && $slug in tags[]->slug.current] ${i}`,(0,h.A)`*[_type == "post" && author->slug.current == $slug] ${i}`,(0,h.A)`*[_type == "post" && category->slug.current == $slug] ${i}`;let m=(0,h.A)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${i}
  `,n=(0,h.A)`*[_type == "seoContent" && language == $language]{title,body}`,o=(0,h.A)`*[_type == "termsOfUse" && language == $language]{title,body}`,p=(0,h.A)`*[_type == "privacyPolicy" && language == $language]{title,body}`,q=(0,h.A)`*[_type == "userDataDeletion" && language == $language]{title,body}`,r=(0,f.UU)(g);function s(a){return e()(g).image(a)}async function t({query:a,qParams:b,tags:c}){return r.fetch(a,b,{next:{tags:c,revalidate:3600}})}let u=async()=>await t({query:k,qParams:{},tags:["post","author","category"]}),v=async()=>await t({query:j,qParams:{},tags:["post","author","category"]}),w=async a=>await t({query:l,qParams:{slug:a},tags:["post","author","category"]}),x=async(a,b)=>await t({query:m,qParams:{slug:a,id:b},tags:[]}),y=async a=>await t({query:n,qParams:{language:a},tags:[]}),z=async a=>await t({query:o,qParams:{language:a},tags:[]}),A=async a=>await t({query:p,qParams:{language:a},tags:[]}),B=async a=>await t({query:q,qParams:{language:a},tags:[]})}};