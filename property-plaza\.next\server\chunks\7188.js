exports.id=7188,exports.ids=[7188],exports.modules={22:(a,b,c)=>{var d=c(75254),e=c(20623),f=c(48169),g=c(40542),h=c(45058);a.exports=function(a){return"function"==typeof a?a:null==a?f:"object"==typeof a?g(a)?e(a[0],a[1]):d(a):h(a)}},658:(a,b,c)=>{a.exports=c(41547)(c(85718),"Map")},1566:(a,b,c)=>{var d=c(89167),e=c(658),f=c(30401),g=c(34772),h=c(17830),i=c(29395),j=c(12290),k="[object Map]",l="[object Promise]",m="[object Set]",n="[object WeakMap]",o="[object DataView]",p=j(d),q=j(e),r=j(f),s=j(g),t=j(h),u=i;(d&&u(new d(new ArrayBuffer(1)))!=o||e&&u(new e)!=k||f&&u(f.resolve())!=l||g&&u(new g)!=m||h&&u(new h)!=n)&&(u=function(a){var b=i(a),c="[object Object]"==b?a.constructor:void 0,d=c?j(c):"";if(d)switch(d){case p:return o;case q:return k;case r:return l;case s:return m;case t:return n}return b}),a.exports=u},1707:(a,b,c)=>{var d=c(35142),e=c(46436);a.exports=function(a,b){b=d(b,a);for(var c=0,f=b.length;null!=a&&c<f;)a=a[e(b[c++])];return c&&c==f?a:void 0}},1944:a=>{a.exports=function(){return!1}},2408:a=>{a.exports=function(a){var b=-1,c=Array(a.size);return a.forEach(function(a){c[++b]=a}),c}},2896:(a,b,c)=>{var d=c(81488),e=c(59467);a.exports=function(a,b){return null!=a&&e(a,b,d)}},2984:(a,b,c)=>{var d=c(49227);a.exports=function(a,b,c){for(var e=-1,f=a.length;++e<f;){var g=a[e],h=b(g);if(null!=h&&(void 0===i?h==h&&!d(h):c(h,i)))var i=h,j=g}return j}},3105:a=>{a.exports=function(a){return a.split("")}},4057:(a,b,c)=>{"use strict";c.d(b,{QQ:()=>h,VU:()=>j,XC:()=>m,_U:()=>l,j2:()=>k});var d=c(43210),e=c(55048),f=c.n(e);function g(a){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var h=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],i=["points","pathLength"],j={svg:["viewBox","children"],polygon:i,polyline:i},k=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],l=function(a,b){if(!a||"function"==typeof a||"boolean"==typeof a)return null;var c=a;if((0,d.isValidElement)(a)&&(c=a.props),!f()(c))return null;var e={};return Object.keys(c).forEach(function(a){k.includes(a)&&(e[a]=b||function(b){return c[a](c,b)})}),e},m=function(a,b,c){if(!f()(a)||"object"!==g(a))return null;var d=null;return Object.keys(a).forEach(function(e){var f=a[e];k.includes(e)&&"function"==typeof f&&(d||(d={}),d[e]=function(a){return f(b,c,a),null})}),d}},4768:a=>{function b(c,d){return a.exports=b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a},a.exports.__esModule=!0,a.exports.default=a.exports,b(c,d)}a.exports=b,a.exports.__esModule=!0,a.exports.default=a.exports},4999:(a,b,c)=>{a.exports=c(85718).Uint8Array},5231:(a,b,c)=>{var d=c(29395),e=c(55048);a.exports=function(a){if(!e(a))return!1;var b=d(a);return"[object Function]"==b||"[object GeneratorFunction]"==b||"[object AsyncFunction]"==b||"[object Proxy]"==b}},5359:a=>{a.exports=function(a){var b=null==a?0:a.length;return b?a[b-1]:void 0}},5566:(a,b,c)=>{var d=c(41011),e=c(34117),f=c(66713),g=c(42403);a.exports=function(a){return function(b){var c=e(b=g(b))?f(b):void 0,h=c?c[0]:b.charAt(0),i=c?d(c,1).join(""):b.slice(1);return h[a]()+i}}},5748:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},6053:a=>{var b=/\s/;a.exports=function(a){for(var c=a.length;c--&&b.test(a.charAt(c)););return c}},6330:a=>{a.exports=function(){return[]}},7383:(a,b,c)=>{var d=c(67009),e=c(32269),f=c(38428),g=c(55048);a.exports=function(a,b,c){if(!g(c))return!1;var h=typeof b;return("number"==h?!!(e(c)&&f(b,c.length)):"string"==h&&b in c)&&d(c[b],a)}},7651:(a,b,c)=>{var d=c(82038),e=c(52931),f=c(32269);a.exports=function(a){return f(a)?d(a):e(a)}},8336:(a,b,c)=>{var d=c(45803);a.exports=function(a,b){var c=a.__data__;return d(b)?c["string"==typeof b?"string":"hash"]:c.map}},8731:(a,b,c)=>{"use strict";c.d(b,{E:()=>dk});var d=c(43210),e=c.n(d),f=c(37456),g=c.n(f),h=c(5231),i=c.n(h),j=c(34990),k=c.n(j),l=c(40491),m=c.n(l),n=c(85938),o=c.n(n),p=c(45603),q=c.n(p),r=c(49384),s=c(89653),t=c(21080),u=c(98986),v=c(38246),w=c(79740),x=c(4057),y=c(54186);function z(){return(z=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var A=function(a){var b=a.cx,c=a.cy,d=a.r,f=a.className,g=(0,r.A)("recharts-dot",f);return b===+b&&c===+c&&d===+d?e().createElement("circle",z({},(0,y.J9)(a,!1),(0,x._U)(a),{className:g,cx:b,cy:c,r:d})):null},B=c(71524),C=c(52415),D=c(23561),E=c(30087),F=c(45370);function G(a){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function H(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function I(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?H(Object(c),!0).forEach(function(b){J(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):H(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function J(a,b,c){var d;return(d=function(a,b){if("object"!=G(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=G(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"),(b="symbol"==G(d)?d:d+"")in a)?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}var K=["Webkit","Moz","O","ms"],L=function(a,b){if(!a)return null;var c=a.replace(/(\w)/,function(a){return a.toUpperCase()}),d=K.reduce(function(a,d){return I(I({},a),{},J({},d+c,b))},{});return d[a]=b,d};function M(a){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function N(){return(N=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function O(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function P(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?O(Object(c),!0).forEach(function(b){U(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):O(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function Q(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,V(d.key),d)}}function R(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(R=function(){return!!a})()}function S(a){return(S=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function T(a,b){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function U(a,b,c){return(b=V(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function V(a){var b=function(a,b){if("object"!=M(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=M(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==M(b)?b:b+""}var W=function(a){var b=a.data,c=a.startIndex,d=a.endIndex,e=a.x,f=a.width,g=a.travellerWidth;if(!b||!b.length)return{};var h=b.length,i=(0,C.z)().domain(k()(0,h)).range([e,e+f-g]),j=i.domain().map(function(a){return i(a)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:i(c),endX:i(d),scale:i,scaleValues:j}},X=function(a){return a.changedTouches&&!!a.changedTouches.length},Y=function(a){var b,c;function f(a){var b,c,d;if(!(this instanceof f))throw TypeError("Cannot call a class as a function");return c=f,d=[a],c=S(c),U(b=function(a,b){if(b&&("object"===M(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,R()?Reflect.construct(c,d||[],S(this).constructor):c.apply(this,d)),"handleDrag",function(a){b.leaveTimer&&(clearTimeout(b.leaveTimer),b.leaveTimer=null),b.state.isTravellerMoving?b.handleTravellerMove(a):b.state.isSlideMoving&&b.handleSlideDrag(a)}),U(b,"handleTouchMove",function(a){null!=a.changedTouches&&a.changedTouches.length>0&&b.handleDrag(a.changedTouches[0])}),U(b,"handleDragEnd",function(){b.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var a=b.props,c=a.endIndex,d=a.onDragEnd,e=a.startIndex;null==d||d({endIndex:c,startIndex:e})}),b.detachDragEndListener()}),U(b,"handleLeaveWrapper",function(){(b.state.isTravellerMoving||b.state.isSlideMoving)&&(b.leaveTimer=window.setTimeout(b.handleDragEnd,b.props.leaveTimeOut))}),U(b,"handleEnterSlideOrTraveller",function(){b.setState({isTextActive:!0})}),U(b,"handleLeaveSlideOrTraveller",function(){b.setState({isTextActive:!1})}),U(b,"handleSlideDragStart",function(a){var c=X(a)?a.changedTouches[0]:a;b.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:c.pageX}),b.attachDragEndListener()}),b.travellerDragStartHandlers={startX:b.handleTravellerDragStart.bind(b,"startX"),endX:b.handleTravellerDragStart.bind(b,"endX")},b.state={},b}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return f.prototype=Object.create(a&&a.prototype,{constructor:{value:f,writable:!0,configurable:!0}}),Object.defineProperty(f,"prototype",{writable:!1}),a&&T(f,a),b=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(a){var b=a.startX,c=a.endX,d=this.state.scaleValues,e=this.props,g=e.gap,h=e.data.length-1,i=Math.min(b,c),j=Math.max(b,c),k=f.getIndexInRange(d,i),l=f.getIndexInRange(d,j);return{startIndex:k-k%g,endIndex:l===h?h:l-l%g}}},{key:"getTextOfTick",value:function(a){var b=this.props,c=b.data,d=b.tickFormatter,e=b.dataKey,f=(0,E.kr)(c[a],e,a);return i()(d)?d(f,a):f}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(a){var b=this.state,c=b.slideMoveStartX,d=b.startX,e=b.endX,f=this.props,g=f.x,h=f.width,i=f.travellerWidth,j=f.startIndex,k=f.endIndex,l=f.onChange,m=a.pageX-c;m>0?m=Math.min(m,g+h-i-e,g+h-i-d):m<0&&(m=Math.max(m,g-d,g-e));var n=this.getIndex({startX:d+m,endX:e+m});(n.startIndex!==j||n.endIndex!==k)&&l&&l(n),this.setState({startX:d+m,endX:e+m,slideMoveStartX:a.pageX})}},{key:"handleTravellerDragStart",value:function(a,b){var c=X(b)?b.changedTouches[0]:b;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:a,brushMoveStartX:c.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(a){var b=this.state,c=b.brushMoveStartX,d=b.movingTravellerId,e=b.endX,f=b.startX,g=this.state[d],h=this.props,i=h.x,j=h.width,k=h.travellerWidth,l=h.onChange,m=h.gap,n=h.data,o={startX:this.state.startX,endX:this.state.endX},p=a.pageX-c;p>0?p=Math.min(p,i+j-k-g):p<0&&(p=Math.max(p,i-g)),o[d]=g+p;var q=this.getIndex(o),r=q.startIndex,s=q.endIndex,t=function(){var a=n.length-1;return"startX"===d&&(e>f?r%m==0:s%m==0)||!!(e<f)&&s===a||"endX"===d&&(e>f?s%m==0:r%m==0)||!!(e>f)&&s===a};this.setState(U(U({},d,g+p),"brushMoveStartX",a.pageX),function(){l&&t()&&l(q)})}},{key:"handleTravellerMoveKeyboard",value:function(a,b){var c=this,d=this.state,e=d.scaleValues,f=d.startX,g=d.endX,h=this.state[b],i=e.indexOf(h);if(-1!==i){var j=i+a;if(-1!==j&&!(j>=e.length)){var k=e[j];"startX"===b&&k>=g||"endX"===b&&k<=f||this.setState(U({},b,k),function(){c.props.onChange(c.getIndex({startX:c.state.startX,endX:c.state.endX}))})}}}},{key:"renderBackground",value:function(){var a=this.props,b=a.x,c=a.y,d=a.width,f=a.height,g=a.fill,h=a.stroke;return e().createElement("rect",{stroke:h,fill:g,x:b,y:c,width:d,height:f})}},{key:"renderPanorama",value:function(){var a=this.props,b=a.x,c=a.y,f=a.width,g=a.height,h=a.data,i=a.children,j=a.padding,k=d.Children.only(i);return k?e().cloneElement(k,{x:b,y:c,width:f,height:g,margin:j,compact:!0,data:h}):null}},{key:"renderTravellerLayer",value:function(a,b){var c,d,g=this,h=this.props,i=h.y,j=h.travellerWidth,k=h.height,l=h.traveller,m=h.ariaLabel,n=h.data,o=h.startIndex,p=h.endIndex,q=Math.max(a,this.props.x),r=P(P({},(0,y.J9)(this.props,!1)),{},{x:q,y:i,width:j,height:k}),s=m||"Min value: ".concat(null==(c=n[o])?void 0:c.name,", Max value: ").concat(null==(d=n[p])?void 0:d.name);return e().createElement(u.W,{tabIndex:0,role:"slider","aria-label":s,"aria-valuenow":a,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[b],onTouchStart:this.travellerDragStartHandlers[b],onKeyDown:function(a){["ArrowLeft","ArrowRight"].includes(a.key)&&(a.preventDefault(),a.stopPropagation(),g.handleTravellerMoveKeyboard("ArrowRight"===a.key?1:-1,b))},onFocus:function(){g.setState({isTravellerFocused:!0})},onBlur:function(){g.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},f.renderTraveller(l,r))}},{key:"renderSlide",value:function(a,b){var c=this.props,d=c.y,f=c.height,g=c.stroke,h=c.travellerWidth,i=Math.min(a,b)+h,j=Math.max(Math.abs(b-a)-h,0);return e().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:g,fillOpacity:.2,x:i,y:d,width:j,height:f})}},{key:"renderText",value:function(){var a=this.props,b=a.startIndex,c=a.endIndex,d=a.y,f=a.height,g=a.travellerWidth,h=a.stroke,i=this.state,j=i.startX,k=i.endX,l={pointerEvents:"none",fill:h};return e().createElement(u.W,{className:"recharts-brush-texts"},e().createElement(D.E,N({textAnchor:"end",verticalAnchor:"middle",x:Math.min(j,k)-5,y:d+f/2},l),this.getTextOfTick(b)),e().createElement(D.E,N({textAnchor:"start",verticalAnchor:"middle",x:Math.max(j,k)+g+5,y:d+f/2},l),this.getTextOfTick(c)))}},{key:"render",value:function(){var a=this.props,b=a.data,c=a.className,d=a.children,f=a.x,g=a.y,h=a.width,i=a.height,j=a.alwaysShowText,k=this.state,l=k.startX,m=k.endX,n=k.isTextActive,o=k.isSlideMoving,p=k.isTravellerMoving,q=k.isTravellerFocused;if(!b||!b.length||!(0,F.Et)(f)||!(0,F.Et)(g)||!(0,F.Et)(h)||!(0,F.Et)(i)||h<=0||i<=0)return null;var s=(0,r.A)("recharts-brush",c),t=1===e().Children.count(d),v=L("userSelect","none");return e().createElement(u.W,{className:s,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:v},this.renderBackground(),t&&this.renderPanorama(),this.renderSlide(l,m),this.renderTravellerLayer(l,"startX"),this.renderTravellerLayer(m,"endX"),(n||o||p||q||j)&&this.renderText())}}],c=[{key:"renderDefaultTraveller",value:function(a){var b=a.x,c=a.y,d=a.width,f=a.height,g=a.stroke,h=Math.floor(c+f/2)-1;return e().createElement(e().Fragment,null,e().createElement("rect",{x:b,y:c,width:d,height:f,fill:g,stroke:"none"}),e().createElement("line",{x1:b+1,y1:h,x2:b+d-1,y2:h,fill:"none",stroke:"#fff"}),e().createElement("line",{x1:b+1,y1:h+2,x2:b+d-1,y2:h+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(a,b){return e().isValidElement(a)?e().cloneElement(a,b):i()(a)?a(b):f.renderDefaultTraveller(b)}},{key:"getDerivedStateFromProps",value:function(a,b){var c=a.data,d=a.width,e=a.x,f=a.travellerWidth,g=a.updateId,h=a.startIndex,i=a.endIndex;if(c!==b.prevData||g!==b.prevUpdateId)return P({prevData:c,prevTravellerWidth:f,prevUpdateId:g,prevX:e,prevWidth:d},c&&c.length?W({data:c,width:d,x:e,travellerWidth:f,startIndex:h,endIndex:i}):{scale:null,scaleValues:null});if(b.scale&&(d!==b.prevWidth||e!==b.prevX||f!==b.prevTravellerWidth)){b.scale.range([e,e+d-f]);var j=b.scale.domain().map(function(a){return b.scale(a)});return{prevData:c,prevTravellerWidth:f,prevUpdateId:g,prevX:e,prevWidth:d,startX:b.scale(a.startIndex),endX:b.scale(a.endIndex),scaleValues:j}}return null}},{key:"getIndexInRange",value:function(a,b){for(var c=a.length,d=0,e=c-1;e-d>1;){var f=Math.floor((d+e)/2);a[f]>b?e=f:d=f}return b>=a[e]?e:d}}],b&&Q(f.prototype,b),c&&Q(f,c),Object.defineProperty(f,"prototype",{writable:!1}),f}(d.PureComponent);U(Y,"displayName","Brush"),U(Y,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Z=c(96075),$=c(46707),_=c(97633),aa=function(a,b){var c=a.alwaysShow,d=a.ifOverflow;return c&&(d="extendDomain"),d===b},ab=c(69691),ac=c.n(ab),ad=c(47212),ae=c.n(ad),af=c(91827);function ag(a){return(ag="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function ah(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,al(d.key),d)}}function ai(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function aj(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ai(Object(c),!0).forEach(function(b){ak(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ai(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function ak(a,b,c){return(b=al(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function al(a){var b=function(a,b){if("object"!=ag(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=ag(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==ag(b)?b:b+""}var am=function(a,b){var c=a.x,d=a.y,e=b.x,f=b.y;return{x:Math.min(c,e),y:Math.min(d,f),width:Math.abs(e-c),height:Math.abs(f-d)}},an=function(){var a,b;function c(a){if(!(this instanceof c))throw TypeError("Cannot call a class as a function");this.scale=a}return a=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},c=b.bandAware,d=b.position;if(void 0!==a){if(d)switch(d){case"start":default:return this.scale(a);case"middle":var e=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+e;case"end":var f=this.bandwidth?this.bandwidth():0;return this.scale(a)+f}if(c){var g=this.bandwidth?this.bandwidth()/2:0;return this.scale(a)+g}return this.scale(a)}}},{key:"isInRange",value:function(a){var b=this.range(),c=b[0],d=b[b.length-1];return c<=d?a>=c&&a<=d:a>=d&&a<=c}}],b=[{key:"create",value:function(a){return new c(a)}}],a&&ah(c.prototype,a),b&&ah(c,b),Object.defineProperty(c,"prototype",{writable:!1}),c}();ak(an,"EPS",1e-4);var ao=function(a){var b=Object.keys(a).reduce(function(b,c){return aj(aj({},b),{},ak({},c,an.create(a[c])))},{});return aj(aj({},b),{},{apply:function(a){var c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=c.bandAware,e=c.position;return ac()(a,function(a,c){return b[c].apply(a,{bandAware:d,position:e})})},isInRange:function(a){return ae()(a,function(a,c){return b[c].isInRange(a)})}})},ap=function(a){var b=a.width,c=a.height,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=(d%180+180)%180*Math.PI/180,f=Math.atan(c/b);return Math.abs(e>f&&e<Math.PI-f?c/Math.sin(e):b/Math.cos(e))},aq=c(10521);function ar(){return(ar=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function as(a){return(as="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function at(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function au(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?at(Object(c),!0).forEach(function(b){ay(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):at(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function av(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(av=function(){return!!a})()}function aw(a){return(aw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function ax(a,b){return(ax=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function ay(a,b,c){return(b=az(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function az(a){var b=function(a,b){if("object"!=as(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=as(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==as(b)?b:b+""}var aA=function(a){var b=a.x,c=a.y,d=a.xAxis,e=a.yAxis,f=ao({x:d.scale,y:e.scale}),g=f.apply({x:b,y:c},{bandAware:!0});return aa(a,"discard")&&!f.isInRange(g)?null:g},aB=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=aw(a),function(a,b){if(b&&("object"===as(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,av()?Reflect.construct(a,b||[],aw(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&ax(c,a),b=[{key:"render",value:function(){var a=this.props,b=a.x,d=a.y,f=a.r,g=a.alwaysShow,h=a.clipPathId,i=(0,F.vh)(b),j=(0,F.vh)(d);if((0,aq.R)(void 0===g,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!i||!j)return null;var k=aA(this.props);if(!k)return null;var l=k.x,m=k.y,n=this.props,o=n.shape,p=n.className,q=au(au({clipPath:aa(this.props,"hidden")?"url(#".concat(h,")"):void 0},(0,y.J9)(this.props,!0)),{},{cx:l,cy:m});return e().createElement(u.W,{className:(0,r.A)("recharts-reference-dot",p)},c.renderDot(o,q),_.J.renderCallByParent(this.props,{x:l-f,y:m-f,width:2*f,height:2*f}))}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,az(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);ay(aB,"displayName","ReferenceDot"),ay(aB,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ay(aB,"renderDot",function(a,b){return e().isValidElement(a)?e().cloneElement(a,b):i()(a)?a(b):e().createElement(A,ar({},b,{cx:b.cx,cy:b.cy,className:"recharts-reference-dot-dot"}))});var aC=c(67367),aD=c.n(aC);c(22964);var aE=c(86451),aF=c.n(aE)()(function(a){return{x:a.left,y:a.top,width:a.width,height:a.height}},function(a){return["l",a.left,"t",a.top,"w",a.width,"h",a.height].join("")}),aG=(0,d.createContext)(void 0),aH=(0,d.createContext)(void 0),aI=(0,d.createContext)(void 0),aJ=(0,d.createContext)({}),aK=(0,d.createContext)(void 0),aL=(0,d.createContext)(0),aM=(0,d.createContext)(0),aN=function(a){var b=a.state,c=b.xAxisMap,d=b.yAxisMap,f=b.offset,g=a.clipPathId,h=a.children,i=a.width,j=a.height,k=aF(f);return e().createElement(aG.Provider,{value:c},e().createElement(aH.Provider,{value:d},e().createElement(aJ.Provider,{value:f},e().createElement(aI.Provider,{value:k},e().createElement(aK.Provider,{value:g},e().createElement(aL.Provider,{value:j},e().createElement(aM.Provider,{value:i},h)))))))},aO=function(a){var b=(0,d.useContext)(aG);null==b&&(0,s.A)(!1);var c=b[a];return null==c&&(0,s.A)(!1),c},aP=function(a){var b=(0,d.useContext)(aH);null==b&&(0,s.A)(!1);var c=b[a];return null==c&&(0,s.A)(!1),c},aQ=function(){return(0,d.useContext)(aM)},aR=function(){return(0,d.useContext)(aL)};function aS(a){return(aS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function aT(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(aT=function(){return!!a})()}function aU(a){return(aU=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function aV(a,b){return(aV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function aW(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function aX(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?aW(Object(c),!0).forEach(function(b){aY(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):aW(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function aY(a,b,c){return(b=aZ(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function aZ(a){var b=function(a,b){if("object"!=aS(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=aS(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==aS(b)?b:b+""}function a$(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function a_(){return(a_=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var a0=function(a,b){return e().isValidElement(a)?e().cloneElement(a,b):i()(a)?a(b):e().createElement("line",a_({},b,{className:"recharts-reference-line-line"}))},a1=function(a,b,c,d,e,f,g,h,i){var j=e.x,k=e.y,l=e.width,m=e.height;if(c){var n=i.y,o=a.y.apply(n,{position:f});if(aa(i,"discard")&&!a.y.isInRange(o))return null;var p=[{x:j+l,y:o},{x:j,y:o}];return"left"===h?p.reverse():p}if(b){var q=i.x,r=a.x.apply(q,{position:f});if(aa(i,"discard")&&!a.x.isInRange(r))return null;var s=[{x:r,y:k+m},{x:r,y:k}];return"top"===g?s.reverse():s}if(d){var t=i.segment.map(function(b){return a.apply(b,{position:f})});return aa(i,"discard")&&aD()(t,function(b){return!a.isInRange(b)})?null:t}return null};function a2(a){var b,c=a.x,f=a.y,g=a.segment,h=a.xAxisId,i=a.yAxisId,j=a.shape,k=a.className,l=a.alwaysShow,m=(0,d.useContext)(aK),n=aO(h),o=aP(i),p=(0,d.useContext)(aI);if(!m||!p)return null;(0,aq.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var q=a1(ao({x:n.scale,y:o.scale}),(0,F.vh)(c),(0,F.vh)(f),g&&2===g.length,p,a.position,n.orientation,o.orientation,a);if(!q)return null;var s=function(a){if(Array.isArray(a))return a}(q)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(q,2)||function(a,b){if(a){if("string"==typeof a)return a$(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return a$(a,b)}}(q,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),t=s[0],v=t.x,w=t.y,x=s[1],z=x.x,A=x.y,B=aX(aX({clipPath:aa(a,"hidden")?"url(#".concat(m,")"):void 0},(0,y.J9)(a,!0)),{},{x1:v,y1:w,x2:z,y2:A});return e().createElement(u.W,{className:(0,r.A)("recharts-reference-line",k)},a0(j,B),_.J.renderCallByParent(a,am({x:(b={x1:v,y1:w,x2:z,y2:A}).x1,y:b.y1},{x:b.x2,y:b.y2})))}var a3=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=aU(a),function(a,b){if(b&&("object"===aS(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,aT()?Reflect.construct(a,b||[],aU(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&aV(c,a),b=[{key:"render",value:function(){return e().createElement(a2,this.props)}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,aZ(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);function a4(){return(a4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function a5(a){return(a5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function a6(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function a7(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?a6(Object(c),!0).forEach(function(b){bb(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):a6(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}aY(a3,"displayName","ReferenceLine"),aY(a3,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function a8(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(a8=function(){return!!a})()}function a9(a){return(a9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function ba(a,b){return(ba=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function bb(a,b,c){return(b=bc(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function bc(a){var b=function(a,b){if("object"!=a5(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=a5(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==a5(b)?b:b+""}var bd=function(a,b,c,d,e){var f=e.x1,g=e.x2,h=e.y1,i=e.y2,j=e.xAxis,k=e.yAxis;if(!j||!k)return null;var l=ao({x:j.scale,y:k.scale}),m={x:a?l.x.apply(f,{position:"start"}):l.x.rangeMin,y:c?l.y.apply(h,{position:"start"}):l.y.rangeMin},n={x:b?l.x.apply(g,{position:"end"}):l.x.rangeMax,y:d?l.y.apply(i,{position:"end"}):l.y.rangeMax};return!aa(e,"discard")||l.isInRange(m)&&l.isInRange(n)?am(m,n):null},be=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=a9(a),function(a,b){if(b&&("object"===a5(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,a8()?Reflect.construct(a,b||[],a9(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&ba(c,a),b=[{key:"render",value:function(){var a=this.props,b=a.x1,d=a.x2,f=a.y1,g=a.y2,h=a.className,i=a.alwaysShow,j=a.clipPathId;(0,aq.R)(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var k=(0,F.vh)(b),l=(0,F.vh)(d),m=(0,F.vh)(f),n=(0,F.vh)(g),o=this.props.shape;if(!k&&!l&&!m&&!n&&!o)return null;var p=bd(k,l,m,n,this.props);if(!p&&!o)return null;var q=aa(this.props,"hidden")?"url(#".concat(j,")"):void 0;return e().createElement(u.W,{className:(0,r.A)("recharts-reference-area",h)},c.renderRect(o,a7(a7({clipPath:q},(0,y.J9)(this.props,!0)),p)),_.J.renderCallByParent(this.props,p))}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,bc(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);function bf(a){return function(a){if(Array.isArray(a))return bg(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||function(a,b){if(a){if("string"==typeof a)return bg(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return bg(a,b)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bg(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}bb(be,"displayName","ReferenceArea"),bb(be,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),bb(be,"renderRect",function(a,b){return e().isValidElement(a)?e().cloneElement(a,b):i()(a)?a(b):e().createElement(B.M,a4({},b,{className:"recharts-reference-area-rect"}))});var bh=function(a,b,c,d,e){var f=(0,y.aS)(a,a3),g=(0,y.aS)(a,aB),h=[].concat(bf(f),bf(g)),i=(0,y.aS)(a,be),j="".concat(d,"Id"),k=d[0],l=b;if(h.length&&(l=h.reduce(function(a,b){if(b.props[j]===c&&aa(b.props,"extendDomain")&&(0,F.Et)(b.props[k])){var d=b.props[k];return[Math.min(a[0],d),Math.max(a[1],d)]}return a},l)),i.length){var m="".concat(k,"1"),n="".concat(k,"2");l=i.reduce(function(a,b){if(b.props[j]===c&&aa(b.props,"extendDomain")&&(0,F.Et)(b.props[m])&&(0,F.Et)(b.props[n])){var d=b.props[m],e=b.props[n];return[Math.min(a[0],d,e),Math.max(a[1],d,e)]}return a},l)}return e&&e.length&&(l=e.reduce(function(a,b){return(0,F.Et)(b)?[Math.min(a[0],b),Math.max(a[1],b)]:a},l)),l},bi=c(19335),bj=c(18842),bk=c(11117),bl=new(c.n(bk)()),bm="recharts.syncMouseEvents";function bn(a){return(bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function bo(a,b,c){return(b=bp(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function bp(a){var b=function(a,b){if("object"!=bn(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=bn(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==bn(b)?b:b+""}var bq=function(){var a,b;return a=function a(){if(!(this instanceof a))throw TypeError("Cannot call a class as a function");bo(this,"activeIndex",0),bo(this,"coordinateList",[]),bo(this,"layout","horizontal")},b=[{key:"setDetails",value:function(a){var b,c=a.coordinateList,d=void 0===c?null:c,e=a.container,f=void 0===e?null:e,g=a.layout,h=void 0===g?null:g,i=a.offset,j=void 0===i?null:i,k=a.mouseHandlerCallback,l=void 0===k?null:k;this.coordinateList=null!=(b=null!=d?d:this.coordinateList)?b:[],this.container=null!=f?f:this.container,this.layout=null!=h?h:this.layout,this.offset=null!=j?j:this.offset,this.mouseHandlerCallback=null!=l?l:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(a){if(0!==this.coordinateList.length)switch(a.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(a){this.activeIndex=a}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var a,b,c=this.container.getBoundingClientRect(),d=c.x,e=c.y,f=c.height,g=this.coordinateList[this.activeIndex].coordinate,h=(null==(a=window)?void 0:a.scrollX)||0,i=(null==(b=window)?void 0:b.scrollY)||0,j=e+this.offset.top+f/2+i;this.mouseHandlerCallback({pageX:d+g+h,pageY:j})}}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,bp(d.key),d)}}(a.prototype,b),Object.defineProperty(a,"prototype",{writable:!1}),a}(),br=c(67629);function bs(){}function bt(a,b,c){a._context.bezierCurveTo((2*a._x0+a._x1)/3,(2*a._y0+a._y1)/3,(a._x0+2*a._x1)/3,(a._y0+2*a._y1)/3,(a._x0+4*a._x1+b)/6,(a._y0+4*a._y1+c)/6)}function bu(a){this._context=a}function bv(a){this._context=a}function bw(a){this._context=a}bu.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:bt(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:bt(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},bv.prototype={areaStart:bs,areaEnd:bs,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._x2=a,this._y2=b;break;case 1:this._point=2,this._x3=a,this._y3=b;break;case 2:this._point=3,this._x4=a,this._y4=b,this._context.moveTo((this._x0+4*this._x1+a)/6,(this._y0+4*this._y1+b)/6);break;default:bt(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}},bw.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var c=(this._x0+4*this._x1+a)/6,d=(this._y0+4*this._y1+b)/6;this._line?this._context.lineTo(c,d):this._context.moveTo(c,d);break;case 3:this._point=4;default:bt(this,a,b)}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b}};class bx{constructor(a,b){this._context=a,this._x=b}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+a)/2,this._y0,this._x0,b,a,b):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+b)/2,a,this._y0,a,b)}this._x0=a,this._y0=b}}function by(a){this._context=a}function bz(a){this._context=a}function bA(a){return new bz(a)}by.prototype={areaStart:bs,areaEnd:bs,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(a,b){a*=1,b*=1,this._point?this._context.lineTo(a,b):(this._point=1,this._context.moveTo(a,b))}};function bB(a,b,c){var d=a._x1-a._x0,e=b-a._x1,f=(a._y1-a._y0)/(d||e<0&&-0),g=(c-a._y1)/(e||d<0&&-0);return((f<0?-1:1)+(g<0?-1:1))*Math.min(Math.abs(f),Math.abs(g),.5*Math.abs((f*e+g*d)/(d+e)))||0}function bC(a,b){var c=a._x1-a._x0;return c?(3*(a._y1-a._y0)/c-b)/2:b}function bD(a,b,c){var d=a._x0,e=a._y0,f=a._x1,g=a._y1,h=(f-d)/3;a._context.bezierCurveTo(d+h,e+h*b,f-h,g-h*c,f,g)}function bE(a){this._context=a}function bF(a){this._context=new bG(a)}function bG(a){this._context=a}function bH(a){this._context=a}function bI(a){var b,c,d=a.length-1,e=Array(d),f=Array(d),g=Array(d);for(e[0]=0,f[0]=2,g[0]=a[0]+2*a[1],b=1;b<d-1;++b)e[b]=1,f[b]=4,g[b]=4*a[b]+2*a[b+1];for(e[d-1]=2,f[d-1]=7,g[d-1]=8*a[d-1]+a[d],b=1;b<d;++b)c=e[b]/f[b-1],f[b]-=c,g[b]-=c*g[b-1];for(e[d-1]=g[d-1]/f[d-1],b=d-2;b>=0;--b)e[b]=(g[b]-e[b+1])/f[b];for(b=0,f[d-1]=(a[d]+e[d-1])/2;b<d-1;++b)f[b]=2*a[b+1]-e[b+1];return[e,f]}function bJ(a,b){this._context=a,this._t=b}bz.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:this._context.lineTo(a,b)}}},bE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:bD(this,this._t0,bC(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(a,b){var c=NaN;if(b*=1,(a*=1)!==this._x1||b!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;break;case 2:this._point=3,bD(this,bC(this,c=bB(this,a,b)),c);break;default:bD(this,this._t0,c=bB(this,a,b))}this._x0=this._x1,this._x1=a,this._y0=this._y1,this._y1=b,this._t0=c}}},(bF.prototype=Object.create(bE.prototype)).point=function(a,b){bE.prototype.point.call(this,b,a)},bG.prototype={moveTo:function(a,b){this._context.moveTo(b,a)},closePath:function(){this._context.closePath()},lineTo:function(a,b){this._context.lineTo(b,a)},bezierCurveTo:function(a,b,c,d,e,f){this._context.bezierCurveTo(b,a,d,c,f,e)}},bH.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var a=this._x,b=this._y,c=a.length;if(c)if(this._line?this._context.lineTo(a[0],b[0]):this._context.moveTo(a[0],b[0]),2===c)this._context.lineTo(a[1],b[1]);else for(var d=bI(a),e=bI(b),f=0,g=1;g<c;++f,++g)this._context.bezierCurveTo(d[0][f],e[0][f],d[1][f],e[1][f],a[g],b[g]);(this._line||0!==this._line&&1===c)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(a,b){this._x.push(+a),this._y.push(+b)}},bJ.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(a,b){switch(a*=1,b*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(a,b):this._context.moveTo(a,b);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,b),this._context.lineTo(a,b);else{var c=this._x*(1-this._t)+a*this._t;this._context.lineTo(c,this._y),this._context.lineTo(c,b)}}this._x=a,this._y=b}};var bK=c(48657),bL=c(22786),bM=c(15606);function bN(a){return a[0]}function bO(a){return a[1]}function bP(a,b){var c=(0,bL.A)(!0),d=null,e=bA,f=null,g=(0,bM.i)(h);function h(h){var i,j,k,l=(h=(0,bK.A)(h)).length,m=!1;for(null==d&&(f=e(k=g())),i=0;i<=l;++i)!(i<l&&c(j=h[i],i,h))===m&&((m=!m)?f.lineStart():f.lineEnd()),m&&f.point(+a(j,i,h),+b(j,i,h));if(k)return f=null,k+""||null}return a="function"==typeof a?a:void 0===a?bN:(0,bL.A)(a),b="function"==typeof b?b:void 0===b?bO:(0,bL.A)(b),h.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,bL.A)(+b),h):a},h.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,bL.A)(+a),h):b},h.defined=function(a){return arguments.length?(c="function"==typeof a?a:(0,bL.A)(!!a),h):c},h.curve=function(a){return arguments.length?(e=a,null!=d&&(f=e(d)),h):e},h.context=function(a){return arguments.length?(null==a?d=f=null:f=e(d=a),h):d},h}function bQ(a,b,c){var d=null,e=(0,bL.A)(!0),f=null,g=bA,h=null,i=(0,bM.i)(j);function j(j){var k,l,m,n,o,p=(j=(0,bK.A)(j)).length,q=!1,r=Array(p),s=Array(p);for(null==f&&(h=g(o=i())),k=0;k<=p;++k){if(!(k<p&&e(n=j[k],k,j))===q)if(q=!q)l=k,h.areaStart(),h.lineStart();else{for(h.lineEnd(),h.lineStart(),m=k-1;m>=l;--m)h.point(r[m],s[m]);h.lineEnd(),h.areaEnd()}q&&(r[k]=+a(n,k,j),s[k]=+b(n,k,j),h.point(d?+d(n,k,j):r[k],c?+c(n,k,j):s[k]))}if(o)return h=null,o+""||null}function k(){return bP().defined(e).curve(g).context(f)}return a="function"==typeof a?a:void 0===a?bN:(0,bL.A)(+a),b="function"==typeof b?b:void 0===b?(0,bL.A)(0):(0,bL.A)(+b),c="function"==typeof c?c:void 0===c?bO:(0,bL.A)(+c),j.x=function(b){return arguments.length?(a="function"==typeof b?b:(0,bL.A)(+b),d=null,j):a},j.x0=function(b){return arguments.length?(a="function"==typeof b?b:(0,bL.A)(+b),j):a},j.x1=function(a){return arguments.length?(d=null==a?null:"function"==typeof a?a:(0,bL.A)(+a),j):d},j.y=function(a){return arguments.length?(b="function"==typeof a?a:(0,bL.A)(+a),c=null,j):b},j.y0=function(a){return arguments.length?(b="function"==typeof a?a:(0,bL.A)(+a),j):b},j.y1=function(a){return arguments.length?(c=null==a?null:"function"==typeof a?a:(0,bL.A)(+a),j):c},j.lineX0=j.lineY0=function(){return k().x(a).y(b)},j.lineY1=function(){return k().x(a).y(c)},j.lineX1=function(){return k().x(d).y(b)},j.defined=function(a){return arguments.length?(e="function"==typeof a?a:(0,bL.A)(!!a),j):e},j.curve=function(a){return arguments.length?(g=a,null!=f&&(h=g(f)),j):g},j.context=function(a){return arguments.length?(null==a?f=h=null:h=g(f=a),j):f},j}var bR=c(69433),bS=c.n(bR);function bT(a){return(bT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function bU(){return(bU=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function bV(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function bW(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?bV(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=bT(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=bT(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==bT(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):bV(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var bX={curveBasisClosed:function(a){return new bv(a)},curveBasisOpen:function(a){return new bw(a)},curveBasis:function(a){return new bu(a)},curveBumpX:function(a){return new bx(a,!0)},curveBumpY:function(a){return new bx(a,!1)},curveLinearClosed:function(a){return new by(a)},curveLinear:bA,curveMonotoneX:function(a){return new bE(a)},curveMonotoneY:function(a){return new bF(a)},curveNatural:function(a){return new bH(a)},curveStep:function(a){return new bJ(a,.5)},curveStepAfter:function(a){return new bJ(a,1)},curveStepBefore:function(a){return new bJ(a,0)}},bY=function(a){return a.x===+a.x&&a.y===+a.y},bZ=function(a){return a.x},b$=function(a){return a.y},b_=function(a,b){if(i()(a))return a;var c="curve".concat(bS()(a));return("curveMonotone"===c||"curveBump"===c)&&b?bX["".concat(c).concat("vertical"===b?"Y":"X")]:bX[c]||bA},b0=function(a){var b,c=a.type,d=a.points,e=void 0===d?[]:d,f=a.baseLine,g=a.layout,h=a.connectNulls,i=void 0!==h&&h,j=b_(void 0===c?"linear":c,g),k=i?e.filter(function(a){return bY(a)}):e;if(Array.isArray(f)){var l=i?f.filter(function(a){return bY(a)}):f,m=k.map(function(a,b){return bW(bW({},a),{},{base:l[b]})});return(b="vertical"===g?bQ().y(b$).x1(bZ).x0(function(a){return a.base.x}):bQ().x(bZ).y1(b$).y0(function(a){return a.base.y})).defined(bY).curve(j),b(m)}return(b="vertical"===g&&(0,F.Et)(f)?bQ().y(b$).x1(bZ).x0(f):(0,F.Et)(f)?bQ().x(bZ).y1(b$).y0(f):bP().x(bZ).y(b$)).defined(bY).curve(j),b(k)},b1=function(a){var b=a.className,c=a.points,d=a.path,f=a.pathRef;if((!c||!c.length)&&!d)return null;var g=c&&c.length?b0(a):d;return e().createElement("path",bU({},(0,y.J9)(a,!1),(0,x._U)(a),{className:(0,r.A)("recharts-curve",b),d:g,ref:f}))};function b2(a){return(b2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var b3=["x","y","top","left","width","height","className"];function b4(){return(b4=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function b5(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}var b6=function(a){var b=a.x,c=void 0===b?0:b,d=a.y,f=void 0===d?0:d,g=a.top,h=void 0===g?0:g,i=a.left,j=void 0===i?0:i,k=a.width,l=void 0===k?0:k,m=a.height,n=void 0===m?0:m,o=a.className,p=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?b5(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=b2(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=b2(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==b2(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):b5(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({x:c,y:f,top:h,left:j,width:l,height:n},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,b3));return(0,F.Et)(c)&&(0,F.Et)(f)&&(0,F.Et)(l)&&(0,F.Et)(n)&&(0,F.Et)(h)&&(0,F.Et)(j)?e().createElement("path",b4({},(0,y.J9)(p,!0),{className:(0,r.A)("recharts-cross",o),d:"M".concat(c,",").concat(h,"v").concat(n,"M").concat(j,",").concat(f,"h").concat(l)})):null};function b7(a){var b=a.cx,c=a.cy,d=a.radius,e=a.startAngle,f=a.endAngle;return{points:[(0,bi.IZ)(b,c,d,e),(0,bi.IZ)(b,c,d,f)],cx:b,cy:c,radius:d,startAngle:e,endAngle:f}}var b8=c(34955);function b9(a){return(b9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function ca(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cb(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ca(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=b9(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=b9(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==b9(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ca(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cc(a){var b,c,e,f,g=a.element,h=a.tooltipEventType,i=a.isActive,j=a.activeCoordinate,k=a.activePayload,l=a.offset,m=a.activeTooltipIndex,n=a.tooltipAxisBandSize,o=a.layout,p=a.chartName,q=null!=(c=g.props.cursor)?c:null==(e=g.type.defaultProps)?void 0:e.cursor;if(!g||!q||!i||!j||"ScatterChart"!==p&&"axis"!==h)return null;var s=b1;if("ScatterChart"===p)f=j,s=b6;else if("BarChart"===p)b=n/2,f={stroke:"none",fill:"#ccc",x:"horizontal"===o?j.x-b:l.left+.5,y:"horizontal"===o?l.top+.5:j.y-b,width:"horizontal"===o?n:l.width-1,height:"horizontal"===o?l.height-1:n},s=B.M;else if("radial"===o){var t=b7(j),u=t.cx,v=t.cy,w=t.radius;f={cx:u,cy:v,startAngle:t.startAngle,endAngle:t.endAngle,innerRadius:w,outerRadius:w},s=b8.h}else f={points:function(a,b,c){var d,e,f,g;if("horizontal"===a)f=d=b.x,e=c.top,g=c.top+c.height;else if("vertical"===a)g=e=b.y,d=c.left,f=c.left+c.width;else if(null!=b.cx&&null!=b.cy)if("centric"!==a)return b7(b);else{var h=b.cx,i=b.cy,j=b.innerRadius,k=b.outerRadius,l=b.angle,m=(0,bi.IZ)(h,i,j,l),n=(0,bi.IZ)(h,i,k,l);d=m.x,e=m.y,f=n.x,g=n.y}return[{x:d,y:e},{x:f,y:g}]}(o,j,l)},s=b1;var x=cb(cb(cb(cb({stroke:"#ccc",pointerEvents:"none"},l),f),(0,y.J9)(q,!1)),{},{payload:k,payloadIndex:m,className:(0,r.A)("recharts-tooltip-cursor",q.className)});return(0,d.isValidElement)(q)?(0,d.cloneElement)(q,x):(0,d.createElement)(s,x)}var cd=["item"],ce=["children","className","width","height","style","compact","title","desc"];function cf(a){return(cf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function cg(){return(cg=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function ch(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{if(f=(c=c.call(a)).next,0===b){if(Object(c)!==c)return;i=!1}else for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(a,b)||cn(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ci(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function cj(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(cj=function(){return!!a})()}function ck(a){return(ck=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function cl(a,b){return(cl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function cm(a){return function(a){if(Array.isArray(a))return co(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||cn(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cn(a,b){if(a){if("string"==typeof a)return co(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return co(a,b)}}function co(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function cp(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cq(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cp(Object(c),!0).forEach(function(b){cr(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cp(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cr(a,b,c){return(b=cs(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function cs(a){var b=function(a,b){if("object"!=cf(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=cf(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==cf(b)?b:b+""}var ct={xAxis:["bottom","top"],yAxis:["left","right"]},cu={width:"100%",height:"100%"},cv={x:0,y:0};function cw(a){return a}var cx=function(a,b,c,d){var e=b.find(function(a){return a&&a.index===c});if(e){if("horizontal"===a)return{x:e.coordinate,y:d.y};if("vertical"===a)return{x:d.x,y:e.coordinate};if("centric"===a){var f=e.coordinate,g=d.radius;return cq(cq(cq({},d),(0,bi.IZ)(d.cx,d.cy,g,f)),{},{angle:f,radius:g})}var h=e.coordinate,i=d.angle;return cq(cq(cq({},d),(0,bi.IZ)(d.cx,d.cy,h,i)),{},{angle:i,radius:h})}return cv},cy=function(a,b){var c=b.graphicalItems,d=b.dataStartIndex,e=b.dataEndIndex,f=(null!=c?c:[]).reduce(function(a,b){var c=b.props.data;return c&&c.length?[].concat(cm(a),cm(c)):a},[]);return f.length>0?f:a&&a.length&&(0,F.Et)(d)&&(0,F.Et)(e)?a.slice(d,e+1):[]};function cz(a){return"number"===a?[0,"auto"]:void 0}var cA=function(a,b,c,d){var e=a.graphicalItems,f=a.tooltipAxis,g=cy(b,a);return c<0||!e||!e.length||c>=g.length?null:e.reduce(function(e,h){var i,j,k=null!=(i=h.props.data)?i:b;if(k&&a.dataStartIndex+a.dataEndIndex!==0&&a.dataEndIndex-a.dataStartIndex>=c&&(k=k.slice(a.dataStartIndex,a.dataEndIndex+1)),f.dataKey&&!f.allowDuplicatedCategory){var l=void 0===k?g:k;j=(0,F.eP)(l,f.dataKey,d)}else j=k&&k[c]||g[c];return j?[].concat(cm(e),[(0,E.zb)(h,j)]):e},[])},cB=function(a,b,c,d){var e=d||{x:a.chartX,y:a.chartY},f="horizontal"===c?e.x:"vertical"===c?e.y:"centric"===c?e.angle:e.radius,g=a.orderedTooltipTicks,h=a.tooltipAxis,i=a.tooltipTicks,j=(0,E.gH)(f,g,i,h);if(j>=0&&i){var k=i[j]&&i[j].value,l=cA(a,b,j,k),m=cx(c,g,j,e);return{activeTooltipIndex:j,activeLabel:k,activePayload:l,activeCoordinate:m}}return null},cC=function(a,b){var c=b.axes,d=b.graphicalItems,e=b.axisType,f=b.axisIdKey,h=b.stackGroups,i=b.dataStartIndex,j=b.dataEndIndex,l=a.layout,m=a.children,n=a.stackOffset,o=(0,E._L)(l,e);return c.reduce(function(b,c){var p=void 0!==c.type.defaultProps?cq(cq({},c.type.defaultProps),c.props):c.props,q=p.type,r=p.dataKey,s=p.allowDataOverflow,t=p.allowDuplicatedCategory,u=p.scale,v=p.ticks,w=p.includeHidden,x=p[f];if(b[x])return b;var y=cy(a.data,{graphicalItems:d.filter(function(a){var b;return(f in a.props?a.props[f]:null==(b=a.type.defaultProps)?void 0:b[f])===x}),dataStartIndex:i,dataEndIndex:j}),z=y.length;(function(a,b,c){if("number"===c&&!0===b&&Array.isArray(a)){var d=null==a?void 0:a[0],e=null==a?void 0:a[1];if(d&&e&&(0,F.Et)(d)&&(0,F.Et)(e))return!0}return!1})(p.domain,s,q)&&(C=(0,E.AQ)(p.domain,null,s),o&&("number"===q||"auto"!==u)&&(G=(0,E.Ay)(y,r,"category")));var A=cz(q);if(!C||0===C.length){var B,C,D,G,H,I=null!=(H=p.domain)?H:A;if(r){if(C=(0,E.Ay)(y,r,q),"category"===q&&o){var J=(0,F.CG)(C);t&&J?(D=C,C=k()(0,z)):t||(C=(0,E.KC)(I,C,c).reduce(function(a,b){return a.indexOf(b)>=0?a:[].concat(cm(a),[b])},[]))}else if("category"===q)C=t?C.filter(function(a){return""!==a&&!g()(a)}):(0,E.KC)(I,C,c).reduce(function(a,b){return a.indexOf(b)>=0||""===b||g()(b)?a:[].concat(cm(a),[b])},[]);else if("number"===q){var K=(0,E.A1)(y,d.filter(function(a){var b,c,d=f in a.props?a.props[f]:null==(b=a.type.defaultProps)?void 0:b[f],e="hide"in a.props?a.props.hide:null==(c=a.type.defaultProps)?void 0:c.hide;return d===x&&(w||!e)}),r,e,l);K&&(C=K)}o&&("number"===q||"auto"!==u)&&(G=(0,E.Ay)(y,r,"category"))}else C=o?k()(0,z):h&&h[x]&&h[x].hasStack&&"number"===q?"expand"===n?[0,1]:(0,E.Mk)(h[x].stackGroups,i,j):(0,E.vf)(y,d.filter(function(a){var b=f in a.props?a.props[f]:a.type.defaultProps[f],c="hide"in a.props?a.props.hide:a.type.defaultProps.hide;return b===x&&(w||!c)}),q,l,!0);"number"===q?(C=bh(m,C,x,e,v),I&&(C=(0,E.AQ)(I,C,s))):"category"===q&&I&&C.every(function(a){return I.indexOf(a)>=0})&&(C=I)}return cq(cq({},b),{},cr({},x,cq(cq({},p),{},{axisType:e,domain:C,categoricalDomain:G,duplicateDomain:D,originalDomain:null!=(B=p.domain)?B:A,isCategorical:o,layout:l})))},{})},cD=function(a,b){var c=b.graphicalItems,d=b.Axis,e=b.axisType,f=b.axisIdKey,g=b.stackGroups,h=b.dataStartIndex,i=b.dataEndIndex,j=a.layout,l=a.children,n=cy(a.data,{graphicalItems:c,dataStartIndex:h,dataEndIndex:i}),o=n.length,p=(0,E._L)(j,e),q=-1;return c.reduce(function(a,b){var r,s=(void 0!==b.type.defaultProps?cq(cq({},b.type.defaultProps),b.props):b.props)[f],t=cz("number");return a[s]?a:(q++,r=p?k()(0,o):g&&g[s]&&g[s].hasStack?bh(l,r=(0,E.Mk)(g[s].stackGroups,h,i),s,e):bh(l,r=(0,E.AQ)(t,(0,E.vf)(n,c.filter(function(a){var b,c,d=f in a.props?a.props[f]:null==(b=a.type.defaultProps)?void 0:b[f],e="hide"in a.props?a.props.hide:null==(c=a.type.defaultProps)?void 0:c.hide;return d===s&&!e}),"number",j),d.defaultProps.allowDataOverflow),s,e),cq(cq({},a),{},cr({},s,cq(cq({axisType:e},d.defaultProps),{},{hide:!0,orientation:m()(ct,"".concat(e,".").concat(q%2),null),domain:r,originalDomain:t,isCategorical:p,layout:j}))))},{})},cE=function(a,b){var c=b.axisType,d=void 0===c?"xAxis":c,e=b.AxisComp,f=b.graphicalItems,g=b.stackGroups,h=b.dataStartIndex,i=b.dataEndIndex,j=a.children,k="".concat(d,"Id"),l=(0,y.aS)(j,e),m={};return l&&l.length?m=cC(a,{axes:l,graphicalItems:f,axisType:d,axisIdKey:k,stackGroups:g,dataStartIndex:h,dataEndIndex:i}):f&&f.length&&(m=cD(a,{Axis:e,graphicalItems:f,axisType:d,axisIdKey:k,stackGroups:g,dataStartIndex:h,dataEndIndex:i})),m},cF=function(a){var b=(0,F.lX)(a),c=(0,E.Rh)(b,!1,!0);return{tooltipTicks:c,orderedTooltipTicks:o()(c,function(a){return a.coordinate}),tooltipAxis:b,tooltipAxisBandSize:(0,E.Hj)(b,c)}},cG=function(a){var b=a.children,c=a.defaultShowTooltip,d=(0,y.BU)(b,Y),e=0,f=0;return a.data&&0!==a.data.length&&(f=a.data.length-1),d&&d.props&&(d.props.startIndex>=0&&(e=d.props.startIndex),d.props.endIndex>=0&&(f=d.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:e,dataEndIndex:f,activeTooltipIndex:-1,isTooltipActive:!!c}},cH=function(a){return"horizontal"===a?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===a?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===a?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},cI=function(a,b){var c=a.props,d=a.graphicalItems,e=a.xAxisMap,f=void 0===e?{}:e,g=a.yAxisMap,h=void 0===g?{}:g,i=c.width,j=c.height,k=c.children,l=c.margin||{},n=(0,y.BU)(k,Y),o=(0,y.BU)(k,w.s),p=Object.keys(h).reduce(function(a,b){var c=h[b],d=c.orientation;return c.mirror||c.hide?a:cq(cq({},a),{},cr({},d,a[d]+c.width))},{left:l.left||0,right:l.right||0}),q=Object.keys(f).reduce(function(a,b){var c=f[b],d=c.orientation;return c.mirror||c.hide?a:cq(cq({},a),{},cr({},d,m()(a,"".concat(d))+c.height))},{top:l.top||0,bottom:l.bottom||0}),r=cq(cq({},q),p),s=r.bottom;n&&(r.bottom+=n.props.height||Y.defaultProps.height),o&&b&&(r=(0,E.s0)(r,d,c,b));var t=i-r.left-r.right,u=j-r.top-r.bottom;return cq(cq({brushBottom:s},r),{},{width:Math.max(t,0),height:Math.max(u,0)})},cJ=c(20237);function cK(a,b,c){if(b<1)return[];if(1===b&&void 0===c)return a;for(var d=[],e=0;e<a.length;e+=b)if(void 0!==c&&!0!==c(a[e]))return;else d.push(a[e]);return d}function cL(a,b,c,d,e){if(a*b<a*d||a*b>a*e)return!1;var f=c();return a*(b-a*f/2-d)>=0&&a*(b+a*f/2-e)<=0}function cM(a){return(cM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function cN(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cO(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cN(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=cM(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=cM(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==cM(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cN(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var cP=["viewBox"],cQ=["viewBox"],cR=["ticks"];function cS(a){return(cS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function cT(){return(cT=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function cU(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function cV(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?cU(Object(c),!0).forEach(function(b){c_(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):cU(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function cW(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function cX(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,c0(d.key),d)}}function cY(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(cY=function(){return!!a})()}function cZ(a){return(cZ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function c$(a,b){return(c$=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function c_(a,b,c){return(b=c0(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function c0(a){var b=function(a,b){if("object"!=cS(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=cS(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==cS(b)?b:b+""}var c1=function(a){var b,c;function d(a){var b,c,e;if(!(this instanceof d))throw TypeError("Cannot call a class as a function");return c=d,e=[a],c=cZ(c),(b=function(a,b){if(b&&("object"===cS(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,cY()?Reflect.construct(c,e||[],cZ(this).constructor):c.apply(this,e))).state={fontSize:"",letterSpacing:""},b}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return d.prototype=Object.create(a&&a.prototype,{constructor:{value:d,writable:!0,configurable:!0}}),Object.defineProperty(d,"prototype",{writable:!1}),a&&c$(d,a),b=[{key:"shouldComponentUpdate",value:function(a,b){var c=a.viewBox,d=cW(a,cP),e=this.props,f=e.viewBox,g=cW(e,cQ);return!(0,bj.b)(c,f)||!(0,bj.b)(d,g)||!(0,bj.b)(b,this.state)}},{key:"componentDidMount",value:function(){var a=this.layerReference;if(a){var b=a.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];b&&this.setState({fontSize:window.getComputedStyle(b).fontSize,letterSpacing:window.getComputedStyle(b).letterSpacing})}}},{key:"getTickLineCoord",value:function(a){var b,c,d,e,f,g,h=this.props,i=h.x,j=h.y,k=h.width,l=h.height,m=h.orientation,n=h.tickSize,o=h.mirror,p=h.tickMargin,q=o?-1:1,r=a.tickSize||n,s=(0,F.Et)(a.tickCoord)?a.tickCoord:a.coordinate;switch(m){case"top":b=c=a.coordinate,g=(d=(e=j+!o*l)-q*r)-q*p,f=s;break;case"left":d=e=a.coordinate,f=(b=(c=i+!o*k)-q*r)-q*p,g=s;break;case"right":d=e=a.coordinate,f=(b=(c=i+o*k)+q*r)+q*p,g=s;break;default:b=c=a.coordinate,g=(d=(e=j+o*l)+q*r)+q*p,f=s}return{line:{x1:b,y1:d,x2:c,y2:e},tick:{x:f,y:g}}}},{key:"getTickTextAnchor",value:function(){var a,b=this.props,c=b.orientation,d=b.mirror;switch(c){case"left":a=d?"start":"end";break;case"right":a=d?"end":"start";break;default:a="middle"}return a}},{key:"getTickVerticalAnchor",value:function(){var a=this.props,b=a.orientation,c=a.mirror,d="end";switch(b){case"left":case"right":d="middle";break;case"top":d=c?"start":"end";break;default:d=c?"end":"start"}return d}},{key:"renderAxisLine",value:function(){var a=this.props,b=a.x,c=a.y,d=a.width,f=a.height,g=a.orientation,h=a.mirror,i=a.axisLine,j=cV(cV(cV({},(0,y.J9)(this.props,!1)),(0,y.J9)(i,!1)),{},{fill:"none"});if("top"===g||"bottom"===g){var k=+("top"===g&&!h||"bottom"===g&&h);j=cV(cV({},j),{},{x1:b,y1:c+k*f,x2:b+d,y2:c+k*f})}else{var l=+("left"===g&&!h||"right"===g&&h);j=cV(cV({},j),{},{x1:b+l*d,y1:c,x2:b+l*d,y2:c+f})}return e().createElement("line",cT({},j,{className:(0,r.A)("recharts-cartesian-axis-line",m()(i,"className"))}))}},{key:"renderTicks",value:function(a,b,c){var f=this,g=this.props,h=g.tickLine,j=g.stroke,k=g.tick,l=g.tickFormatter,n=g.unit,o=function(a,b,c){var d,e,f,g,h,j=a.tick,k=a.ticks,l=a.viewBox,m=a.minTickGap,n=a.orientation,o=a.interval,p=a.tickFormatter,q=a.unit,r=a.angle;if(!k||!k.length||!j)return[];if((0,F.Et)(o)||cJ.m.isSsr)return cK(k,("number"==typeof o&&(0,F.Et)(o)?o:0)+1);var s="top"===n||"bottom"===n?"width":"height",t=q&&"width"===s?(0,Z.Pu)(q,{fontSize:b,letterSpacing:c}):{width:0,height:0},u=function(a,d){var e,f=i()(p)?p(a.value,d):a.value;return"width"===s?(e=(0,Z.Pu)(f,{fontSize:b,letterSpacing:c}),ap({width:e.width+t.width,height:e.height+t.height},r)):(0,Z.Pu)(f,{fontSize:b,letterSpacing:c})[s]},v=k.length>=2?(0,F.sA)(k[1].coordinate-k[0].coordinate):1,w=(d="width"===s,e=l.x,f=l.y,g=l.width,h=l.height,1===v?{start:d?e:f,end:d?e+g:f+h}:{start:d?e+g:f+h,end:d?e:f});return"equidistantPreserveStart"===o?function(a,b,c,d,e){for(var f,g=(d||[]).slice(),h=b.start,i=b.end,j=0,k=1,l=h;k<=g.length;)if(f=function(){var b,f=null==d?void 0:d[j];if(void 0===f)return{v:cK(d,k)};var g=j,m=function(){return void 0===b&&(b=c(f,g)),b},n=f.coordinate,o=0===j||cL(a,n,m,l,i);o||(j=0,l=h,k+=1),o&&(l=n+a*(m()/2+e),j+=k)}())return f.v;return[]}(v,w,u,k,m):("preserveStart"===o||"preserveStartEnd"===o?function(a,b,c,d,e,f){var g=(d||[]).slice(),h=g.length,i=b.start,j=b.end;if(f){var k=d[h-1],l=c(k,h-1),m=a*(k.coordinate+a*l/2-j);g[h-1]=k=cO(cO({},k),{},{tickCoord:m>0?k.coordinate-m*a:k.coordinate}),cL(a,k.tickCoord,function(){return l},i,j)&&(j=k.tickCoord-a*(l/2+e),g[h-1]=cO(cO({},k),{},{isShow:!0}))}for(var n=f?h-1:h,o=function(b){var d,f=g[b],h=function(){return void 0===d&&(d=c(f,b)),d};if(0===b){var k=a*(f.coordinate-a*h()/2-i);g[b]=f=cO(cO({},f),{},{tickCoord:k<0?f.coordinate-k*a:f.coordinate})}else g[b]=f=cO(cO({},f),{},{tickCoord:f.coordinate});cL(a,f.tickCoord,h,i,j)&&(i=f.tickCoord+a*(h()/2+e),g[b]=cO(cO({},f),{},{isShow:!0}))},p=0;p<n;p++)o(p);return g}(v,w,u,k,m,"preserveStartEnd"===o):function(a,b,c,d,e){for(var f=(d||[]).slice(),g=f.length,h=b.start,i=b.end,j=function(b){var d,j=f[b],k=function(){return void 0===d&&(d=c(j,b)),d};if(b===g-1){var l=a*(j.coordinate+a*k()/2-i);f[b]=j=cO(cO({},j),{},{tickCoord:l>0?j.coordinate-l*a:j.coordinate})}else f[b]=j=cO(cO({},j),{},{tickCoord:j.coordinate});cL(a,j.tickCoord,k,h,i)&&(i=j.tickCoord-a*(k()/2+e),f[b]=cO(cO({},j),{},{isShow:!0}))},k=g-1;k>=0;k--)j(k);return f}(v,w,u,k,m)).filter(function(a){return a.isShow})}(cV(cV({},this.props),{},{ticks:a}),b,c),p=this.getTickTextAnchor(),q=this.getTickVerticalAnchor(),s=(0,y.J9)(this.props,!1),t=(0,y.J9)(k,!1),v=cV(cV({},s),{},{fill:"none"},(0,y.J9)(h,!1)),w=o.map(function(a,b){var c=f.getTickLineCoord(a),g=c.line,w=c.tick,y=cV(cV(cV(cV({textAnchor:p,verticalAnchor:q},s),{},{stroke:"none",fill:j},t),w),{},{index:b,payload:a,visibleTicksCount:o.length,tickFormatter:l});return e().createElement(u.W,cT({className:"recharts-cartesian-axis-tick",key:"tick-".concat(a.value,"-").concat(a.coordinate,"-").concat(a.tickCoord)},(0,x.XC)(f.props,a,b)),h&&e().createElement("line",cT({},v,g,{className:(0,r.A)("recharts-cartesian-axis-tick-line",m()(h,"className"))})),k&&d.renderTickItem(k,y,"".concat(i()(l)?l(a.value,b):a.value).concat(n||"")))});return e().createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var a=this,b=this.props,c=b.axisLine,d=b.width,f=b.height,g=b.ticksGenerator,h=b.className;if(b.hide)return null;var j=this.props,k=j.ticks,l=cW(j,cR),m=k;return(i()(g)&&(m=g(k&&k.length>0?this.props:l)),d<=0||f<=0||!m||!m.length)?null:e().createElement(u.W,{className:(0,r.A)("recharts-cartesian-axis",h),ref:function(b){a.layerReference=b}},c&&this.renderAxisLine(),this.renderTicks(m,this.state.fontSize,this.state.letterSpacing),_.J.renderCallByParent(this.props))}}],c=[{key:"renderTickItem",value:function(a,b,c){return e().isValidElement(a)?e().cloneElement(a,b):i()(a)?a(b):e().createElement(D.E,cT({},b,{className:"recharts-cartesian-axis-tick-value"}),c)}}],b&&cX(d.prototype,b),c&&cX(d,c),Object.defineProperty(d,"prototype",{writable:!1}),d}(d.Component);function c2(a){return(c2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}c_(c1,"displayName","CartesianAxis"),c_(c1,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function c3(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(c3=function(){return!!a})()}function c4(a){return(c4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function c5(a,b){return(c5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function c6(a,b,c){return(b=c7(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function c7(a){var b=function(a,b){if("object"!=c2(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=c2(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==c2(b)?b:b+""}function c8(){return(c8=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function c9(a){var b=a.xAxisId,c=aQ(),d=aR(),f=aO(b);return null==f?null:e().createElement(c1,c8({},f,{className:(0,r.A)("recharts-".concat(f.axisType," ").concat(f.axisType),f.className),viewBox:{x:0,y:0,width:c,height:d},ticksGenerator:function(a){return(0,E.Rh)(a,!0)}}))}var da=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=c4(a),function(a,b){if(b&&("object"===c2(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,c3()?Reflect.construct(a,b||[],c4(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&c5(c,a),b=[{key:"render",value:function(){return e().createElement(c9,this.props)}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,c7(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);function db(a){return(db="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}c6(da,"displayName","XAxis"),c6(da,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function dc(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(dc=function(){return!!a})()}function dd(a){return(dd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function de(a,b){return(de=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function df(a,b,c){return(b=dg(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function dg(a){var b=function(a,b){if("object"!=db(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=db(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==db(b)?b:b+""}function dh(){return(dh=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var di=function(a){var b=a.yAxisId,c=aQ(),d=aR(),f=aP(b);return null==f?null:e().createElement(c1,dh({},f,{className:(0,r.A)("recharts-".concat(f.axisType," ").concat(f.axisType),f.className),viewBox:{x:0,y:0,width:c,height:d},ticksGenerator:function(a){return(0,E.Rh)(a,!0)}}))},dj=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=dd(a),function(a,b){if(b&&("object"===db(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,dc()?Reflect.construct(a,b||[],dd(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&de(c,a),b=[{key:"render",value:function(){return e().createElement(di,this.props)}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,dg(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);df(dj,"displayName","YAxis"),df(dj,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dk=function(a){var b=a.chartName,c=a.GraphicalChild,f=a.defaultTooltipEventType,h=void 0===f?"axis":f,j=a.validateTooltipEventTypes,k=void 0===j?["axis"]:j,l=a.axisComponents,n=a.legendContent,o=a.formatAxisMap,p=a.defaultProps,w=function(a,b){var c=b.graphicalItems,d=b.stackGroups,e=b.offset,f=b.updateId,h=b.dataStartIndex,i=b.dataEndIndex,j=a.barSize,k=a.layout,m=a.barGap,n=a.barCategoryGap,o=a.maxBarSize,p=cH(k),q=p.numericAxisName,r=p.cateAxisName,t=!!c&&!!c.length&&c.some(function(a){var b=(0,y.Mn)(a&&a.type);return b&&b.indexOf("Bar")>=0}),u=[];return c.forEach(function(c,p){var v=cy(a.data,{graphicalItems:[c],dataStartIndex:h,dataEndIndex:i}),w=void 0!==c.type.defaultProps?cq(cq({},c.type.defaultProps),c.props):c.props,x=w.dataKey,z=w.maxBarSize,A=w["".concat(q,"Id")],B=w["".concat(r,"Id")],C=l.reduce(function(a,c){var d=b["".concat(c.axisType,"Map")],e=w["".concat(c.axisType,"Id")];d&&d[e]||"zAxis"===c.axisType||(0,s.A)(!1);var f=d[e];return cq(cq({},a),{},cr(cr({},c.axisType,f),"".concat(c.axisType,"Ticks"),(0,E.Rh)(f)))},{}),D=C[r],F=C["".concat(r,"Ticks")],G=d&&d[A]&&d[A].hasStack&&(0,E.kA)(c,d[A].stackGroups),H=(0,y.Mn)(c.type).indexOf("Bar")>=0,I=(0,E.Hj)(D,F),J=[],K=t&&(0,E.tA)({barSize:j,stackGroups:d,totalSize:"xAxis"===r?C[r].width:"yAxis"===r?C[r].height:void 0});if(H){var L,M,N=g()(z)?o:z,O=null!=(L=null!=(M=(0,E.Hj)(D,F,!0))?M:N)?L:0;J=(0,E.BX)({barGap:m,barCategoryGap:n,bandSize:O!==I?O:I,sizeList:K[B],maxBarSize:N}),O!==I&&(J=J.map(function(a){return cq(cq({},a),{},{position:cq(cq({},a.position),{},{offset:a.position.offset-O/2})})}))}var P=c&&c.type&&c.type.getComposedData;P&&u.push({props:cq(cq({},P(cq(cq({},C),{},{displayedData:v,props:a,dataKey:x,item:c,bandSize:I,barPosition:J,offset:e,stackedData:G,layout:k,dataStartIndex:h,dataEndIndex:i}))),{},cr(cr(cr({key:c.key||"item-".concat(p)},q,C[q]),r,C[r]),"animationId",f)),childIndex:(0,y.AW)(c,a.children),item:c})}),u},z=function(a,d){var e=a.props,f=a.dataStartIndex,g=a.dataEndIndex,h=a.updateId;if(!(0,y.Me)({props:e}))return null;var i=e.children,j=e.layout,k=e.stackOffset,m=e.data,n=e.reverseStackOrder,p=cH(j),q=p.numericAxisName,r=p.cateAxisName,s=(0,y.aS)(i,c),t=(0,E.Mn)(m,s,"".concat(q,"Id"),"".concat(r,"Id"),k,n),u=l.reduce(function(a,b){var c="".concat(b.axisType,"Map");return cq(cq({},a),{},cr({},c,cE(e,cq(cq({},b),{},{graphicalItems:s,stackGroups:b.axisType===q&&t,dataStartIndex:f,dataEndIndex:g}))))},{}),v=cI(cq(cq({},u),{},{props:e,graphicalItems:s}),null==d?void 0:d.legendBBox);Object.keys(u).forEach(function(a){u[a]=o(e,u[a],v,a.replace("Map",""),b)});var x=cF(u["".concat(r,"Map")]),z=w(e,cq(cq({},u),{},{dataStartIndex:f,dataEndIndex:g,updateId:h,graphicalItems:s,stackGroups:t,offset:v}));return cq(cq({formattedGraphicalItems:z,graphicalItems:s,offset:v,stackGroups:t},x),u)},C=function(a){var c;function f(a){var c,h,j,k,l;if(!(this instanceof f))throw TypeError("Cannot call a class as a function");return k=f,l=[a],k=ck(k),cr(j=function(a,b){if(b&&("object"===cf(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,cj()?Reflect.construct(k,l||[],ck(this).constructor):k.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),cr(j,"accessibilityManager",new bq),cr(j,"handleLegendBBoxUpdate",function(a){if(a){var b=j.state,c=b.dataStartIndex,d=b.dataEndIndex,e=b.updateId;j.setState(cq({legendBBox:a},z({props:j.props,dataStartIndex:c,dataEndIndex:d,updateId:e},cq(cq({},j.state),{},{legendBBox:a}))))}}),cr(j,"handleReceiveSyncEvent",function(a,b,c){j.props.syncId===a&&(c!==j.eventEmitterSymbol||"function"==typeof j.props.syncMethod)&&j.applySyncEvent(b)}),cr(j,"handleBrushChange",function(a){var b=a.startIndex,c=a.endIndex;if(b!==j.state.dataStartIndex||c!==j.state.dataEndIndex){var d=j.state.updateId;j.setState(function(){return cq({dataStartIndex:b,dataEndIndex:c},z({props:j.props,dataStartIndex:b,dataEndIndex:c,updateId:d},j.state))}),j.triggerSyncEvent({dataStartIndex:b,dataEndIndex:c})}}),cr(j,"handleMouseEnter",function(a){var b=j.getMouseInfo(a);if(b){var c=cq(cq({},b),{},{isTooltipActive:!0});j.setState(c),j.triggerSyncEvent(c);var d=j.props.onMouseEnter;i()(d)&&d(c,a)}}),cr(j,"triggeredAfterMouseMove",function(a){var b=j.getMouseInfo(a),c=b?cq(cq({},b),{},{isTooltipActive:!0}):{isTooltipActive:!1};j.setState(c),j.triggerSyncEvent(c);var d=j.props.onMouseMove;i()(d)&&d(c,a)}),cr(j,"handleItemMouseEnter",function(a){j.setState(function(){return{isTooltipActive:!0,activeItem:a,activePayload:a.tooltipPayload,activeCoordinate:a.tooltipPosition||{x:a.cx,y:a.cy}}})}),cr(j,"handleItemMouseLeave",function(){j.setState(function(){return{isTooltipActive:!1}})}),cr(j,"handleMouseMove",function(a){a.persist(),j.throttleTriggeredAfterMouseMove(a)}),cr(j,"handleMouseLeave",function(a){j.throttleTriggeredAfterMouseMove.cancel();var b={isTooltipActive:!1};j.setState(b),j.triggerSyncEvent(b);var c=j.props.onMouseLeave;i()(c)&&c(b,a)}),cr(j,"handleOuterEvent",function(a){var b,c=(0,y.X_)(a),d=m()(j.props,"".concat(c));c&&i()(d)&&d(null!=(b=/.*touch.*/i.test(c)?j.getMouseInfo(a.changedTouches[0]):j.getMouseInfo(a))?b:{},a)}),cr(j,"handleClick",function(a){var b=j.getMouseInfo(a);if(b){var c=cq(cq({},b),{},{isTooltipActive:!0});j.setState(c),j.triggerSyncEvent(c);var d=j.props.onClick;i()(d)&&d(c,a)}}),cr(j,"handleMouseDown",function(a){var b=j.props.onMouseDown;i()(b)&&b(j.getMouseInfo(a),a)}),cr(j,"handleMouseUp",function(a){var b=j.props.onMouseUp;i()(b)&&b(j.getMouseInfo(a),a)}),cr(j,"handleTouchMove",function(a){null!=a.changedTouches&&a.changedTouches.length>0&&j.throttleTriggeredAfterMouseMove(a.changedTouches[0])}),cr(j,"handleTouchStart",function(a){null!=a.changedTouches&&a.changedTouches.length>0&&j.handleMouseDown(a.changedTouches[0])}),cr(j,"handleTouchEnd",function(a){null!=a.changedTouches&&a.changedTouches.length>0&&j.handleMouseUp(a.changedTouches[0])}),cr(j,"handleDoubleClick",function(a){var b=j.props.onDoubleClick;i()(b)&&b(j.getMouseInfo(a),a)}),cr(j,"handleContextMenu",function(a){var b=j.props.onContextMenu;i()(b)&&b(j.getMouseInfo(a),a)}),cr(j,"triggerSyncEvent",function(a){void 0!==j.props.syncId&&bl.emit(bm,j.props.syncId,a,j.eventEmitterSymbol)}),cr(j,"applySyncEvent",function(a){var b=j.props,c=b.layout,d=b.syncMethod,e=j.state.updateId,f=a.dataStartIndex,g=a.dataEndIndex;if(void 0!==a.dataStartIndex||void 0!==a.dataEndIndex)j.setState(cq({dataStartIndex:f,dataEndIndex:g},z({props:j.props,dataStartIndex:f,dataEndIndex:g,updateId:e},j.state)));else if(void 0!==a.activeTooltipIndex){var h=a.chartX,i=a.chartY,k=a.activeTooltipIndex,l=j.state,m=l.offset,n=l.tooltipTicks;if(!m)return;if("function"==typeof d)k=d(n,a);else if("value"===d){k=-1;for(var o=0;o<n.length;o++)if(n[o].value===a.activeLabel){k=o;break}}var p=cq(cq({},m),{},{x:m.left,y:m.top}),q=Math.min(h,p.x+p.width),r=Math.min(i,p.y+p.height),s=n[k]&&n[k].value,t=cA(j.state,j.props.data,k),u=n[k]?{x:"horizontal"===c?n[k].coordinate:q,y:"horizontal"===c?r:n[k].coordinate}:cv;j.setState(cq(cq({},a),{},{activeLabel:s,activeCoordinate:u,activePayload:t,activeTooltipIndex:k}))}else j.setState(a)}),cr(j,"renderCursor",function(a){var c,d=j.state,f=d.isTooltipActive,g=d.activeCoordinate,h=d.activePayload,i=d.offset,k=d.activeTooltipIndex,l=d.tooltipAxisBandSize,m=j.getTooltipEventType(),n=null!=(c=a.props.active)?c:f,o=j.props.layout,p=a.key||"_recharts-cursor";return e().createElement(cc,{key:p,activeCoordinate:g,activePayload:h,activeTooltipIndex:k,chartName:b,element:a,isActive:n,layout:o,offset:i,tooltipAxisBandSize:l,tooltipEventType:m})}),cr(j,"renderPolarAxis",function(a,b,c){var e=m()(a,"type.axisType"),f=m()(j.state,"".concat(e,"Map")),g=a.type.defaultProps,h=void 0!==g?cq(cq({},g),a.props):a.props,i=f&&f[h["".concat(e,"Id")]];return(0,d.cloneElement)(a,cq(cq({},i),{},{className:(0,r.A)(e,i.className),key:a.key||"".concat(b,"-").concat(c),ticks:(0,E.Rh)(i,!0)}))}),cr(j,"renderPolarGrid",function(a){var b=a.props,c=b.radialLines,e=b.polarAngles,f=b.polarRadius,g=j.state,h=g.radiusAxisMap,i=g.angleAxisMap,k=(0,F.lX)(h),l=(0,F.lX)(i),m=l.cx,n=l.cy,o=l.innerRadius,p=l.outerRadius;return(0,d.cloneElement)(a,{polarAngles:Array.isArray(e)?e:(0,E.Rh)(l,!0).map(function(a){return a.coordinate}),polarRadius:Array.isArray(f)?f:(0,E.Rh)(k,!0).map(function(a){return a.coordinate}),cx:m,cy:n,innerRadius:o,outerRadius:p,key:a.key||"polar-grid",radialLines:c})}),cr(j,"renderLegend",function(){var a=j.state.formattedGraphicalItems,b=j.props,c=b.children,e=b.width,f=b.height,g=j.props.margin||{},h=e-(g.left||0)-(g.right||0),i=(0,$.g)({children:c,formattedGraphicalItems:a,legendWidth:h,legendContent:n});if(!i)return null;var k=i.item,l=ci(i,cd);return(0,d.cloneElement)(k,cq(cq({},l),{},{chartWidth:e,chartHeight:f,margin:g,onBBoxUpdate:j.handleLegendBBoxUpdate}))}),cr(j,"renderTooltip",function(){var a,b=j.props,c=b.children,e=b.accessibilityLayer,f=(0,y.BU)(c,v.m);if(!f)return null;var g=j.state,h=g.isTooltipActive,i=g.activeCoordinate,k=g.activePayload,l=g.activeLabel,m=g.offset,n=null!=(a=f.props.active)?a:h;return(0,d.cloneElement)(f,{viewBox:cq(cq({},m),{},{x:m.left,y:m.top}),active:n,label:l,payload:n?k:[],coordinate:i,accessibilityLayer:e})}),cr(j,"renderBrush",function(a){var b=j.props,c=b.margin,e=b.data,f=j.state,g=f.offset,h=f.dataStartIndex,i=f.dataEndIndex,k=f.updateId;return(0,d.cloneElement)(a,{key:a.key||"_recharts-brush",onChange:(0,E.HQ)(j.handleBrushChange,a.props.onChange),data:e,x:(0,F.Et)(a.props.x)?a.props.x:g.left,y:(0,F.Et)(a.props.y)?a.props.y:g.top+g.height+g.brushBottom-(c.bottom||0),width:(0,F.Et)(a.props.width)?a.props.width:g.width,startIndex:h,endIndex:i,updateId:"brush-".concat(k)})}),cr(j,"renderReferenceElement",function(a,b,c){if(!a)return null;var e=j.clipPathId,f=j.state,g=f.xAxisMap,h=f.yAxisMap,i=f.offset,k=a.type.defaultProps||{},l=a.props,m=l.xAxisId,n=void 0===m?k.xAxisId:m,o=l.yAxisId,p=void 0===o?k.yAxisId:o;return(0,d.cloneElement)(a,{key:a.key||"".concat(b,"-").concat(c),xAxis:g[n],yAxis:h[p],viewBox:{x:i.left,y:i.top,width:i.width,height:i.height},clipPathId:e})}),cr(j,"renderActivePoints",function(a){var b=a.item,c=a.activePoint,d=a.basePoint,e=a.childIndex,g=a.isRange,h=[],i=b.props.key,j=void 0!==b.item.type.defaultProps?cq(cq({},b.item.type.defaultProps),b.item.props):b.item.props,k=j.activeDot,l=cq(cq({index:e,dataKey:j.dataKey,cx:c.x,cy:c.y,r:4,fill:(0,E.Ps)(b.item),strokeWidth:2,stroke:"#fff",payload:c.payload,value:c.value},(0,y.J9)(k,!1)),(0,x._U)(k));return h.push(f.renderActiveDot(k,l,"".concat(i,"-activePoint-").concat(e))),d?h.push(f.renderActiveDot(k,cq(cq({},l),{},{cx:d.x,cy:d.y}),"".concat(i,"-basePoint-").concat(e))):g&&h.push(null),h}),cr(j,"renderGraphicChild",function(a,b,c){var e=j.filterFormatItem(a,b,c);if(!e)return null;var f=j.getTooltipEventType(),h=j.state,i=h.isTooltipActive,k=h.tooltipAxis,l=h.activeTooltipIndex,m=h.activeLabel,n=j.props.children,o=(0,y.BU)(n,v.m),p=e.props,q=p.points,r=p.isRange,s=p.baseLine,t=void 0!==e.item.type.defaultProps?cq(cq({},e.item.type.defaultProps),e.item.props):e.item.props,u=t.activeDot,w=t.hide,x=t.activeBar,z=t.activeShape,A=!!(!w&&i&&o&&(u||x||z)),B={};"axis"!==f&&o&&"click"===o.props.trigger?B={onClick:(0,E.HQ)(j.handleItemMouseEnter,a.props.onClick)}:"axis"!==f&&(B={onMouseLeave:(0,E.HQ)(j.handleItemMouseLeave,a.props.onMouseLeave),onMouseEnter:(0,E.HQ)(j.handleItemMouseEnter,a.props.onMouseEnter)});var C=(0,d.cloneElement)(a,cq(cq({},e.props),B));if(A)if(l>=0){if(k.dataKey&&!k.allowDuplicatedCategory){var D="function"==typeof k.dataKey?function(a){return"function"==typeof k.dataKey?k.dataKey(a.payload):null}:"payload.".concat(k.dataKey.toString());H=(0,F.eP)(q,D,m),I=r&&s&&(0,F.eP)(s,D,m)}else H=null==q?void 0:q[l],I=r&&s&&s[l];if(z||x){var G=void 0!==a.props.activeIndex?a.props.activeIndex:l;return[(0,d.cloneElement)(a,cq(cq(cq({},e.props),B),{},{activeIndex:G})),null,null]}if(!g()(H))return[C].concat(cm(j.renderActivePoints({item:e,activePoint:H,basePoint:I,childIndex:l,isRange:r})))}else{var H,I,J,K=(null!=(J=j.getItemByXY(j.state.activeCoordinate))?J:{graphicalItem:C}).graphicalItem,L=K.item,M=void 0===L?a:L,N=K.childIndex,O=cq(cq(cq({},e.props),B),{},{activeIndex:N});return[(0,d.cloneElement)(M,O),null,null]}return r?[C,null,null]:[C,null]}),cr(j,"renderCustomized",function(a,b,c){return(0,d.cloneElement)(a,cq(cq({key:"recharts-customized-".concat(c)},j.props),j.state))}),cr(j,"renderMap",{CartesianGrid:{handler:cw,once:!0},ReferenceArea:{handler:j.renderReferenceElement},ReferenceLine:{handler:cw},ReferenceDot:{handler:j.renderReferenceElement},XAxis:{handler:cw},YAxis:{handler:cw},Brush:{handler:j.renderBrush,once:!0},Bar:{handler:j.renderGraphicChild},Line:{handler:j.renderGraphicChild},Area:{handler:j.renderGraphicChild},Radar:{handler:j.renderGraphicChild},RadialBar:{handler:j.renderGraphicChild},Scatter:{handler:j.renderGraphicChild},Pie:{handler:j.renderGraphicChild},Funnel:{handler:j.renderGraphicChild},Tooltip:{handler:j.renderCursor,once:!0},PolarGrid:{handler:j.renderPolarGrid,once:!0},PolarAngleAxis:{handler:j.renderPolarAxis},PolarRadiusAxis:{handler:j.renderPolarAxis},Customized:{handler:j.renderCustomized}}),j.clipPathId="".concat(null!=(c=a.id)?c:(0,F.NF)("recharts"),"-clip"),j.throttleTriggeredAfterMouseMove=q()(j.triggeredAfterMouseMove,null!=(h=a.throttleDelay)?h:1e3/60),j.state={},j}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return f.prototype=Object.create(a&&a.prototype,{constructor:{value:f,writable:!0,configurable:!0}}),Object.defineProperty(f,"prototype",{writable:!1}),a&&cl(f,a),c=[{key:"componentDidMount",value:function(){var a,b;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(a=this.props.margin.left)?a:0,top:null!=(b=this.props.margin.top)?b:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var a=this.props,b=a.children,c=a.data,d=a.height,e=a.layout,f=(0,y.BU)(b,v.m);if(f){var g=f.props.defaultIndex;if("number"==typeof g&&!(g<0)&&!(g>this.state.tooltipTicks.length-1)){var h=this.state.tooltipTicks[g]&&this.state.tooltipTicks[g].value,i=cA(this.state,c,g,h),j=this.state.tooltipTicks[g].coordinate,k=(this.state.offset.top+d)/2,l="horizontal"===e?{x:j,y:k}:{y:j,x:k},m=this.state.formattedGraphicalItems.find(function(a){return"Scatter"===a.item.type.name});m&&(l=cq(cq({},l),m.props.points[g].tooltipPosition),i=m.props.points[g].tooltipPayload);var n={activeTooltipIndex:g,isTooltipActive:!0,activeLabel:h,activePayload:i,activeCoordinate:l};this.setState(n),this.renderCursor(f),this.accessibilityManager.setIndex(g)}}}},{key:"getSnapshotBeforeUpdate",value:function(a,b){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==b.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==a.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==a.margin){var c,d;this.accessibilityManager.setDetails({offset:{left:null!=(c=this.props.margin.left)?c:0,top:null!=(d=this.props.margin.top)?d:0}})}return null}},{key:"componentDidUpdate",value:function(a){(0,y.OV)([(0,y.BU)(a.children,v.m)],[(0,y.BU)(this.props.children,v.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var a=(0,y.BU)(this.props.children,v.m);if(a&&"boolean"==typeof a.props.shared){var b=a.props.shared?"axis":"item";return k.indexOf(b)>=0?b:h}return h}},{key:"getMouseInfo",value:function(a){if(!this.container)return null;var b=this.container,c=b.getBoundingClientRect(),d=(0,Z.A3)(c),e={chartX:Math.round(a.pageX-d.left),chartY:Math.round(a.pageY-d.top)},f=c.width/b.offsetWidth||1,g=this.inRange(e.chartX,e.chartY,f);if(!g)return null;var h=this.state,i=h.xAxisMap,j=h.yAxisMap;if("axis"!==this.getTooltipEventType()&&i&&j){var k=(0,F.lX)(i).scale,l=(0,F.lX)(j).scale,m=k&&k.invert?k.invert(e.chartX):null,n=l&&l.invert?l.invert(e.chartY):null;return cq(cq({},e),{},{xValue:m,yValue:n})}var o=cB(this.state,this.props.data,this.props.layout,g);return o?cq(cq({},e),o):null}},{key:"inRange",value:function(a,b){var c=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,d=this.props.layout,e=a/c,f=b/c;if("horizontal"===d||"vertical"===d){var g=this.state.offset;return e>=g.left&&e<=g.left+g.width&&f>=g.top&&f<=g.top+g.height?{x:e,y:f}:null}var h=this.state,i=h.angleAxisMap,j=h.radiusAxisMap;if(i&&j){var k=(0,F.lX)(i);return(0,bi.yy)({x:e,y:f},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var a=this.props.children,b=this.getTooltipEventType(),c=(0,y.BU)(a,v.m),d={};return c&&"axis"===b&&(d="click"===c.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),cq(cq({},(0,x._U)(this.props,this.handleOuterEvent)),d)}},{key:"addListener",value:function(){bl.on(bm,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){bl.removeListener(bm,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(a,b,c){for(var d=this.state.formattedGraphicalItems,e=0,f=d.length;e<f;e++){var g=d[e];if(g.item===a||g.props.key===a.key||b===(0,y.Mn)(g.item.type)&&c===g.childIndex)return g}return null}},{key:"renderClipPath",value:function(){var a=this.clipPathId,b=this.state.offset,c=b.left,d=b.top,f=b.height,g=b.width;return e().createElement("defs",null,e().createElement("clipPath",{id:a},e().createElement("rect",{x:c,y:d,height:f,width:g})))}},{key:"getXScales",value:function(){var a=this.state.xAxisMap;return a?Object.entries(a).reduce(function(a,b){var c=ch(b,2),d=c[0],e=c[1];return cq(cq({},a),{},cr({},d,e.scale))},{}):null}},{key:"getYScales",value:function(){var a=this.state.yAxisMap;return a?Object.entries(a).reduce(function(a,b){var c=ch(b,2),d=c[0],e=c[1];return cq(cq({},a),{},cr({},d,e.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(a){var b;return null==(b=this.state.xAxisMap)||null==(b=b[a])?void 0:b.scale}},{key:"getYScaleByAxisId",value:function(a){var b;return null==(b=this.state.yAxisMap)||null==(b=b[a])?void 0:b.scale}},{key:"getItemByXY",value:function(a){var b=this.state,c=b.formattedGraphicalItems,d=b.activeItem;if(c&&c.length)for(var e=0,f=c.length;e<f;e++){var g=c[e],h=g.props,i=g.item,j=void 0!==i.type.defaultProps?cq(cq({},i.type.defaultProps),i.props):i.props,k=(0,y.Mn)(i.type);if("Bar"===k){var l=(h.data||[]).find(function(b){return(0,B.J)(a,b)});if(l)return{graphicalItem:g,payload:l}}else if("RadialBar"===k){var m=(h.data||[]).find(function(b){return(0,bi.yy)(a,b)});if(m)return{graphicalItem:g,payload:m}}else if((0,br.NE)(g,d)||(0,br.nZ)(g,d)||(0,br.xQ)(g,d)){var n=(0,br.GG)({graphicalItem:g,activeTooltipItem:d,itemData:j.data}),o=void 0===j.activeIndex?n:j.activeIndex;return{graphicalItem:cq(cq({},g),{},{childIndex:o}),payload:(0,br.xQ)(g,d)?j.data[n]:g.props.data[n]}}}return null}},{key:"render",value:function(){var a,b,c=this;if(!(0,y.Me)(this))return null;var d=this.props,f=d.children,g=d.className,h=d.width,i=d.height,j=d.style,k=d.compact,l=d.title,m=d.desc,n=ci(d,ce),o=(0,y.J9)(n,!1);if(k)return e().createElement(aN,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},e().createElement(t.u,cg({},o,{width:h,height:i,title:l,desc:m}),this.renderClipPath(),(0,y.ee)(f,this.renderMap)));this.props.accessibilityLayer&&(o.tabIndex=null!=(a=this.props.tabIndex)?a:0,o.role=null!=(b=this.props.role)?b:"application",o.onKeyDown=function(a){c.accessibilityManager.keyboardEvent(a)},o.onFocus=function(){c.accessibilityManager.focus()});var p=this.parseEventsOfWrapper();return e().createElement(aN,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},e().createElement("div",cg({className:(0,r.A)("recharts-wrapper",g),style:cq({position:"relative",cursor:"default",width:h,height:i},j)},p,{ref:function(a){c.container=a}}),e().createElement(t.u,cg({},o,{width:h,height:i,title:l,desc:m,style:cu}),this.renderClipPath(),(0,y.ee)(f,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,cs(d.key),d)}}(f.prototype,c),Object.defineProperty(f,"prototype",{writable:!1}),f}(d.Component);cr(C,"displayName",b),cr(C,"defaultProps",cq({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),cr(C,"getDerivedStateFromProps",function(a,b){var c=a.dataKey,d=a.data,e=a.children,f=a.width,h=a.height,i=a.layout,j=a.stackOffset,k=a.margin,l=b.dataStartIndex,m=b.dataEndIndex;if(void 0===b.updateId){var n=cG(a);return cq(cq(cq({},n),{},{updateId:0},z(cq(cq({props:a},n),{},{updateId:0}),b)),{},{prevDataKey:c,prevData:d,prevWidth:f,prevHeight:h,prevLayout:i,prevStackOffset:j,prevMargin:k,prevChildren:e})}if(c!==b.prevDataKey||d!==b.prevData||f!==b.prevWidth||h!==b.prevHeight||i!==b.prevLayout||j!==b.prevStackOffset||!(0,bj.b)(k,b.prevMargin)){var o=cG(a),p={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},q=cq(cq({},cB(b,d,i)),{},{updateId:b.updateId+1}),r=cq(cq(cq({},o),p),q);return cq(cq(cq({},r),z(cq({props:a},r),b)),{},{prevDataKey:c,prevData:d,prevWidth:f,prevHeight:h,prevLayout:i,prevStackOffset:j,prevMargin:k,prevChildren:e})}if(!(0,y.OV)(e,b.prevChildren)){var s,t,u,v,w=(0,y.BU)(e,Y),x=w&&null!=(s=null==(t=w.props)?void 0:t.startIndex)?s:l,A=w&&null!=(u=null==(v=w.props)?void 0:v.endIndex)?u:m,B=g()(d)||x!==l||A!==m?b.updateId+1:b.updateId;return cq(cq({updateId:B},z(cq(cq({props:a},b),{},{updateId:B,dataStartIndex:x,dataEndIndex:A}),b)),{},{prevChildren:e,dataStartIndex:x,dataEndIndex:A})}return null}),cr(C,"renderActiveDot",function(a,b,c){var f;return f=(0,d.isValidElement)(a)?(0,d.cloneElement)(a,b):i()(a)?a(b):e().createElement(A,b),e().createElement(u.W,{className:"recharts-active-dot",key:c},f)});var D=(0,d.forwardRef)(function(a,b){return e().createElement(C,cg({},a,{ref:b}))});return D.displayName=C.displayName,D}({chartName:"BarChart",GraphicalChild:af.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:da},{axisType:"yAxis",AxisComp:dj}],formatAxisMap:function(a,b,c,d,e){var f=a.width,g=a.height,h=a.layout,i=a.children,j=Object.keys(b),k={left:c.left,leftMirror:c.left,right:f-c.right,rightMirror:f-c.right,top:c.top,topMirror:c.top,bottom:g-c.bottom,bottomMirror:g-c.bottom},l=!!(0,y.BU)(i,af.y);return j.reduce(function(f,g){var i,j,m,n,o,p=b[g],q=p.orientation,r=p.domain,s=p.padding,t=void 0===s?{}:s,u=p.mirror,v=p.reversed,w="".concat(q).concat(u?"Mirror":"");if("number"===p.type&&("gap"===p.padding||"no-gap"===p.padding)){var x=r[1]-r[0],y=1/0,z=p.categoricalDomain.sort();if(z.forEach(function(a,b){b>0&&(y=Math.min((a||0)-(z[b-1]||0),y))}),Number.isFinite(y)){var A=y/x,B="vertical"===p.layout?c.height:c.width;if("gap"===p.padding&&(i=A*B/2),"no-gap"===p.padding){var C=(0,F.F4)(a.barCategoryGap,A*B),D=A*B/2;i=D-C-(D-C)/B*C}}}j="xAxis"===d?[c.left+(t.left||0)+(i||0),c.left+c.width-(t.right||0)-(i||0)]:"yAxis"===d?"horizontal"===h?[c.top+c.height-(t.bottom||0),c.top+(t.top||0)]:[c.top+(t.top||0)+(i||0),c.top+c.height-(t.bottom||0)-(i||0)]:p.range,v&&(j=[j[1],j[0]]);var G=(0,E.W7)(p,e,l),H=G.scale,I=G.realScaleType;H.domain(r).range(j),(0,E.YB)(H);var J=(0,E.w7)(H,aj(aj({},p),{},{realScaleType:I}));"xAxis"===d?(o="top"===q&&!u||"bottom"===q&&u,m=c.left,n=k[w]-o*p.height):"yAxis"===d&&(o="left"===q&&!u||"right"===q&&u,m=k[w]-o*p.width,n=c.top);var K=aj(aj(aj({},p),J),{},{realScaleType:I,x:m,y:n,scale:H,width:"xAxis"===d?c.width:p.width,height:"yAxis"===d?c.height:p.height});return K.bandSize=(0,E.Hj)(K,J),p.hide||"xAxis"!==d?p.hide||(k[w]+=(o?-1:1)*K.width):k[w]+=(o?-1:1)*K.height,aj(aj({},f),{},ak({},g,K))},{})}})},8852:(a,b,c)=>{var d=c(1707);a.exports=function(a){return function(b){return d(b,a)}}},8886:(a,b,c)=>{"use strict";c.d(b,{A:()=>function a(){var b=new d,c=[],e=[],f=h;function i(a){let d=b.get(a);if(void 0===d){if(f!==h)return f;b.set(a,d=c.push(a)-1)}return e[d%e.length]}return i.domain=function(a){if(!arguments.length)return c.slice();for(let e of(c=[],b=new d,a))b.has(e)||b.set(e,c.push(e)-1);return i},i.range=function(a){return arguments.length?(e=Array.from(a),i):e.slice()},i.unknown=function(a){return arguments.length?(f=a,i):f},i.copy=function(){return a(c,e).unknown(f)},g.C.apply(i,arguments),i},h:()=>h});class d extends Map{constructor(a,b=f){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:b}}),null!=a)for(let[b,c]of a)this.set(b,c)}get(a){return super.get(e(this,a))}has(a){return super.has(e(this,a))}set(a,b){return super.set(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):(a.set(d,c),c)}(this,a),b)}delete(a){return super.delete(function({_intern:a,_key:b},c){let d=b(c);return a.has(d)&&(c=a.get(d),a.delete(d)),c}(this,a))}}function e({_intern:a,_key:b},c){let d=b(c);return a.has(d)?a.get(d):c}function f(a){return null!==a&&"object"==typeof a?a.valueOf():a}var g=c(37877);let h=Symbol("implicit")},10090:(a,b,c)=>{var d=c(80458),e=c(89624),f=c(47282),g=f&&f.isTypedArray;a.exports=g?e(g):d},10486:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Droplet",[["path",{d:"M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z",key:"c7niix"}]])},10521:(a,b,c)=>{"use strict";c.d(b,{R:()=>d});var d=function(a,b){for(var c=arguments.length,d=Array(c>2?c-2:0),e=2;e<c;e++)d[e-2]=arguments[e]}},10653:(a,b,c)=>{var d=c(21456),e=c(63979),f=c(7651);a.exports=function(a){return d(a,f,e)}},10663:a=>{a.exports="object"==typeof global&&global&&global.Object===Object&&global},10919:(a,b,c)=>{"use strict";c.d(b,{i:()=>I});var d=c(43210),e=c.n(d),f=c(69433),g=c.n(f);let h=Math.cos,i=Math.sin,j=Math.sqrt,k=Math.PI,l=2*k,m={draw(a,b){let c=j(b/k);a.moveTo(c,0),a.arc(0,0,c,0,l)}},n=j(1/3),o=2*n,p=i(k/10)/i(7*k/10),q=i(l/10)*p,r=-h(l/10)*p,s=j(3),t=j(3)/2,u=1/j(12),v=(u/2+1)*3;var w=c(22786),x=c(15606);j(3),j(3);var y=c(49384),z=c(54186);function A(a){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var B=["type","size","sizeType"];function C(){return(C=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function D(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function E(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?D(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=A(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=A(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==A(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):D(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var F={symbolCircle:m,symbolCross:{draw(a,b){let c=j(b/5)/2;a.moveTo(-3*c,-c),a.lineTo(-c,-c),a.lineTo(-c,-3*c),a.lineTo(c,-3*c),a.lineTo(c,-c),a.lineTo(3*c,-c),a.lineTo(3*c,c),a.lineTo(c,c),a.lineTo(c,3*c),a.lineTo(-c,3*c),a.lineTo(-c,c),a.lineTo(-3*c,c),a.closePath()}},symbolDiamond:{draw(a,b){let c=j(b/o),d=c*n;a.moveTo(0,-c),a.lineTo(d,0),a.lineTo(0,c),a.lineTo(-d,0),a.closePath()}},symbolSquare:{draw(a,b){let c=j(b),d=-c/2;a.rect(d,d,c,c)}},symbolStar:{draw(a,b){let c=j(.8908130915292852*b),d=q*c,e=r*c;a.moveTo(0,-c),a.lineTo(d,e);for(let b=1;b<5;++b){let f=l*b/5,g=h(f),j=i(f);a.lineTo(j*c,-g*c),a.lineTo(g*d-j*e,j*d+g*e)}a.closePath()}},symbolTriangle:{draw(a,b){let c=-j(b/(3*s));a.moveTo(0,2*c),a.lineTo(-s*c,-c),a.lineTo(s*c,-c),a.closePath()}},symbolWye:{draw(a,b){let c=j(b/v),d=c/2,e=c*u,f=c*u+c,g=-d;a.moveTo(d,e),a.lineTo(d,f),a.lineTo(g,f),a.lineTo(-.5*d-t*e,t*d+-.5*e),a.lineTo(-.5*d-t*f,t*d+-.5*f),a.lineTo(-.5*g-t*f,t*g+-.5*f),a.lineTo(-.5*d+t*e,-.5*e-t*d),a.lineTo(-.5*d+t*f,-.5*f-t*d),a.lineTo(-.5*g+t*f,-.5*f-t*g),a.closePath()}}},G=Math.PI/180,H=function(a,b,c){if("area"===b)return a;switch(c){case"cross":return 5*a*a/9;case"diamond":return .5*a*a/Math.sqrt(3);case"square":return a*a;case"star":var d=18*G;return 1.25*a*a*(Math.tan(d)-Math.tan(2*d)*Math.pow(Math.tan(d),2));case"triangle":return Math.sqrt(3)*a*a/4;case"wye":return(21-10*Math.sqrt(3))*a*a/8;default:return Math.PI*a*a/4}},I=function(a){var b,c=a.type,d=void 0===c?"circle":c,f=a.size,h=void 0===f?64:f,i=a.sizeType,j=void 0===i?"area":i,k=E(E({},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,B)),{},{type:d,size:h,sizeType:j}),l=k.className,n=k.cx,o=k.cy,p=(0,z.J9)(k,!0);return n===+n&&o===+o&&h===+h?e().createElement("path",C({},p,{className:(0,y.A)("recharts-symbols",l),transform:"translate(".concat(n,", ").concat(o,")"),d:(b=F["symbol".concat(g()(d))]||m,(function(a,b){let c=null,d=(0,x.i)(e);function e(){let e;if(c||(c=e=d()),a.apply(this,arguments).draw(c,+b.apply(this,arguments)),e)return c=null,e+""||null}return a="function"==typeof a?a:(0,w.A)(a||m),b="function"==typeof b?b:(0,w.A)(void 0===b?64:+b),e.type=function(b){return arguments.length?(a="function"==typeof b?b:(0,w.A)(b),e):a},e.size=function(a){return arguments.length?(b="function"==typeof a?a:(0,w.A)(+a),e):b},e.context=function(a){return arguments.length?(c=null==a?null:a,e):c},e})().type(b).size(H(h,j,d))())})):null};I.registerSymbol=function(a,b){F["symbol".concat(g()(a))]=b}},11117:a=>{"use strict";var b=Object.prototype.hasOwnProperty,c="~";function d(){}function e(a,b,c){this.fn=a,this.context=b,this.once=c||!1}function f(a,b,d,f,g){if("function"!=typeof d)throw TypeError("The listener must be a function");var h=new e(d,f||a,g),i=c?c+b:b;return a._events[i]?a._events[i].fn?a._events[i]=[a._events[i],h]:a._events[i].push(h):(a._events[i]=h,a._eventsCount++),a}function g(a,b){0==--a._eventsCount?a._events=new d:delete a._events[b]}function h(){this._events=new d,this._eventsCount=0}Object.create&&(d.prototype=Object.create(null),new d().__proto__||(c=!1)),h.prototype.eventNames=function(){var a,d,e=[];if(0===this._eventsCount)return e;for(d in a=this._events)b.call(a,d)&&e.push(c?d.slice(1):d);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(a)):e},h.prototype.listeners=function(a){var b=c?c+a:a,d=this._events[b];if(!d)return[];if(d.fn)return[d.fn];for(var e=0,f=d.length,g=Array(f);e<f;e++)g[e]=d[e].fn;return g},h.prototype.listenerCount=function(a){var b=c?c+a:a,d=this._events[b];return d?d.fn?1:d.length:0},h.prototype.emit=function(a,b,d,e,f,g){var h=c?c+a:a;if(!this._events[h])return!1;var i,j,k=this._events[h],l=arguments.length;if(k.fn){switch(k.once&&this.removeListener(a,k.fn,void 0,!0),l){case 1:return k.fn.call(k.context),!0;case 2:return k.fn.call(k.context,b),!0;case 3:return k.fn.call(k.context,b,d),!0;case 4:return k.fn.call(k.context,b,d,e),!0;case 5:return k.fn.call(k.context,b,d,e,f),!0;case 6:return k.fn.call(k.context,b,d,e,f,g),!0}for(j=1,i=Array(l-1);j<l;j++)i[j-1]=arguments[j];k.fn.apply(k.context,i)}else{var m,n=k.length;for(j=0;j<n;j++)switch(k[j].once&&this.removeListener(a,k[j].fn,void 0,!0),l){case 1:k[j].fn.call(k[j].context);break;case 2:k[j].fn.call(k[j].context,b);break;case 3:k[j].fn.call(k[j].context,b,d);break;case 4:k[j].fn.call(k[j].context,b,d,e);break;default:if(!i)for(m=1,i=Array(l-1);m<l;m++)i[m-1]=arguments[m];k[j].fn.apply(k[j].context,i)}}return!0},h.prototype.on=function(a,b,c){return f(this,a,b,c,!1)},h.prototype.once=function(a,b,c){return f(this,a,b,c,!0)},h.prototype.removeListener=function(a,b,d,e){var f=c?c+a:a;if(!this._events[f])return this;if(!b)return g(this,f),this;var h=this._events[f];if(h.fn)h.fn!==b||e&&!h.once||d&&h.context!==d||g(this,f);else{for(var i=0,j=[],k=h.length;i<k;i++)(h[i].fn!==b||e&&!h[i].once||d&&h[i].context!==d)&&j.push(h[i]);j.length?this._events[f]=1===j.length?j[0]:j:g(this,f)}return this},h.prototype.removeAllListeners=function(a){var b;return a?(b=c?c+a:a,this._events[b]&&g(this,b)):(this._events=new d,this._eventsCount=0),this},h.prototype.off=h.prototype.removeListener,h.prototype.addListener=h.prototype.on,h.prefixed=c,h.EventEmitter=h,a.exports=h},11424:(a,b,c)=>{var d=c(47603);a.exports=c(66400)(d)},11539:(a,b,c)=>{var d=c(37643),e=c(55048),f=c(49227),g=0/0,h=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,j=/^0o[0-7]+$/i,k=parseInt;a.exports=function(a){if("number"==typeof a)return a;if(f(a))return g;if(e(a)){var b="function"==typeof a.valueOf?a.valueOf():a;a=e(b)?b+"":b}if("string"!=typeof a)return 0===a?a:+a;a=d(a);var c=i.test(a);return c||j.test(a)?k(a.slice(2),c?2:8):h.test(a)?g:+a}},12290:a=>{var b=Function.prototype.toString;a.exports=function(a){if(null!=a){try{return b.call(a)}catch(a){}try{return a+""}catch(a){}}return""}},12344:(a,b,c)=>{a.exports=c(65984)()},14675:a=>{a.exports=function(a){return function(){return a}}},14719:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},15451:(a,b,c)=>{var d=c(29395),e=c(27467);a.exports=function(a){return e(a)&&"[object Arguments]"==d(a)}},15606:(a,b,c)=>{"use strict";c.d(b,{i:()=>i});let d=Math.PI,e=2*d,f=e-1e-6;function g(a){this._+=a[0];for(let b=1,c=a.length;b<c;++b)this._+=arguments[b]+a[b]}class h{constructor(a){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==a?g:function(a){let b=Math.floor(a);if(!(b>=0))throw Error(`invalid digits: ${a}`);if(b>15)return g;let c=10**b;return function(a){this._+=a[0];for(let b=1,d=a.length;b<d;++b)this._+=Math.round(arguments[b]*c)/c+a[b]}}(a)}moveTo(a,b){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(a,b){this._append`L${this._x1=+a},${this._y1=+b}`}quadraticCurveTo(a,b,c,d){this._append`Q${+a},${+b},${this._x1=+c},${this._y1=+d}`}bezierCurveTo(a,b,c,d,e,f){this._append`C${+a},${+b},${+c},${+d},${this._x1=+e},${this._y1=+f}`}arcTo(a,b,c,e,f){if(a*=1,b*=1,c*=1,e*=1,(f*=1)<0)throw Error(`negative radius: ${f}`);let g=this._x1,h=this._y1,i=c-a,j=e-b,k=g-a,l=h-b,m=k*k+l*l;if(null===this._x1)this._append`M${this._x1=a},${this._y1=b}`;else if(m>1e-6)if(Math.abs(l*i-j*k)>1e-6&&f){let n=c-g,o=e-h,p=i*i+j*j,q=Math.sqrt(p),r=Math.sqrt(m),s=f*Math.tan((d-Math.acos((p+m-(n*n+o*o))/(2*q*r)))/2),t=s/r,u=s/q;Math.abs(t-1)>1e-6&&this._append`L${a+t*k},${b+t*l}`,this._append`A${f},${f},0,0,${+(l*n>k*o)},${this._x1=a+u*i},${this._y1=b+u*j}`}else this._append`L${this._x1=a},${this._y1=b}`}arc(a,b,c,g,h,i){if(a*=1,b*=1,c*=1,i=!!i,c<0)throw Error(`negative radius: ${c}`);let j=c*Math.cos(g),k=c*Math.sin(g),l=a+j,m=b+k,n=1^i,o=i?g-h:h-g;null===this._x1?this._append`M${l},${m}`:(Math.abs(this._x1-l)>1e-6||Math.abs(this._y1-m)>1e-6)&&this._append`L${l},${m}`,c&&(o<0&&(o=o%e+e),o>f?this._append`A${c},${c},0,1,${n},${a-j},${b-k}A${c},${c},0,1,${n},${this._x1=l},${this._y1=m}`:o>1e-6&&this._append`A${c},${c},0,${+(o>=d)},${n},${this._x1=a+c*Math.cos(h)},${this._y1=b+c*Math.sin(h)}`)}rect(a,b,c,d){this._append`M${this._x0=this._x1=+a},${this._y0=this._y1=+b}h${c*=1}v${+d}h${-c}Z`}toString(){return this._}}function i(a){let b=3;return a.digits=function(c){if(!arguments.length)return b;if(null==c)b=null;else{let a=Math.floor(c);if(!(a>=0))throw RangeError(`invalid digits: ${c}`);b=a}return a},()=>new h(b)}h.prototype},15871:(a,b,c)=>{var d=c(36341),e=c(27467);a.exports=function a(b,c,f,g,h){return b===c||(null!=b&&null!=c&&(e(b)||e(c))?d(b,c,f,g,a,h):b!=b&&c!=c)}},15883:(a,b,c)=>{var d=c(2984),e=c(46063),f=c(48169);a.exports=function(a){return a&&a.length?d(a,f,e):void 0}},15909:(a,b,c)=>{var d=c(87506),e=c(66930),f=c(658);a.exports=function(){this.size=0,this.__data__={hash:new d,map:new(f||e),string:new d}}},16854:a=>{a.exports=function(a){return this.__data__.has(a)}},17518:(a,b,c)=>{var d=c(21367),e=c(1707),f=c(22),g=c(54765),h=c(43378),i=c(89624),j=c(65727),k=c(48169),l=c(40542);a.exports=function(a,b,c){b=b.length?d(b,function(a){return l(a)?function(b){return e(b,1===a.length?a[0]:a)}:a}):[k];var m=-1;return b=d(b,i(f)),h(g(a,function(a,c,e){return{criteria:d(b,function(b){return b(a)}),index:++m,value:a}}),function(a,b){return j(a,b,c)})}},17585:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("BedDouble",[["path",{d:"M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8",key:"1k78r4"}],["path",{d:"M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4",key:"fb3tl2"}],["path",{d:"M12 4v6",key:"1dcgq2"}],["path",{d:"M2 18h20",key:"ajqnye"}]])},17830:(a,b,c)=>{a.exports=c(41547)(c(85718),"WeakMap")},18234:(a,b,c)=>{var d=c(91290),e=c(22),f=c(84482),g=Math.max;a.exports=function(a,b,c){var h=null==a?0:a.length;if(!h)return -1;var i=null==c?0:f(c);return i<0&&(i=g(h+i,0)),d(a,e(b,3),i)}},18842:(a,b,c)=>{"use strict";function d(a,b){for(var c in a)if(({}).hasOwnProperty.call(a,c)&&(!({}).hasOwnProperty.call(b,c)||a[c]!==b[c]))return!1;for(var d in b)if(({}).hasOwnProperty.call(b,d)&&!({}).hasOwnProperty.call(a,d))return!1;return!0}c.d(b,{b:()=>d})},19335:(a,b,c)=>{"use strict";function d(a){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var e,f,g;e=a,f=b,g=c[b],(f=function(a){var b=function(a,b){if("object"!=d(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var e=c.call(a,b||"default");if("object"!=d(e))return e;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==d(b)?b:b+""}(f))in e?Object.defineProperty(e,f,{value:g,enumerable:!0,configurable:!0,writable:!0}):e[f]=g}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}c.d(b,{IZ:()=>h,Kg:()=>g,yy:()=>l}),c(37456),c(43210),c(5231);var g=Math.PI/180,h=function(a,b,c,d){return{x:a+Math.cos(-g*d)*c,y:b+Math.sin(-g*d)*c}},i=function(a,b){var c=a.x,d=a.y;return Math.sqrt(Math.pow(c-b.x,2)+Math.pow(d-b.y,2))},j=function(a,b){var c=a.x,d=a.y,e=b.cx,f=b.cy,g=i({x:c,y:d},{x:e,y:f});if(g<=0)return{radius:g};var h=Math.acos((c-e)/g);return d>f&&(h=2*Math.PI-h),{radius:g,angle:180*h/Math.PI,angleInRadian:h}},k=function(a){var b=a.startAngle,c=a.endAngle,d=Math.min(Math.floor(b/360),Math.floor(c/360));return{startAngle:b-360*d,endAngle:c-360*d}},l=function(a,b){var c,d=j({x:a.x,y:a.y},b),e=d.radius,g=d.angle,h=b.innerRadius,i=b.outerRadius;if(e<h||e>i)return!1;if(0===e)return!0;var l=k(b),m=l.startAngle,n=l.endAngle,o=g;if(m<=n){for(;o>n;)o-=360;for(;o<m;)o+=360;c=o>=m&&o<=n}else{for(;o>m;)o-=360;for(;o<n;)o+=360;c=o>=n&&o<=m}return c?f(f({},b),{},{radius:e,angle:o+360*Math.min(Math.floor(b.startAngle/360),Math.floor(b.endAngle/360))}):null}},19976:(a,b,c)=>{var d=c(8336);a.exports=function(a,b){var c=d(this,a),e=c.size;return c.set(a,b),this.size+=+(c.size!=e),this}},20237:(a,b,c)=>{"use strict";c.d(b,{m:()=>d});var d={isSsr:!0,get:function(a){return d[a]},set:function(a,b){if("string"==typeof a)d[a]=b;else{var c=Object.keys(a);c&&c.length&&c.forEach(function(b){d[b]=a[b]})}}}},20540:(a,b,c)=>{var d=c(55048),e=c(70151),f=c(11539),g=Math.max,h=Math.min;a.exports=function(a,b,c){var i,j,k,l,m,n,o=0,p=!1,q=!1,r=!0;if("function"!=typeof a)throw TypeError("Expected a function");function s(b){var c=i,d=j;return i=j=void 0,o=b,l=a.apply(d,c)}function t(a){var c=a-n,d=a-o;return void 0===n||c>=b||c<0||q&&d>=k}function u(){var a,c,d,f=e();if(t(f))return v(f);m=setTimeout(u,(a=f-n,c=f-o,d=b-a,q?h(d,k-c):d))}function v(a){return(m=void 0,r&&i)?s(a):(i=j=void 0,l)}function w(){var a,c=e(),d=t(c);if(i=arguments,j=this,n=c,d){if(void 0===m)return o=a=n,m=setTimeout(u,b),p?s(a):l;if(q)return clearTimeout(m),m=setTimeout(u,b),s(n)}return void 0===m&&(m=setTimeout(u,b)),l}return b=f(b)||0,d(c)&&(p=!!c.leading,k=(q="maxWait"in c)?g(f(c.maxWait)||0,b):k,r="trailing"in c?!!c.trailing:r),w.cancel=function(){void 0!==m&&clearTimeout(m),o=0,i=n=j=m=void 0},w.flush=function(){return void 0===m?l:v(e())},w}},20623:(a,b,c)=>{var d=c(15871),e=c(40491),f=c(2896),g=c(67619),h=c(34883),i=c(41132),j=c(46436);a.exports=function(a,b){return g(a)&&h(b)?i(j(a),b):function(c){var g=e(c,a);return void 0===g&&g===b?f(c,a):d(b,g,3)}}},21080:(a,b,c)=>{"use strict";c.d(b,{u:()=>j});var d=c(43210),e=c.n(d),f=c(49384),g=c(54186),h=["children","width","height","viewBox","className","style","title","desc"];function i(){return(i=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function j(a){var b=a.children,c=a.width,d=a.height,j=a.viewBox,k=a.className,l=a.style,m=a.title,n=a.desc,o=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,h),p=j||{width:c,height:d,x:0,y:0},q=(0,f.A)("recharts-surface",k);return e().createElement("svg",i({},(0,g.J9)(o,!0,"svg"),{className:q,width:c,height:d,style:l,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height)}),e().createElement("title",null,m),e().createElement("desc",null,n),b)}},21367:a=>{a.exports=function(a,b){for(var c=-1,d=null==a?0:a.length,e=Array(d);++c<d;)e[c]=b(a[c],c,a);return e}},21456:(a,b,c)=>{var d=c(41693),e=c(40542);a.exports=function(a,b,c){var f=b(a);return e(a)?f:d(f,c(a))}},21592:(a,b,c)=>{var d=c(42205),e=c(61837);a.exports=function(a,b){return d(e(a,b),1)}},21630:(a,b,c)=>{var d=c(10653),e=Object.prototype.hasOwnProperty;a.exports=function(a,b,c,f,g,h){var i=1&c,j=d(a),k=j.length;if(k!=d(b).length&&!i)return!1;for(var l=k;l--;){var m=j[l];if(!(i?m in b:e.call(b,m)))return!1}var n=h.get(a),o=h.get(b);if(n&&o)return n==b&&o==a;var p=!0;h.set(a,b),h.set(b,a);for(var q=i;++l<k;){var r=a[m=j[l]],s=b[m];if(f)var t=i?f(s,r,m,b,a,h):f(r,s,m,a,b,h);if(!(void 0===t?r===s||g(r,s,c,f,h):t)){p=!1;break}q||(q="constructor"==m)}if(p&&!q){var u=a.constructor,v=b.constructor;u!=v&&"constructor"in a&&"constructor"in b&&!("function"==typeof u&&u instanceof u&&"function"==typeof v&&v instanceof v)&&(p=!1)}return h.delete(a),h.delete(b),p}},22786:(a,b,c)=>{"use strict";function d(a){return function(){return a}}c.d(b,{A:()=>d})},22964:(a,b,c)=>{a.exports=c(46110)(c(18234))},23561:(a,b,c)=>{"use strict";c.d(b,{E:()=>M});var d=c(43210),e=c.n(d),f=c(37456),g=c.n(f),h=c(49384),i=c(45370),j=c(20237),k=c(54186),l=c(96075);function m(a){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function n(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{if(f=(c=c.call(a)).next,0===b){if(Object(c)!==c)return;i=!1}else for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(a,b)||function(a,b){if(a){if("string"==typeof a)return o(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return o(a,b)}}(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function p(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,function(a){var b=function(a,b){if("object"!=m(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=m(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==m(b)?b:b+""}(d.key),d)}}var q=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,r=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,t=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,u={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},v=Object.keys(u),w=function(){var a,b;function c(a,b){if(!(this instanceof c))throw TypeError("Cannot call a class as a function");this.num=a,this.unit=b,this.num=a,this.unit=b,Number.isNaN(a)&&(this.unit=""),""===b||s.test(b)||(this.num=NaN,this.unit=""),v.includes(b)&&(this.num=a*u[b],this.unit="px")}return a=[{key:"add",value:function(a){return this.unit!==a.unit?new c(NaN,""):new c(this.num+a.num,this.unit)}},{key:"subtract",value:function(a){return this.unit!==a.unit?new c(NaN,""):new c(this.num-a.num,this.unit)}},{key:"multiply",value:function(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new c(NaN,""):new c(this.num*a.num,this.unit||a.unit)}},{key:"divide",value:function(a){return""!==this.unit&&""!==a.unit&&this.unit!==a.unit?new c(NaN,""):new c(this.num/a.num,this.unit||a.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],b=[{key:"parse",value:function(a){var b,d=n(null!=(b=t.exec(a))?b:[],3),e=d[1],f=d[2];return new c(parseFloat(e),null!=f?f:"")}}],a&&p(c.prototype,a),b&&p(c,b),Object.defineProperty(c,"prototype",{writable:!1}),c}();function x(a){if(a.includes("NaN"))return"NaN";for(var b=a;b.includes("*")||b.includes("/");){var c,d=n(null!=(c=q.exec(b))?c:[],4),e=d[1],f=d[2],g=d[3],h=w.parse(null!=e?e:""),i=w.parse(null!=g?g:""),j="*"===f?h.multiply(i):h.divide(i);if(j.isNaN())return"NaN";b=b.replace(q,j.toString())}for(;b.includes("+")||/.-\d+(?:\.\d+)?/.test(b);){var k,l=n(null!=(k=r.exec(b))?k:[],4),m=l[1],o=l[2],p=l[3],s=w.parse(null!=m?m:""),t=w.parse(null!=p?p:""),u="+"===o?s.add(t):s.subtract(t);if(u.isNaN())return"NaN";b=b.replace(r,u.toString())}return b}var y=/\(([^()]*)\)/;function z(a){var b=function(a){try{var b;return b=a.replace(/\s+/g,""),b=function(a){for(var b=a;b.includes("(");){var c=n(y.exec(b),2)[1];b=b.replace(y,x(c))}return b}(b),b=x(b)}catch(a){return"NaN"}}(a.slice(5,-1));return"NaN"===b?"":b}var A=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],B=["dx","dy","angle","className","breakAll"];function C(){return(C=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function D(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}function E(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{if(f=(c=c.call(a)).next,0===b){if(Object(c)!==c)return;i=!1}else for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(a,b)||function(a,b){if(a){if("string"==typeof a)return F(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return F(a,b)}}(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function F(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}var G=/[ \f\n\r\t\v\u2028\u2029]+/,H=function(a){var b=a.children,c=a.breakAll,d=a.style;try{var e=[];g()(b)||(e=c?b.toString().split(""):b.toString().split(G));var f=e.map(function(a){return{word:a,width:(0,l.Pu)(a,d).width}}),h=c?0:(0,l.Pu)("\xa0",d).width;return{wordsWithComputedWidth:f,spaceWidth:h}}catch(a){return null}},I=function(a,b,c,d,e){var f,g=a.maxLines,h=a.children,j=a.style,k=a.breakAll,l=(0,i.Et)(g),m=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return a.reduce(function(a,b){var f=b.word,g=b.width,h=a[a.length-1];return h&&(null==d||e||h.width+g+c<Number(d))?(h.words.push(f),h.width+=g+c):a.push({words:[f],width:g}),a},[])},n=m(b);if(!l)return n;for(var o=function(a){var b=m(H({breakAll:k,style:j,children:h.slice(0,a)+"…"}).wordsWithComputedWidth);return[b.length>g||b.reduce(function(a,b){return a.width>b.width?a:b}).width>Number(d),b]},p=0,q=h.length-1,r=0;p<=q&&r<=h.length-1;){var s=Math.floor((p+q)/2),t=E(o(s-1),2),u=t[0],v=t[1],w=E(o(s),1)[0];if(u||w||(p=s+1),u&&w&&(q=s-1),!u&&w){f=v;break}r++}return f||n},J=function(a){return[{words:g()(a)?[]:a.toString().split(G)}]},K=function(a){var b=a.width,c=a.scaleToFit,d=a.children,e=a.style,f=a.breakAll,g=a.maxLines;if((b||c)&&!j.m.isSsr){var h=H({breakAll:f,children:d,style:e});if(!h)return J(d);var i=h.wordsWithComputedWidth,k=h.spaceWidth;return I({breakAll:f,children:d,maxLines:g,style:e},i,k,b,c)}return J(d)},L="#808080",M=function(a){var b,c=a.x,f=void 0===c?0:c,g=a.y,j=void 0===g?0:g,l=a.lineHeight,m=void 0===l?"1em":l,n=a.capHeight,o=void 0===n?"0.71em":n,p=a.scaleToFit,q=void 0!==p&&p,r=a.textAnchor,s=a.verticalAnchor,t=a.fill,u=void 0===t?L:t,v=D(a,A),w=(0,d.useMemo)(function(){return K({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:q,style:v.style,width:v.width})},[v.breakAll,v.children,v.maxLines,q,v.style,v.width]),x=v.dx,y=v.dy,E=v.angle,F=v.className,G=v.breakAll,H=D(v,B);if(!(0,i.vh)(f)||!(0,i.vh)(j))return null;var I=f+((0,i.Et)(x)?x:0),J=j+((0,i.Et)(y)?y:0);switch(void 0===s?"end":s){case"start":b=z("calc(".concat(o,")"));break;case"middle":b=z("calc(".concat((w.length-1)/2," * -").concat(m," + (").concat(o," / 2))"));break;default:b=z("calc(".concat(w.length-1," * -").concat(m,")"))}var M=[];if(q){var N=w[0].width,O=v.width;M.push("scale(".concat(((0,i.Et)(O)?O/N:1)/N,")"))}return E&&M.push("rotate(".concat(E,", ").concat(I,", ").concat(J,")")),M.length&&(H.transform=M.join(" ")),e().createElement("text",C({},(0,k.J9)(H,!0),{x:I,y:J,className:(0,h.A)("recharts-text",F),textAnchor:void 0===r?"start":r,fill:u.includes("url")?L:u}),w.map(function(a,c){var d=a.words.join(G?"":" ");return e().createElement("tspan",{x:I,dy:0===c?b:m,key:"".concat(d,"-").concat(c)},d)}))}},25118:a=>{a.exports=function(a){return this.__data__.has(a)}},25679:(a,b,c)=>{"use strict";c.d(b,{f:()=>d});var d=function(a){return null};d.displayName="Cell"},27006:(a,b,c)=>{var d=c(46328),e=c(99525),f=c(58276);a.exports=function(a,b,c,g,h,i){var j=1&c,k=a.length,l=b.length;if(k!=l&&!(j&&l>k))return!1;var m=i.get(a),n=i.get(b);if(m&&n)return m==b&&n==a;var o=-1,p=!0,q=2&c?new d:void 0;for(i.set(a,b),i.set(b,a);++o<k;){var r=a[o],s=b[o];if(g)var t=j?g(s,r,o,b,a,i):g(r,s,o,a,b,i);if(void 0!==t){if(t)continue;p=!1;break}if(q){if(!e(b,function(a,b){if(!f(q,b)&&(r===a||h(r,a,c,g,i)))return q.push(b)})){p=!1;break}}else if(!(r===s||h(r,s,c,g,i))){p=!1;break}}return i.delete(a),i.delete(b),p}},27467:a=>{a.exports=function(a){return null!=a&&"object"==typeof a}},27669:a=>{a.exports=function(){this.__data__=[],this.size=0}},28837:(a,b,c)=>{var d=c(57797),e=Array.prototype.splice;a.exports=function(a){var b=this.__data__,c=d(b,a);return!(c<0)&&(c==b.length-1?b.pop():e.call(b,c,1),--this.size,!0)}},28977:(a,b,c)=>{var d=c(11539),e=1/0;a.exports=function(a){return a?(a=d(a))===e||a===-e?(a<0?-1:1)*17976931348623157e292:a==a?a:0:0===a?a:0}},29205:(a,b,c)=>{var d=c(8336);a.exports=function(a){var b=d(this,a).delete(a);return this.size-=!!b,b}},29395:(a,b,c)=>{var d=c(79474),e=c(70222),f=c(84713),g=d?d.toStringTag:void 0;a.exports=function(a){return null==a?void 0===a?"[object Undefined]":"[object Null]":g&&g in Object(a)?e(a):f(a)}},29508:(a,b,c)=>{var d=c(8336);a.exports=function(a){return d(this,a).get(a)}},30087:(a,b,c)=>{"use strict";c.d(b,{s0:()=>eu,gH:()=>eq,YB:()=>eD,HQ:()=>eB,xi:()=>eE,Hj:()=>eR,BX:()=>et,tA:()=>es,DW:()=>eL,y2:()=>eK,Ay:()=>ep,vf:()=>ex,Mk:()=>eN,Ps:()=>er,Mn:()=>eI,kA:()=>eM,Rh:()=>ez,w7:()=>eJ,zb:()=>eT,kr:()=>eo,_L:()=>ey,KC:()=>eS,A1:()=>ew,W7:()=>eC,AQ:()=>eQ,_f:()=>eF});var d={};c.r(d),c.d(d,{scaleBand:()=>e.A,scaleDiverging:()=>function a(){var b=aC(cT()(aj));return b.copy=function(){return cQ(b,a())},aq.K.apply(b,arguments)},scaleDivergingLog:()=>function a(){var b=aL(cT()).domain([.1,1,10]);return b.copy=function(){return cQ(b,a()).base(b.base())},aq.K.apply(b,arguments)},scaleDivergingPow:()=>cU,scaleDivergingSqrt:()=>cV,scaleDivergingSymlog:()=>function a(){var b=aO(cT());return b.copy=function(){return cQ(b,a()).constant(b.constant())},aq.K.apply(b,arguments)},scaleIdentity:()=>function a(b){var c;function d(a){return null==a||isNaN(a*=1)?c:a}return d.invert=d,d.domain=d.range=function(a){return arguments.length?(b=Array.from(a,ah),d):b.slice()},d.unknown=function(a){return arguments.length?(c=a,d):c},d.copy=function(){return a(b).unknown(c)},b=arguments.length?Array.from(b,ah):[0,1],aC(d)},scaleImplicit:()=>aP.h,scaleLinear:()=>aD,scaleLog:()=>function a(){let b=aL(ao()).domain([1,10]);return b.copy=()=>an(b,a()).base(b.base()),aq.C.apply(b,arguments),b},scaleOrdinal:()=>aP.A,scalePoint:()=>e.z,scalePow:()=>aU,scaleQuantile:()=>function a(){var b,c=[],d=[],e=[];function f(){var a=0,b=Math.max(1,d.length);for(e=Array(b-1);++a<b;)e[a-1]=function(a,b,c=q){if(!(!(d=a.length)||isNaN(b*=1))){if(b<=0||d<2)return+c(a[0],0,a);if(b>=1)return+c(a[d-1],d-1,a);var d,e=(d-1)*b,f=Math.floor(e),g=+c(a[f],f,a);return g+(c(a[f+1],f+1,a)-g)*(e-f)}}(c,a/b);return g}function g(a){return null==a||isNaN(a*=1)?b:d[s(e,a)]}return g.invertExtent=function(a){var b=d.indexOf(a);return b<0?[NaN,NaN]:[b>0?e[b-1]:c[0],b<e.length?e[b]:c[c.length-1]]},g.domain=function(a){if(!arguments.length)return c.slice();for(let b of(c=[],a))null==b||isNaN(b*=1)||c.push(b);return c.sort(m),f()},g.range=function(a){return arguments.length?(d=Array.from(a),f()):d.slice()},g.unknown=function(a){return arguments.length?(b=a,g):b},g.quantiles=function(){return e.slice()},g.copy=function(){return a().domain(c).range(d).unknown(b)},aq.C.apply(g,arguments)},scaleQuantize:()=>function a(){var b,c=0,d=1,e=1,f=[.5],g=[0,1];function h(a){return null!=a&&a<=a?g[s(f,a,0,e)]:b}function i(){var a=-1;for(f=Array(e);++a<e;)f[a]=((a+1)*d-(a-e)*c)/(e+1);return h}return h.domain=function(a){return arguments.length?([c,d]=a,c*=1,d*=1,i()):[c,d]},h.range=function(a){return arguments.length?(e=(g=Array.from(a)).length-1,i()):g.slice()},h.invertExtent=function(a){var b=g.indexOf(a);return b<0?[NaN,NaN]:b<1?[c,f[0]]:b>=e?[f[e-1],d]:[f[b-1],f[b]]},h.unknown=function(a){return arguments.length&&(b=a),h},h.thresholds=function(){return f.slice()},h.copy=function(){return a().domain([c,d]).range(g).unknown(b)},aq.C.apply(aC(h),arguments)},scaleRadial:()=>function a(){var b,c=ap(),d=[0,1],e=!1;function f(a){var d,f=Math.sign(d=c(a))*Math.sqrt(Math.abs(d));return isNaN(f)?b:e?Math.round(f):f}return f.invert=function(a){return c.invert(aW(a))},f.domain=function(a){return arguments.length?(c.domain(a),f):c.domain()},f.range=function(a){return arguments.length?(c.range((d=Array.from(a,ah)).map(aW)),f):d.slice()},f.rangeRound=function(a){return f.range(a).round(!0)},f.round=function(a){return arguments.length?(e=!!a,f):e},f.clamp=function(a){return arguments.length?(c.clamp(a),f):c.clamp()},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a(c.domain(),d).round(e).clamp(c.clamp()).unknown(b)},aq.C.apply(f,arguments),aC(f)},scaleSequential:()=>function a(){var b=aC(cP()(aj));return b.copy=function(){return cQ(b,a())},aq.K.apply(b,arguments)},scaleSequentialLog:()=>function a(){var b=aL(cP()).domain([1,10]);return b.copy=function(){return cQ(b,a()).base(b.base())},aq.K.apply(b,arguments)},scaleSequentialPow:()=>cR,scaleSequentialQuantile:()=>function a(){var b=[],c=aj;function d(a){if(null!=a&&!isNaN(a*=1))return c((s(b,a,1)-1)/(b.length-1))}return d.domain=function(a){if(!arguments.length)return b.slice();for(let c of(b=[],a))null==c||isNaN(c*=1)||b.push(c);return b.sort(m),d},d.interpolator=function(a){return arguments.length?(c=a,d):c},d.range=function(){return b.map((a,d)=>c(d/(b.length-1)))},d.quantiles=function(a){return Array.from({length:a+1},(c,d)=>(function(a,b,c){if(!(!(d=(a=Float64Array.from(function*(a,b){if(void 0===b)for(let b of a)null!=b&&(b*=1)>=b&&(yield b);else{let c=-1;for(let d of a)null!=(d=b(d,++c,a))&&(d*=1)>=d&&(yield d)}}(a,void 0))).length)||isNaN(b*=1))){if(b<=0||d<2)return aY(a);if(b>=1)return aX(a);var d,e=(d-1)*b,f=Math.floor(e),g=aX((function a(b,c,d=0,e=1/0,f){if(c=Math.floor(c),d=Math.floor(Math.max(0,d)),e=Math.floor(Math.min(b.length-1,e)),!(d<=c&&c<=e))return b;for(f=void 0===f?aZ:function(a=m){if(a===m)return aZ;if("function"!=typeof a)throw TypeError("compare is not a function");return(b,c)=>{let d=a(b,c);return d||0===d?d:(0===a(c,c))-(0===a(b,b))}}(f);e>d;){if(e-d>600){let g=e-d+1,h=c-d+1,i=Math.log(g),j=.5*Math.exp(2*i/3),k=.5*Math.sqrt(i*j*(g-j)/g)*(h-g/2<0?-1:1),l=Math.max(d,Math.floor(c-h*j/g+k)),m=Math.min(e,Math.floor(c+(g-h)*j/g+k));a(b,c,l,m,f)}let g=b[c],h=d,i=e;for(a$(b,d,c),f(b[e],g)>0&&a$(b,d,e);h<i;){for(a$(b,h,i),++h,--i;0>f(b[h],g);)++h;for(;f(b[i],g)>0;)--i}0===f(b[d],g)?a$(b,d,i):a$(b,++i,e),i<=c&&(d=i+1),c<=i&&(e=i-1)}return b})(a,f).subarray(0,f+1));return g+(aY(a.subarray(f+1))-g)*(e-f)}})(b,d/a))},d.copy=function(){return a(c).domain(b)},aq.K.apply(d,arguments)},scaleSequentialSqrt:()=>cS,scaleSequentialSymlog:()=>function a(){var b=aO(cP());return b.copy=function(){return cQ(b,a()).constant(b.constant())},aq.K.apply(b,arguments)},scaleSqrt:()=>aV,scaleSymlog:()=>function a(){var b=aO(ao());return b.copy=function(){return an(b,a()).constant(b.constant())},aq.C.apply(b,arguments)},scaleThreshold:()=>function a(){var b,c=[.5],d=[0,1],e=1;function f(a){return null!=a&&a<=a?d[s(c,a,0,e)]:b}return f.domain=function(a){return arguments.length?(e=Math.min((c=Array.from(a)).length,d.length-1),f):c.slice()},f.range=function(a){return arguments.length?(d=Array.from(a),e=Math.min(c.length,d.length-1),f):d.slice()},f.invertExtent=function(a){var b=d.indexOf(a);return[c[b-1],c[b]]},f.unknown=function(a){return arguments.length?(b=a,f):b},f.copy=function(){return a().domain(c).range(d).unknown(b)},aq.C.apply(f,arguments)},scaleTime:()=>cN,scaleUtc:()=>cO,tickFormat:()=>aB});var e=c(52415);let f=Math.sqrt(50),g=Math.sqrt(10),h=Math.sqrt(2);function i(a,b,c){let d,e,j,k=(b-a)/Math.max(0,c),l=Math.floor(Math.log10(k)),m=k/Math.pow(10,l),n=m>=f?10:m>=g?5:m>=h?2:1;return(l<0?(d=Math.round(a*(j=Math.pow(10,-l)/n)),e=Math.round(b*j),d/j<a&&++d,e/j>b&&--e,j=-j):(d=Math.round(a/(j=Math.pow(10,l)*n)),e=Math.round(b/j),d*j<a&&++d,e*j>b&&--e),e<d&&.5<=c&&c<2)?i(a,b,2*c):[d,e,j]}function j(a,b,c){if(b*=1,a*=1,!((c*=1)>0))return[];if(a===b)return[a];let d=b<a,[e,f,g]=d?i(b,a,c):i(a,b,c);if(!(f>=e))return[];let h=f-e+1,j=Array(h);if(d)if(g<0)for(let a=0;a<h;++a)j[a]=-((f-a)/g);else for(let a=0;a<h;++a)j[a]=(f-a)*g;else if(g<0)for(let a=0;a<h;++a)j[a]=-((e+a)/g);else for(let a=0;a<h;++a)j[a]=(e+a)*g;return j}function k(a,b,c){return i(a*=1,b*=1,c*=1)[2]}function l(a,b,c){b*=1,a*=1,c*=1;let d=b<a,e=d?k(b,a,c):k(a,b,c);return(d?-1:1)*(e<0?-(1/e):e)}function m(a,b){return null==a||null==b?NaN:a<b?-1:a>b?1:a>=b?0:NaN}function n(a,b){return null==a||null==b?NaN:b<a?-1:b>a?1:b>=a?0:NaN}function o(a){let b,c,d;function e(a,d,f=0,g=a.length){if(f<g){if(0!==b(d,d))return g;do{let b=f+g>>>1;0>c(a[b],d)?f=b+1:g=b}while(f<g)}return f}return 2!==a.length?(b=m,c=(b,c)=>m(a(b),c),d=(b,c)=>a(b)-c):(b=a===m||a===n?a:p,c=a,d=a),{left:e,center:function(a,b,c=0,f=a.length){let g=e(a,b,c,f-1);return g>c&&d(a[g-1],b)>-d(a[g],b)?g-1:g},right:function(a,d,e=0,f=a.length){if(e<f){if(0!==b(d,d))return f;do{let b=e+f>>>1;0>=c(a[b],d)?e=b+1:f=b}while(e<f)}return e}}}function p(){return 0}function q(a){return null===a?NaN:+a}let r=o(m),s=r.right;function t(a,b,c){a.prototype=b.prototype=c,c.constructor=a}function u(a,b){var c=Object.create(a.prototype);for(var d in b)c[d]=b[d];return c}function v(){}r.left,o(q).center;var w="\\s*([+-]?\\d+)\\s*",x="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",y="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",z=/^#([0-9a-f]{3,8})$/,A=RegExp(`^rgb\\(${w},${w},${w}\\)$`),B=RegExp(`^rgb\\(${y},${y},${y}\\)$`),C=RegExp(`^rgba\\(${w},${w},${w},${x}\\)$`),D=RegExp(`^rgba\\(${y},${y},${y},${x}\\)$`),E=RegExp(`^hsl\\(${x},${y},${y}\\)$`),F=RegExp(`^hsla\\(${x},${y},${y},${x}\\)$`),G={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function H(){return this.rgb().formatHex()}function I(){return this.rgb().formatRgb()}function J(a){var b,c;return a=(a+"").trim().toLowerCase(),(b=z.exec(a))?(c=b[1].length,b=parseInt(b[1],16),6===c?K(b):3===c?new N(b>>8&15|b>>4&240,b>>4&15|240&b,(15&b)<<4|15&b,1):8===c?L(b>>24&255,b>>16&255,b>>8&255,(255&b)/255):4===c?L(b>>12&15|b>>8&240,b>>8&15|b>>4&240,b>>4&15|240&b,((15&b)<<4|15&b)/255):null):(b=A.exec(a))?new N(b[1],b[2],b[3],1):(b=B.exec(a))?new N(255*b[1]/100,255*b[2]/100,255*b[3]/100,1):(b=C.exec(a))?L(b[1],b[2],b[3],b[4]):(b=D.exec(a))?L(255*b[1]/100,255*b[2]/100,255*b[3]/100,b[4]):(b=E.exec(a))?T(b[1],b[2]/100,b[3]/100,1):(b=F.exec(a))?T(b[1],b[2]/100,b[3]/100,b[4]):G.hasOwnProperty(a)?K(G[a]):"transparent"===a?new N(NaN,NaN,NaN,0):null}function K(a){return new N(a>>16&255,a>>8&255,255&a,1)}function L(a,b,c,d){return d<=0&&(a=b=c=NaN),new N(a,b,c,d)}function M(a,b,c,d){var e;return 1==arguments.length?((e=a)instanceof v||(e=J(e)),e)?new N((e=e.rgb()).r,e.g,e.b,e.opacity):new N:new N(a,b,c,null==d?1:d)}function N(a,b,c,d){this.r=+a,this.g=+b,this.b=+c,this.opacity=+d}function O(){return`#${S(this.r)}${S(this.g)}${S(this.b)}`}function P(){let a=Q(this.opacity);return`${1===a?"rgb(":"rgba("}${R(this.r)}, ${R(this.g)}, ${R(this.b)}${1===a?")":`, ${a})`}`}function Q(a){return isNaN(a)?1:Math.max(0,Math.min(1,a))}function R(a){return Math.max(0,Math.min(255,Math.round(a)||0))}function S(a){return((a=R(a))<16?"0":"")+a.toString(16)}function T(a,b,c,d){return d<=0?a=b=c=NaN:c<=0||c>=1?a=b=NaN:b<=0&&(a=NaN),new V(a,b,c,d)}function U(a){if(a instanceof V)return new V(a.h,a.s,a.l,a.opacity);if(a instanceof v||(a=J(a)),!a)return new V;if(a instanceof V)return a;var b=(a=a.rgb()).r/255,c=a.g/255,d=a.b/255,e=Math.min(b,c,d),f=Math.max(b,c,d),g=NaN,h=f-e,i=(f+e)/2;return h?(g=b===f?(c-d)/h+(c<d)*6:c===f?(d-b)/h+2:(b-c)/h+4,h/=i<.5?f+e:2-f-e,g*=60):h=i>0&&i<1?0:g,new V(g,h,i,a.opacity)}function V(a,b,c,d){this.h=+a,this.s=+b,this.l=+c,this.opacity=+d}function W(a){return(a=(a||0)%360)<0?a+360:a}function X(a){return Math.max(0,Math.min(1,a||0))}function Y(a,b,c){return(a<60?b+(c-b)*a/60:a<180?c:a<240?b+(c-b)*(240-a)/60:b)*255}function Z(a,b,c,d,e){var f=a*a,g=f*a;return((1-3*a+3*f-g)*b+(4-6*f+3*g)*c+(1+3*a+3*f-3*g)*d+g*e)/6}t(v,J,{copy(a){return Object.assign(new this.constructor,this,a)},displayable(){return this.rgb().displayable()},hex:H,formatHex:H,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return U(this).formatHsl()},formatRgb:I,toString:I}),t(N,M,u(v,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new N(this.r*a,this.g*a,this.b*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new N(this.r*a,this.g*a,this.b*a,this.opacity)},rgb(){return this},clamp(){return new N(R(this.r),R(this.g),R(this.b),Q(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:O,formatHex:O,formatHex8:function(){return`#${S(this.r)}${S(this.g)}${S(this.b)}${S((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:P,toString:P})),t(V,function(a,b,c,d){return 1==arguments.length?U(a):new V(a,b,c,null==d?1:d)},u(v,{brighter(a){return a=null==a?1.4285714285714286:Math.pow(1.4285714285714286,a),new V(this.h,this.s,this.l*a,this.opacity)},darker(a){return a=null==a?.7:Math.pow(.7,a),new V(this.h,this.s,this.l*a,this.opacity)},rgb(){var a=this.h%360+(this.h<0)*360,b=isNaN(a)||isNaN(this.s)?0:this.s,c=this.l,d=c+(c<.5?c:1-c)*b,e=2*c-d;return new N(Y(a>=240?a-240:a+120,e,d),Y(a,e,d),Y(a<120?a+240:a-120,e,d),this.opacity)},clamp(){return new V(W(this.h),X(this.s),X(this.l),Q(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let a=Q(this.opacity);return`${1===a?"hsl(":"hsla("}${W(this.h)}, ${100*X(this.s)}%, ${100*X(this.l)}%${1===a?")":`, ${a})`}`}}));let $=a=>()=>a;function _(a,b){var c=b-a;return c?function(b){return a+b*c}:$(isNaN(a)?b:a)}let aa=function a(b){var c,d=1==(c=+b)?_:function(a,b){var d,e,f;return b-a?(d=a,e=b,d=Math.pow(d,f=c),e=Math.pow(e,f)-d,f=1/f,function(a){return Math.pow(d+a*e,f)}):$(isNaN(a)?b:a)};function e(a,b){var c=d((a=M(a)).r,(b=M(b)).r),e=d(a.g,b.g),f=d(a.b,b.b),g=_(a.opacity,b.opacity);return function(b){return a.r=c(b),a.g=e(b),a.b=f(b),a.opacity=g(b),a+""}}return e.gamma=a,e}(1);function ab(a){return function(b){var c,d,e=b.length,f=Array(e),g=Array(e),h=Array(e);for(c=0;c<e;++c)d=M(b[c]),f[c]=d.r||0,g[c]=d.g||0,h[c]=d.b||0;return f=a(f),g=a(g),h=a(h),d.opacity=1,function(a){return d.r=f(a),d.g=g(a),d.b=h(a),d+""}}}function ac(a,b){return a*=1,b*=1,function(c){return a*(1-c)+b*c}}ab(function(a){var b=a.length-1;return function(c){var d=c<=0?c=0:c>=1?(c=1,b-1):Math.floor(c*b),e=a[d],f=a[d+1],g=d>0?a[d-1]:2*e-f,h=d<b-1?a[d+2]:2*f-e;return Z((c-d/b)*b,g,e,f,h)}}),ab(function(a){var b=a.length;return function(c){var d=Math.floor(((c%=1)<0?++c:c)*b),e=a[(d+b-1)%b],f=a[d%b],g=a[(d+1)%b],h=a[(d+2)%b];return Z((c-d/b)*b,e,f,g,h)}});var ad=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ae=RegExp(ad.source,"g");function af(a,b){var c,d,e=typeof b;return null==b||"boolean"===e?$(b):("number"===e?ac:"string"===e?(d=J(b))?(b=d,aa):function(a,b){var c,d,e,f,g,h=ad.lastIndex=ae.lastIndex=0,i=-1,j=[],k=[];for(a+="",b+="";(e=ad.exec(a))&&(f=ae.exec(b));)(g=f.index)>h&&(g=b.slice(h,g),j[i]?j[i]+=g:j[++i]=g),(e=e[0])===(f=f[0])?j[i]?j[i]+=f:j[++i]=f:(j[++i]=null,k.push({i:i,x:ac(e,f)})),h=ae.lastIndex;return h<b.length&&(g=b.slice(h),j[i]?j[i]+=g:j[++i]=g),j.length<2?k[0]?(c=k[0].x,function(a){return c(a)+""}):(d=b,function(){return d}):(b=k.length,function(a){for(var c,d=0;d<b;++d)j[(c=k[d]).i]=c.x(a);return j.join("")})}:b instanceof J?aa:b instanceof Date?function(a,b){var c=new Date;return a*=1,b*=1,function(d){return c.setTime(a*(1-d)+b*d),c}}:!ArrayBuffer.isView(c=b)||c instanceof DataView?Array.isArray(b)?function(a,b){var c,d=b?b.length:0,e=a?Math.min(d,a.length):0,f=Array(e),g=Array(d);for(c=0;c<e;++c)f[c]=af(a[c],b[c]);for(;c<d;++c)g[c]=b[c];return function(a){for(c=0;c<e;++c)g[c]=f[c](a);return g}}:"function"!=typeof b.valueOf&&"function"!=typeof b.toString||isNaN(b)?function(a,b){var c,d={},e={};for(c in(null===a||"object"!=typeof a)&&(a={}),(null===b||"object"!=typeof b)&&(b={}),b)c in a?d[c]=af(a[c],b[c]):e[c]=b[c];return function(a){for(c in d)e[c]=d[c](a);return e}}:ac:function(a,b){b||(b=[]);var c,d=a?Math.min(b.length,a.length):0,e=b.slice();return function(f){for(c=0;c<d;++c)e[c]=a[c]*(1-f)+b[c]*f;return e}})(a,b)}function ag(a,b){return a*=1,b*=1,function(c){return Math.round(a*(1-c)+b*c)}}function ah(a){return+a}var ai=[0,1];function aj(a){return a}function ak(a,b){var c;return(b-=a*=1)?function(c){return(c-a)/b}:(c=isNaN(b)?NaN:.5,function(){return c})}function al(a,b,c){var d=a[0],e=a[1],f=b[0],g=b[1];return e<d?(d=ak(e,d),f=c(g,f)):(d=ak(d,e),f=c(f,g)),function(a){return f(d(a))}}function am(a,b,c){var d=Math.min(a.length,b.length)-1,e=Array(d),f=Array(d),g=-1;for(a[d]<a[0]&&(a=a.slice().reverse(),b=b.slice().reverse());++g<d;)e[g]=ak(a[g],a[g+1]),f[g]=c(b[g],b[g+1]);return function(b){var c=s(a,b,1,d)-1;return f[c](e[c](b))}}function an(a,b){return b.domain(a.domain()).range(a.range()).interpolate(a.interpolate()).clamp(a.clamp()).unknown(a.unknown())}function ao(){var a,b,c,d,e,f,g=ai,h=ai,i=af,j=aj;function k(){var a,b,c,i=Math.min(g.length,h.length);return j!==aj&&(a=g[0],b=g[i-1],a>b&&(c=a,a=b,b=c),j=function(c){return Math.max(a,Math.min(b,c))}),d=i>2?am:al,e=f=null,l}function l(b){return null==b||isNaN(b*=1)?c:(e||(e=d(g.map(a),h,i)))(a(j(b)))}return l.invert=function(c){return j(b((f||(f=d(h,g.map(a),ac)))(c)))},l.domain=function(a){return arguments.length?(g=Array.from(a,ah),k()):g.slice()},l.range=function(a){return arguments.length?(h=Array.from(a),k()):h.slice()},l.rangeRound=function(a){return h=Array.from(a),i=ag,k()},l.clamp=function(a){return arguments.length?(j=!!a||aj,k()):j!==aj},l.interpolate=function(a){return arguments.length?(i=a,k()):i},l.unknown=function(a){return arguments.length?(c=a,l):c},function(c,d){return a=c,b=d,k()}}function ap(){return ao()(aj,aj)}var aq=c(37877),ar=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function as(a){var b;if(!(b=ar.exec(a)))throw Error("invalid format: "+a);return new at({fill:b[1],align:b[2],sign:b[3],symbol:b[4],zero:b[5],width:b[6],comma:b[7],precision:b[8]&&b[8].slice(1),trim:b[9],type:b[10]})}function at(a){this.fill=void 0===a.fill?" ":a.fill+"",this.align=void 0===a.align?">":a.align+"",this.sign=void 0===a.sign?"-":a.sign+"",this.symbol=void 0===a.symbol?"":a.symbol+"",this.zero=!!a.zero,this.width=void 0===a.width?void 0:+a.width,this.comma=!!a.comma,this.precision=void 0===a.precision?void 0:+a.precision,this.trim=!!a.trim,this.type=void 0===a.type?"":a.type+""}function au(a,b){if((c=(a=b?a.toExponential(b-1):a.toExponential()).indexOf("e"))<0)return null;var c,d=a.slice(0,c);return[d.length>1?d[0]+d.slice(2):d,+a.slice(c+1)]}function av(a){return(a=au(Math.abs(a)))?a[1]:NaN}function aw(a,b){var c=au(a,b);if(!c)return a+"";var d=c[0],e=c[1];return e<0?"0."+Array(-e).join("0")+d:d.length>e+1?d.slice(0,e+1)+"."+d.slice(e+1):d+Array(e-d.length+2).join("0")}as.prototype=at.prototype,at.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ax={"%":(a,b)=>(100*a).toFixed(b),b:a=>Math.round(a).toString(2),c:a=>a+"",d:function(a){return Math.abs(a=Math.round(a))>=1e21?a.toLocaleString("en").replace(/,/g,""):a.toString(10)},e:(a,b)=>a.toExponential(b),f:(a,b)=>a.toFixed(b),g:(a,b)=>a.toPrecision(b),o:a=>Math.round(a).toString(8),p:(a,b)=>aw(100*a,b),r:aw,s:function(a,b){var c=au(a,b);if(!c)return a+"";var d=c[0],e=c[1],f=e-(c0=3*Math.max(-8,Math.min(8,Math.floor(e/3))))+1,g=d.length;return f===g?d:f>g?d+Array(f-g+1).join("0"):f>0?d.slice(0,f)+"."+d.slice(f):"0."+Array(1-f).join("0")+au(a,Math.max(0,b+f-1))[0]},X:a=>Math.round(a).toString(16).toUpperCase(),x:a=>Math.round(a).toString(16)};function ay(a){return a}var az=Array.prototype.map,aA=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function aB(a,b,c,d){var e,f,g=l(a,b,c);switch((d=as(null==d?",f":d)).type){case"s":var h=Math.max(Math.abs(a),Math.abs(b));return null!=d.precision||isNaN(f=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(av(h)/3)))-av(Math.abs(g))))||(d.precision=f),c3(d,h);case"":case"e":case"g":case"p":case"r":null!=d.precision||isNaN(f=Math.max(0,av(Math.abs(Math.max(Math.abs(a),Math.abs(b)))-(e=Math.abs(e=g)))-av(e))+1)||(d.precision=f-("e"===d.type));break;case"f":case"%":null!=d.precision||isNaN(f=Math.max(0,-av(Math.abs(g))))||(d.precision=f-("%"===d.type)*2)}return c2(d)}function aC(a){var b=a.domain;return a.ticks=function(a){var c=b();return j(c[0],c[c.length-1],null==a?10:a)},a.tickFormat=function(a,c){var d=b();return aB(d[0],d[d.length-1],null==a?10:a,c)},a.nice=function(c){null==c&&(c=10);var d,e,f=b(),g=0,h=f.length-1,i=f[g],j=f[h],l=10;for(j<i&&(e=i,i=j,j=e,e=g,g=h,h=e);l-- >0;){if((e=k(i,j,c))===d)return f[g]=i,f[h]=j,b(f);if(e>0)i=Math.floor(i/e)*e,j=Math.ceil(j/e)*e;else if(e<0)i=Math.ceil(i*e)/e,j=Math.floor(j*e)/e;else break;d=e}return a},a}function aD(){var a=ap();return a.copy=function(){return an(a,aD())},aq.C.apply(a,arguments),aC(a)}function aE(a,b){a=a.slice();var c,d=0,e=a.length-1,f=a[d],g=a[e];return g<f&&(c=d,d=e,e=c,c=f,f=g,g=c),a[d]=b.floor(f),a[e]=b.ceil(g),a}function aF(a){return Math.log(a)}function aG(a){return Math.exp(a)}function aH(a){return-Math.log(-a)}function aI(a){return-Math.exp(-a)}function aJ(a){return isFinite(a)?+("1e"+a):a<0?0:a}function aK(a){return(b,c)=>-a(-b,c)}function aL(a){let b,c,d=a(aF,aG),e=d.domain,f=10;function g(){var g,h;return b=(g=f)===Math.E?Math.log:10===g&&Math.log10||2===g&&Math.log2||(g=Math.log(g),a=>Math.log(a)/g),c=10===(h=f)?aJ:h===Math.E?Math.exp:a=>Math.pow(h,a),e()[0]<0?(b=aK(b),c=aK(c),a(aH,aI)):a(aF,aG),d}return d.base=function(a){return arguments.length?(f=+a,g()):f},d.domain=function(a){return arguments.length?(e(a),g()):e()},d.ticks=a=>{let d,g,h=e(),i=h[0],k=h[h.length-1],l=k<i;l&&([i,k]=[k,i]);let m=b(i),n=b(k),o=null==a?10:+a,p=[];if(!(f%1)&&n-m<o){if(m=Math.floor(m),n=Math.ceil(n),i>0){for(;m<=n;++m)for(d=1;d<f;++d)if(!((g=m<0?d/c(-m):d*c(m))<i)){if(g>k)break;p.push(g)}}else for(;m<=n;++m)for(d=f-1;d>=1;--d)if(!((g=m>0?d/c(-m):d*c(m))<i)){if(g>k)break;p.push(g)}2*p.length<o&&(p=j(i,k,o))}else p=j(m,n,Math.min(n-m,o)).map(c);return l?p.reverse():p},d.tickFormat=(a,e)=>{if(null==a&&(a=10),null==e&&(e=10===f?"s":","),"function"!=typeof e&&(f%1||null!=(e=as(e)).precision||(e.trim=!0),e=c2(e)),a===1/0)return e;let g=Math.max(1,f*a/d.ticks().length);return a=>{let d=a/c(Math.round(b(a)));return d*f<f-.5&&(d*=f),d<=g?e(a):""}},d.nice=()=>e(aE(e(),{floor:a=>c(Math.floor(b(a))),ceil:a=>c(Math.ceil(b(a)))})),d}function aM(a){return function(b){return Math.sign(b)*Math.log1p(Math.abs(b/a))}}function aN(a){return function(b){return Math.sign(b)*Math.expm1(Math.abs(b))*a}}function aO(a){var b=1,c=a(aM(1),aN(b));return c.constant=function(c){return arguments.length?a(aM(b=+c),aN(b)):b},aC(c)}c2=(c1=function(a){var b,c,d,e=void 0===a.grouping||void 0===a.thousands?ay:(b=az.call(a.grouping,Number),c=a.thousands+"",function(a,d){for(var e=a.length,f=[],g=0,h=b[0],i=0;e>0&&h>0&&(i+h+1>d&&(h=Math.max(1,d-i)),f.push(a.substring(e-=h,e+h)),!((i+=h+1)>d));)h=b[g=(g+1)%b.length];return f.reverse().join(c)}),f=void 0===a.currency?"":a.currency[0]+"",g=void 0===a.currency?"":a.currency[1]+"",h=void 0===a.decimal?".":a.decimal+"",i=void 0===a.numerals?ay:(d=az.call(a.numerals,String),function(a){return a.replace(/[0-9]/g,function(a){return d[+a]})}),j=void 0===a.percent?"%":a.percent+"",k=void 0===a.minus?"−":a.minus+"",l=void 0===a.nan?"NaN":a.nan+"";function m(a){var b=(a=as(a)).fill,c=a.align,d=a.sign,m=a.symbol,n=a.zero,o=a.width,p=a.comma,q=a.precision,r=a.trim,s=a.type;"n"===s?(p=!0,s="g"):ax[s]||(void 0===q&&(q=12),r=!0,s="g"),(n||"0"===b&&"="===c)&&(n=!0,b="0",c="=");var t="$"===m?f:"#"===m&&/[boxX]/.test(s)?"0"+s.toLowerCase():"",u="$"===m?g:/[%p]/.test(s)?j:"",v=ax[s],w=/[defgprs%]/.test(s);function x(a){var f,g,j,m=t,x=u;if("c"===s)x=v(a)+x,a="";else{var y=(a*=1)<0||1/a<0;if(a=isNaN(a)?l:v(Math.abs(a),q),r&&(a=function(a){a:for(var b,c=a.length,d=1,e=-1;d<c;++d)switch(a[d]){case".":e=b=d;break;case"0":0===e&&(e=d),b=d;break;default:if(!+a[d])break a;e>0&&(e=0)}return e>0?a.slice(0,e)+a.slice(b+1):a}(a)),y&&0==+a&&"+"!==d&&(y=!1),m=(y?"("===d?d:k:"-"===d||"("===d?"":d)+m,x=("s"===s?aA[8+c0/3]:"")+x+(y&&"("===d?")":""),w){for(f=-1,g=a.length;++f<g;)if(48>(j=a.charCodeAt(f))||j>57){x=(46===j?h+a.slice(f+1):a.slice(f))+x,a=a.slice(0,f);break}}}p&&!n&&(a=e(a,1/0));var z=m.length+a.length+x.length,A=z<o?Array(o-z+1).join(b):"";switch(p&&n&&(a=e(A+a,A.length?o-x.length:1/0),A=""),c){case"<":a=m+a+x+A;break;case"=":a=m+A+a+x;break;case"^":a=A.slice(0,z=A.length>>1)+m+a+x+A.slice(z);break;default:a=A+m+a+x}return i(a)}return q=void 0===q?6:/[gprs]/.test(s)?Math.max(1,Math.min(21,q)):Math.max(0,Math.min(20,q)),x.toString=function(){return a+""},x}return{format:m,formatPrefix:function(a,b){var c=m(((a=as(a)).type="f",a)),d=3*Math.max(-8,Math.min(8,Math.floor(av(b)/3))),e=Math.pow(10,-d),f=aA[8+d/3];return function(a){return c(e*a)+f}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,c3=c1.formatPrefix;var aP=c(8886);function aQ(a){return function(b){return b<0?-Math.pow(-b,a):Math.pow(b,a)}}function aR(a){return a<0?-Math.sqrt(-a):Math.sqrt(a)}function aS(a){return a<0?-a*a:a*a}function aT(a){var b=a(aj,aj),c=1;return b.exponent=function(b){return arguments.length?1==(c=+b)?a(aj,aj):.5===c?a(aR,aS):a(aQ(c),aQ(1/c)):c},aC(b)}function aU(){var a=aT(ao());return a.copy=function(){return an(a,aU()).exponent(a.exponent())},aq.C.apply(a,arguments),a}function aV(){return aU.apply(null,arguments).exponent(.5)}function aW(a){return Math.sign(a)*a*a}function aX(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c<b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c<e||void 0===c&&e>=e)&&(c=e)}return c}function aY(a,b){let c;if(void 0===b)for(let b of a)null!=b&&(c>b||void 0===c&&b>=b)&&(c=b);else{let d=-1;for(let e of a)null!=(e=b(e,++d,a))&&(c>e||void 0===c&&e>=e)&&(c=e)}return c}function aZ(a,b){return(null==a||!(a>=a))-(null==b||!(b>=b))||(a<b?-1:+(a>b))}function a$(a,b,c){let d=a[b];a[b]=a[c],a[c]=d}let a_=new Date,a0=new Date;function a1(a,b,c,d){function e(b){return a(b=0==arguments.length?new Date:new Date(+b)),b}return e.floor=b=>(a(b=new Date(+b)),b),e.ceil=c=>(a(c=new Date(c-1)),b(c,1),a(c),c),e.round=a=>{let b=e(a),c=e.ceil(a);return a-b<c-a?b:c},e.offset=(a,c)=>(b(a=new Date(+a),null==c?1:Math.floor(c)),a),e.range=(c,d,f)=>{let g,h=[];if(c=e.ceil(c),f=null==f?1:Math.floor(f),!(c<d)||!(f>0))return h;do h.push(g=new Date(+c)),b(c,f),a(c);while(g<c&&c<d);return h},e.filter=c=>a1(b=>{if(b>=b)for(;a(b),!c(b);)b.setTime(b-1)},(a,d)=>{if(a>=a)if(d<0)for(;++d<=0;)for(;b(a,-1),!c(a););else for(;--d>=0;)for(;b(a,1),!c(a););}),c&&(e.count=(b,d)=>(a_.setTime(+b),a0.setTime(+d),a(a_),a(a0),Math.floor(c(a_,a0))),e.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?e.filter(d?b=>d(b)%a==0:b=>e.count(0,b)%a==0):e:null),e}let a2=a1(()=>{},(a,b)=>{a.setTime(+a+b)},(a,b)=>b-a);a2.every=a=>isFinite(a=Math.floor(a))&&a>0?a>1?a1(b=>{b.setTime(Math.floor(b/a)*a)},(b,c)=>{b.setTime(+b+c*a)},(b,c)=>(c-b)/a):a2:null,a2.range;let a3=a1(a=>{a.setTime(a-a.getMilliseconds())},(a,b)=>{a.setTime(+a+1e3*b)},(a,b)=>(b-a)/1e3,a=>a.getUTCSeconds());a3.range;let a4=a1(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds())},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getMinutes());a4.range;let a5=a1(a=>{a.setUTCSeconds(0,0)},(a,b)=>{a.setTime(+a+6e4*b)},(a,b)=>(b-a)/6e4,a=>a.getUTCMinutes());a5.range;let a6=a1(a=>{a.setTime(a-a.getMilliseconds()-1e3*a.getSeconds()-6e4*a.getMinutes())},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getHours());a6.range;let a7=a1(a=>{a.setUTCMinutes(0,0,0)},(a,b)=>{a.setTime(+a+36e5*b)},(a,b)=>(b-a)/36e5,a=>a.getUTCHours());a7.range;let a8=a1(a=>a.setHours(0,0,0,0),(a,b)=>a.setDate(a.getDate()+b),(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/864e5,a=>a.getDate()-1);a8.range;let a9=a1(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>a.getUTCDate()-1);a9.range;let ba=a1(a=>{a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+b)},(a,b)=>(b-a)/864e5,a=>Math.floor(a/864e5));function bb(a){return a1(b=>{b.setDate(b.getDate()-(b.getDay()+7-a)%7),b.setHours(0,0,0,0)},(a,b)=>{a.setDate(a.getDate()+7*b)},(a,b)=>(b-a-(b.getTimezoneOffset()-a.getTimezoneOffset())*6e4)/6048e5)}ba.range;let bc=bb(0),bd=bb(1),be=bb(2),bf=bb(3),bg=bb(4),bh=bb(5),bi=bb(6);function bj(a){return a1(b=>{b.setUTCDate(b.getUTCDate()-(b.getUTCDay()+7-a)%7),b.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCDate(a.getUTCDate()+7*b)},(a,b)=>(b-a)/6048e5)}bc.range,bd.range,be.range,bf.range,bg.range,bh.range,bi.range;let bk=bj(0),bl=bj(1),bm=bj(2),bn=bj(3),bo=bj(4),bp=bj(5),bq=bj(6);bk.range,bl.range,bm.range,bn.range,bo.range,bp.range,bq.range;let br=a1(a=>{a.setDate(1),a.setHours(0,0,0,0)},(a,b)=>{a.setMonth(a.getMonth()+b)},(a,b)=>b.getMonth()-a.getMonth()+(b.getFullYear()-a.getFullYear())*12,a=>a.getMonth());br.range;let bs=a1(a=>{a.setUTCDate(1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCMonth(a.getUTCMonth()+b)},(a,b)=>b.getUTCMonth()-a.getUTCMonth()+(b.getUTCFullYear()-a.getUTCFullYear())*12,a=>a.getUTCMonth());bs.range;let bt=a1(a=>{a.setMonth(0,1),a.setHours(0,0,0,0)},(a,b)=>{a.setFullYear(a.getFullYear()+b)},(a,b)=>b.getFullYear()-a.getFullYear(),a=>a.getFullYear());bt.every=a=>isFinite(a=Math.floor(a))&&a>0?a1(b=>{b.setFullYear(Math.floor(b.getFullYear()/a)*a),b.setMonth(0,1),b.setHours(0,0,0,0)},(b,c)=>{b.setFullYear(b.getFullYear()+c*a)}):null,bt.range;let bu=a1(a=>{a.setUTCMonth(0,1),a.setUTCHours(0,0,0,0)},(a,b)=>{a.setUTCFullYear(a.getUTCFullYear()+b)},(a,b)=>b.getUTCFullYear()-a.getUTCFullYear(),a=>a.getUTCFullYear());function bv(a,b,c,d,e,f){let g=[[a3,1,1e3],[a3,5,5e3],[a3,15,15e3],[a3,30,3e4],[f,1,6e4],[f,5,3e5],[f,15,9e5],[f,30,18e5],[e,1,36e5],[e,3,108e5],[e,6,216e5],[e,12,432e5],[d,1,864e5],[d,2,1728e5],[c,1,6048e5],[b,1,2592e6],[b,3,7776e6],[a,1,31536e6]];function h(b,c,d){let e=Math.abs(c-b)/d,f=o(([,,a])=>a).right(g,e);if(f===g.length)return a.every(l(b/31536e6,c/31536e6,d));if(0===f)return a2.every(Math.max(l(b,c,d),1));let[h,i]=g[e/g[f-1][2]<g[f][2]/e?f-1:f];return h.every(i)}return[function(a,b,c){let d=b<a;d&&([a,b]=[b,a]);let e=c&&"function"==typeof c.range?c:h(a,b,c),f=e?e.range(a,+b+1):[];return d?f.reverse():f},h]}bu.every=a=>isFinite(a=Math.floor(a))&&a>0?a1(b=>{b.setUTCFullYear(Math.floor(b.getUTCFullYear()/a)*a),b.setUTCMonth(0,1),b.setUTCHours(0,0,0,0)},(b,c)=>{b.setUTCFullYear(b.getUTCFullYear()+c*a)}):null,bu.range;let[bw,bx]=bv(bu,bs,bk,ba,a7,a5),[by,bz]=bv(bt,br,bc,a8,a6,a4);function bA(a){if(0<=a.y&&a.y<100){var b=new Date(-1,a.m,a.d,a.H,a.M,a.S,a.L);return b.setFullYear(a.y),b}return new Date(a.y,a.m,a.d,a.H,a.M,a.S,a.L)}function bB(a){if(0<=a.y&&a.y<100){var b=new Date(Date.UTC(-1,a.m,a.d,a.H,a.M,a.S,a.L));return b.setUTCFullYear(a.y),b}return new Date(Date.UTC(a.y,a.m,a.d,a.H,a.M,a.S,a.L))}function bC(a,b,c){return{y:a,m:b,d:c,H:0,M:0,S:0,L:0}}var bD={"-":"",_:" ",0:"0"},bE=/^\s*\d+/,bF=/^%/,bG=/[\\^$*+?|[\]().{}]/g;function bH(a,b,c){var d=a<0?"-":"",e=(d?-a:a)+"",f=e.length;return d+(f<c?Array(c-f+1).join(b)+e:e)}function bI(a){return a.replace(bG,"\\$&")}function bJ(a){return RegExp("^(?:"+a.map(bI).join("|")+")","i")}function bK(a){return new Map(a.map((a,b)=>[a.toLowerCase(),b]))}function bL(a,b,c){var d=bE.exec(b.slice(c,c+1));return d?(a.w=+d[0],c+d[0].length):-1}function bM(a,b,c){var d=bE.exec(b.slice(c,c+1));return d?(a.u=+d[0],c+d[0].length):-1}function bN(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.U=+d[0],c+d[0].length):-1}function bO(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.V=+d[0],c+d[0].length):-1}function bP(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.W=+d[0],c+d[0].length):-1}function bQ(a,b,c){var d=bE.exec(b.slice(c,c+4));return d?(a.y=+d[0],c+d[0].length):-1}function bR(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.y=+d[0]+(+d[0]>68?1900:2e3),c+d[0].length):-1}function bS(a,b,c){var d=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(b.slice(c,c+6));return d?(a.Z=d[1]?0:-(d[2]+(d[3]||"00")),c+d[0].length):-1}function bT(a,b,c){var d=bE.exec(b.slice(c,c+1));return d?(a.q=3*d[0]-3,c+d[0].length):-1}function bU(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.m=d[0]-1,c+d[0].length):-1}function bV(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.d=+d[0],c+d[0].length):-1}function bW(a,b,c){var d=bE.exec(b.slice(c,c+3));return d?(a.m=0,a.d=+d[0],c+d[0].length):-1}function bX(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.H=+d[0],c+d[0].length):-1}function bY(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.M=+d[0],c+d[0].length):-1}function bZ(a,b,c){var d=bE.exec(b.slice(c,c+2));return d?(a.S=+d[0],c+d[0].length):-1}function b$(a,b,c){var d=bE.exec(b.slice(c,c+3));return d?(a.L=+d[0],c+d[0].length):-1}function b_(a,b,c){var d=bE.exec(b.slice(c,c+6));return d?(a.L=Math.floor(d[0]/1e3),c+d[0].length):-1}function b0(a,b,c){var d=bF.exec(b.slice(c,c+1));return d?c+d[0].length:-1}function b1(a,b,c){var d=bE.exec(b.slice(c));return d?(a.Q=+d[0],c+d[0].length):-1}function b2(a,b,c){var d=bE.exec(b.slice(c));return d?(a.s=+d[0],c+d[0].length):-1}function b3(a,b){return bH(a.getDate(),b,2)}function b4(a,b){return bH(a.getHours(),b,2)}function b5(a,b){return bH(a.getHours()%12||12,b,2)}function b6(a,b){return bH(1+a8.count(bt(a),a),b,3)}function b7(a,b){return bH(a.getMilliseconds(),b,3)}function b8(a,b){return b7(a,b)+"000"}function b9(a,b){return bH(a.getMonth()+1,b,2)}function ca(a,b){return bH(a.getMinutes(),b,2)}function cb(a,b){return bH(a.getSeconds(),b,2)}function cc(a){var b=a.getDay();return 0===b?7:b}function cd(a,b){return bH(bc.count(bt(a)-1,a),b,2)}function ce(a){var b=a.getDay();return b>=4||0===b?bg(a):bg.ceil(a)}function cf(a,b){return a=ce(a),bH(bg.count(bt(a),a)+(4===bt(a).getDay()),b,2)}function cg(a){return a.getDay()}function ch(a,b){return bH(bd.count(bt(a)-1,a),b,2)}function ci(a,b){return bH(a.getFullYear()%100,b,2)}function cj(a,b){return bH((a=ce(a)).getFullYear()%100,b,2)}function ck(a,b){return bH(a.getFullYear()%1e4,b,4)}function cl(a,b){var c=a.getDay();return bH((a=c>=4||0===c?bg(a):bg.ceil(a)).getFullYear()%1e4,b,4)}function cm(a){var b=a.getTimezoneOffset();return(b>0?"-":(b*=-1,"+"))+bH(b/60|0,"0",2)+bH(b%60,"0",2)}function cn(a,b){return bH(a.getUTCDate(),b,2)}function co(a,b){return bH(a.getUTCHours(),b,2)}function cp(a,b){return bH(a.getUTCHours()%12||12,b,2)}function cq(a,b){return bH(1+a9.count(bu(a),a),b,3)}function cr(a,b){return bH(a.getUTCMilliseconds(),b,3)}function cs(a,b){return cr(a,b)+"000"}function ct(a,b){return bH(a.getUTCMonth()+1,b,2)}function cu(a,b){return bH(a.getUTCMinutes(),b,2)}function cv(a,b){return bH(a.getUTCSeconds(),b,2)}function cw(a){var b=a.getUTCDay();return 0===b?7:b}function cx(a,b){return bH(bk.count(bu(a)-1,a),b,2)}function cy(a){var b=a.getUTCDay();return b>=4||0===b?bo(a):bo.ceil(a)}function cz(a,b){return a=cy(a),bH(bo.count(bu(a),a)+(4===bu(a).getUTCDay()),b,2)}function cA(a){return a.getUTCDay()}function cB(a,b){return bH(bl.count(bu(a)-1,a),b,2)}function cC(a,b){return bH(a.getUTCFullYear()%100,b,2)}function cD(a,b){return bH((a=cy(a)).getUTCFullYear()%100,b,2)}function cE(a,b){return bH(a.getUTCFullYear()%1e4,b,4)}function cF(a,b){var c=a.getUTCDay();return bH((a=c>=4||0===c?bo(a):bo.ceil(a)).getUTCFullYear()%1e4,b,4)}function cG(){return"+0000"}function cH(){return"%"}function cI(a){return+a}function cJ(a){return Math.floor(a/1e3)}function cK(a){return new Date(a)}function cL(a){return a instanceof Date?+a:+new Date(+a)}function cM(a,b,c,d,e,f,g,h,i,j){var k=ap(),l=k.invert,m=k.domain,n=j(".%L"),o=j(":%S"),p=j("%I:%M"),q=j("%I %p"),r=j("%a %d"),s=j("%b %d"),t=j("%B"),u=j("%Y");function v(a){return(i(a)<a?n:h(a)<a?o:g(a)<a?p:f(a)<a?q:d(a)<a?e(a)<a?r:s:c(a)<a?t:u)(a)}return k.invert=function(a){return new Date(l(a))},k.domain=function(a){return arguments.length?m(Array.from(a,cL)):m().map(cK)},k.ticks=function(b){var c=m();return a(c[0],c[c.length-1],null==b?10:b)},k.tickFormat=function(a,b){return null==b?v:j(b)},k.nice=function(a){var c=m();return a&&"function"==typeof a.range||(a=b(c[0],c[c.length-1],null==a?10:a)),a?m(aE(c,a)):k},k.copy=function(){return an(k,cM(a,b,c,d,e,f,g,h,i,j))},k}function cN(){return aq.C.apply(cM(by,bz,bt,br,bc,a8,a6,a4,a3,c5).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cO(){return aq.C.apply(cM(bw,bx,bu,bs,bk,a9,a7,a5,a3,c6).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cP(){var a,b,c,d,e,f=0,g=1,h=aj,i=!1;function j(b){return null==b||isNaN(b*=1)?e:h(0===c?.5:(b=(d(b)-a)*c,i?Math.max(0,Math.min(1,b)):b))}function k(a){return function(b){var c,d;return arguments.length?([c,d]=b,h=a(c,d),j):[h(0),h(1)]}}return j.domain=function(e){return arguments.length?([f,g]=e,a=d(f*=1),b=d(g*=1),c=a===b?0:1/(b-a),j):[f,g]},j.clamp=function(a){return arguments.length?(i=!!a,j):i},j.interpolator=function(a){return arguments.length?(h=a,j):h},j.range=k(af),j.rangeRound=k(ag),j.unknown=function(a){return arguments.length?(e=a,j):e},function(e){return d=e,a=e(f),b=e(g),c=a===b?0:1/(b-a),j}}function cQ(a,b){return b.domain(a.domain()).interpolator(a.interpolator()).clamp(a.clamp()).unknown(a.unknown())}function cR(){var a=aT(cP());return a.copy=function(){return cQ(a,cR()).exponent(a.exponent())},aq.K.apply(a,arguments)}function cS(){return cR.apply(null,arguments).exponent(.5)}function cT(){var a,b,c,d,e,f,g,h=0,i=.5,j=1,k=1,l=aj,m=!1;function n(a){return isNaN(a*=1)?g:(a=.5+((a=+f(a))-b)*(k*a<k*b?d:e),l(m?Math.max(0,Math.min(1,a)):a))}function o(a){return function(b){var c,d,e;return arguments.length?([c,d,e]=b,l=function(a,b){void 0===b&&(b=a,a=af);for(var c=0,d=b.length-1,e=b[0],f=Array(d<0?0:d);c<d;)f[c]=a(e,e=b[++c]);return function(a){var b=Math.max(0,Math.min(d-1,Math.floor(a*=d)));return f[b](a-b)}}(a,[c,d,e]),n):[l(0),l(.5),l(1)]}}return n.domain=function(g){return arguments.length?([h,i,j]=g,a=f(h*=1),b=f(i*=1),c=f(j*=1),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n):[h,i,j]},n.clamp=function(a){return arguments.length?(m=!!a,n):m},n.interpolator=function(a){return arguments.length?(l=a,n):l},n.range=o(af),n.rangeRound=o(ag),n.unknown=function(a){return arguments.length?(g=a,n):g},function(g){return f=g,a=g(h),b=g(i),c=g(j),d=a===b?0:.5/(b-a),e=b===c?0:.5/(c-b),k=b<a?-1:1,n}}function cU(){var a=aT(cT());return a.copy=function(){return cQ(a,cU()).exponent(a.exponent())},aq.K.apply(a,arguments)}function cV(){return cU.apply(null,arguments).exponent(.5)}function cW(a,b){if((e=a.length)>1)for(var c,d,e,f=1,g=a[b[0]],h=g.length;f<e;++f)for(d=g,g=a[b[f]],c=0;c<h;++c)g[c][1]+=g[c][0]=isNaN(d[c][1])?d[c][0]:d[c][1]}c5=(c4=function(a){var b=a.dateTime,c=a.date,d=a.time,e=a.periods,f=a.days,g=a.shortDays,h=a.months,i=a.shortMonths,j=bJ(e),k=bK(e),l=bJ(f),m=bK(f),n=bJ(g),o=bK(g),p=bJ(h),q=bK(h),r=bJ(i),s=bK(i),t={a:function(a){return g[a.getDay()]},A:function(a){return f[a.getDay()]},b:function(a){return i[a.getMonth()]},B:function(a){return h[a.getMonth()]},c:null,d:b3,e:b3,f:b8,g:cj,G:cl,H:b4,I:b5,j:b6,L:b7,m:b9,M:ca,p:function(a){return e[+(a.getHours()>=12)]},q:function(a){return 1+~~(a.getMonth()/3)},Q:cI,s:cJ,S:cb,u:cc,U:cd,V:cf,w:cg,W:ch,x:null,X:null,y:ci,Y:ck,Z:cm,"%":cH},u={a:function(a){return g[a.getUTCDay()]},A:function(a){return f[a.getUTCDay()]},b:function(a){return i[a.getUTCMonth()]},B:function(a){return h[a.getUTCMonth()]},c:null,d:cn,e:cn,f:cs,g:cD,G:cF,H:co,I:cp,j:cq,L:cr,m:ct,M:cu,p:function(a){return e[+(a.getUTCHours()>=12)]},q:function(a){return 1+~~(a.getUTCMonth()/3)},Q:cI,s:cJ,S:cv,u:cw,U:cx,V:cz,w:cA,W:cB,x:null,X:null,y:cC,Y:cE,Z:cG,"%":cH},v={a:function(a,b,c){var d=n.exec(b.slice(c));return d?(a.w=o.get(d[0].toLowerCase()),c+d[0].length):-1},A:function(a,b,c){var d=l.exec(b.slice(c));return d?(a.w=m.get(d[0].toLowerCase()),c+d[0].length):-1},b:function(a,b,c){var d=r.exec(b.slice(c));return d?(a.m=s.get(d[0].toLowerCase()),c+d[0].length):-1},B:function(a,b,c){var d=p.exec(b.slice(c));return d?(a.m=q.get(d[0].toLowerCase()),c+d[0].length):-1},c:function(a,c,d){return y(a,b,c,d)},d:bV,e:bV,f:b_,g:bR,G:bQ,H:bX,I:bX,j:bW,L:b$,m:bU,M:bY,p:function(a,b,c){var d=j.exec(b.slice(c));return d?(a.p=k.get(d[0].toLowerCase()),c+d[0].length):-1},q:bT,Q:b1,s:b2,S:bZ,u:bM,U:bN,V:bO,w:bL,W:bP,x:function(a,b,d){return y(a,c,b,d)},X:function(a,b,c){return y(a,d,b,c)},y:bR,Y:bQ,Z:bS,"%":b0};function w(a,b){return function(c){var d,e,f,g=[],h=-1,i=0,j=a.length;for(c instanceof Date||(c=new Date(+c));++h<j;)37===a.charCodeAt(h)&&(g.push(a.slice(i,h)),null!=(e=bD[d=a.charAt(++h)])?d=a.charAt(++h):e="e"===d?" ":"0",(f=b[d])&&(d=f(c,e)),g.push(d),i=h+1);return g.push(a.slice(i,h)),g.join("")}}function x(a,b){return function(c){var d,e,f=bC(1900,void 0,1);if(y(f,a,c+="",0)!=c.length)return null;if("Q"in f)return new Date(f.Q);if("s"in f)return new Date(1e3*f.s+("L"in f?f.L:0));if(!b||"Z"in f||(f.Z=0),"p"in f&&(f.H=f.H%12+12*f.p),void 0===f.m&&(f.m="q"in f?f.q:0),"V"in f){if(f.V<1||f.V>53)return null;"w"in f||(f.w=1),"Z"in f?(d=(e=(d=bB(bC(f.y,0,1))).getUTCDay())>4||0===e?bl.ceil(d):bl(d),d=a9.offset(d,(f.V-1)*7),f.y=d.getUTCFullYear(),f.m=d.getUTCMonth(),f.d=d.getUTCDate()+(f.w+6)%7):(d=(e=(d=bA(bC(f.y,0,1))).getDay())>4||0===e?bd.ceil(d):bd(d),d=a8.offset(d,(f.V-1)*7),f.y=d.getFullYear(),f.m=d.getMonth(),f.d=d.getDate()+(f.w+6)%7)}else("W"in f||"U"in f)&&("w"in f||(f.w="u"in f?f.u%7:+("W"in f)),e="Z"in f?bB(bC(f.y,0,1)).getUTCDay():bA(bC(f.y,0,1)).getDay(),f.m=0,f.d="W"in f?(f.w+6)%7+7*f.W-(e+5)%7:f.w+7*f.U-(e+6)%7);return"Z"in f?(f.H+=f.Z/100|0,f.M+=f.Z%100,bB(f)):bA(f)}}function y(a,b,c,d){for(var e,f,g=0,h=b.length,i=c.length;g<h;){if(d>=i)return -1;if(37===(e=b.charCodeAt(g++))){if(!(f=v[(e=b.charAt(g++))in bD?b.charAt(g++):e])||(d=f(a,c,d))<0)return -1}else if(e!=c.charCodeAt(d++))return -1}return d}return t.x=w(c,t),t.X=w(d,t),t.c=w(b,t),u.x=w(c,u),u.X=w(d,u),u.c=w(b,u),{format:function(a){var b=w(a+="",t);return b.toString=function(){return a},b},parse:function(a){var b=x(a+="",!1);return b.toString=function(){return a},b},utcFormat:function(a){var b=w(a+="",u);return b.toString=function(){return a},b},utcParse:function(a){var b=x(a+="",!0);return b.toString=function(){return a},b}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c4.parse,c6=c4.utcFormat,c4.utcParse;var cX=c(48657),cY=c(22786);function cZ(a){for(var b=a.length,c=Array(b);--b>=0;)c[b]=b;return c}function c$(a,b){return a[b]}function c_(a){let b=[];return b.key=a,b}var c0,c1,c2,c3,c4,c5,c6,c7,c8,c9=c(90453),da=c.n(c9),db=c(15883),dc=c.n(db),dd=c(37456),de=c.n(dd),df=c(5231),dg=c.n(df),dh=c(63866),di=c.n(dh),dj=c(40491),dk=c.n(dj),dl=c(21592),dm=c.n(dl),dn=c(77822),dp=c.n(dn),dq=c(69433),dr=c.n(dq),ds=c(71967),dt=c.n(ds),du=c(85938),dv=c.n(du),dw=!0,dx="[DecimalError] ",dy=dx+"Invalid argument: ",dz=dx+"Exponent out of range: ",dA=Math.floor,dB=Math.pow,dC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,dD=dA(1286742750677284.5),dE={};function dF(a,b){var c,d,e,f,g,h,i,j,k=a.constructor,l=k.precision;if(!a.s||!b.s)return b.s||(b=new k(a)),dw?dP(b,l):b;if(i=a.d,j=b.d,g=a.e,e=b.e,i=i.slice(),f=g-e){for(f<0?(d=i,f=-f,h=j.length):(d=j,e=g,h=i.length),f>(h=(g=Math.ceil(l/7))>h?g+1:h+1)&&(f=h,d.length=1),d.reverse();f--;)d.push(0);d.reverse()}for((h=i.length)-(f=j.length)<0&&(f=h,d=j,j=i,i=d),c=0;f;)c=(i[--f]=i[f]+j[f]+c)/1e7|0,i[f]%=1e7;for(c&&(i.unshift(c),++e),h=i.length;0==i[--h];)i.pop();return b.d=i,b.e=e,dw?dP(b,l):b}function dG(a,b,c){if(a!==~~a||a<b||a>c)throw Error(dy+a)}function dH(a){var b,c,d,e=a.length-1,f="",g=a[0];if(e>0){for(f+=g,b=1;b<e;b++)(c=7-(d=a[b]+"").length)&&(f+=dM(c)),f+=d;(c=7-(d=(g=a[b])+"").length)&&(f+=dM(c))}else if(0===g)return"0";for(;g%10==0;)g/=10;return f+g}dE.absoluteValue=dE.abs=function(){var a=new this.constructor(this);return a.s&&(a.s=1),a},dE.comparedTo=dE.cmp=function(a){var b,c,d,e;if(a=new this.constructor(a),this.s!==a.s)return this.s||-a.s;if(this.e!==a.e)return this.e>a.e^this.s<0?1:-1;for(b=0,c=(d=this.d.length)<(e=a.d.length)?d:e;b<c;++b)if(this.d[b]!==a.d[b])return this.d[b]>a.d[b]^this.s<0?1:-1;return d===e?0:d>e^this.s<0?1:-1},dE.decimalPlaces=dE.dp=function(){var a=this.d.length-1,b=(a-this.e)*7;if(a=this.d[a])for(;a%10==0;a/=10)b--;return b<0?0:b},dE.dividedBy=dE.div=function(a){return dI(this,new this.constructor(a))},dE.dividedToIntegerBy=dE.idiv=function(a){var b=this.constructor;return dP(dI(this,new b(a),0,1),b.precision)},dE.equals=dE.eq=function(a){return!this.cmp(a)},dE.exponent=function(){return dK(this)},dE.greaterThan=dE.gt=function(a){return this.cmp(a)>0},dE.greaterThanOrEqualTo=dE.gte=function(a){return this.cmp(a)>=0},dE.isInteger=dE.isint=function(){return this.e>this.d.length-2},dE.isNegative=dE.isneg=function(){return this.s<0},dE.isPositive=dE.ispos=function(){return this.s>0},dE.isZero=function(){return 0===this.s},dE.lessThan=dE.lt=function(a){return 0>this.cmp(a)},dE.lessThanOrEqualTo=dE.lte=function(a){return 1>this.cmp(a)},dE.logarithm=dE.log=function(a){var b,c=this.constructor,d=c.precision,e=d+5;if(void 0===a)a=new c(10);else if((a=new c(a)).s<1||a.eq(c8))throw Error(dx+"NaN");if(this.s<1)throw Error(dx+(this.s?"NaN":"-Infinity"));return this.eq(c8)?new c(0):(dw=!1,b=dI(dN(this,e),dN(a,e),e),dw=!0,dP(b,d))},dE.minus=dE.sub=function(a){return a=new this.constructor(a),this.s==a.s?dQ(this,a):dF(this,(a.s=-a.s,a))},dE.modulo=dE.mod=function(a){var b,c=this.constructor,d=c.precision;if(!(a=new c(a)).s)throw Error(dx+"NaN");return this.s?(dw=!1,b=dI(this,a,0,1).times(a),dw=!0,this.minus(b)):dP(new c(this),d)},dE.naturalExponential=dE.exp=function(){return dJ(this)},dE.naturalLogarithm=dE.ln=function(){return dN(this)},dE.negated=dE.neg=function(){var a=new this.constructor(this);return a.s=-a.s||0,a},dE.plus=dE.add=function(a){return a=new this.constructor(a),this.s==a.s?dF(this,a):dQ(this,(a.s=-a.s,a))},dE.precision=dE.sd=function(a){var b,c,d;if(void 0!==a&&!!a!==a&&1!==a&&0!==a)throw Error(dy+a);if(b=dK(this)+1,c=7*(d=this.d.length-1)+1,d=this.d[d]){for(;d%10==0;d/=10)c--;for(d=this.d[0];d>=10;d/=10)c++}return a&&b>c?b:c},dE.squareRoot=dE.sqrt=function(){var a,b,c,d,e,f,g,h=this.constructor;if(this.s<1){if(!this.s)return new h(0);throw Error(dx+"NaN")}for(a=dK(this),dw=!1,0==(e=Math.sqrt(+this))||e==1/0?(((b=dH(this.d)).length+a)%2==0&&(b+="0"),e=Math.sqrt(b),a=dA((a+1)/2)-(a<0||a%2),d=new h(b=e==1/0?"5e"+a:(b=e.toExponential()).slice(0,b.indexOf("e")+1)+a)):d=new h(e.toString()),e=g=(c=h.precision)+3;;)if(d=(f=d).plus(dI(this,f,g+2)).times(.5),dH(f.d).slice(0,g)===(b=dH(d.d)).slice(0,g)){if(b=b.slice(g-3,g+1),e==g&&"4999"==b){if(dP(f,c+1,0),f.times(f).eq(this)){d=f;break}}else if("9999"!=b)break;g+=4}return dw=!0,dP(d,c)},dE.times=dE.mul=function(a){var b,c,d,e,f,g,h,i,j,k=this.constructor,l=this.d,m=(a=new k(a)).d;if(!this.s||!a.s)return new k(0);for(a.s*=this.s,c=this.e+a.e,(i=l.length)<(j=m.length)&&(f=l,l=m,m=f,g=i,i=j,j=g),f=[],d=g=i+j;d--;)f.push(0);for(d=j;--d>=0;){for(b=0,e=i+d;e>d;)h=f[e]+m[d]*l[e-d-1]+b,f[e--]=h%1e7|0,b=h/1e7|0;f[e]=(f[e]+b)%1e7|0}for(;!f[--g];)f.pop();return b?++c:f.shift(),a.d=f,a.e=c,dw?dP(a,k.precision):a},dE.toDecimalPlaces=dE.todp=function(a,b){var c=this,d=c.constructor;return(c=new d(c),void 0===a)?c:(dG(a,0,1e9),void 0===b?b=d.rounding:dG(b,0,8),dP(c,a+dK(c)+1,b))},dE.toExponential=function(a,b){var c,d=this,e=d.constructor;return void 0===a?c=dR(d,!0):(dG(a,0,1e9),void 0===b?b=e.rounding:dG(b,0,8),c=dR(d=dP(new e(d),a+1,b),!0,a+1)),c},dE.toFixed=function(a,b){var c,d,e=this.constructor;return void 0===a?dR(this):(dG(a,0,1e9),void 0===b?b=e.rounding:dG(b,0,8),c=dR((d=dP(new e(this),a+dK(this)+1,b)).abs(),!1,a+dK(d)+1),this.isneg()&&!this.isZero()?"-"+c:c)},dE.toInteger=dE.toint=function(){var a=this.constructor;return dP(new a(this),dK(this)+1,a.rounding)},dE.toNumber=function(){return+this},dE.toPower=dE.pow=function(a){var b,c,d,e,f,g,h=this,i=h.constructor,j=+(a=new i(a));if(!a.s)return new i(c8);if(!(h=new i(h)).s){if(a.s<1)throw Error(dx+"Infinity");return h}if(h.eq(c8))return h;if(d=i.precision,a.eq(c8))return dP(h,d);if(g=(b=a.e)>=(c=a.d.length-1),f=h.s,g){if((c=j<0?-j:j)<=0x1fffffffffffff){for(e=new i(c8),b=Math.ceil(d/7+4),dw=!1;c%2&&dS((e=e.times(h)).d,b),0!==(c=dA(c/2));)dS((h=h.times(h)).d,b);return dw=!0,a.s<0?new i(c8).div(e):dP(e,d)}}else if(f<0)throw Error(dx+"NaN");return f=f<0&&1&a.d[Math.max(b,c)]?-1:1,h.s=1,dw=!1,e=a.times(dN(h,d+12)),dw=!0,(e=dJ(e)).s=f,e},dE.toPrecision=function(a,b){var c,d,e=this,f=e.constructor;return void 0===a?(c=dK(e),d=dR(e,c<=f.toExpNeg||c>=f.toExpPos)):(dG(a,1,1e9),void 0===b?b=f.rounding:dG(b,0,8),c=dK(e=dP(new f(e),a,b)),d=dR(e,a<=c||c<=f.toExpNeg,a)),d},dE.toSignificantDigits=dE.tosd=function(a,b){var c=this.constructor;return void 0===a?(a=c.precision,b=c.rounding):(dG(a,1,1e9),void 0===b?b=c.rounding:dG(b,0,8)),dP(new c(this),a,b)},dE.toString=dE.valueOf=dE.val=dE.toJSON=dE[Symbol.for("nodejs.util.inspect.custom")]=function(){var a=dK(this),b=this.constructor;return dR(this,a<=b.toExpNeg||a>=b.toExpPos)};var dI=function(){function a(a,b){var c,d=0,e=a.length;for(a=a.slice();e--;)c=a[e]*b+d,a[e]=c%1e7|0,d=c/1e7|0;return d&&a.unshift(d),a}function b(a,b,c,d){var e,f;if(c!=d)f=c>d?1:-1;else for(e=f=0;e<c;e++)if(a[e]!=b[e]){f=a[e]>b[e]?1:-1;break}return f}function c(a,b,c){for(var d=0;c--;)a[c]-=d,d=+(a[c]<b[c]),a[c]=1e7*d+a[c]-b[c];for(;!a[0]&&a.length>1;)a.shift()}return function(d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z=d.constructor,A=d.s==e.s?1:-1,B=d.d,C=e.d;if(!d.s)return new z(d);if(!e.s)throw Error(dx+"Division by zero");for(j=0,i=d.e-e.e,x=C.length,v=B.length,o=(n=new z(A)).d=[];C[j]==(B[j]||0);)++j;if(C[j]>(B[j]||0)&&--i,(s=null==f?f=z.precision:g?f+(dK(d)-dK(e))+1:f)<0)return new z(0);if(s=s/7+2|0,j=0,1==x)for(k=0,C=C[0],s++;(j<v||k)&&s--;j++)t=1e7*k+(B[j]||0),o[j]=t/C|0,k=t%C|0;else{for((k=1e7/(C[0]+1)|0)>1&&(C=a(C,k),B=a(B,k),x=C.length,v=B.length),u=x,q=(p=B.slice(0,x)).length;q<x;)p[q++]=0;(y=C.slice()).unshift(0),w=C[0],C[1]>=1e7/2&&++w;do k=0,(h=b(C,p,x,q))<0?(r=p[0],x!=q&&(r=1e7*r+(p[1]||0)),(k=r/w|0)>1?(k>=1e7&&(k=1e7-1),m=(l=a(C,k)).length,q=p.length,1==(h=b(l,p,m,q))&&(k--,c(l,x<m?y:C,m))):(0==k&&(h=k=1),l=C.slice()),(m=l.length)<q&&l.unshift(0),c(p,l,q),-1==h&&(q=p.length,(h=b(C,p,x,q))<1&&(k++,c(p,x<q?y:C,q))),q=p.length):0===h&&(k++,p=[0]),o[j++]=k,h&&p[0]?p[q++]=B[u]||0:(p=[B[u]],q=1);while((u++<v||void 0!==p[0])&&s--)}return o[0]||o.shift(),n.e=i,dP(n,g?f+dK(n)+1:f)}}();function dJ(a,b){var c,d,e,f,g,h=0,i=0,j=a.constructor,k=j.precision;if(dK(a)>16)throw Error(dz+dK(a));if(!a.s)return new j(c8);for(null==b?(dw=!1,g=k):g=b,f=new j(.03125);a.abs().gte(.1);)a=a.times(f),i+=5;for(g+=Math.log(dB(2,i))/Math.LN10*2+5|0,c=d=e=new j(c8),j.precision=g;;){if(d=dP(d.times(a),g),c=c.times(++h),dH((f=e.plus(dI(d,c,g))).d).slice(0,g)===dH(e.d).slice(0,g)){for(;i--;)e=dP(e.times(e),g);return j.precision=k,null==b?(dw=!0,dP(e,k)):e}e=f}}function dK(a){for(var b=7*a.e,c=a.d[0];c>=10;c/=10)b++;return b}function dL(a,b,c){if(b>a.LN10.sd())throw dw=!0,c&&(a.precision=c),Error(dx+"LN10 precision limit exceeded");return dP(new a(a.LN10),b)}function dM(a){for(var b="";a--;)b+="0";return b}function dN(a,b){var c,d,e,f,g,h,i,j,k,l=1,m=a,n=m.d,o=m.constructor,p=o.precision;if(m.s<1)throw Error(dx+(m.s?"NaN":"-Infinity"));if(m.eq(c8))return new o(0);if(null==b?(dw=!1,j=p):j=b,m.eq(10))return null==b&&(dw=!0),dL(o,j);if(o.precision=j+=10,d=(c=dH(n)).charAt(0),!(15e14>Math.abs(f=dK(m))))return i=dL(o,j+2,p).times(f+""),m=dN(new o(d+"."+c.slice(1)),j-10).plus(i),o.precision=p,null==b?(dw=!0,dP(m,p)):m;for(;d<7&&1!=d||1==d&&c.charAt(1)>3;)d=(c=dH((m=m.times(a)).d)).charAt(0),l++;for(f=dK(m),d>1?(m=new o("0."+c),f++):m=new o(d+"."+c.slice(1)),h=g=m=dI(m.minus(c8),m.plus(c8),j),k=dP(m.times(m),j),e=3;;){if(g=dP(g.times(k),j),dH((i=h.plus(dI(g,new o(e),j))).d).slice(0,j)===dH(h.d).slice(0,j))return h=h.times(2),0!==f&&(h=h.plus(dL(o,j+2,p).times(f+""))),h=dI(h,new o(l),j),o.precision=p,null==b?(dw=!0,dP(h,p)):h;h=i,e+=2}}function dO(a,b){var c,d,e;for((c=b.indexOf("."))>-1&&(b=b.replace(".","")),(d=b.search(/e/i))>0?(c<0&&(c=d),c+=+b.slice(d+1),b=b.substring(0,d)):c<0&&(c=b.length),d=0;48===b.charCodeAt(d);)++d;for(e=b.length;48===b.charCodeAt(e-1);)--e;if(b=b.slice(d,e)){if(e-=d,a.e=dA((c=c-d-1)/7),a.d=[],d=(c+1)%7,c<0&&(d+=7),d<e){for(d&&a.d.push(+b.slice(0,d)),e-=7;d<e;)a.d.push(+b.slice(d,d+=7));d=7-(b=b.slice(d)).length}else d-=e;for(;d--;)b+="0";if(a.d.push(+b),dw&&(a.e>dD||a.e<-dD))throw Error(dz+c)}else a.s=0,a.e=0,a.d=[0];return a}function dP(a,b,c){var d,e,f,g,h,i,j,k,l=a.d;for(g=1,f=l[0];f>=10;f/=10)g++;if((d=b-g)<0)d+=7,e=b,j=l[k=0];else{if((k=Math.ceil((d+1)/7))>=(f=l.length))return a;for(g=1,j=f=l[k];f>=10;f/=10)g++;d%=7,e=d-7+g}if(void 0!==c&&(h=j/(f=dB(10,g-e-1))%10|0,i=b<0||void 0!==l[k+1]||j%f,i=c<4?(h||i)&&(0==c||c==(a.s<0?3:2)):h>5||5==h&&(4==c||i||6==c&&(d>0?e>0?j/dB(10,g-e):0:l[k-1])%10&1||c==(a.s<0?8:7))),b<1||!l[0])return i?(f=dK(a),l.length=1,b=b-f-1,l[0]=dB(10,(7-b%7)%7),a.e=dA(-b/7)||0):(l.length=1,l[0]=a.e=a.s=0),a;if(0==d?(l.length=k,f=1,k--):(l.length=k+1,f=dB(10,7-d),l[k]=e>0?(j/dB(10,g-e)%dB(10,e)|0)*f:0),i)for(;;)if(0==k){1e7==(l[0]+=f)&&(l[0]=1,++a.e);break}else{if(l[k]+=f,1e7!=l[k])break;l[k--]=0,f=1}for(d=l.length;0===l[--d];)l.pop();if(dw&&(a.e>dD||a.e<-dD))throw Error(dz+dK(a));return a}function dQ(a,b){var c,d,e,f,g,h,i,j,k,l,m=a.constructor,n=m.precision;if(!a.s||!b.s)return b.s?b.s=-b.s:b=new m(a),dw?dP(b,n):b;if(i=a.d,l=b.d,d=b.e,j=a.e,i=i.slice(),g=j-d){for((k=g<0)?(c=i,g=-g,h=l.length):(c=l,d=j,h=i.length),g>(e=Math.max(Math.ceil(n/7),h)+2)&&(g=e,c.length=1),c.reverse(),e=g;e--;)c.push(0);c.reverse()}else{for((k=(e=i.length)<(h=l.length))&&(h=e),e=0;e<h;e++)if(i[e]!=l[e]){k=i[e]<l[e];break}g=0}for(k&&(c=i,i=l,l=c,b.s=-b.s),h=i.length,e=l.length-h;e>0;--e)i[h++]=0;for(e=l.length;e>g;){if(i[--e]<l[e]){for(f=e;f&&0===i[--f];)i[f]=1e7-1;--i[f],i[e]+=1e7}i[e]-=l[e]}for(;0===i[--h];)i.pop();for(;0===i[0];i.shift())--d;return i[0]?(b.d=i,b.e=d,dw?dP(b,n):b):new m(0)}function dR(a,b,c){var d,e=dK(a),f=dH(a.d),g=f.length;return b?(c&&(d=c-g)>0?f=f.charAt(0)+"."+f.slice(1)+dM(d):g>1&&(f=f.charAt(0)+"."+f.slice(1)),f=f+(e<0?"e":"e+")+e):e<0?(f="0."+dM(-e-1)+f,c&&(d=c-g)>0&&(f+=dM(d))):e>=g?(f+=dM(e+1-g),c&&(d=c-e-1)>0&&(f=f+"."+dM(d))):((d=e+1)<g&&(f=f.slice(0,d)+"."+f.slice(d)),c&&(d=c-g)>0&&(e+1===g&&(f+="."),f+=dM(d))),a.s<0?"-"+f:f}function dS(a,b){if(a.length>b)return a.length=b,!0}function dT(a){if(!a||"object"!=typeof a)throw Error(dx+"Object expected");var b,c,d,e=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(b=0;b<e.length;b+=3)if(void 0!==(d=a[c=e[b]]))if(dA(d)===d&&d>=e[b+1]&&d<=e[b+2])this[c]=d;else throw Error(dy+c+": "+d);if(void 0!==(d=a[c="LN10"]))if(d==Math.LN10)this[c]=new this(d);else throw Error(dy+c+": "+d);return this}var c7=function a(b){var c,d,e;function f(a){if(!(this instanceof f))return new f(a);if(this.constructor=f,a instanceof f){this.s=a.s,this.e=a.e,this.d=(a=a.d)?a.slice():a;return}if("number"==typeof a){if(0*a!=0)throw Error(dy+a);if(a>0)this.s=1;else if(a<0)a=-a,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(a===~~a&&a<1e7){this.e=0,this.d=[a];return}return dO(this,a.toString())}if("string"!=typeof a)throw Error(dy+a);if(45===a.charCodeAt(0)?(a=a.slice(1),this.s=-1):this.s=1,dC.test(a))dO(this,a);else throw Error(dy+a)}if(f.prototype=dE,f.ROUND_UP=0,f.ROUND_DOWN=1,f.ROUND_CEIL=2,f.ROUND_FLOOR=3,f.ROUND_HALF_UP=4,f.ROUND_HALF_DOWN=5,f.ROUND_HALF_EVEN=6,f.ROUND_HALF_CEIL=7,f.ROUND_HALF_FLOOR=8,f.clone=a,f.config=f.set=dT,void 0===b&&(b={}),b)for(c=0,e=["precision","rounding","toExpNeg","toExpPos","LN10"];c<e.length;)b.hasOwnProperty(d=e[c++])||(b[d]=this[d]);return f.config(b),f}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c8=new c7(1);let dU=c7;function dV(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}var dW=function(a){return a},dX={},dY=function(a){return a===dX},dZ=function(a){return function b(){return 0==arguments.length||1==arguments.length&&dY(arguments.length<=0?void 0:arguments[0])?b:a.apply(void 0,arguments)}},d$=function(a){return function a(b,c){return 1===b?c:dZ(function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];var g=e.filter(function(a){return a!==dX}).length;return g>=b?c.apply(void 0,e):a(b-g,dZ(function(){for(var a=arguments.length,b=Array(a),d=0;d<a;d++)b[d]=arguments[d];var f=e.map(function(a){return dY(a)?b.shift():a});return c.apply(void 0,((function(a){if(Array.isArray(a))return dV(a)})(f)||function(a){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(a))return Array.from(a)}(f)||function(a,b){if(a){if("string"==typeof a)return dV(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return dV(a,b)}}(f)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(b))}))})}(a.length,a)},d_=function(a,b){for(var c=[],d=a;d<b;++d)c[d-a]=d;return c},d0=d$(function(a,b){return Array.isArray(b)?b.map(a):Object.keys(b).map(function(a){return b[a]}).map(a)}),d1=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];if(!b.length)return dW;var d=b.reverse(),e=d[0],f=d.slice(1);return function(){return f.reduce(function(a,b){return b(a)},e.apply(void 0,arguments))}},d2=function(a){return Array.isArray(a)?a.reverse():a.split("").reverse.join("")},d3=function(a){var b=null,c=null;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return b&&e.every(function(a,c){return a===b[c]})?c:(b=e,c=a.apply(void 0,e))}};d$(function(a,b,c){var d=+a;return d+c*(b-d)}),d$(function(a,b,c){var d=b-a;return(c-a)/(d=d||1/0)}),d$(function(a,b,c){var d=b-a;return Math.max(0,Math.min(1,(c-a)/(d=d||1/0)))});let d4={rangeStep:function(a,b,c){for(var d=new dU(a),e=0,f=[];d.lt(b)&&e<1e5;)f.push(d.toNumber()),d=d.add(c),e++;return f},getDigitCount:function(a){return 0===a?1:Math.floor(new dU(a).abs().log(10).toNumber())+1}};function d5(a){return function(a){if(Array.isArray(a))return d8(a)}(a)||function(a){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(a))return Array.from(a)}(a)||d7(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d6(a,b){return function(a){if(Array.isArray(a))return a}(a)||function(a,b){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(a)){var c=[],d=!0,e=!1,f=void 0;try{for(var g,h=a[Symbol.iterator]();!(d=(g=h.next()).done)&&(c.push(g.value),!b||c.length!==b);d=!0);}catch(a){e=!0,f=a}finally{try{d||null==h.return||h.return()}finally{if(e)throw f}}return c}}(a,b)||d7(a,b)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d7(a,b){if(a){if("string"==typeof a)return d8(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return d8(a,b)}}function d8(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function d9(a){var b=d6(a,2),c=b[0],d=b[1],e=c,f=d;return c>d&&(e=d,f=c),[e,f]}function ea(a,b,c){if(a.lte(0))return new dU(0);var d=d4.getDigitCount(a.toNumber()),e=new dU(10).pow(d),f=a.div(e),g=1!==d?.05:.1,h=new dU(Math.ceil(f.div(g).toNumber())).add(c).mul(g).mul(e);return b?h:new dU(Math.ceil(h))}function eb(a,b,c){var d=1,e=new dU(a);if(!e.isint()&&c){var f=Math.abs(a);f<1?(d=new dU(10).pow(d4.getDigitCount(a)-1),e=new dU(Math.floor(e.div(d).toNumber())).mul(d)):f>1&&(e=new dU(Math.floor(a)))}else 0===a?e=new dU(Math.floor((b-1)/2)):c||(e=new dU(Math.floor(a)));var g=Math.floor((b-1)/2);return d1(d0(function(a){return e.add(new dU(a-g).mul(d)).toNumber()}),d_)(0,b)}var ec=d3(function(a){var b=d6(a,2),c=b[0],d=b[1],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,f=!(arguments.length>2)||void 0===arguments[2]||arguments[2],g=Math.max(e,2),h=d6(d9([c,d]),2),i=h[0],j=h[1];if(i===-1/0||j===1/0){var k=j===1/0?[i].concat(d5(d_(0,e-1).map(function(){return 1/0}))):[].concat(d5(d_(0,e-1).map(function(){return-1/0})),[j]);return c>d?d2(k):k}if(i===j)return eb(i,e,f);var l=function a(b,c,d,e){var f,g=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((c-b)/(d-1)))return{step:new dU(0),tickMin:new dU(0),tickMax:new dU(0)};var h=ea(new dU(c).sub(b).div(d-1),e,g),i=Math.ceil((f=b<=0&&c>=0?new dU(0):(f=new dU(b).add(c).div(2)).sub(new dU(f).mod(h))).sub(b).div(h).toNumber()),j=Math.ceil(new dU(c).sub(f).div(h).toNumber()),k=i+j+1;return k>d?a(b,c,d,e,g+1):(k<d&&(j=c>0?j+(d-k):j,i=c>0?i:i+(d-k)),{step:h,tickMin:f.sub(new dU(i).mul(h)),tickMax:f.add(new dU(j).mul(h))})}(i,j,g,f),m=l.step,n=l.tickMin,o=l.tickMax,p=d4.rangeStep(n,o.add(new dU(.1).mul(m)),m);return c>d?d2(p):p});d3(function(a){var b=d6(a,2),c=b[0],d=b[1],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,f=!(arguments.length>2)||void 0===arguments[2]||arguments[2],g=Math.max(e,2),h=d6(d9([c,d]),2),i=h[0],j=h[1];if(i===-1/0||j===1/0)return[c,d];if(i===j)return eb(i,e,f);var k=ea(new dU(j).sub(i).div(g-1),f,0),l=d1(d0(function(a){return new dU(i).add(new dU(a).mul(k)).toNumber()}),d_)(0,g).filter(function(a){return a>=i&&a<=j});return c>d?d2(l):l});var ed=d3(function(a,b){var c=d6(a,2),d=c[0],e=c[1],f=!(arguments.length>2)||void 0===arguments[2]||arguments[2],g=d6(d9([d,e]),2),h=g[0],i=g[1];if(h===-1/0||i===1/0)return[d,e];if(h===i)return[h];var j=Math.max(b,2),k=ea(new dU(i).sub(h).div(j-1),f,0),l=[].concat(d5(d4.rangeStep(new dU(h),new dU(i).sub(new dU(.99).mul(k)),k)),[i]);return d>e?d2(l):l}),ee=c(60927),ef=c(45370),eg=c(54186),eh=c(46707);function ei(a){return(ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function ej(a){return function(a){if(Array.isArray(a))return ek(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||function(a,b){if(a){if("string"==typeof a)return ek(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return ek(a,b)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ek(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function el(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function em(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?el(Object(c),!0).forEach(function(b){en(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):el(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function en(a,b,c){var d;return(d=function(a,b){if("object"!=ei(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=ei(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"),(b="symbol"==ei(d)?d:d+"")in a)?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function eo(a,b,c){return de()(a)||de()(b)?c:(0,ef.vh)(b)?dk()(a,b,c):dg()(b)?b(a):c}function ep(a,b,c,d){var e=dm()(a,function(a){return eo(a,b)});if("number"===c){var f=e.filter(function(a){return(0,ef.Et)(a)||parseFloat(a)});return f.length?[dc()(f),da()(f)]:[1/0,-1/0]}return(d?e.filter(function(a){return!de()(a)}):e).map(function(a){return(0,ef.vh)(a)||a instanceof Date?a:""})}var eq=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],d=arguments.length>2?arguments[2]:void 0,e=arguments.length>3?arguments[3]:void 0,f=-1,g=null!=(b=null==c?void 0:c.length)?b:0;if(g<=1)return 0;if(e&&"angleAxis"===e.axisType&&1e-6>=Math.abs(Math.abs(e.range[1]-e.range[0])-360))for(var h=e.range,i=0;i<g;i++){var j=i>0?d[i-1].coordinate:d[g-1].coordinate,k=d[i].coordinate,l=i>=g-1?d[0].coordinate:d[i+1].coordinate,m=void 0;if((0,ef.sA)(k-j)!==(0,ef.sA)(l-k)){var n=[];if((0,ef.sA)(l-k)===(0,ef.sA)(h[1]-h[0])){m=l;var o=k+h[1]-h[0];n[0]=Math.min(o,(o+j)/2),n[1]=Math.max(o,(o+j)/2)}else{m=j;var p=l+h[1]-h[0];n[0]=Math.min(k,(p+k)/2),n[1]=Math.max(k,(p+k)/2)}var q=[Math.min(k,(m+k)/2),Math.max(k,(m+k)/2)];if(a>q[0]&&a<=q[1]||a>=n[0]&&a<=n[1]){f=d[i].index;break}}else{var r=Math.min(j,l),s=Math.max(j,l);if(a>(r+k)/2&&a<=(s+k)/2){f=d[i].index;break}}}else for(var t=0;t<g;t++)if(0===t&&a<=(c[t].coordinate+c[t+1].coordinate)/2||t>0&&t<g-1&&a>(c[t].coordinate+c[t-1].coordinate)/2&&a<=(c[t].coordinate+c[t+1].coordinate)/2||t===g-1&&a>(c[t].coordinate+c[t-1].coordinate)/2){f=c[t].index;break}return f},er=function(a){var b,c,d=a.type.displayName,e=null!=(b=a.type)&&b.defaultProps?em(em({},a.type.defaultProps),a.props):a.props,f=e.stroke,g=e.fill;switch(d){case"Line":c=f;break;case"Area":case"Radar":c=f&&"none"!==f?f:g;break;default:c=g}return c},es=function(a){var b=a.barSize,c=a.totalSize,d=a.stackGroups,e=void 0===d?{}:d;if(!e)return{};for(var f={},g=Object.keys(e),h=0,i=g.length;h<i;h++)for(var j=e[g[h]].stackGroups,k=Object.keys(j),l=0,m=k.length;l<m;l++){var n=j[k[l]],o=n.items,p=n.cateAxisId,q=o.filter(function(a){return(0,eg.Mn)(a.type).indexOf("Bar")>=0});if(q&&q.length){var r=q[0].type.defaultProps,s=void 0!==r?em(em({},r),q[0].props):q[0].props,t=s.barSize,u=s[p];f[u]||(f[u]=[]);var v=de()(t)?b:t;f[u].push({item:q[0],stackList:q.slice(1),barSize:de()(v)?void 0:(0,ef.F4)(v,c,0)})}}return f},et=function(a){var b,c=a.barGap,d=a.barCategoryGap,e=a.bandSize,f=a.sizeList,g=void 0===f?[]:f,h=a.maxBarSize,i=g.length;if(i<1)return null;var j=(0,ef.F4)(c,e,0,!0),k=[];if(g[0].barSize===+g[0].barSize){var l=!1,m=e/i,n=g.reduce(function(a,b){return a+b.barSize||0},0);(n+=(i-1)*j)>=e&&(n-=(i-1)*j,j=0),n>=e&&m>0&&(l=!0,m*=.9,n=i*m);var o={offset:((e-n)/2|0)-j,size:0};b=g.reduce(function(a,b){var c={item:b.item,position:{offset:o.offset+o.size+j,size:l?m:b.barSize}},d=[].concat(ej(a),[c]);return o=d[d.length-1].position,b.stackList&&b.stackList.length&&b.stackList.forEach(function(a){d.push({item:a,position:o})}),d},k)}else{var p=(0,ef.F4)(d,e,0,!0);e-2*p-(i-1)*j<=0&&(j=0);var q=(e-2*p-(i-1)*j)/i;q>1&&(q>>=0);var r=h===+h?Math.min(q,h):q;b=g.reduce(function(a,b,c){var d=[].concat(ej(a),[{item:b.item,position:{offset:p+(q+j)*c+(q-r)/2,size:r}}]);return b.stackList&&b.stackList.length&&b.stackList.forEach(function(a){d.push({item:a,position:d[d.length-1].position})}),d},k)}return b},eu=function(a,b,c,d){var e=c.children,f=c.width,g=c.margin,h=f-(g.left||0)-(g.right||0),i=(0,eh.g)({children:e,legendWidth:h});if(i){var j=d||{},k=j.width,l=j.height,m=i.align,n=i.verticalAlign,o=i.layout;if(("vertical"===o||"horizontal"===o&&"middle"===n)&&"center"!==m&&(0,ef.Et)(a[m]))return em(em({},a),{},en({},m,a[m]+(k||0)));if(("horizontal"===o||"vertical"===o&&"center"===m)&&"middle"!==n&&(0,ef.Et)(a[n]))return em(em({},a),{},en({},n,a[n]+(l||0)))}return a},ev=function(a,b,c,d,e){var f=b.props.children,g=(0,eg.aS)(f,ee.u).filter(function(a){var b;return b=a.props.direction,!!de()(e)||("horizontal"===d?"yAxis"===e:"vertical"===d||"x"===b?"xAxis"===e:"y"!==b||"yAxis"===e)});if(g&&g.length){var h=g.map(function(a){return a.props.dataKey});return a.reduce(function(a,b){var d=eo(b,c);if(de()(d))return a;var e=Array.isArray(d)?[dc()(d),da()(d)]:[d,d],f=h.reduce(function(a,c){var d=eo(b,c,0),f=e[0]-Math.abs(Array.isArray(d)?d[0]:d),g=e[1]+Math.abs(Array.isArray(d)?d[1]:d);return[Math.min(f,a[0]),Math.max(g,a[1])]},[1/0,-1/0]);return[Math.min(f[0],a[0]),Math.max(f[1],a[1])]},[1/0,-1/0])}return null},ew=function(a,b,c,d,e){var f=b.map(function(b){return ev(a,b,c,e,d)}).filter(function(a){return!de()(a)});return f&&f.length?f.reduce(function(a,b){return[Math.min(a[0],b[0]),Math.max(a[1],b[1])]},[1/0,-1/0]):null},ex=function(a,b,c,d,e){var f=b.map(function(b){var f=b.props.dataKey;return"number"===c&&f&&ev(a,b,f,d)||ep(a,f,c,e)});if("number"===c)return f.reduce(function(a,b){return[Math.min(a[0],b[0]),Math.max(a[1],b[1])]},[1/0,-1/0]);var g={};return f.reduce(function(a,b){for(var c=0,d=b.length;c<d;c++)g[b[c]]||(g[b[c]]=!0,a.push(b[c]));return a},[])},ey=function(a,b){return"horizontal"===a&&"xAxis"===b||"vertical"===a&&"yAxis"===b||"centric"===a&&"angleAxis"===b||"radial"===a&&"radiusAxis"===b},ez=function(a,b,c){if(!a)return null;var d=a.scale,e=a.duplicateDomain,f=a.type,g=a.range,h="scaleBand"===a.realScaleType?d.bandwidth()/2:2,i=(b||c)&&"category"===f&&d.bandwidth?d.bandwidth()/h:0;return(i="angleAxis"===a.axisType&&(null==g?void 0:g.length)>=2?2*(0,ef.sA)(g[0]-g[1])*i:i,b&&(a.ticks||a.niceTicks))?(a.ticks||a.niceTicks).map(function(a){return{coordinate:d(e?e.indexOf(a):a)+i,value:a,offset:i}}).filter(function(a){return!dp()(a.coordinate)}):a.isCategorical&&a.categoricalDomain?a.categoricalDomain.map(function(a,b){return{coordinate:d(a)+i,value:a,index:b,offset:i}}):d.ticks&&!c?d.ticks(a.tickCount).map(function(a){return{coordinate:d(a)+i,value:a,offset:i}}):d.domain().map(function(a,b){return{coordinate:d(a)+i,value:e?e[a]:a,index:b,offset:i}})},eA=new WeakMap,eB=function(a,b){if("function"!=typeof b)return a;eA.has(a)||eA.set(a,new WeakMap);var c=eA.get(a);if(c.has(b))return c.get(b);var d=function(){a.apply(void 0,arguments),b.apply(void 0,arguments)};return c.set(b,d),d},eC=function(a,b,c){var f=a.scale,g=a.type,h=a.layout,i=a.axisType;if("auto"===f)return"radial"===h&&"radiusAxis"===i?{scale:e.A(),realScaleType:"band"}:"radial"===h&&"angleAxis"===i?{scale:aD(),realScaleType:"linear"}:"category"===g&&b&&(b.indexOf("LineChart")>=0||b.indexOf("AreaChart")>=0||b.indexOf("ComposedChart")>=0&&!c)?{scale:e.z(),realScaleType:"point"}:"category"===g?{scale:e.A(),realScaleType:"band"}:{scale:aD(),realScaleType:"linear"};if(di()(f)){var j="scale".concat(dr()(f));return{scale:(d[j]||e.z)(),realScaleType:d[j]?j:"point"}}return dg()(f)?{scale:f}:{scale:e.z(),realScaleType:"point"}},eD=function(a){var b=a.domain();if(b&&!(b.length<=2)){var c=b.length,d=a.range(),e=Math.min(d[0],d[1])-1e-4,f=Math.max(d[0],d[1])+1e-4,g=a(b[0]),h=a(b[c-1]);(g<e||g>f||h<e||h>f)&&a.domain([b[0],b[c-1]])}},eE=function(a,b){if(!a)return null;for(var c=0,d=a.length;c<d;c++)if(a[c].item===b)return a[c].position;return null},eF=function(a,b){if(!b||2!==b.length||!(0,ef.Et)(b[0])||!(0,ef.Et)(b[1]))return a;var c=Math.min(b[0],b[1]),d=Math.max(b[0],b[1]),e=[a[0],a[1]];return(!(0,ef.Et)(a[0])||a[0]<c)&&(e[0]=c),(!(0,ef.Et)(a[1])||a[1]>d)&&(e[1]=d),e[0]>d&&(e[0]=d),e[1]<c&&(e[1]=c),e},eG={sign:function(a){var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0,g=0;g<b;++g){var h=dp()(a[g][c][1])?a[g][c][0]:a[g][c][1];h>=0?(a[g][c][0]=e,a[g][c][1]=e+h,e=a[g][c][1]):(a[g][c][0]=f,a[g][c][1]=f+h,f=a[g][c][1])}},expand:function(a,b){if((d=a.length)>0){for(var c,d,e,f=0,g=a[0].length;f<g;++f){for(e=c=0;c<d;++c)e+=a[c][f][1]||0;if(e)for(c=0;c<d;++c)a[c][f][1]/=e}cW(a,b)}},none:cW,silhouette:function(a,b){if((c=a.length)>0){for(var c,d=0,e=a[b[0]],f=e.length;d<f;++d){for(var g=0,h=0;g<c;++g)h+=a[g][d][1]||0;e[d][1]+=e[d][0]=-h/2}cW(a,b)}},wiggle:function(a,b){if((e=a.length)>0&&(d=(c=a[b[0]]).length)>0){for(var c,d,e,f=0,g=1;g<d;++g){for(var h=0,i=0,j=0;h<e;++h){for(var k=a[b[h]],l=k[g][1]||0,m=(l-(k[g-1][1]||0))/2,n=0;n<h;++n){var o=a[b[n]];m+=(o[g][1]||0)-(o[g-1][1]||0)}i+=l,j+=m*l}c[g-1][1]+=c[g-1][0]=f,i&&(f-=j/i)}c[g-1][1]+=c[g-1][0]=f,cW(a,b)}},positive:function(a){var b=a.length;if(!(b<=0))for(var c=0,d=a[0].length;c<d;++c)for(var e=0,f=0;f<b;++f){var g=dp()(a[f][c][1])?a[f][c][0]:a[f][c][1];g>=0?(a[f][c][0]=e,a[f][c][1]=e+g,e=a[f][c][1]):(a[f][c][0]=0,a[f][c][1]=0)}}},eH=function(a,b,c){var d=b.map(function(a){return a.props.dataKey}),e=eG[c];return(function(){var a=(0,cY.A)([]),b=cZ,c=cW,d=c$;function e(e){var f,g,h=Array.from(a.apply(this,arguments),c_),i=h.length,j=-1;for(let a of e)for(f=0,++j;f<i;++f)(h[f][j]=[0,+d(a,h[f].key,j,e)]).data=a;for(f=0,g=(0,cX.A)(b(h));f<i;++f)h[g[f]].index=f;return c(h,g),h}return e.keys=function(b){return arguments.length?(a="function"==typeof b?b:(0,cY.A)(Array.from(b)),e):a},e.value=function(a){return arguments.length?(d="function"==typeof a?a:(0,cY.A)(+a),e):d},e.order=function(a){return arguments.length?(b=null==a?cZ:"function"==typeof a?a:(0,cY.A)(Array.from(a)),e):b},e.offset=function(a){return arguments.length?(c=null==a?cW:a,e):c},e})().keys(d).value(function(a,b){return+eo(a,b,0)}).order(cZ).offset(e)(a)},eI=function(a,b,c,d,e,f){if(!a)return null;var g=(f?b.reverse():b).reduce(function(a,b){var e,f=null!=(e=b.type)&&e.defaultProps?em(em({},b.type.defaultProps),b.props):b.props,g=f.stackId;if(f.hide)return a;var h=f[c],i=a[h]||{hasStack:!1,stackGroups:{}};if((0,ef.vh)(g)){var j=i.stackGroups[g]||{numericAxisId:c,cateAxisId:d,items:[]};j.items.push(b),i.hasStack=!0,i.stackGroups[g]=j}else i.stackGroups[(0,ef.NF)("_stackId_")]={numericAxisId:c,cateAxisId:d,items:[b]};return em(em({},a),{},en({},h,i))},{});return Object.keys(g).reduce(function(b,f){var h=g[f];return h.hasStack&&(h.stackGroups=Object.keys(h.stackGroups).reduce(function(b,f){var g=h.stackGroups[f];return em(em({},b),{},en({},f,{numericAxisId:c,cateAxisId:d,items:g.items,stackedData:eH(a,g.items,e)}))},{})),em(em({},b),{},en({},f,h))},{})},eJ=function(a,b){var c=b.realScaleType,d=b.type,e=b.tickCount,f=b.originalDomain,g=b.allowDecimals,h=c||b.scale;if("auto"!==h&&"linear"!==h)return null;if(e&&"number"===d&&f&&("auto"===f[0]||"auto"===f[1])){var i=a.domain();if(!i.length)return null;var j=ec(i,e,g);return a.domain([dc()(j),da()(j)]),{niceTicks:j}}return e&&"number"===d?{niceTicks:ed(a.domain(),e,g)}:null},eK=function(a){var b=a.axis,c=a.ticks,d=a.offset,e=a.bandSize,f=a.entry,g=a.index;if("category"===b.type)return c[g]?c[g].coordinate+d:null;var h=eo(f,b.dataKey,b.domain[g]);return de()(h)?null:b.scale(h)-e/2+d},eL=function(a){var b=a.numericAxis,c=b.scale.domain();if("number"===b.type){var d=Math.min(c[0],c[1]),e=Math.max(c[0],c[1]);return d<=0&&e>=0?0:e<0?e:d}return c[0]},eM=function(a,b){var c,d=(null!=(c=a.type)&&c.defaultProps?em(em({},a.type.defaultProps),a.props):a.props).stackId;if((0,ef.vh)(d)){var e=b[d];if(e){var f=e.items.indexOf(a);return f>=0?e.stackedData[f]:null}}return null},eN=function(a,b,c){return Object.keys(a).reduce(function(d,e){var f=a[e].stackedData.reduce(function(a,d){var e=d.slice(b,c+1).reduce(function(a,b){return[dc()(b.concat([a[0]]).filter(ef.Et)),da()(b.concat([a[1]]).filter(ef.Et))]},[1/0,-1/0]);return[Math.min(a[0],e[0]),Math.max(a[1],e[1])]},[1/0,-1/0]);return[Math.min(f[0],d[0]),Math.max(f[1],d[1])]},[1/0,-1/0]).map(function(a){return a===1/0||a===-1/0?0:a})},eO=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,eP=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,eQ=function(a,b,c){if(dg()(a))return a(b,c);if(!Array.isArray(a))return b;var d=[];if((0,ef.Et)(a[0]))d[0]=c?a[0]:Math.min(a[0],b[0]);else if(eO.test(a[0])){var e=+eO.exec(a[0])[1];d[0]=b[0]-e}else dg()(a[0])?d[0]=a[0](b[0]):d[0]=b[0];if((0,ef.Et)(a[1]))d[1]=c?a[1]:Math.max(a[1],b[1]);else if(eP.test(a[1])){var f=+eP.exec(a[1])[1];d[1]=b[1]+f}else dg()(a[1])?d[1]=a[1](b[1]):d[1]=b[1];return d},eR=function(a,b,c){if(a&&a.scale&&a.scale.bandwidth){var d=a.scale.bandwidth();if(!c||d>0)return d}if(a&&b&&b.length>=2){for(var e=dv()(b,function(a){return a.coordinate}),f=1/0,g=1,h=e.length;g<h;g++){var i=e[g],j=e[g-1];f=Math.min((i.coordinate||0)-(j.coordinate||0),f)}return f===1/0?0:f}return c?void 0:0},eS=function(a,b,c){return!a||!a.length||dt()(a,dk()(c,"type.defaultProps.domain"))?b:a},eT=function(a,b){var c=a.type.defaultProps?em(em({},a.type.defaultProps),a.props):a.props,d=c.dataKey,e=c.name,f=c.unit,g=c.formatter,h=c.tooltipType,i=c.chartType,j=c.hide;return em(em({},(0,eg.J9)(a,!1)),{},{dataKey:d,unit:f,formatter:g,name:e||d,color:er(a),value:eo(b,d),type:h,payload:b,chartType:i,hide:j})}},30316:(a,b,c)=>{var d=c(67554);a.exports=function(a,b){var c=!0;return d(a,function(a,d,e){return c=!!b(a,d,e)}),c}},30401:(a,b,c)=>{a.exports=c(41547)(c(85718),"Promise")},30854:(a,b,c)=>{var d=c(66930),e=c(658),f=c(95746);a.exports=function(a,b){var c=this.__data__;if(c instanceof d){var g=c.__data__;if(!e||g.length<199)return g.push([a,b]),this.size=++c.size,this;c=this.__data__=new f(g)}return c.set(a,b),this.size=c.size,this}},32269:(a,b,c)=>{var d=c(5231),e=c(69619);a.exports=function(a){return null!=a&&e(a.length)&&!d(a)}},34117:a=>{var b=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");a.exports=function(a){return b.test(a)}},34746:a=>{a.exports=function(a){return this.__data__.get(a)}},34772:(a,b,c)=>{a.exports=c(41547)(c(85718),"Set")},34821:a=>{a.exports=function(a,b){for(var c=-1,d=Array(a);++c<a;)d[c]=b(c);return d}},34883:(a,b,c)=>{var d=c(55048);a.exports=function(a){return a==a&&!d(a)}},34955:(a,b,c)=>{"use strict";c.d(b,{h:()=>r});var d=c(43210),e=c.n(d),f=c(49384),g=c(54186),h=c(19335),i=c(45370);function j(a){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function k(){return(k=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=j(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=j(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==j(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n=function(a){var b=a.cx,c=a.cy,d=a.radius,e=a.angle,f=a.sign,g=a.isExternal,i=a.cornerRadius,j=a.cornerIsExternal,k=i*(g?1:-1)+d,l=Math.asin(i/k)/h.Kg,m=j?e:e+f*l;return{center:(0,h.IZ)(b,c,k,m),circleTangency:(0,h.IZ)(b,c,d,m),lineTangency:(0,h.IZ)(b,c,k*Math.cos(l*h.Kg),j?e-f*l:e),theta:l}},o=function(a){var b=a.cx,c=a.cy,d=a.innerRadius,e=a.outerRadius,f=a.startAngle,g=a.endAngle,j=(0,i.sA)(g-f)*Math.min(Math.abs(g-f),359.999),k=f+j,l=(0,h.IZ)(b,c,e,f),m=(0,h.IZ)(b,c,e,k),n="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(e,",").concat(e,",0,\n    ").concat(+(Math.abs(j)>180),",").concat(+(f>k),",\n    ").concat(m.x,",").concat(m.y,"\n  ");if(d>0){var o=(0,h.IZ)(b,c,d,f),p=(0,h.IZ)(b,c,d,k);n+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(d,",").concat(d,",0,\n            ").concat(+(Math.abs(j)>180),",").concat(+(f<=k),",\n            ").concat(o.x,",").concat(o.y," Z")}else n+="L ".concat(b,",").concat(c," Z");return n},p=function(a){var b=a.cx,c=a.cy,d=a.innerRadius,e=a.outerRadius,f=a.cornerRadius,g=a.forceCornerRadius,h=a.cornerIsExternal,j=a.startAngle,k=a.endAngle,l=(0,i.sA)(k-j),m=n({cx:b,cy:c,radius:e,angle:j,sign:l,cornerRadius:f,cornerIsExternal:h}),p=m.circleTangency,q=m.lineTangency,r=m.theta,s=n({cx:b,cy:c,radius:e,angle:k,sign:-l,cornerRadius:f,cornerIsExternal:h}),t=s.circleTangency,u=s.lineTangency,v=s.theta,w=h?Math.abs(j-k):Math.abs(j-k)-r-v;if(w<0)return g?"M ".concat(q.x,",").concat(q.y,"\n        a").concat(f,",").concat(f,",0,0,1,").concat(2*f,",0\n        a").concat(f,",").concat(f,",0,0,1,").concat(-(2*f),",0\n      "):o({cx:b,cy:c,innerRadius:d,outerRadius:e,startAngle:j,endAngle:k});var x="M ".concat(q.x,",").concat(q.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(l<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(e,",").concat(e,",0,").concat(+(w>180),",").concat(+(l<0),",").concat(t.x,",").concat(t.y,"\n    A").concat(f,",").concat(f,",0,0,").concat(+(l<0),",").concat(u.x,",").concat(u.y,"\n  ");if(d>0){var y=n({cx:b,cy:c,radius:d,angle:j,sign:l,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),z=y.circleTangency,A=y.lineTangency,B=y.theta,C=n({cx:b,cy:c,radius:d,angle:k,sign:-l,isExternal:!0,cornerRadius:f,cornerIsExternal:h}),D=C.circleTangency,E=C.lineTangency,F=C.theta,G=h?Math.abs(j-k):Math.abs(j-k)-B-F;if(G<0&&0===f)return"".concat(x,"L").concat(b,",").concat(c,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(l<0),",").concat(D.x,",").concat(D.y,"\n      A").concat(d,",").concat(d,",0,").concat(+(G>180),",").concat(+(l>0),",").concat(z.x,",").concat(z.y,"\n      A").concat(f,",").concat(f,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else x+="L".concat(b,",").concat(c,"Z");return x},q={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},r=function(a){var b,c=m(m({},q),a),d=c.cx,h=c.cy,j=c.innerRadius,l=c.outerRadius,n=c.cornerRadius,r=c.forceCornerRadius,s=c.cornerIsExternal,t=c.startAngle,u=c.endAngle,v=c.className;if(l<j||t===u)return null;var w=(0,f.A)("recharts-sector",v),x=l-j,y=(0,i.F4)(n,x,0,!0);return b=y>0&&360>Math.abs(t-u)?p({cx:d,cy:h,innerRadius:j,outerRadius:l,cornerRadius:Math.min(y,x/2),forceCornerRadius:r,cornerIsExternal:s,startAngle:t,endAngle:u}):o({cx:d,cy:h,innerRadius:j,outerRadius:l,startAngle:t,endAngle:u}),e().createElement("path",k({},(0,g.J9)(c,!0),{className:w,d:b,role:"img"}))}},34990:(a,b,c)=>{a.exports=c(87321)()},35142:(a,b,c)=>{var d=c(40542),e=c(67619),f=c(51449),g=c(42403);a.exports=function(a,b){return d(a)?a:e(a,b)?[a]:f(g(a))}},35163:(a,b,c)=>{var d=c(15451),e=c(27467),f=Object.prototype,g=f.hasOwnProperty,h=f.propertyIsEnumerable;a.exports=d(function(){return arguments}())?d:function(a){return e(a)&&g.call(a,"callee")&&!h.call(a,"callee")}},35697:(a,b,c)=>{var d=c(79474),e=c(4999),f=c(67009),g=c(27006),h=c(59774),i=c(2408),j=d?d.prototype:void 0,k=j?j.valueOf:void 0;a.exports=function(a,b,c,d,j,l,m){switch(c){case"[object DataView]":if(a.byteLength!=b.byteLength||a.byteOffset!=b.byteOffset)break;a=a.buffer,b=b.buffer;case"[object ArrayBuffer]":if(a.byteLength!=b.byteLength||!l(new e(a),new e(b)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return f(+a,+b);case"[object Error]":return a.name==b.name&&a.message==b.message;case"[object RegExp]":case"[object String]":return a==b+"";case"[object Map]":var n=h;case"[object Set]":var o=1&d;if(n||(n=i),a.size!=b.size&&!o)break;var p=m.get(a);if(p)return p==b;d|=2,m.set(a,b);var q=g(n(a),n(b),d,j,l,m);return m.delete(a),q;case"[object Symbol]":if(k)return k.call(a)==k.call(b)}return!1}},35800:(a,b,c)=>{var d=c(57797);a.exports=function(a){return d(this.__data__,a)>-1}},36315:(a,b,c)=>{var d=c(22),e=c(92662);a.exports=function(a,b){return a&&a.length?e(a,d(b,2)):[]}},36341:(a,b,c)=>{var d=c(67200),e=c(27006),f=c(35697),g=c(21630),h=c(1566),i=c(40542),j=c(80329),k=c(10090),l="[object Arguments]",m="[object Array]",n="[object Object]",o=Object.prototype.hasOwnProperty;a.exports=function(a,b,c,p,q,r){var s=i(a),t=i(b),u=s?m:h(a),v=t?m:h(b);u=u==l?n:u,v=v==l?n:v;var w=u==n,x=v==n,y=u==v;if(y&&j(a)){if(!j(b))return!1;s=!0,w=!1}if(y&&!w)return r||(r=new d),s||k(a)?e(a,b,c,p,q,r):f(a,b,u,c,p,q,r);if(!(1&c)){var z=w&&o.call(a,"__wrapped__"),A=x&&o.call(b,"__wrapped__");if(z||A){var B=z?a.value():a,C=A?b.value():b;return r||(r=new d),q(B,C,c,p,r)}}return!!y&&(r||(r=new d),g(a,b,c,p,q,r))}},36959:a=>{a.exports=function(){}},37456:a=>{a.exports=function(a){return null==a}},37575:(a,b,c)=>{var d=c(66930);a.exports=function(){this.__data__=new d,this.size=0}},37643:(a,b,c)=>{var d=c(6053),e=/^\s+/;a.exports=function(a){return a?a.slice(0,d(a)+1).replace(e,""):a}},37877:(a,b,c)=>{"use strict";function d(a,b){switch(arguments.length){case 0:break;case 1:this.range(a);break;default:this.range(b).domain(a)}return this}function e(a,b){switch(arguments.length){case 0:break;case 1:"function"==typeof a?this.interpolator(a):this.range(a);break;default:this.domain(a),"function"==typeof b?this.interpolator(b):this.range(b)}return this}c.d(b,{C:()=>d,K:()=>e})},38246:(a,b,c)=>{"use strict";c.d(b,{m:()=>R});var d=c(43210),e=c.n(d),f=c(85938),g=c.n(f),h=c(37456),i=c.n(h),j=c(49384),k=c(45370);function l(a){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function m(){return(m=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function n(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function o(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=l(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=l(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==l(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function q(a){return Array.isArray(a)&&(0,k.vh)(a[0])&&(0,k.vh)(a[1])?a.join(" ~ "):a}var r=function(a){var b=a.separator,c=void 0===b?" : ":b,d=a.contentStyle,f=a.itemStyle,h=void 0===f?{}:f,l=a.labelStyle,o=a.payload,r=a.formatter,s=a.itemSorter,t=a.wrapperClassName,u=a.labelClassName,v=a.label,w=a.labelFormatter,x=a.accessibilityLayer,y=p({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===d?{}:d),z=p({margin:0},void 0===l?{}:l),A=!i()(v),B=A?v:"",C=(0,j.A)("recharts-default-tooltip",t),D=(0,j.A)("recharts-tooltip-label",u);return A&&w&&null!=o&&(B=w(v,o)),e().createElement("div",m({className:C,style:y},void 0!==x&&x?{role:"status","aria-live":"assertive"}:{}),e().createElement("p",{className:D,style:z},e().isValidElement(B)?B:"".concat(B)),function(){if(o&&o.length){var a=(s?g()(o,s):o).map(function(a,b){if("none"===a.type)return null;var d=p({display:"block",paddingTop:4,paddingBottom:4,color:a.color||"#000"},h),f=a.formatter||r||q,g=a.value,i=a.name,j=g,l=i;if(f&&null!=j&&null!=l){var m=f(g,i,a,b,o);if(Array.isArray(m)){var s=function(a){if(Array.isArray(a))return a}(m)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(m,2)||function(a,b){if(a){if("string"==typeof a)return n(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return n(a,b)}}(m,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();j=s[0],l=s[1]}else j=m}return e().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(b),style:d},(0,k.vh)(l)?e().createElement("span",{className:"recharts-tooltip-item-name"},l):null,(0,k.vh)(l)?e().createElement("span",{className:"recharts-tooltip-item-separator"},c):null,e().createElement("span",{className:"recharts-tooltip-item-value"},j),e().createElement("span",{className:"recharts-tooltip-item-unit"},a.unit||""))});return e().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},a)}return null}())};function s(a){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function t(a,b,c){var d;return(d=function(a,b){if("object"!=s(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=s(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"),(b="symbol"==s(d)?d:d+"")in a)?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}var u="recharts-tooltip-wrapper",v={visibility:"hidden"};function w(a){var b=a.allowEscapeViewBox,c=a.coordinate,d=a.key,e=a.offsetTopLeft,f=a.position,g=a.reverseDirection,h=a.tooltipDimension,i=a.viewBox,j=a.viewBoxDimension;if(f&&(0,k.Et)(f[d]))return f[d];var l=c[d]-h-e,m=c[d]+e;return b[d]?g[d]?l:m:g[d]?l<i[d]?Math.max(m,i[d]):Math.max(l,i[d]):m+h>i[d]+j?Math.max(l,i[d]):Math.max(m,i[d])}function x(a){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function y(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function z(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?y(Object(c),!0).forEach(function(b){D(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):y(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function A(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(A=function(){return!!a})()}function B(a){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function C(a,b){return(C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function D(a,b,c){return(b=E(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function E(a){var b=function(a,b){if("object"!=x(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=x(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==x(b)?b:b+""}var F=function(a){var b;function c(){var a,b,d;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");for(var e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];return b=c,d=[].concat(f),b=B(b),D(a=function(a,b){if(b&&("object"===x(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,A()?Reflect.construct(b,d||[],B(this).constructor):b.apply(this,d)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),D(a,"handleKeyDown",function(b){if("Escape"===b.key){var c,d,e,f;a.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(c=null==(d=a.props.coordinate)?void 0:d.x)?c:0,y:null!=(e=null==(f=a.props.coordinate)?void 0:f.y)?e:0}})}}),a}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&C(c,a),b=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var a=this.wrapperNode.getBoundingClientRect();(Math.abs(a.width-this.state.lastBoundingBox.width)>1||Math.abs(a.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:a.width,height:a.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var a,b;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(a=this.props.coordinate)?void 0:a.x)!==this.state.dismissedAtCoordinate.x||(null==(b=this.props.coordinate)?void 0:b.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var a,b,c,d,f,g,h,i,l,m,n,o,p,q,r,s,x,y,A,B=this,C=this.props,D=C.active,E=C.allowEscapeViewBox,F=C.animationDuration,G=C.animationEasing,H=C.children,I=C.coordinate,J=C.hasPayload,K=C.isAnimationActive,L=C.offset,M=C.position,N=C.reverseDirection,O=C.useTranslate3d,P=C.viewBox,Q=C.wrapperStyle,R=(o=(a={allowEscapeViewBox:E,coordinate:I,offsetTopLeft:L,position:M,reverseDirection:N,tooltipBox:this.state.lastBoundingBox,useTranslate3d:O,viewBox:P}).allowEscapeViewBox,p=a.coordinate,q=a.offsetTopLeft,r=a.position,s=a.reverseDirection,x=a.tooltipBox,y=a.useTranslate3d,A=a.viewBox,x.height>0&&x.width>0&&p?(c=(b={translateX:m=w({allowEscapeViewBox:o,coordinate:p,key:"x",offsetTopLeft:q,position:r,reverseDirection:s,tooltipDimension:x.width,viewBox:A,viewBoxDimension:A.width}),translateY:n=w({allowEscapeViewBox:o,coordinate:p,key:"y",offsetTopLeft:q,position:r,reverseDirection:s,tooltipDimension:x.height,viewBox:A,viewBoxDimension:A.height}),useTranslate3d:y}).translateX,d=b.translateY,l={transform:b.useTranslate3d?"translate3d(".concat(c,"px, ").concat(d,"px, 0)"):"translate(".concat(c,"px, ").concat(d,"px)")}):l=v,{cssProperties:l,cssClasses:(g=(f={translateX:m,translateY:n,coordinate:p}).coordinate,h=f.translateX,i=f.translateY,(0,j.A)(u,t(t(t(t({},"".concat(u,"-right"),(0,k.Et)(h)&&g&&(0,k.Et)(g.x)&&h>=g.x),"".concat(u,"-left"),(0,k.Et)(h)&&g&&(0,k.Et)(g.x)&&h<g.x),"".concat(u,"-bottom"),(0,k.Et)(i)&&g&&(0,k.Et)(g.y)&&i>=g.y),"".concat(u,"-top"),(0,k.Et)(i)&&g&&(0,k.Et)(g.y)&&i<g.y)))}),S=R.cssClasses,T=R.cssProperties,U=z(z({transition:K&&D?"transform ".concat(F,"ms ").concat(G):void 0},T),{},{pointerEvents:"none",visibility:!this.state.dismissed&&D&&J?"visible":"hidden",position:"absolute",top:0,left:0},Q);return e().createElement("div",{tabIndex:-1,className:S,style:U,ref:function(a){B.wrapperNode=a}},H)}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,E(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(d.PureComponent),G=c(20237),H=c(45796);function I(a){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function J(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function K(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?J(Object(c),!0).forEach(function(b){O(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):J(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function L(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(L=function(){return!!a})()}function M(a){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function N(a,b){return(N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function O(a,b,c){return(b=P(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function P(a){var b=function(a,b){if("object"!=I(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=I(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==I(b)?b:b+""}function Q(a){return a.dataKey}var R=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=M(a),function(a,b){if(b&&("object"===I(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,L()?Reflect.construct(a,b||[],M(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&N(c,a),b=[{key:"render",value:function(){var a,b=this,c=this.props,d=c.active,f=c.allowEscapeViewBox,g=c.animationDuration,h=c.animationEasing,i=c.content,j=c.coordinate,k=c.filterNull,l=c.isAnimationActive,m=c.offset,n=c.payload,o=c.payloadUniqBy,p=c.position,q=c.reverseDirection,s=c.useTranslate3d,t=c.viewBox,u=c.wrapperStyle,v=null!=n?n:[];k&&v.length&&(v=(0,H.s)(n.filter(function(a){return null!=a.value&&(!0!==a.hide||b.props.includeHidden)}),o,Q));var w=v.length>0;return e().createElement(F,{allowEscapeViewBox:f,animationDuration:g,animationEasing:h,isAnimationActive:l,active:d,coordinate:j,hasPayload:w,offset:m,position:p,reverseDirection:q,useTranslate3d:s,viewBox:t,wrapperStyle:u},(a=K(K({},this.props),{},{payload:v}),e().isValidElement(i)?e().cloneElement(i,a):"function"==typeof i?e().createElement(i,a):e().createElement(r,a)))}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,P(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(d.PureComponent);O(R,"displayName","Tooltip"),O(R,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!G.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},38404:(a,b,c)=>{var d=c(29395),e=c(65932),f=c(27467),g=Object.prototype,h=Function.prototype.toString,i=g.hasOwnProperty,j=h.call(Object);a.exports=function(a){if(!f(a)||"[object Object]"!=d(a))return!1;var b=e(a);if(null===b)return!0;var c=i.call(b,"constructor")&&b.constructor;return"function"==typeof c&&c instanceof c&&h.call(c)==j}},38428:a=>{var b=/^(?:0|[1-9]\d*)$/;a.exports=function(a,c){var d=typeof a;return!!(c=null==c?0x1fffffffffffff:c)&&("number"==d||"symbol"!=d&&b.test(a))&&a>-1&&a%1==0&&a<c}},39672:(a,b,c)=>{var d=c(58141);a.exports=function(a,b){var c=this.__data__;return this.size+=+!this.has(a),c[a]=d&&void 0===b?"__lodash_hash_undefined__":b,this}},39774:a=>{a.exports=function(a){return a!=a}},40491:(a,b,c)=>{var d=c(1707);a.exports=function(a,b,c){var e=null==a?void 0:d(a,b);return void 0===e?c:e}},40542:a=>{a.exports=Array.isArray},41011:(a,b,c)=>{var d=c(41353);a.exports=function(a,b,c){var e=a.length;return c=void 0===c?e:c,!b&&c>=e?a:d(a,b,c)}},41132:a=>{a.exports=function(a,b){return function(c){return null!=c&&c[a]===b&&(void 0!==b||a in Object(c))}}},41157:(a,b,c)=>{var d=c(91928);a.exports=function(a,b,c){"__proto__"==b&&d?d(a,b,{configurable:!0,enumerable:!0,value:c,writable:!0}):a[b]=c}},41353:a=>{a.exports=function(a,b,c){var d=-1,e=a.length;b<0&&(b=-b>e?0:e+b),(c=c>e?e:c)<0&&(c+=e),e=b>c?0:c-b>>>0,b>>>=0;for(var f=Array(e);++d<e;)f[d]=a[d+b];return f}},41547:(a,b,c)=>{var d=c(61548),e=c(90851);a.exports=function(a,b){var c=e(a,b);return d(c)?c:void 0}},41693:a=>{a.exports=function(a,b){for(var c=-1,d=b.length,e=a.length;++c<d;)a[e+c]=b[c];return a}},42082:a=>{a.exports=function(a){return function(b){return null==b?void 0:b[a]}}},42205:(a,b,c)=>{var d=c(41693),e=c(85450);a.exports=function a(b,c,f,g,h){var i=-1,j=b.length;for(f||(f=e),h||(h=[]);++i<j;){var k=b[i];c>0&&f(k)?c>1?a(k,c-1,f,g,h):d(h,k):g||(h[h.length]=k)}return h}},42403:(a,b,c)=>{var d=c(80195);a.exports=function(a){return null==a?"":d(a)}},43378:a=>{a.exports=function(a,b){var c=a.length;for(a.sort(b);c--;)a[c]=a[c].value;return a}},45058:(a,b,c)=>{var d=c(42082),e=c(8852),f=c(67619),g=c(46436);a.exports=function(a){return f(a)?d(g(a)):e(a)}},45370:(a,b,c)=>{"use strict";c.d(b,{CG:()=>t,Dj:()=>u,Et:()=>n,F4:()=>r,NF:()=>q,_3:()=>m,eP:()=>v,lX:()=>s,sA:()=>l,vh:()=>o});var d=c(63866),e=c.n(d),f=c(77822),g=c.n(f),h=c(40491),i=c.n(h),j=c(93490),k=c.n(j),l=function(a){return 0===a?0:a>0?1:-1},m=function(a){return e()(a)&&a.indexOf("%")===a.length-1},n=function(a){return k()(a)&&!g()(a)},o=function(a){return n(a)||e()(a)},p=0,q=function(a){var b=++p;return"".concat(a||"").concat(b)},r=function(a,b){var c,d=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,f=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!n(a)&&!e()(a))return d;if(m(a)){var h=a.indexOf("%");c=b*parseFloat(a.slice(0,h))/100}else c=+a;return g()(c)&&(c=d),f&&c>b&&(c=b),c},s=function(a){if(!a)return null;var b=Object.keys(a);return b&&b.length?a[b[0]]:null},t=function(a){if(!Array.isArray(a))return!1;for(var b=a.length,c={},d=0;d<b;d++)if(c[a[d]])return!0;else c[a[d]]=!0;return!1},u=function(a,b){return n(a)&&n(b)?function(c){return a+c*(b-a)}:function(){return b}};function v(a,b,c){return a&&a.length?a.find(function(a){return a&&("function"==typeof b?b(a):i()(a,b))===c}):null}},45603:(a,b,c)=>{var d=c(20540),e=c(55048);a.exports=function(a,b,c){var f=!0,g=!0;if("function"!=typeof a)throw TypeError("Expected a function");return e(c)&&(f="leading"in c?!!c.leading:f,g="trailing"in c?!!c.trailing:g),d(a,b,{leading:f,maxWait:b,trailing:g})}},45796:(a,b,c)=>{"use strict";c.d(b,{s:()=>h});var d=c(36315),e=c.n(d),f=c(5231),g=c.n(f);function h(a,b,c){return!0===b?e()(a,c):g()(b)?e()(a,b):a}},45803:a=>{a.exports=function(a){var b=typeof a;return"string"==b||"number"==b||"symbol"==b||"boolean"==b?"__proto__"!==a:null===a}},46063:a=>{a.exports=function(a,b){return a<b}},46110:(a,b,c)=>{var d=c(22),e=c(32269),f=c(7651);a.exports=function(a){return function(b,c,g){var h=Object(b);if(!e(b)){var i=d(c,3);b=f(b),c=function(a){return i(h[a],a,h)}}var j=a(b,c,g);return j>-1?h[i?b[j]:j]:void 0}}},46229:(a,b,c)=>{var d=c(48169),e=c(66354),f=c(11424);a.exports=function(a,b){return f(e(a,b,d),a+"")}},46328:(a,b,c)=>{var d=c(95746),e=c(89185),f=c(16854);function g(a){var b=-1,c=null==a?0:a.length;for(this.__data__=new d;++b<c;)this.add(a[b])}g.prototype.add=g.prototype.push=e,g.prototype.has=f,a.exports=g},46436:(a,b,c)=>{var d=c(49227),e=1/0;a.exports=function(a){if("string"==typeof a||d(a))return a;var b=a+"";return"0"==b&&1/a==-e?"-0":b}},46707:(a,b,c)=>{"use strict";c.d(b,{g:()=>j});var d=c(79740),e=c(30087),f=c(54186);function g(a){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function h(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function i(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?h(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=g(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=g(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==g(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):h(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var j=function(a){var b,c=a.children,g=a.formattedGraphicalItems,h=a.legendWidth,j=a.legendContent,k=(0,f.BU)(c,d.s);if(!k)return null;var l=d.s.defaultProps,m=void 0!==l?i(i({},l),k.props):{};return b=k.props&&k.props.payload?k.props&&k.props.payload:"children"===j?(g||[]).reduce(function(a,b){var c=b.item,d=b.props,e=d.sectors||d.data||[];return a.concat(e.map(function(a){return{type:k.props.iconType||c.props.legendType,value:a.name,color:a.fill,payload:a}}))},[]):(g||[]).map(function(a){var b=a.item,c=b.type.defaultProps,d=void 0!==c?i(i({},c),b.props):{},f=d.dataKey,g=d.name,h=d.legendType;return{inactive:d.hide,dataKey:f,type:m.iconType||h||"square",color:(0,e.Ps)(b),value:g||f,payload:d}}),i(i(i({},m),d.s.getWithHeight(k,h)),{},{payload:b,item:k})}},47212:(a,b,c)=>{var d=c(87270),e=c(30316),f=c(22),g=c(40542),h=c(7383);a.exports=function(a,b,c){var i=g(a)?d:e;return c&&h(a,b,c)&&(b=void 0),i(a,f(b,3))}},47282:(a,b,c)=>{a=c.nmd(a);var d=c(10663),e=b&&!b.nodeType&&b,f=e&&a&&!a.nodeType&&a,g=f&&f.exports===e&&d.process,h=function(){try{var a=f&&f.require&&f.require("util").types;if(a)return a;return g&&g.binding&&g.binding("util")}catch(a){}}();a.exports=h},47603:(a,b,c)=>{var d=c(14675),e=c(91928),f=c(48169);a.exports=e?function(a,b){return e(a,"toString",{configurable:!0,enumerable:!1,value:d(b),writable:!0})}:f},48169:a=>{a.exports=function(a){return a}},48385:a=>{var b="\ud800-\udfff",c="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",d="\ud83c[\udffb-\udfff]",e="[^"+b+"]",f="(?:\ud83c[\udde6-\uddff]){2}",g="[\ud800-\udbff][\udc00-\udfff]",h="(?:"+c+"|"+d+")?",i="[\\ufe0e\\ufe0f]?",j="(?:\\u200d(?:"+[e,f,g].join("|")+")"+i+h+")*",k=RegExp(d+"(?="+d+")|"+("(?:"+[e+c+"?",c,f,g,"["+b+"]"].join("|"))+")"+(i+h+j),"g");a.exports=function(a){return a.match(k)||[]}},48482:(a,b,c)=>{"use strict";c.d(b,{u:()=>p});var d=c(49384),e=c(43210),f=c.n(e),g=c(45603),h=c.n(g),i=c(45370),j=c(10521),k=c(54186);function l(a){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function m(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function n(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?m(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=l(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=l(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==l(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):m(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function o(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}var p=(0,e.forwardRef)(function(a,b){var c,g=a.aspect,l=a.initialDimension,m=void 0===l?{width:-1,height:-1}:l,p=a.width,q=void 0===p?"100%":p,r=a.height,s=void 0===r?"100%":r,t=a.minWidth,u=void 0===t?0:t,v=a.minHeight,w=a.maxHeight,x=a.children,y=a.debounce,z=void 0===y?0:y,A=a.id,B=a.className,C=a.onResize,D=a.style,E=(0,e.useRef)(null),F=(0,e.useRef)();F.current=C,(0,e.useImperativeHandle)(b,function(){return Object.defineProperty(E.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),E.current},configurable:!0})});var G=function(a){if(Array.isArray(a))return a}(c=(0,e.useState)({containerWidth:m.width,containerHeight:m.height}))||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(c,2)||function(a,b){if(a){if("string"==typeof a)return o(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return o(a,b)}}(c,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),H=G[0],I=G[1],J=(0,e.useCallback)(function(a,b){I(function(c){var d=Math.round(a),e=Math.round(b);return c.containerWidth===d&&c.containerHeight===e?c:{containerWidth:d,containerHeight:e}})},[]);(0,e.useEffect)(function(){var a=function(a){var b,c=a[0].contentRect,d=c.width,e=c.height;J(d,e),null==(b=F.current)||b.call(F,d,e)};z>0&&(a=h()(a,z,{trailing:!0,leading:!1}));var b=new ResizeObserver(a),c=E.current.getBoundingClientRect();return J(c.width,c.height),b.observe(E.current),function(){b.disconnect()}},[J,z]);var K=(0,e.useMemo)(function(){var a=H.containerWidth,b=H.containerHeight;if(a<0||b<0)return null;(0,j.R)((0,i._3)(q)||(0,i._3)(s),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",q,s),(0,j.R)(!g||g>0,"The aspect(%s) must be greater than zero.",g);var c=(0,i._3)(q)?a:q,d=(0,i._3)(s)?b:s;g&&g>0&&(c?d=c/g:d&&(c=d*g),w&&d>w&&(d=w)),(0,j.R)(c>0||d>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",c,d,q,s,u,v,g);var h=!Array.isArray(x)&&(0,k.Mn)(x.type).endsWith("Chart");return f().Children.map(x,function(a){return f().isValidElement(a)?(0,e.cloneElement)(a,n({width:c,height:d},h?{style:n({height:"100%",width:"100%",maxHeight:d,maxWidth:c},a.props.style)}:{})):a})},[g,x,s,w,v,u,H,q]);return f().createElement("div",{id:A?"".concat(A):void 0,className:(0,d.A)("recharts-responsive-container",B),style:n(n({},void 0===D?{}:D),{},{width:q,height:s,minWidth:u,minHeight:v,maxHeight:w}),ref:E},K)})},48657:(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&"length"in a?a:Array.from(a)}c.d(b,{A:()=>d}),Array.prototype.slice},49227:(a,b,c)=>{var d=c(29395),e=c(27467);a.exports=function(a){return"symbol"==typeof a||e(a)&&"[object Symbol]"==d(a)}},49897:(a,b,c)=>{var d=c(4768);a.exports=function(a,b){a.prototype=Object.create(b.prototype),a.prototype.constructor=a,d(a,b)},a.exports.__esModule=!0,a.exports.default=a.exports},51449:(a,b,c)=>{var d=c(85745),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,f=/\\(\\)?/g;a.exports=d(function(a){var b=[];return 46===a.charCodeAt(0)&&b.push(""),a.replace(e,function(a,c,d,e){b.push(d?e.replace(f,"$1"):c||a)}),b})},52415:(a,b,c)=>{"use strict";c.d(b,{A:()=>f,z:()=>g});var d=c(37877),e=c(8886);function f(){var a,b,c=(0,e.A)().unknown(void 0),g=c.domain,h=c.range,i=0,j=1,k=!1,l=0,m=0,n=.5;function o(){var c=g().length,d=j<i,e=d?j:i,f=d?i:j;a=(f-e)/Math.max(1,c-l+2*m),k&&(a=Math.floor(a)),e+=(f-e-a*(c-l))*n,b=a*(1-l),k&&(e=Math.round(e),b=Math.round(b));var o=(function(a,b,c){a*=1,b*=1,c=(e=arguments.length)<2?(b=a,a=0,1):e<3?1:+c;for(var d=-1,e=0|Math.max(0,Math.ceil((b-a)/c)),f=Array(e);++d<e;)f[d]=a+d*c;return f})(c).map(function(b){return e+a*b});return h(d?o.reverse():o)}return delete c.unknown,c.domain=function(a){return arguments.length?(g(a),o()):g()},c.range=function(a){return arguments.length?([i,j]=a,i*=1,j*=1,o()):[i,j]},c.rangeRound=function(a){return[i,j]=a,i*=1,j*=1,k=!0,o()},c.bandwidth=function(){return b},c.step=function(){return a},c.round=function(a){return arguments.length?(k=!!a,o()):k},c.padding=function(a){return arguments.length?(l=Math.min(1,m=+a),o()):l},c.paddingInner=function(a){return arguments.length?(l=Math.min(1,a),o()):l},c.paddingOuter=function(a){return arguments.length?(m=+a,o()):m},c.align=function(a){return arguments.length?(n=Math.max(0,Math.min(1,a)),o()):n},c.copy=function(){return f(g(),[i,j]).round(k).paddingInner(l).paddingOuter(m).align(n)},d.C.apply(o(),arguments)}function g(){return function a(b){var c=b.copy;return b.padding=b.paddingOuter,delete b.paddingInner,delete b.paddingOuter,b.copy=function(){return a(c())},b}(f.apply(null,arguments).paddingInner(1))}},52599:a=>{a.exports=function(a,b){for(var c=-1,d=null==a?0:a.length,e=0,f=[];++c<d;){var g=a[c];b(g,c,a)&&(f[e++]=g)}return f}},52823:(a,b,c)=>{var d=c(85406),e=function(){var a=/[^.]+$/.exec(d&&d.keys&&d.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}();a.exports=function(a){return!!e&&e in a}},52931:(a,b,c)=>{var d=c(77834),e=c(89605),f=Object.prototype.hasOwnProperty;a.exports=function(a){if(!d(a))return e(a);var b=[];for(var c in Object(a))f.call(a,c)&&"constructor"!=c&&b.push(c);return b}},54186:(a,b,c)=>{"use strict";c.d(b,{AW:()=>K,BU:()=>B,J9:()=>F,Me:()=>C,Mn:()=>w,OV:()=>G,X_:()=>J,aS:()=>A,ee:()=>I});var d=c(40491),e=c.n(d),f=c(37456),g=c.n(f),h=c(63866),i=c.n(h),j=c(5231),k=c.n(j),l=c(55048),m=c.n(l),n=c(43210),o=c(93780),p=c(45370),q=c(18842),r=c(4057),s=["children"],t=["children"];function u(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var v={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},w=function(a){return"string"==typeof a?a:a?a.displayName||a.name||"Component":""},x=null,y=null,z=function a(b){if(b===x&&Array.isArray(y))return y;var c=[];return n.Children.forEach(b,function(b){g()(b)||((0,o.isFragment)(b)?c=c.concat(a(b.props.children)):c.push(b))}),y=c,x=b,c};function A(a,b){var c=[],d=[];return d=Array.isArray(b)?b.map(function(a){return w(a)}):[w(b)],z(a).forEach(function(a){var b=e()(a,"type.displayName")||e()(a,"type.name");-1!==d.indexOf(b)&&c.push(a)}),c}function B(a,b){var c=A(a,b);return c&&c[0]}var C=function(a){if(!a||!a.props)return!1;var b=a.props,c=b.width,d=b.height;return!!(0,p.Et)(c)&&!(c<=0)&&!!(0,p.Et)(d)&&!(d<=0)},D=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],E=function(a,b,c,d){var e,f=null!=(e=null===r.VU||void 0===r.VU?void 0:r.VU[d])?e:[];return!k()(a)&&(d&&f.includes(b)||r.QQ.includes(b))||c&&r.j2.includes(b)},F=function(a,b,c){if(!a||"function"==typeof a||"boolean"==typeof a)return null;var d=a;if((0,n.isValidElement)(a)&&(d=a.props),!m()(d))return null;var e={};return Object.keys(d).forEach(function(a){var f;E(null==(f=d)?void 0:f[a],a,b,c)&&(e[a]=d[a])}),e},G=function a(b,c){if(b===c)return!0;var d=n.Children.count(b);if(d!==n.Children.count(c))return!1;if(0===d)return!0;if(1===d)return H(Array.isArray(b)?b[0]:b,Array.isArray(c)?c[0]:c);for(var e=0;e<d;e++){var f=b[e],g=c[e];if(Array.isArray(f)||Array.isArray(g)){if(!a(f,g))return!1}else if(!H(f,g))return!1}return!0},H=function(a,b){if(g()(a)&&g()(b))return!0;if(!g()(a)&&!g()(b)){var c=a.props||{},d=c.children,e=u(c,s),f=b.props||{},h=f.children,i=u(f,t);if(d&&h)return(0,q.b)(e,i)&&G(d,h);if(!d&&!h)return(0,q.b)(e,i)}return!1},I=function(a,b){var c=[],d={};return z(a).forEach(function(a,e){if(a&&a.type&&i()(a.type)&&D.indexOf(a.type)>=0)c.push(a);else if(a){var f=w(a.type),g=b[f]||{},h=g.handler,j=g.once;if(h&&(!j||!d[f])){var k=h(a,f,e);c.push(k),d[f]=!0}}}),c},J=function(a){var b=a&&a.type;return b&&v[b]?v[b]:null},K=function(a,b){return z(b).indexOf(a)}},54765:(a,b,c)=>{var d=c(67554),e=c(32269);a.exports=function(a,b){var c=-1,f=e(a)?Array(a.length):[];return d(a,function(a,d,e){f[++c]=b(a,d,e)}),f}},55048:a=>{a.exports=function(a){var b=typeof a;return null!=a&&("object"==b||"function"==b)}},56476:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},56506:(a,b,c)=>{var d=c(32269);a.exports=function(a,b){return function(c,e){if(null==c)return c;if(!d(c))return a(c,e);for(var f=c.length,g=b?f:-1,h=Object(c);(b?g--:++g<f)&&!1!==e(h[g],g,h););return c}}},57155:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Sprout",[["path",{d:"M7 20h10",key:"e6iznv"}],["path",{d:"M10 20c5.5-2.5.8-6.4 3-10",key:"161w41"}],["path",{d:"M9.5 9.4c1.1.8 1.8 2.2 2.3 3.7-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2 2.8-.5 4.4 0 5.5.8z",key:"9gtqwd"}],["path",{d:"M14.1 6a7 7 0 0 0-1.1 4c1.9-.1 3.3-.6 4.3-1.4 1-1 1.6-2.3 1.7-4.6-2.7.1-4 1-4.9 2z",key:"bkxnd2"}]])},57797:(a,b,c)=>{var d=c(67009);a.exports=function(a,b){for(var c=a.length;c--;)if(d(a[c][0],b))return c;return -1}},58141:(a,b,c)=>{a.exports=c(41547)(Object,"create")},58276:a=>{a.exports=function(a,b){return a.has(b)}},58744:(a,b,c)=>{var d=c(57797);a.exports=function(a,b){var c=this.__data__,e=d(c,a);return e<0?(++this.size,c.push([a,b])):c[e][1]=b,this}},58965:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]])},59467:(a,b,c)=>{var d=c(35142),e=c(35163),f=c(40542),g=c(38428),h=c(69619),i=c(46436);a.exports=function(a,b,c){b=d(b,a);for(var j=-1,k=b.length,l=!1;++j<k;){var m=i(b[j]);if(!(l=null!=a&&c(a,m)))break;a=a[m]}return l||++j!=k?l:!!(k=null==a?0:a.length)&&h(k)&&g(m,k)&&(f(a)||e(a))}},59774:a=>{a.exports=function(a){var b=-1,c=Array(a.size);return a.forEach(function(a,d){c[++b]=[d,a]}),c}},60927:(a,b,c)=>{"use strict";c.d(b,{u:()=>r});var d=c(43210),e=c.n(d),f=c(89653),g=c(98986),h=c(54186),i=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function j(a){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function k(){return(k=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function l(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function m(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(m=function(){return!!a})()}function n(a){return(n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function o(a,b){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function p(a,b,c){return(b=q(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function q(a){var b=function(a,b){if("object"!=j(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=j(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==j(b)?b:b+""}var r=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=n(a),function(a,b){if(b&&("object"===j(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,m()?Reflect.construct(a,b||[],n(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&o(c,a),b=[{key:"render",value:function(){var a=this.props,b=a.offset,c=a.layout,d=a.width,j=a.dataKey,m=a.data,n=a.dataPointFormatter,o=a.xAxis,p=a.yAxis,q=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,i),r=(0,h.J9)(q,!1);"x"===this.props.direction&&"number"!==o.type&&(0,f.A)(!1);var s=m.map(function(a){var f,h,i=n(a,j),m=i.x,q=i.y,s=i.value,t=i.errorVal;if(!t)return null;var u=[];if(Array.isArray(t)){var v=function(a){if(Array.isArray(a))return a}(t)||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(t,2)||function(a,b){if(a){if("string"==typeof a)return l(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return l(a,b)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=v[0],h=v[1]}else f=h=t;if("vertical"===c){var w=o.scale,x=q+b,y=x+d,z=x-d,A=w(s-f),B=w(s+h);u.push({x1:B,y1:y,x2:B,y2:z}),u.push({x1:A,y1:x,x2:B,y2:x}),u.push({x1:A,y1:y,x2:A,y2:z})}else if("horizontal"===c){var C=p.scale,D=m+b,E=D-d,F=D+d,G=C(s-f),H=C(s+h);u.push({x1:E,y1:H,x2:F,y2:H}),u.push({x1:D,y1:G,x2:D,y2:H}),u.push({x1:E,y1:G,x2:F,y2:G})}return e().createElement(g.W,k({className:"recharts-errorBar",key:"bar-".concat(u.map(function(a){return"".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)}))},r),u.map(function(a){return e().createElement("line",k({},a,{key:"line-".concat(a.x1,"-").concat(a.x2,"-").concat(a.y1,"-").concat(a.y2)}))}))});return e().createElement(g.W,{className:"recharts-errorBars"},s)}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,q(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(e().Component);p(r,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),p(r,"displayName","ErrorBar")},61320:(a,b,c)=>{var d=c(8336);a.exports=function(a){return d(this,a).has(a)}},61548:(a,b,c)=>{var d=c(5231),e=c(52823),f=c(55048),g=c(12290),h=/^\[object .+?Constructor\]$/,i=Object.prototype,j=Function.prototype.toString,k=i.hasOwnProperty,l=RegExp("^"+j.call(k).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");a.exports=function(a){return!(!f(a)||e(a))&&(d(a)?l:h).test(g(a))}},61837:(a,b,c)=>{var d=c(21367),e=c(22),f=c(54765),g=c(40542);a.exports=function(a,b){return(g(a)?d:f)(a,e(b,3))}},63866:(a,b,c)=>{var d=c(29395),e=c(40542),f=c(27467);a.exports=function(a){return"string"==typeof a||!e(a)&&f(a)&&"[object String]"==d(a)}},63979:(a,b,c)=>{var d=c(52599),e=c(6330),f=Object.prototype.propertyIsEnumerable,g=Object.getOwnPropertySymbols;a.exports=g?function(a){return null==a?[]:d(g(a=Object(a)),function(b){return f.call(a,b)})}:e},65662:a=>{a.exports=function(a,b){return function(c){return a(b(c))}}},65727:(a,b,c)=>{var d=c(81957);a.exports=function(a,b,c){for(var e=-1,f=a.criteria,g=b.criteria,h=f.length,i=c.length;++e<h;){var j=d(f[e],g[e]);if(j){if(e>=i)return j;return j*("desc"==c[e]?-1:1)}}return a.index-b.index}},65932:(a,b,c)=>{a.exports=c(65662)(Object.getPrototypeOf,Object)},65984:a=>{a.exports=function(a){return function(b,c,d){for(var e=-1,f=Object(b),g=d(b),h=g.length;h--;){var i=g[a?h:++e];if(!1===c(f[i],i,f))break}return b}}},66354:(a,b,c)=>{var d=c(85244),e=Math.max;a.exports=function(a,b,c){return b=e(void 0===b?a.length-1:b,0),function(){for(var f=arguments,g=-1,h=e(f.length-b,0),i=Array(h);++g<h;)i[g]=f[b+g];g=-1;for(var j=Array(b+1);++g<b;)j[g]=f[g];return j[b]=c(i),d(a,this,j)}}},66400:a=>{var b=Date.now;a.exports=function(a){var c=0,d=0;return function(){var e=b(),f=16-(e-d);if(d=e,f>0){if(++c>=800)return arguments[0]}else c=0;return a.apply(void 0,arguments)}}},66713:(a,b,c)=>{var d=c(3105),e=c(34117),f=c(48385);a.exports=function(a){return e(a)?f(a):d(a)}},66837:(a,b,c)=>{var d=c(58141);a.exports=function(){this.__data__=d?d(null):{},this.size=0}},66930:(a,b,c)=>{var d=c(27669),e=c(28837),f=c(94388),g=c(35800),h=c(58744);function i(a){var b=-1,c=null==a?0:a.length;for(this.clear();++b<c;){var d=a[b];this.set(d[0],d[1])}}i.prototype.clear=d,i.prototype.delete=e,i.prototype.get=f,i.prototype.has=g,i.prototype.set=h,a.exports=i},66992:(a,b)=>{"use strict";var c=Symbol.for("react.element"),d=Symbol.for("react.portal"),e=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),h=Symbol.for("react.provider"),i=Symbol.for("react.context"),j=Symbol.for("react.server_context"),k=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),n=Symbol.for("react.memo"),o=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),b.isFragment=function(a){return function(a){if("object"==typeof a&&null!==a){var b=a.$$typeof;switch(b){case c:switch(a=a.type){case e:case g:case f:case l:case m:return a;default:switch(a=a&&a.$$typeof){case j:case i:case k:case o:case n:case h:return a;default:return b}}case d:return b}}}(a)===e}},67009:a=>{a.exports=function(a,b){return a===b||a!=a&&b!=b}},67200:(a,b,c)=>{var d=c(66930),e=c(37575),f=c(75411),g=c(34746),h=c(25118),i=c(30854);function j(a){var b=this.__data__=new d(a);this.size=b.size}j.prototype.clear=e,j.prototype.delete=f,j.prototype.get=g,j.prototype.has=h,j.prototype.set=i,a.exports=j},67367:(a,b,c)=>{var d=c(99525),e=c(22),f=c(75847),g=c(40542),h=c(7383);a.exports=function(a,b,c){var i=g(a)?d:f;return c&&h(a,b,c)&&(b=void 0),i(a,e(b,3))}},67554:(a,b,c)=>{var d=c(99114);a.exports=c(56506)(d)},67619:(a,b,c)=>{var d=c(40542),e=c(49227),f=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,g=/^\w*$/;a.exports=function(a,b){if(d(a))return!1;var c=typeof a;return!!("number"==c||"symbol"==c||"boolean"==c||null==a||e(a))||g.test(a)||!f.test(a)||null!=b&&a in Object(b)}},67629:(a,b,c)=>{"use strict";c.d(b,{yp:()=>H,GG:()=>O,NE:()=>I,nZ:()=>J,xQ:()=>K});var d=c(43210),e=c.n(d),f=c(5231),g=c.n(f),h=c(38404),i=c.n(h),j=c(98451),k=c.n(j),l=c(71967),m=c.n(l),n=c(71524),o=c(49384),p=c(93492),q=c(54186);function r(a){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function s(){return(s=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function t(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function u(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function v(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?u(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=r(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=r(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==r(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):u(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var w=function(a,b,c,d,e){var f=c-d;return"M ".concat(a,",").concat(b)+"L ".concat(a+c,",").concat(b)+"L ".concat(a+c-f/2,",").concat(b+e)+"L ".concat(a+c-f/2-d,",").concat(b+e)+"L ".concat(a,",").concat(b," Z")},x={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(a){var b,c=v(v({},x),a),f=(0,d.useRef)(),g=function(a){if(Array.isArray(a))return a}(b=(0,d.useState)(-1))||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(b,2)||function(a,b){if(a){if("string"==typeof a)return t(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return t(a,b)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=g[0],i=g[1];(0,d.useEffect)(function(){if(f.current&&f.current.getTotalLength)try{var a=f.current.getTotalLength();a&&i(a)}catch(a){}},[]);var j=c.x,k=c.y,l=c.upperWidth,m=c.lowerWidth,n=c.height,r=c.className,u=c.animationEasing,y=c.animationDuration,z=c.animationBegin,A=c.isUpdateAnimationActive;if(j!==+j||k!==+k||l!==+l||m!==+m||n!==+n||0===l&&0===m||0===n)return null;var B=(0,o.A)("recharts-trapezoid",r);return A?e().createElement(p.Ay,{canBegin:h>0,from:{upperWidth:0,lowerWidth:0,height:n,x:j,y:k},to:{upperWidth:l,lowerWidth:m,height:n,x:j,y:k},duration:y,animationEasing:u,isActive:A},function(a){var b=a.upperWidth,d=a.lowerWidth,g=a.height,i=a.x,j=a.y;return e().createElement(p.Ay,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:z,duration:y,easing:u},e().createElement("path",s({},(0,q.J9)(c,!0),{className:B,d:w(i,j,b,d,g),ref:f})))}):e().createElement("g",null,e().createElement("path",s({},(0,q.J9)(c,!0),{className:B,d:w(j,k,l,m,n)})))},z=c(34955),A=c(98986),B=c(10919),C=["option","shapeType","propTransformer","activeClassName","isActive"];function D(a){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function E(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function F(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?E(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=D(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=D(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==D(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):E(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function G(a){var b=a.shapeType,c=a.elementProps;switch(b){case"rectangle":return e().createElement(n.M,c);case"trapezoid":return e().createElement(y,c);case"sector":return e().createElement(z.h,c);case"symbols":if("symbols"===b)return e().createElement(B.i,c);break;default:return null}}function H(a){var b,c=a.option,f=a.shapeType,h=a.propTransformer,j=a.activeClassName,l=a.isActive,m=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,C);if((0,d.isValidElement)(c))b=(0,d.cloneElement)(c,F(F({},m),(0,d.isValidElement)(c)?c.props:c));else if(g()(c))b=c(m);else if(i()(c)&&!k()(c)){var n=(void 0===h?function(a,b){return F(F({},b),a)}:h)(c,m);b=e().createElement(G,{shapeType:f,elementProps:n})}else b=e().createElement(G,{shapeType:f,elementProps:m});return l?e().createElement(A.W,{className:void 0===j?"recharts-active-shape":j},b):b}function I(a,b){return null!=b&&"trapezoids"in a.props}function J(a,b){return null!=b&&"sectors"in a.props}function K(a,b){return null!=b&&"points"in a.props}function L(a,b){var c,d,e=a.x===(null==b||null==(c=b.labelViewBox)?void 0:c.x)||a.x===b.x,f=a.y===(null==b||null==(d=b.labelViewBox)?void 0:d.y)||a.y===b.y;return e&&f}function M(a,b){var c=a.endAngle===b.endAngle,d=a.startAngle===b.startAngle;return c&&d}function N(a,b){var c=a.x===b.x,d=a.y===b.y,e=a.z===b.z;return c&&d&&e}function O(a){var b,c,d,e=a.activeTooltipItem,f=a.graphicalItem,g=a.itemData,h=(I(f,e)?b="trapezoids":J(f,e)?b="sectors":K(f,e)&&(b="points"),b),i=I(f,e)?null==(c=e.tooltipPayload)||null==(c=c[0])||null==(c=c.payload)?void 0:c.payload:J(f,e)?null==(d=e.tooltipPayload)||null==(d=d[0])||null==(d=d.payload)?void 0:d.payload:K(f,e)?e.payload:{},j=g.filter(function(a,b){var c=m()(i,a),d=f.props[h].filter(function(a){var b;return(I(f,e)?b=L:J(f,e)?b=M:K(f,e)&&(b=N),b)(a,e)}),g=f.props[h].indexOf(d[d.length-1]);return c&&b===g});return g.indexOf(j[j.length-1])}},67854:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(49897),e=c(43210);function f(a){return a&&a.stopPropagation&&a.stopPropagation(),a&&a.preventDefault&&a.preventDefault(),!1}function g(a){return null==a?[]:Array.isArray(a)?a.slice():[a]}function h(a){return null!==a&&1===a.length?a[0]:a.slice()}function i(a){Object.keys(a).forEach(b=>{"undefined"!=typeof document&&document.addEventListener(b,a[b],!1)})}function j(a,b){let c;return k(((c=a)<=b.min&&(c=b.min),c>=b.max&&(c=b.max),c),b)}function k(a,b){let c=(a-b.min)%b.step,d=a-c;return 2*Math.abs(c)>=b.step&&(d+=c>0?b.step:-b.step),parseFloat(d.toFixed(5))}let l=function(a){function b(b){var c;(c=a.call(this,b)||this).onKeyUp=()=>{c.onEnd()},c.onMouseUp=()=>{c.onEnd(c.getMouseEventMap())},c.onTouchEnd=a=>{a.preventDefault(),c.onEnd(c.getTouchEventMap())},c.onBlur=()=>{c.setState({index:-1},c.onEnd(c.getKeyDownEventMap()))},c.onMouseMove=a=>{c.setState({pending:!0});let b=c.getMousePosition(a),d=c.getDiffPosition(b[0]),e=c.getValueFromPosition(d);c.move(e)},c.onTouchMove=a=>{if(a.touches.length>1)return;c.setState({pending:!0});let b=c.getTouchPosition(a);if(void 0===c.isScrolling){let a=b[0]-c.startPosition[0],d=b[1]-c.startPosition[1];c.isScrolling=Math.abs(d)>Math.abs(a)}if(c.isScrolling)return void c.setState({index:-1});let d=c.getDiffPosition(b[0]),e=c.getValueFromPosition(d);c.move(e)},c.onKeyDown=a=>{if(!(a.ctrlKey||a.shiftKey||a.altKey||a.metaKey))switch(c.setState({pending:!0}),a.key){case"ArrowLeft":case"ArrowDown":case"Left":case"Down":a.preventDefault(),c.moveDownByStep();break;case"ArrowRight":case"ArrowUp":case"Right":case"Up":a.preventDefault(),c.moveUpByStep();break;case"Home":a.preventDefault(),c.move(c.props.min);break;case"End":a.preventDefault(),c.move(c.props.max);break;case"PageDown":a.preventDefault(),c.moveDownByStep(c.props.pageFn(c.props.step));break;case"PageUp":a.preventDefault(),c.moveUpByStep(c.props.pageFn(c.props.step))}},c.onSliderMouseDown=a=>{if(!c.props.disabled&&2!==a.button){if(c.setState({pending:!0}),!c.props.snapDragDisabled){let b=c.getMousePosition(a);c.forceValueFromPosition(b[0],a=>{c.start(a,b[0]),i(c.getMouseEventMap())})}f(a)}},c.onSliderClick=a=>{if(!c.props.disabled&&c.props.onSliderClick&&!c.hasMoved){let b=c.getMousePosition(a),d=j(c.calcValue(c.calcOffsetFromPosition(b[0])),c.props);c.props.onSliderClick(d)}},c.createOnKeyDown=a=>b=>{c.props.disabled||(c.start(a),i(c.getKeyDownEventMap()),f(b))},c.createOnMouseDown=a=>b=>{if(c.props.disabled||2===b.button)return;c.setState({pending:!0});let d=c.getMousePosition(b);c.start(a,d[0]),i(c.getMouseEventMap()),f(b)},c.createOnTouchStart=a=>b=>{if(c.props.disabled||b.touches.length>1)return;c.setState({pending:!0});let d=c.getTouchPosition(b);c.startPosition=d,c.isScrolling=void 0,c.start(a,d[0]),i(c.getTouchEventMap()),b.stopPropagation&&b.stopPropagation()},c.handleResize=()=>{let a=window.setTimeout(()=>{c.pendingResizeTimeouts.shift(),c.resize()},0);c.pendingResizeTimeouts.push(a)},c.renderThumb=(a,b)=>{let d=c.props.thumbClassName+" "+c.props.thumbClassName+"-"+b+" "+(c.state.index===b?c.props.thumbActiveClassName:""),e={ref:a=>{c["thumb"+b]=a},key:c.props.thumbClassName+"-"+b,className:d,style:a,onMouseDown:c.createOnMouseDown(b),onTouchStart:c.createOnTouchStart(b),onFocus:c.createOnKeyDown(b),tabIndex:0,role:"slider","aria-orientation":c.props.orientation,"aria-valuenow":c.state.value[b],"aria-valuemin":c.props.min,"aria-valuemax":c.props.max,"aria-label":Array.isArray(c.props.ariaLabel)?c.props.ariaLabel[b]:c.props.ariaLabel,"aria-labelledby":Array.isArray(c.props.ariaLabelledby)?c.props.ariaLabelledby[b]:c.props.ariaLabelledby,"aria-disabled":c.props.disabled},f={index:b,value:h(c.state.value),valueNow:c.state.value[b]};return c.props.ariaValuetext&&(e["aria-valuetext"]="string"==typeof c.props.ariaValuetext?c.props.ariaValuetext:c.props.ariaValuetext(f)),c.props.renderThumb(e,f)},c.renderTrack=(a,b,d)=>{let e={key:c.props.trackClassName+"-"+a,className:c.props.trackClassName+" "+c.props.trackClassName+"-"+a,style:c.buildTrackStyle(b,c.state.upperBound-d)},f={index:a,value:h(c.state.value)};return c.props.renderTrack(e,f)};let d=g(b.value);d.length||(d=g(b.defaultValue)),c.pendingResizeTimeouts=[];let k=[];for(let a=0;a<d.length;a+=1)d[a]=j(d[a],b),k.push(a);return c.resizeObserver=null,c.resizeElementRef=e.createRef(),c.state={index:-1,upperBound:0,sliderLength:0,value:d,zIndices:k},c}d(b,a);var c=b.prototype;return c.componentDidMount=function(){"undefined"!=typeof window&&(this.resizeObserver=new ResizeObserver(this.handleResize),this.resizeObserver.observe(this.resizeElementRef.current),this.resize())},b.getDerivedStateFromProps=function(a,b){let c=g(a.value);return c.length?b.pending?null:{value:c.map(b=>j(b,a))}:null},c.componentDidUpdate=function(){0===this.state.upperBound&&this.resize()},c.componentWillUnmount=function(){this.clearPendingResizeTimeouts(),this.resizeObserver&&this.resizeObserver.disconnect()},c.onEnd=function(a){a&&Object.keys(a).forEach(b=>{"undefined"!=typeof document&&document.removeEventListener(b,a[b],!1)}),this.hasMoved&&this.fireChangeEvent("onAfterChange"),this.setState({pending:!1}),this.hasMoved=!1},c.getValue=function(){return h(this.state.value)},c.getClosestIndex=function(a){let b=Number.MAX_VALUE,c=-1,{value:d}=this.state,e=d.length;for(let f=0;f<e;f+=1){let e=Math.abs(a-this.calcOffset(d[f]));e<b&&(b=e,c=f)}return c},c.getMousePosition=function(a){return[a["page"+this.axisKey()],a["page"+this.orthogonalAxisKey()]]},c.getTouchPosition=function(a){let b=a.touches[0];return[b["page"+this.axisKey()],b["page"+this.orthogonalAxisKey()]]},c.getKeyDownEventMap=function(){return{keydown:this.onKeyDown,keyup:this.onKeyUp,focusout:this.onBlur}},c.getMouseEventMap=function(){return{mousemove:this.onMouseMove,mouseup:this.onMouseUp}},c.getTouchEventMap=function(){return{touchmove:this.onTouchMove,touchend:this.onTouchEnd}},c.getValueFromPosition=function(a){let b=a/(this.state.sliderLength-this.state.thumbSize)*(this.props.max-this.props.min);return j(this.state.startValue+b,this.props)},c.getDiffPosition=function(a){let b=a-this.state.startPosition;return this.props.invert&&(b*=-1),b},c.resize=function(){let{slider:a,thumb0:b}=this;if(!a||!b)return;let c=this.sizeKey(),d=a.getBoundingClientRect(),e=a[c],f=d[this.posMaxKey()],g=d[this.posMinKey()],h=b.getBoundingClientRect()[c.replace("client","").toLowerCase()],i=e-h,j=Math.abs(f-g);this.state.upperBound===i&&this.state.sliderLength===j&&this.state.thumbSize===h||this.setState({upperBound:i,sliderLength:j,thumbSize:h})},c.calcOffset=function(a){let b=this.props.max-this.props.min;return 0===b?0:(a-this.props.min)/b*this.state.upperBound},c.calcValue=function(a){return a/this.state.upperBound*(this.props.max-this.props.min)+this.props.min},c.calcOffsetFromPosition=function(a){let{slider:b}=this,c=b.getBoundingClientRect(),d=c[this.posMaxKey()],e=c[this.posMinKey()],f=a-(window["page"+this.axisKey()+"Offset"]+(this.props.invert?d:e));return this.props.invert&&(f=this.state.sliderLength-f),f-=this.state.thumbSize/2},c.forceValueFromPosition=function(a,b){let c=this.calcOffsetFromPosition(a),d=this.getClosestIndex(c),e=j(this.calcValue(c),this.props),f=this.state.value.slice();f[d]=e;for(let a=0;a<f.length-1;a+=1)if(f[a+1]-f[a]<this.props.minDistance)return;this.fireChangeEvent("onBeforeChange"),this.hasMoved=!0,this.setState({value:f},()=>{b(d),this.fireChangeEvent("onChange")})},c.clearPendingResizeTimeouts=function(){do clearTimeout(this.pendingResizeTimeouts.shift());while(this.pendingResizeTimeouts.length)},c.start=function(a,b){let c=this["thumb"+a];c&&c.focus();let{zIndices:d}=this.state;d.splice(d.indexOf(a),1),d.push(a),this.setState(c=>({startValue:c.value[a],startPosition:void 0!==b?b:c.startPosition,index:a,zIndices:d}))},c.moveUpByStep=function(a){void 0===a&&(a=this.props.step);let b=this.state.value[this.state.index],c=j(this.props.invert&&"horizontal"===this.props.orientation?b-a:b+a,this.props);this.move(Math.min(c,this.props.max))},c.moveDownByStep=function(a){void 0===a&&(a=this.props.step);let b=this.state.value[this.state.index],c=j(this.props.invert&&"horizontal"===this.props.orientation?b+a:b-a,this.props);this.move(Math.max(c,this.props.min))},c.move=function(a){let b=this.state.value.slice(),{index:c}=this.state,{length:d}=b,e=b[c];if(a===e)return;this.hasMoved||this.fireChangeEvent("onBeforeChange"),this.hasMoved=!0;let{pearling:f,max:g,min:h,minDistance:i}=this.props;if(!f){if(c>0){let d=b[c-1];a<d+i&&(a=d+i)}if(c<d-1){let d=b[c+1];a>d-i&&(a=d-i)}}b[c]=a,f&&d>1&&(a>e?(this.pushSucceeding(b,i,c),function(a,b,c,d){for(let e=0;e<a;e+=1){let f=d-e*c;b[a-1-e]>f&&(b[a-1-e]=f)}}(d,b,i,g)):a<e&&(this.pushPreceding(b,i,c),function(a,b,c,d){for(let e=0;e<a;e+=1){let a=d+e*c;b[e]<a&&(b[e]=a)}}(d,b,i,h))),this.setState({value:b},this.fireChangeEvent.bind(this,"onChange"))},c.pushSucceeding=function(a,b,c){let d,e;for(e=a[d=c]+b;null!==a[d+1]&&e>a[d+1];d+=1,e=a[d]+b)a[d+1]=k(e,this.props)},c.pushPreceding=function(a,b,c){for(let d=c,e=a[d]-b;null!==a[d-1]&&e<a[d-1];d-=1,e=a[d]-b)a[d-1]=k(e,this.props)},c.axisKey=function(){return"vertical"===this.props.orientation?"Y":"X"},c.orthogonalAxisKey=function(){return"vertical"===this.props.orientation?"X":"Y"},c.posMinKey=function(){return"vertical"===this.props.orientation?this.props.invert?"bottom":"top":this.props.invert?"right":"left"},c.posMaxKey=function(){return"vertical"===this.props.orientation?this.props.invert?"top":"bottom":this.props.invert?"left":"right"},c.sizeKey=function(){return"vertical"===this.props.orientation?"clientHeight":"clientWidth"},c.fireChangeEvent=function(a){this.props[a]&&this.props[a](h(this.state.value),this.state.index)},c.buildThumbStyle=function(a,b){let c={position:"absolute",touchAction:"none",willChange:this.state.index>=0?this.posMinKey():void 0,zIndex:this.state.zIndices.indexOf(b)+1};return c[this.posMinKey()]=a+"px",c},c.buildTrackStyle=function(a,b){let c={position:"absolute",willChange:this.state.index>=0?this.posMinKey()+","+this.posMaxKey():void 0};return c[this.posMinKey()]=a,c[this.posMaxKey()]=b,c},c.buildMarkStyle=function(a){var b;return(b={position:"absolute"})[this.posMinKey()]=a,b},c.renderThumbs=function(a){let{length:b}=a,c=[];for(let d=0;d<b;d+=1)c[d]=this.buildThumbStyle(a[d],d);let d=[];for(let a=0;a<b;a+=1)d[a]=this.renderThumb(c[a],a);return d},c.renderTracks=function(a){let b=[],c=a.length-1;b.push(this.renderTrack(0,0,a[0]));for(let d=0;d<c;d+=1)b.push(this.renderTrack(d+1,a[d],a[d+1]));return b.push(this.renderTrack(c+1,a[c],this.state.upperBound)),b},c.renderMarks=function(){let{marks:a}=this.props,b=this.props.max-this.props.min+1;return"boolean"==typeof a?a=Array.from({length:b}).map((a,b)=>b):"number"==typeof a&&(a=Array.from({length:b}).map((a,b)=>b).filter(b=>b%a==0)),a.map(parseFloat).sort((a,b)=>a-b).map(a=>{let b=this.calcOffset(a),c={key:a,className:this.props.markClassName,style:this.buildMarkStyle(b)};return this.props.renderMark(c)})},c.render=function(){let a=[],{value:b}=this.state,c=b.length;for(let d=0;d<c;d+=1)a[d]=this.calcOffset(b[d],d);let d=this.props.withTracks?this.renderTracks(a):null,f=this.renderThumbs(a),g=this.props.marks?this.renderMarks():null;return e.createElement("div",{ref:a=>{this.slider=a,this.resizeElementRef.current=a},style:{position:"relative"},className:this.props.className+(this.props.disabled?" disabled":""),onMouseDown:this.onSliderMouseDown,onClick:this.onSliderClick},d,f,g)},b}(e.Component);l.displayName="ReactSlider",l.defaultProps={min:0,max:100,step:1,pageFn:a=>10*a,minDistance:0,defaultValue:0,orientation:"horizontal",className:"slider",thumbClassName:"thumb",thumbActiveClassName:"active",trackClassName:"track",markClassName:"mark",withTracks:!0,pearling:!1,disabled:!1,snapDragDisabled:!1,invert:!1,marks:[],renderThumb:a=>e.createElement("div",a),renderTrack:a=>e.createElement("div",a),renderMark:a=>e.createElement("span",a)};var m=l},69433:(a,b,c)=>{a.exports=c(5566)("toUpperCase")},69619:a=>{a.exports=function(a){return"number"==typeof a&&a>-1&&a%1==0&&a<=0x1fffffffffffff}},69691:(a,b,c)=>{var d=c(41157),e=c(99114),f=c(22);a.exports=function(a,b){var c={};return b=f(b,3),e(a,function(a,e,f){d(c,e,b(a,e,f))}),c}},70151:(a,b,c)=>{var d=c(85718);a.exports=function(){return d.Date.now()}},70222:(a,b,c)=>{var d=c(79474),e=Object.prototype,f=e.hasOwnProperty,g=e.toString,h=d?d.toStringTag:void 0;a.exports=function(a){var b=f.call(a,h),c=a[h];try{a[h]=void 0;var d=!0}catch(a){}var e=g.call(a);return d&&(b?a[h]=c:delete a[h]),e}},71524:(a,b,c)=>{"use strict";c.d(b,{J:()=>o,M:()=>q});var d=c(43210),e=c.n(d),f=c(49384),g=c(93492),h=c(54186);function i(a){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function j(){return(j=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function k(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function l(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function m(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?l(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=i(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=i(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==i(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):l(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var n=function(a,b,c,d,e){var f,g=Math.min(Math.abs(c)/2,Math.abs(d)/2),h=d>=0?1:-1,i=c>=0?1:-1,j=+(d>=0&&c>=0||d<0&&c<0);if(g>0&&e instanceof Array){for(var k=[0,0,0,0],l=0;l<4;l++)k[l]=e[l]>g?g:e[l];f="M".concat(a,",").concat(b+h*k[0]),k[0]>0&&(f+="A ".concat(k[0],",").concat(k[0],",0,0,").concat(j,",").concat(a+i*k[0],",").concat(b)),f+="L ".concat(a+c-i*k[1],",").concat(b),k[1]>0&&(f+="A ".concat(k[1],",").concat(k[1],",0,0,").concat(j,",\n        ").concat(a+c,",").concat(b+h*k[1])),f+="L ".concat(a+c,",").concat(b+d-h*k[2]),k[2]>0&&(f+="A ".concat(k[2],",").concat(k[2],",0,0,").concat(j,",\n        ").concat(a+c-i*k[2],",").concat(b+d)),f+="L ".concat(a+i*k[3],",").concat(b+d),k[3]>0&&(f+="A ".concat(k[3],",").concat(k[3],",0,0,").concat(j,",\n        ").concat(a,",").concat(b+d-h*k[3])),f+="Z"}else if(g>0&&e===+e&&e>0){var m=Math.min(g,e);f="M ".concat(a,",").concat(b+h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+i*m,",").concat(b,"\n            L ").concat(a+c-i*m,",").concat(b,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c,",").concat(b+h*m,"\n            L ").concat(a+c,",").concat(b+d-h*m,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a+c-i*m,",").concat(b+d,"\n            L ").concat(a+i*m,",").concat(b+d,"\n            A ").concat(m,",").concat(m,",0,0,").concat(j,",").concat(a,",").concat(b+d-h*m," Z")}else f="M ".concat(a,",").concat(b," h ").concat(c," v ").concat(d," h ").concat(-c," Z");return f},o=function(a,b){if(!a||!b)return!1;var c=a.x,d=a.y,e=b.x,f=b.y,g=b.width,h=b.height;if(Math.abs(g)>0&&Math.abs(h)>0){var i=Math.min(e,e+g),j=Math.max(e,e+g),k=Math.min(f,f+h),l=Math.max(f,f+h);return c>=i&&c<=j&&d>=k&&d<=l}return!1},p={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},q=function(a){var b,c=m(m({},p),a),i=(0,d.useRef)(),l=function(a){if(Array.isArray(a))return a}(b=(0,d.useState)(-1))||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(b,2)||function(a,b){if(a){if("string"==typeof a)return k(a,2);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return k(a,b)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=l[0],q=l[1];(0,d.useEffect)(function(){if(i.current&&i.current.getTotalLength)try{var a=i.current.getTotalLength();a&&q(a)}catch(a){}},[]);var r=c.x,s=c.y,t=c.width,u=c.height,v=c.radius,w=c.className,x=c.animationEasing,y=c.animationDuration,z=c.animationBegin,A=c.isAnimationActive,B=c.isUpdateAnimationActive;if(r!==+r||s!==+s||t!==+t||u!==+u||0===t||0===u)return null;var C=(0,f.A)("recharts-rectangle",w);return B?e().createElement(g.Ay,{canBegin:o>0,from:{width:t,height:u,x:r,y:s},to:{width:t,height:u,x:r,y:s},duration:y,animationEasing:x,isActive:B},function(a){var b=a.width,d=a.height,f=a.x,k=a.y;return e().createElement(g.Ay,{canBegin:o>0,from:"0px ".concat(-1===o?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:z,duration:y,isActive:A,easing:x},e().createElement("path",j({},(0,h.J9)(c,!0),{className:C,d:n(f,k,b,d,v),ref:i})))}):e().createElement("path",j({},(0,h.J9)(c,!0),{className:C,d:n(r,s,t,u,v)}))}},71960:a=>{a.exports=function(a,b,c){for(var d=-1,e=null==a?0:a.length;++d<e;)if(c(b,a[d]))return!0;return!1}},71967:(a,b,c)=>{var d=c(15871);a.exports=function(a,b){return d(a,b)}},74610:a=>{a.exports=function(a,b,c){for(var d=c-1,e=a.length;++d<e;)if(a[d]===b)return d;return -1}},75254:(a,b,c)=>{var d=c(78418),e=c(93311),f=c(41132);a.exports=function(a){var b=e(a);return 1==b.length&&b[0][2]?f(b[0][0],b[0][1]):function(c){return c===a||d(c,a,b)}}},75411:a=>{a.exports=function(a){var b=this.__data__,c=b.delete(a);return this.size=b.size,c}},75847:(a,b,c)=>{var d=c(67554);a.exports=function(a,b){var c;return d(a,function(a,d,e){return!(c=b(a,d,e))}),!!c}},77822:(a,b,c)=>{var d=c(93490);a.exports=function(a){return d(a)&&a!=+a}},77834:a=>{var b=Object.prototype;a.exports=function(a){var c=a&&a.constructor;return a===("function"==typeof c&&c.prototype||b)}},78418:(a,b,c)=>{var d=c(67200),e=c(15871);a.exports=function(a,b,c,f){var g=c.length,h=g,i=!f;if(null==a)return!h;for(a=Object(a);g--;){var j=c[g];if(i&&j[2]?j[1]!==a[j[0]]:!(j[0]in a))return!1}for(;++g<h;){var k=(j=c[g])[0],l=a[k],m=j[1];if(i&&j[2]){if(void 0===l&&!(k in a))return!1}else{var n=new d;if(f)var o=f(l,m,k,a,b,n);if(!(void 0===o?e(m,l,3,f,n):o))return!1}}return!0}},79474:(a,b,c)=>{a.exports=c(85718).Symbol},79740:(a,b,c)=>{"use strict";c.d(b,{s:()=>I});var d=c(43210),e=c.n(d),f=c(5231),g=c.n(f),h=c(49384),i=c(10521),j=c(21080),k=c(10919),l=c(4057);function m(a){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function n(){return(n=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function o(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function p(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(p=function(){return!!a})()}function q(a){return(q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function r(a,b){return(r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function s(a,b,c){return(b=t(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function t(a){var b=function(a,b){if("object"!=m(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=m(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==m(b)?b:b+""}var u=function(a){var b;function c(){var a,b;if(!(this instanceof c))throw TypeError("Cannot call a class as a function");return a=c,b=arguments,a=q(a),function(a,b){if(b&&("object"===m(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,p()?Reflect.construct(a,b||[],q(this).constructor):a.apply(this,b))}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return c.prototype=Object.create(a&&a.prototype,{constructor:{value:c,writable:!0,configurable:!0}}),Object.defineProperty(c,"prototype",{writable:!1}),a&&r(c,a),b=[{key:"renderIcon",value:function(a){var b=this.props.inactiveColor,c=32/6,d=32/3,f=a.inactive?b:a.color;if("plainline"===a.type)return e().createElement("line",{strokeWidth:4,fill:"none",stroke:f,strokeDasharray:a.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===a.type)return e().createElement("path",{strokeWidth:4,fill:"none",stroke:f,d:"M0,".concat(16,"h").concat(d,"\n            A").concat(c,",").concat(c,",0,1,1,").concat(2*d,",").concat(16,"\n            H").concat(32,"M").concat(2*d,",").concat(16,"\n            A").concat(c,",").concat(c,",0,1,1,").concat(d,",").concat(16),className:"recharts-legend-icon"});if("rect"===a.type)return e().createElement("path",{stroke:"none",fill:f,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(e().isValidElement(a.legendIcon)){var g=function(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?o(Object(c),!0).forEach(function(b){s(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):o(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}({},a);return delete g.legendIcon,e().cloneElement(a.legendIcon,g)}return e().createElement(k.i,{fill:f,cx:16,cy:16,size:32,sizeType:"diameter",type:a.type})}},{key:"renderItems",value:function(){var a=this,b=this.props,c=b.payload,d=b.iconSize,f=b.layout,k=b.formatter,m=b.inactiveColor,o={x:0,y:0,width:32,height:32},p={display:"horizontal"===f?"inline-block":"block",marginRight:10},q={display:"inline-block",verticalAlign:"middle",marginRight:4};return c.map(function(b,c){var f=b.formatter||k,r=(0,h.A)(s(s({"recharts-legend-item":!0},"legend-item-".concat(c),!0),"inactive",b.inactive));if("none"===b.type)return null;var t=g()(b.value)?null:b.value;(0,i.R)(!g()(b.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var u=b.inactive?m:b.color;return e().createElement("li",n({className:r,style:p,key:"legend-item-".concat(c)},(0,l.XC)(a.props,b,c)),e().createElement(j.u,{width:d,height:d,viewBox:o,style:q},a.renderIcon(b)),e().createElement("span",{className:"recharts-legend-item-text",style:{color:u}},f?f(t,b,c):t))})}},{key:"render",value:function(){var a=this.props,b=a.payload,c=a.layout,d=a.align;return b&&b.length?e().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===c?d:"left"}},this.renderItems()):null}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,t(d.key),d)}}(c.prototype,b),Object.defineProperty(c,"prototype",{writable:!1}),c}(d.PureComponent);s(u,"displayName","Legend"),s(u,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var v=c(45370),w=c(45796);function x(a){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var y=["ref"];function z(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function A(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?z(Object(c),!0).forEach(function(b){F(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):z(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function B(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,G(d.key),d)}}function C(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return(C=function(){return!!a})()}function D(a){return(D=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function E(a,b){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function F(a,b,c){return(b=G(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function G(a){var b=function(a,b){if("object"!=x(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=x(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==x(b)?b:b+""}function H(a){return a.value}var I=function(a){var b,c;function d(){var a,b,c;if(!(this instanceof d))throw TypeError("Cannot call a class as a function");for(var e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];return b=d,c=[].concat(f),b=D(b),F(a=function(a,b){if(b&&("object"===x(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,C()?Reflect.construct(b,c||[],D(this).constructor):b.apply(this,c)),"lastBoundingBox",{width:-1,height:-1}),a}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return d.prototype=Object.create(a&&a.prototype,{constructor:{value:d,writable:!0,configurable:!0}}),Object.defineProperty(d,"prototype",{writable:!1}),a&&E(d,a),b=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var a=this.wrapperNode.getBoundingClientRect();return a.height=this.wrapperNode.offsetHeight,a.width=this.wrapperNode.offsetWidth,a}return null}},{key:"updateBBox",value:function(){var a=this.props.onBBoxUpdate,b=this.getBBox();b?(Math.abs(b.width-this.lastBoundingBox.width)>1||Math.abs(b.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=b.width,this.lastBoundingBox.height=b.height,a&&a(b)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,a&&a(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?A({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(a){var b,c,d=this.props,e=d.layout,f=d.align,g=d.verticalAlign,h=d.margin,i=d.chartWidth,j=d.chartHeight;return a&&(void 0!==a.left&&null!==a.left||void 0!==a.right&&null!==a.right)||(b="center"===f&&"vertical"===e?{left:((i||0)-this.getBBoxSnapshot().width)/2}:"right"===f?{right:h&&h.right||0}:{left:h&&h.left||0}),a&&(void 0!==a.top&&null!==a.top||void 0!==a.bottom&&null!==a.bottom)||(c="middle"===g?{top:((j||0)-this.getBBoxSnapshot().height)/2}:"bottom"===g?{bottom:h&&h.bottom||0}:{top:h&&h.top||0}),A(A({},b),c)}},{key:"render",value:function(){var a=this,b=this.props,c=b.content,d=b.width,f=b.height,g=b.wrapperStyle,h=b.payloadUniqBy,i=b.payload,j=A(A({position:"absolute",width:d||"auto",height:f||"auto"},this.getDefaultPosition(g)),g);return e().createElement("div",{className:"recharts-legend-wrapper",style:j,ref:function(b){a.wrapperNode=b}},function(a,b){if(e().isValidElement(a))return e().cloneElement(a,b);if("function"==typeof a)return e().createElement(a,b);b.ref;var c=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(b,y);return e().createElement(u,c)}(c,A(A({},this.props),{},{payload:(0,w.s)(i,h,H)})))}}],c=[{key:"getWithHeight",value:function(a,b){var c=A(A({},this.defaultProps),a.props).layout;return"vertical"===c&&(0,v.Et)(a.props.height)?{height:a.props.height}:"horizontal"===c?{width:a.props.width||b}:null}}],b&&B(d.prototype,b),c&&B(d,c),Object.defineProperty(d,"prototype",{writable:!1}),d}(d.PureComponent);F(I,"displayName","Legend"),F(I,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},80195:(a,b,c)=>{var d=c(79474),e=c(21367),f=c(40542),g=c(49227),h=1/0,i=d?d.prototype:void 0,j=i?i.toString:void 0;a.exports=function a(b){if("string"==typeof b)return b;if(f(b))return e(b,a)+"";if(g(b))return j?j.call(b):"";var c=b+"";return"0"==c&&1/b==-h?"-0":c}},80329:(a,b,c)=>{a=c.nmd(a);var d=c(85718),e=c(1944),f=b&&!b.nodeType&&b,g=f&&a&&!a.nodeType&&a,h=g&&g.exports===f?d.Buffer:void 0,i=h?h.isBuffer:void 0;a.exports=i||e},80458:(a,b,c)=>{var d=c(29395),e=c(69619),f=c(27467),g={};g["[object Float32Array]"]=g["[object Float64Array]"]=g["[object Int8Array]"]=g["[object Int16Array]"]=g["[object Int32Array]"]=g["[object Uint8Array]"]=g["[object Uint8ClampedArray]"]=g["[object Uint16Array]"]=g["[object Uint32Array]"]=!0,g["[object Arguments]"]=g["[object Array]"]=g["[object ArrayBuffer]"]=g["[object Boolean]"]=g["[object DataView]"]=g["[object Date]"]=g["[object Error]"]=g["[object Function]"]=g["[object Map]"]=g["[object Number]"]=g["[object Object]"]=g["[object RegExp]"]=g["[object Set]"]=g["[object String]"]=g["[object WeakMap]"]=!1,a.exports=function(a){return f(a)&&e(a.length)&&!!g[d(a)]}},80704:(a,b,c)=>{var d=c(96678);a.exports=function(a,b){return!!(null==a?0:a.length)&&d(a,b,0)>-1}},81488:a=>{a.exports=function(a,b){return null!=a&&b in Object(a)}},81957:(a,b,c)=>{var d=c(49227);a.exports=function(a,b){if(a!==b){var c=void 0!==a,e=null===a,f=a==a,g=d(a),h=void 0!==b,i=null===b,j=b==b,k=d(b);if(!i&&!k&&!g&&a>b||g&&h&&j&&!i&&!k||e&&h&&j||!c&&j||!f)return 1;if(!e&&!g&&!k&&a<b||k&&c&&f&&!e&&!g||i&&c&&f||!h&&f||!j)return -1}return 0}},82038:(a,b,c)=>{var d=c(34821),e=c(35163),f=c(40542),g=c(80329),h=c(38428),i=c(10090),j=Object.prototype.hasOwnProperty;a.exports=function(a,b){var c=f(a),k=!c&&e(a),l=!c&&!k&&g(a),m=!c&&!k&&!l&&i(a),n=c||k||l||m,o=n?d(a.length,String):[],p=o.length;for(var q in a)(b||j.call(a,q))&&!(n&&("length"==q||l&&("offset"==q||"parent"==q)||m&&("buffer"==q||"byteLength"==q||"byteOffset"==q)||h(q,p)))&&o.push(q);return o}},83923:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Bath",[["path",{d:"M9 6 6.5 3.5a1.5 1.5 0 0 0-1-.5C4.683 3 4 3.683 4 4.5V17a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5",key:"1r8yf5"}],["line",{x1:"10",x2:"8",y1:"5",y2:"7",key:"h5g8z4"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"7",x2:"7",y1:"19",y2:"21",key:"16jp00"}],["line",{x1:"17",x2:"17",y1:"19",y2:"21",key:"1pxrnk"}]])},84261:a=>{a.exports=function(a){var b=this.has(a)&&delete this.__data__[a];return this.size-=!!b,b}},84482:(a,b,c)=>{var d=c(28977);a.exports=function(a){var b=d(a),c=b%1;return b==b?c?b-c:b:0}},84713:a=>{var b=Object.prototype.toString;a.exports=function(a){return b.call(a)}},85244:a=>{a.exports=function(a,b,c){switch(c.length){case 0:return a.call(b);case 1:return a.call(b,c[0]);case 2:return a.call(b,c[0],c[1]);case 3:return a.call(b,c[0],c[1],c[2])}return a.apply(b,c)}},85406:(a,b,c)=>{a.exports=c(85718)["__core-js_shared__"]},85450:(a,b,c)=>{var d=c(79474),e=c(35163),f=c(40542),g=d?d.isConcatSpreadable:void 0;a.exports=function(a){return f(a)||e(a)||!!(g&&a&&a[g])}},85718:(a,b,c)=>{var d=c(10663),e="object"==typeof self&&self&&self.Object===Object&&self;a.exports=d||e||Function("return this")()},85745:(a,b,c)=>{var d=c(86451);a.exports=function(a){var b=d(a,function(a){return 500===c.size&&c.clear(),a}),c=b.cache;return b}},85938:(a,b,c)=>{var d=c(42205),e=c(17518),f=c(46229),g=c(7383);a.exports=f(function(a,b){if(null==a)return[];var c=b.length;return c>1&&g(a,b[0],b[1])?b=[]:c>2&&g(b[0],b[1],b[2])&&(b=[b[0]]),e(a,d(b,1),[])})},86451:(a,b,c)=>{var d=c(95746);function e(a,b){if("function"!=typeof a||null!=b&&"function"!=typeof b)throw TypeError("Expected a function");var c=function(){var d=arguments,e=b?b.apply(this,d):d[0],f=c.cache;if(f.has(e))return f.get(e);var g=a.apply(this,d);return c.cache=f.set(e,g)||f,g};return c.cache=new(e.Cache||d),c}e.Cache=d,a.exports=e},87270:a=>{a.exports=function(a,b){for(var c=-1,d=null==a?0:a.length;++c<d;)if(!b(a[c],c,a))return!1;return!0}},87321:(a,b,c)=>{var d=c(98798),e=c(7383),f=c(28977);a.exports=function(a){return function(b,c,g){return g&&"number"!=typeof g&&e(b,c,g)&&(c=g=void 0),b=f(b),void 0===c?(c=b,b=0):c=f(c),g=void 0===g?b<c?1:-1:f(g),d(b,c,g,a)}}},87506:(a,b,c)=>{var d=c(66837),e=c(84261),f=c(89492),g=c(90200),h=c(39672);function i(a){var b=-1,c=null==a?0:a.length;for(this.clear();++b<c;){var d=a[b];this.set(d[0],d[1])}}i.prototype.clear=d,i.prototype.delete=e,i.prototype.get=f,i.prototype.has=g,i.prototype.set=h,a.exports=i},87891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89167:(a,b,c)=>{a.exports=c(41547)(c(85718),"DataView")},89185:a=>{a.exports=function(a){return this.__data__.set(a,"__lodash_hash_undefined__"),this}},89413:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]])},89492:(a,b,c)=>{var d=c(58141),e=Object.prototype.hasOwnProperty;a.exports=function(a){var b=this.__data__;if(d){var c=b[a];return"__lodash_hash_undefined__"===c?void 0:c}return e.call(b,a)?b[a]:void 0}},89605:(a,b,c)=>{a.exports=c(65662)(Object.keys,Object)},89624:a=>{a.exports=function(a){return function(b){return a(b)}}},89653:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});function d(a,b){if(!a)throw Error("Invariant failed")}},90200:(a,b,c)=>{var d=c(58141),e=Object.prototype.hasOwnProperty;a.exports=function(a){var b=this.__data__;return d?void 0!==b[a]:e.call(b,a)}},90453:(a,b,c)=>{var d=c(2984),e=c(99180),f=c(48169);a.exports=function(a){return a&&a.length?d(a,f,e):void 0}},90851:a=>{a.exports=function(a,b){return null==a?void 0:a[b]}},91290:a=>{a.exports=function(a,b,c,d){for(var e=a.length,f=c+(d?1:-1);d?f--:++f<e;)if(b(a[f],f,a))return f;return -1}},91827:(a,b,c)=>{"use strict";c.d(b,{y:()=>ad});var d=c(43210),e=c.n(d),f=c(49384),g=c(93492),h=c(71967),i=c.n(h),j=c(37456),k=c.n(j),l=c(98986),m=c(60927),n=c(25679),o=c(55048),p=c.n(o),q=c(5231),r=c.n(q),s=c(5359),t=c.n(s),u=c(97633),v=c(54186),w=c(30087);function x(a){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var y=["valueAccessor"],z=["data","dataKey","clockWise","id","textBreakAll"];function A(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function B(){return(B=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function C(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function D(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?C(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=x(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=x(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==x(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):C(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function E(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}var F=function(a){return Array.isArray(a.value)?t()(a.value):a.value};function G(a){var b=a.valueAccessor,c=void 0===b?F:b,d=E(a,y),f=d.data,g=d.dataKey,h=d.clockWise,i=d.id,j=d.textBreakAll,m=E(d,z);return f&&f.length?e().createElement(l.W,{className:"recharts-label-list"},f.map(function(a,b){var d=k()(g)?c(a,b):(0,w.kr)(a&&a.payload,g),f=k()(i)?{}:{id:"".concat(i,"-").concat(b)};return e().createElement(u.J,B({},(0,v.J9)(a,!0),m,f,{parentViewBox:a.parentViewBox,value:d,textBreakAll:j,viewBox:u.J.parseViewBox(k()(h)?a:D(D({},a),{},{clockWise:h})),key:"label-".concat(b),index:b}))})):null}G.displayName="LabelList",G.renderCallByParent=function(a,b){var c,f=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&f&&!a.label)return null;var g=a.children,h=(0,v.aS)(g,G).map(function(a,c){return(0,d.cloneElement)(a,{data:b,key:"labelList-".concat(c)})});return f?[(c=a.label,!c?null:!0===c?e().createElement(G,{key:"labelList-implicit",data:b}):e().isValidElement(c)||r()(c)?e().createElement(G,{key:"labelList-implicit",data:b,content:c}):p()(c)?e().createElement(G,B({data:b},c,{key:"labelList-implicit"})):null)].concat(function(a){if(Array.isArray(a))return A(a)}(h)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(h)||function(a,b){if(a){if("string"==typeof a)return A(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return A(a,b)}}(h)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):h};var H=c(45370),I=c(20237),J=c(4057),K=c(89653),L=c(67629),M=["x","y"];function N(a){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function O(){return(O=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function P(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function Q(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?P(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=N(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=N(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==N(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):P(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function R(a,b){var c=a.x,d=a.y,e=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,M),f=parseInt("".concat(c),10),g=parseInt("".concat(d),10),h=parseInt("".concat(b.height||e.height),10),i=parseInt("".concat(b.width||e.width),10);return Q(Q(Q(Q(Q({},b),e),f?{x:f}:{}),g?{y:g}:{}),{},{height:h,width:i,name:b.name,radius:b.radius})}function S(a){return e().createElement(L.yp,O({shapeType:"rectangle",propTransformer:R,activeClassName:"recharts-active-bar"},a))}var T=function(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(c,d){if("number"==typeof a)return a;var e="number"==typeof c;return e?a(c,d):(e||(0,K.A)(!1),b)}},U=["value","background"];function V(a){return(V="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function W(){return(W=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}function X(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function Y(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?X(Object(c),!0).forEach(function(b){ab(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):X(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function Z(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,ac(d.key),d)}}function $(){try{var a=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(a){}return($=function(){return!!a})()}function _(a){return(_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}function aa(a,b){return(aa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function ab(a,b,c){return(b=ac(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function ac(a){var b=function(a,b){if("object"!=V(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=V(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==V(b)?b:b+""}var ad=function(a){var b,c;function d(){var a,b,c;if(!(this instanceof d))throw TypeError("Cannot call a class as a function");for(var e=arguments.length,f=Array(e),g=0;g<e;g++)f[g]=arguments[g];return b=d,c=[].concat(f),b=_(b),ab(a=function(a,b){if(b&&("object"===V(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");var c=a;if(void 0===c)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return c}(this,$()?Reflect.construct(b,c||[],_(this).constructor):b.apply(this,c)),"state",{isAnimationFinished:!1}),ab(a,"id",(0,H.NF)("recharts-bar-")),ab(a,"handleAnimationEnd",function(){var b=a.props.onAnimationEnd;a.setState({isAnimationFinished:!0}),b&&b()}),ab(a,"handleAnimationStart",function(){var b=a.props.onAnimationStart;a.setState({isAnimationFinished:!1}),b&&b()}),a}if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");return d.prototype=Object.create(a&&a.prototype,{constructor:{value:d,writable:!0,configurable:!0}}),Object.defineProperty(d,"prototype",{writable:!1}),a&&aa(d,a),b=[{key:"renderRectanglesStatically",value:function(a){var b=this,c=this.props,d=c.shape,f=c.dataKey,g=c.activeIndex,h=c.activeBar,i=(0,v.J9)(this.props,!1);return a&&a.map(function(a,c){var j=c===g,k=Y(Y(Y({},i),a),{},{isActive:j,option:j?h:d,index:c,dataKey:f,onAnimationStart:b.handleAnimationStart,onAnimationEnd:b.handleAnimationEnd});return e().createElement(l.W,W({className:"recharts-bar-rectangle"},(0,J.XC)(b.props,a,c),{key:"rectangle-".concat(null==a?void 0:a.x,"-").concat(null==a?void 0:a.y,"-").concat(null==a?void 0:a.value)}),e().createElement(S,k))})}},{key:"renderRectanglesWithAnimation",value:function(){var a=this,b=this.props,c=b.data,d=b.layout,f=b.isAnimationActive,h=b.animationBegin,i=b.animationDuration,j=b.animationEasing,k=b.animationId,m=this.state.prevData;return e().createElement(g.Ay,{begin:h,duration:i,isActive:f,easing:j,from:{t:0},to:{t:1},key:"bar-".concat(k),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(b){var f=b.t,g=c.map(function(a,b){var c=m&&m[b];if(c){var e=(0,H.Dj)(c.x,a.x),g=(0,H.Dj)(c.y,a.y),h=(0,H.Dj)(c.width,a.width),i=(0,H.Dj)(c.height,a.height);return Y(Y({},a),{},{x:e(f),y:g(f),width:h(f),height:i(f)})}if("horizontal"===d){var j=(0,H.Dj)(0,a.height)(f);return Y(Y({},a),{},{y:a.y+a.height-j,height:j})}var k=(0,H.Dj)(0,a.width)(f);return Y(Y({},a),{},{width:k})});return e().createElement(l.W,null,a.renderRectanglesStatically(g))})}},{key:"renderRectangles",value:function(){var a=this.props,b=a.data,c=a.isAnimationActive,d=this.state.prevData;return c&&b&&b.length&&(!d||!i()(d,b))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(b)}},{key:"renderBackground",value:function(){var a=this,b=this.props,c=b.data,d=b.dataKey,f=b.activeIndex,g=(0,v.J9)(this.props.background,!1);return c.map(function(b,c){b.value;var h=b.background,i=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(b,U);if(!h)return null;var j=Y(Y(Y(Y(Y({},i),{},{fill:"#eee"},h),g),(0,J.XC)(a.props,b,c)),{},{onAnimationStart:a.handleAnimationStart,onAnimationEnd:a.handleAnimationEnd,dataKey:d,index:c,className:"recharts-bar-background-rectangle"});return e().createElement(S,W({key:"background-bar-".concat(c),option:a.props.background,isActive:c===f},j))})}},{key:"renderErrorBar",value:function(a,b){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var c=this.props,d=c.data,f=c.xAxis,g=c.yAxis,h=c.layout,i=c.children,j=(0,v.aS)(i,m.u);if(!j)return null;var k="vertical"===h?d[0].height/2:d[0].width/2,n=function(a,b){var c=Array.isArray(a.value)?a.value[1]:a.value;return{x:a.x,y:a.y,value:c,errorVal:(0,w.kr)(a,b)}};return e().createElement(l.W,{clipPath:a?"url(#clipPath-".concat(b,")"):null},j.map(function(a){return e().cloneElement(a,{key:"error-bar-".concat(b,"-").concat(a.props.dataKey),data:d,xAxis:f,yAxis:g,layout:h,offset:k,dataPointFormatter:n})}))}},{key:"render",value:function(){var a=this.props,b=a.hide,c=a.data,d=a.className,g=a.xAxis,h=a.yAxis,i=a.left,j=a.top,m=a.width,n=a.height,o=a.isAnimationActive,p=a.background,q=a.id;if(b||!c||!c.length)return null;var r=this.state.isAnimationFinished,s=(0,f.A)("recharts-bar",d),t=g&&g.allowDataOverflow,u=h&&h.allowDataOverflow,v=t||u,w=k()(q)?this.id:q;return e().createElement(l.W,{className:s},t||u?e().createElement("defs",null,e().createElement("clipPath",{id:"clipPath-".concat(w)},e().createElement("rect",{x:t?i:i-m/2,y:u?j:j-n/2,width:t?m:2*m,height:u?n:2*n}))):null,e().createElement(l.W,{className:"recharts-bar-rectangles",clipPath:v?"url(#clipPath-".concat(w,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(v,w),(!o||r)&&G.renderCallByParent(this.props,c))}}],c=[{key:"getDerivedStateFromProps",value:function(a,b){return a.animationId!==b.prevAnimationId?{prevAnimationId:a.animationId,curData:a.data,prevData:b.curData}:a.data!==b.curData?{curData:a.data}:null}}],b&&Z(d.prototype,b),c&&Z(d,c),Object.defineProperty(d,"prototype",{writable:!1}),d}(d.PureComponent);ab(ad,"displayName","Bar"),ab(ad,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!I.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),ab(ad,"getComposedData",function(a){var b=a.props,c=a.item,d=a.barPosition,e=a.bandSize,f=a.xAxis,g=a.yAxis,h=a.xAxisTicks,i=a.yAxisTicks,j=a.stackedData,k=a.dataStartIndex,l=a.displayedData,m=a.offset,o=(0,w.xi)(d,c);if(!o)return null;var p=b.layout,q=c.type.defaultProps,r=void 0!==q?Y(Y({},q),c.props):c.props,s=r.dataKey,t=r.children,u=r.minPointSize,x="horizontal"===p?g:f,y=j?x.scale.domain():null,z=(0,w.DW)({numericAxis:x}),A=(0,v.aS)(t,n.f),B=l.map(function(a,b){j?l=(0,w._f)(j[k+b],y):Array.isArray(l=(0,w.kr)(a,s))||(l=[z,l]);var d=T(u,ad.defaultProps.minPointSize)(l[1],b);if("horizontal"===p){var l,m,n,q,r,t,v,x=[g.scale(l[0]),g.scale(l[1])],B=x[0],C=x[1];m=(0,w.y2)({axis:f,ticks:h,bandSize:e,offset:o.offset,entry:a,index:b}),n=null!=(v=null!=C?C:B)?v:void 0,q=o.size;var D=B-C;if(r=Number.isNaN(D)?0:D,t={x:m,y:g.y,width:q,height:g.height},Math.abs(d)>0&&Math.abs(r)<Math.abs(d)){var E=(0,H.sA)(r||d)*(Math.abs(d)-Math.abs(r));n-=E,r+=E}}else{var F=[f.scale(l[0]),f.scale(l[1])],G=F[0],I=F[1];if(m=G,n=(0,w.y2)({axis:g,ticks:i,bandSize:e,offset:o.offset,entry:a,index:b}),q=I-G,r=o.size,t={x:f.x,y:n,width:f.width,height:r},Math.abs(d)>0&&Math.abs(q)<Math.abs(d)){var J=(0,H.sA)(q||d)*(Math.abs(d)-Math.abs(q));q+=J}}return Y(Y(Y({},a),{},{x:m,y:n,width:q,height:r,value:j?l:l[1],payload:a,background:t},A&&A[b]&&A[b].props),{},{tooltipPayload:[(0,w.zb)(c,a)],tooltipPosition:{x:m+q/2,y:n+r/2}})});return Y({data:B,layout:p},m)})},91928:(a,b,c)=>{var d=c(41547);a.exports=function(){try{var a=d(Object,"defineProperty");return a({},"",{}),a}catch(a){}}()},92662:(a,b,c)=>{var d=c(46328),e=c(80704),f=c(71960),g=c(58276),h=c(95308),i=c(2408);a.exports=function(a,b,c){var j=-1,k=e,l=a.length,m=!0,n=[],o=n;if(c)m=!1,k=f;else if(l>=200){var p=b?null:h(a);if(p)return i(p);m=!1,k=g,o=new d}else o=b?[]:n;b:for(;++j<l;){var q=a[j],r=b?b(q):q;if(q=c||0!==q?q:0,m&&r==r){for(var s=o.length;s--;)if(o[s]===r)continue b;b&&o.push(r),n.push(q)}else k(o,r,c)||(o!==n&&o.push(r),n.push(q))}return n}},93311:(a,b,c)=>{var d=c(34883),e=c(7651);a.exports=function(a){for(var b=e(a),c=b.length;c--;){var f=b[c],g=a[f];b[c]=[f,g,d(g)]}return b}},93490:(a,b,c)=>{var d=c(29395),e=c(27467);a.exports=function(a){return"number"==typeof a||e(a)&&"[object Number]"==d(a)}},93492:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>az});var d=c(43210),e=c.n(d),f=c(87955),g=c.n(f),h=Object.getOwnPropertyNames,i=Object.getOwnPropertySymbols,j=Object.prototype.hasOwnProperty;function k(a,b){return function(c,d,e){return a(c,d,e)&&b(c,d,e)}}function l(a){return function(b,c,d){if(!b||!c||"object"!=typeof b||"object"!=typeof c)return a(b,c,d);var e=d.cache,f=e.get(b),g=e.get(c);if(f&&g)return f===c&&g===b;e.set(b,c),e.set(c,b);var h=a(b,c,d);return e.delete(b),e.delete(c),h}}function m(a){return h(a).concat(i(a))}var n=Object.hasOwn||function(a,b){return j.call(a,b)};function o(a,b){return a===b||!a&&!b&&a!=a&&b!=b}var p=Object.getOwnPropertyDescriptor,q=Object.keys;function r(a,b,c){var d=a.length;if(b.length!==d)return!1;for(;d-- >0;)if(!c.equals(a[d],b[d],d,d,a,b,c))return!1;return!0}function s(a,b){return o(a.getTime(),b.getTime())}function t(a,b){return a.name===b.name&&a.message===b.message&&a.cause===b.cause&&a.stack===b.stack}function u(a,b){return a===b}function v(a,b,c){var d,e,f=a.size;if(f!==b.size)return!1;if(!f)return!0;for(var g=Array(f),h=a.entries(),i=0;(d=h.next())&&!d.done;){for(var j=b.entries(),k=!1,l=0;(e=j.next())&&!e.done;){if(g[l]){l++;continue}var m=d.value,n=e.value;if(c.equals(m[0],n[0],i,l,a,b,c)&&c.equals(m[1],n[1],m[0],n[0],a,b,c)){k=g[l]=!0;break}l++}if(!k)return!1;i++}return!0}function w(a,b,c){var d=q(a),e=d.length;if(q(b).length!==e)return!1;for(;e-- >0;)if(!D(a,b,c,d[e]))return!1;return!0}function x(a,b,c){var d,e,f,g=m(a),h=g.length;if(m(b).length!==h)return!1;for(;h-- >0;)if(!D(a,b,c,d=g[h])||(e=p(a,d),f=p(b,d),(e||f)&&(!e||!f||e.configurable!==f.configurable||e.enumerable!==f.enumerable||e.writable!==f.writable)))return!1;return!0}function y(a,b){return o(a.valueOf(),b.valueOf())}function z(a,b){return a.source===b.source&&a.flags===b.flags}function A(a,b,c){var d,e,f=a.size;if(f!==b.size)return!1;if(!f)return!0;for(var g=Array(f),h=a.values();(d=h.next())&&!d.done;){for(var i=b.values(),j=!1,k=0;(e=i.next())&&!e.done;){if(!g[k]&&c.equals(d.value,e.value,d.value,e.value,a,b,c)){j=g[k]=!0;break}k++}if(!j)return!1}return!0}function B(a,b){var c=a.length;if(b.length!==c)return!1;for(;c-- >0;)if(a[c]!==b[c])return!1;return!0}function C(a,b){return a.hostname===b.hostname&&a.pathname===b.pathname&&a.protocol===b.protocol&&a.port===b.port&&a.hash===b.hash&&a.username===b.username&&a.password===b.password}function D(a,b,c,d){return("_owner"===d||"__o"===d||"__v"===d)&&(!!a.$$typeof||!!b.$$typeof)||n(b,d)&&c.equals(a[d],b[d],d,d,a,b,c)}var E=Array.isArray,F="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,G=Object.assign,H=Object.prototype.toString.call.bind(Object.prototype.toString),I=J();function J(a){void 0===a&&(a={});var b,c,d,e,f,g,h,i,j,m,n,p,q,D=a.circular,I=a.createInternalComparator,J=a.createState,K=a.strict,L=(c=(b=function(a){var b=a.circular,c=a.createCustomConfig,d=a.strict,e={areArraysEqual:d?x:r,areDatesEqual:s,areErrorsEqual:t,areFunctionsEqual:u,areMapsEqual:d?k(v,x):v,areNumbersEqual:o,areObjectsEqual:d?x:w,arePrimitiveWrappersEqual:y,areRegExpsEqual:z,areSetsEqual:d?k(A,x):A,areTypedArraysEqual:d?x:B,areUrlsEqual:C};if(c&&(e=G({},e,c(e))),b){var f=l(e.areArraysEqual),g=l(e.areMapsEqual),h=l(e.areObjectsEqual),i=l(e.areSetsEqual);e=G({},e,{areArraysEqual:f,areMapsEqual:g,areObjectsEqual:h,areSetsEqual:i})}return e}(a)).areArraysEqual,d=b.areDatesEqual,e=b.areErrorsEqual,f=b.areFunctionsEqual,g=b.areMapsEqual,h=b.areNumbersEqual,i=b.areObjectsEqual,j=b.arePrimitiveWrappersEqual,m=b.areRegExpsEqual,n=b.areSetsEqual,p=b.areTypedArraysEqual,q=b.areUrlsEqual,function(a,b,k){if(a===b)return!0;if(null==a||null==b)return!1;var l=typeof a;if(l!==typeof b)return!1;if("object"!==l)return"number"===l?h(a,b,k):"function"===l&&f(a,b,k);var o=a.constructor;if(o!==b.constructor)return!1;if(o===Object)return i(a,b,k);if(E(a))return c(a,b,k);if(null!=F&&F(a))return p(a,b,k);if(o===Date)return d(a,b,k);if(o===RegExp)return m(a,b,k);if(o===Map)return g(a,b,k);if(o===Set)return n(a,b,k);var r=H(a);return"[object Date]"===r?d(a,b,k):"[object RegExp]"===r?m(a,b,k):"[object Map]"===r?g(a,b,k):"[object Set]"===r?n(a,b,k):"[object Object]"===r?"function"!=typeof a.then&&"function"!=typeof b.then&&i(a,b,k):"[object URL]"===r?q(a,b,k):"[object Error]"===r?e(a,b,k):"[object Arguments]"===r?i(a,b,k):("[object Boolean]"===r||"[object Number]"===r||"[object String]"===r)&&j(a,b,k)}),M=I?I(L):function(a,b,c,d,e,f,g){return L(a,b,g)};return function(a){var b=a.circular,c=a.comparator,d=a.createState,e=a.equals,f=a.strict;if(d)return function(a,g){var h=d(),i=h.cache;return c(a,g,{cache:void 0===i?b?new WeakMap:void 0:i,equals:e,meta:h.meta,strict:f})};if(b)return function(a,b){return c(a,b,{cache:new WeakMap,equals:e,meta:void 0,strict:f})};var g={cache:void 0,equals:e,meta:void 0,strict:f};return function(a,b){return c(a,b,g)}}({circular:void 0!==D&&D,comparator:L,createState:J,equals:M,strict:void 0!==K&&K})}function K(a){var b=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,c=-1;requestAnimationFrame(function d(e){if(c<0&&(c=e),e-c>b)a(e),c=-1;else{var f;f=d,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(f)}})}function L(a){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function M(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function N(a){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function O(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function P(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?O(Object(c),!0).forEach(function(b){Q(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):O(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function Q(a,b,c){var d;return(d=function(a,b){if("object"!==N(a)||null===a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!==N(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"),(b="symbol"===N(d)?d:String(d))in a)?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}J({strict:!0}),J({circular:!0}),J({circular:!0,strict:!0}),J({createInternalComparator:function(){return o}}),J({strict:!0,createInternalComparator:function(){return o}}),J({circular:!0,createInternalComparator:function(){return o}}),J({circular:!0,createInternalComparator:function(){return o},strict:!0});var R=function(a){return a},S=function(a,b){return Object.keys(b).reduce(function(c,d){return P(P({},c),{},Q({},d,a(d,b[d])))},{})},T=function(a,b,c){return a.map(function(a){return"".concat(a.replace(/([A-Z])/g,function(a){return"-".concat(a.toLowerCase())})," ").concat(b,"ms ").concat(c)}).join(",")},U=function(a,b,c,d,e,f,g,h){};function V(a,b){if(a){if("string"==typeof a)return W(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return W(a,b)}}function W(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}var X=function(a,b){return[0,3*a,3*b-6*a,3*a-3*b+1]},Y=function(a,b){return a.map(function(a,c){return a*Math.pow(b,c)}).reduce(function(a,b){return a+b})},Z=function(a,b){return function(c){return Y(X(a,b),c)}},$=function(){for(var a,b,c=arguments.length,d=Array(c),e=0;e<c;e++)d[e]=arguments[e];var f=d[0],g=d[1],h=d[2],i=d[3];if(1===d.length)switch(d[0]){case"linear":f=0,g=0,h=1,i=1;break;case"ease":f=.25,g=.1,h=.25,i=1;break;case"ease-in":f=.42,g=0,h=1,i=1;break;case"ease-out":f=.42,g=0,h=.58,i=1;break;case"ease-in-out":f=0,g=0,h=.58,i=1;break;default:var j=d[0].split("(");if("cubic-bezier"===j[0]&&4===j[1].split(")")[0].split(",").length){var k,l=function(a){if(Array.isArray(a))return a}(k=j[1].split(")")[0].split(",").map(function(a){return parseFloat(a)}))||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(k,4)||V(k,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=l[0],g=l[1],h=l[2],i=l[3]}else U(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",d)}U([f,h,g,i].every(function(a){return"number"==typeof a&&a>=0&&a<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",d);var m=Z(f,h),n=Z(g,i),o=(a=f,b=h,function(c){var d;return Y([].concat(function(a){if(Array.isArray(a))return W(a)}(d=X(a,b).map(function(a,b){return a*b}).slice(1))||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(d)||V(d)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),c)}),p=function(a){for(var b=a>1?1:a,c=b,d=0;d<8;++d){var e,f=m(c)-b,g=o(c);if(1e-4>Math.abs(f-b)||g<1e-4)break;c=(e=c-f/g)>1?1:e<0?0:e}return n(c)};return p.isStepper=!1,p},_=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},b=a.stiff,c=void 0===b?100:b,d=a.damping,e=void 0===d?8:d,f=a.dt,g=void 0===f?17:f,h=function(a,b,d){var f=d+(-(a-b)*c-d*e)*g/1e3,h=d*g/1e3+a;return 1e-4>Math.abs(h-b)&&1e-4>Math.abs(f)?[b,0]:[h,f]};return h.isStepper=!0,h.dt=g,h},aa=function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];var d=b[0];if("string"==typeof d)switch(d){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return $(d);case"spring":return _();default:if("cubic-bezier"===d.split("(")[0])return $(d);U(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",b)}return"function"==typeof d?d:(U(!1,"[configEasing]: first argument type should be function or string, instead received %s",b),null)};function ab(a){return(ab="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function ac(a){return function(a){if(Array.isArray(a))return ah(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||ag(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ad(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ae(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?ad(Object(c),!0).forEach(function(b){af(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):ad(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function af(a,b,c){var d;return(d=function(a,b){if("object"!==ab(a)||null===a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!==ab(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(b,"string"),(b="symbol"===ab(d)?d:String(d))in a)?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function ag(a,b){if(a){if("string"==typeof a)return ah(a,b);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return ah(a,b)}}function ah(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}var ai=function(a,b,c){return a+(b-a)*c},aj=function(a){return a.from!==a.to},ak=function a(b,c,d){var e=S(function(a,c){if(aj(c)){var d,e=function(a){if(Array.isArray(a))return a}(d=b(c.from,c.to,c.velocity))||function(a,b){var c=null==a?null:"undefined"!=typeof Symbol&&a[Symbol.iterator]||a["@@iterator"];if(null!=c){var d,e,f,g,h=[],i=!0,j=!1;try{f=(c=c.call(a)).next,!1;for(;!(i=(d=f.call(c)).done)&&(h.push(d.value),h.length!==b);i=!0);}catch(a){j=!0,e=a}finally{try{if(!i&&null!=c.return&&(g=c.return(),Object(g)!==g))return}finally{if(j)throw e}}return h}}(d,2)||ag(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),f=e[0],g=e[1];return ae(ae({},c),{},{from:f,velocity:g})}return c},c);return d<1?S(function(a,b){return aj(b)?ae(ae({},b),{},{velocity:ai(b.velocity,e[a].velocity,d),from:ai(b.from,e[a].from,d)}):b},c):a(b,e,d-1)};let al=function(a,b,c,d,e){var f,g,h=[Object.keys(a),Object.keys(b)].reduce(function(a,b){return a.filter(function(a){return b.includes(a)})}),i=h.reduce(function(c,d){return ae(ae({},c),{},af({},d,[a[d],b[d]]))},{}),j=h.reduce(function(c,d){return ae(ae({},c),{},af({},d,{from:a[d],velocity:0,to:b[d]}))},{}),k=-1,l=function(){return null};return l=c.isStepper?function(d){f||(f=d);var g=(d-f)/c.dt;j=ak(c,j,g),e(ae(ae(ae({},a),b),S(function(a,b){return b.from},j))),f=d,Object.values(j).filter(aj).length&&(k=requestAnimationFrame(l))}:function(f){g||(g=f);var h=(f-g)/d,j=S(function(a,b){return ai.apply(void 0,ac(b).concat([c(h)]))},i);if(e(ae(ae(ae({},a),b),j)),h<1)k=requestAnimationFrame(l);else{var m=S(function(a,b){return ai.apply(void 0,ac(b).concat([c(1)]))},i);e(ae(ae(ae({},a),b),m))}},function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(k)}}};function am(a){return(am="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var an=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function ao(a){return function(a){if(Array.isArray(a))return ap(a)}(a)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(a)||function(a,b){if(a){if("string"==typeof a)return ap(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return ap(a,b)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ap(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function aq(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function ar(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?aq(Object(c),!0).forEach(function(b){as(a,b,c[b])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):aq(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function as(a,b,c){return(b=at(b))in a?Object.defineProperty(a,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[b]=c,a}function at(a){var b=function(a,b){if("object"!==am(a)||null===a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!==am(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"===am(b)?b:String(b)}function au(a,b){return(au=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(a,b){return a.__proto__=b,a})(a,b)}function av(a,b){if(b&&("object"===am(b)||"function"==typeof b))return b;if(void 0!==b)throw TypeError("Derived constructors may only return object or undefined");return aw(a)}function aw(a){if(void 0===a)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function ax(a){return(ax=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(a){return a.__proto__||Object.getPrototypeOf(a)})(a)}var ay=function(a){if("function"!=typeof a&&null!==a)throw TypeError("Super expression must either be null or a function");g.prototype=Object.create(a&&a.prototype,{constructor:{value:g,writable:!0,configurable:!0}}),Object.defineProperty(g,"prototype",{writable:!1}),a&&au(g,a);var b,c,f=(b=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}(),function(){var a,c=ax(g);return a=b?Reflect.construct(c,arguments,ax(this).constructor):c.apply(this,arguments),av(this,a)});function g(a,b){if(!(this instanceof g))throw TypeError("Cannot call a class as a function");var c=f.call(this,a,b),d=c.props,e=d.isActive,h=d.attributeName,i=d.from,j=d.to,k=d.steps,l=d.children,m=d.duration;if(c.handleStyleChange=c.handleStyleChange.bind(aw(c)),c.changeStyle=c.changeStyle.bind(aw(c)),!e||m<=0)return c.state={style:{}},"function"==typeof l&&(c.state={style:j}),av(c);if(k&&k.length)c.state={style:k[0].style};else if(i){if("function"==typeof l)return c.state={style:i},av(c);c.state={style:h?as({},h,i):i}}else c.state={style:{}};return c}return c=[{key:"componentDidMount",value:function(){var a=this.props,b=a.isActive,c=a.canBegin;this.mounted=!0,b&&c&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(a){var b=this.props,c=b.isActive,d=b.canBegin,e=b.attributeName,f=b.shouldReAnimate,g=b.to,h=b.from,i=this.state.style;if(d){if(!c){var j={style:e?as({},e,g):g};this.state&&i&&(e&&i[e]!==g||!e&&i!==g)&&this.setState(j);return}if(!I(a.to,g)||!a.canBegin||!a.isActive){var k=!a.canBegin||!a.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var l=k||f?h:a.to;if(this.state&&i){var m={style:e?as({},e,l):l};(e&&i[e]!==l||!e&&i!==l)&&this.setState(m)}this.runAnimation(ar(ar({},this.props),{},{from:l,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var a=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),a&&a()}},{key:"handleStyleChange",value:function(a){this.changeStyle(a)}},{key:"changeStyle",value:function(a){this.mounted&&this.setState({style:a})}},{key:"runJSAnimation",value:function(a){var b=this,c=a.from,d=a.to,e=a.duration,f=a.easing,g=a.begin,h=a.onAnimationEnd,i=a.onAnimationStart,j=al(c,d,aa(f),e,this.changeStyle);this.manager.start([i,g,function(){b.stopJSAnimation=j()},e,h])}},{key:"runStepAnimation",value:function(a){var b=this,c=a.steps,d=a.begin,e=a.onAnimationStart,f=c[0],g=f.style,h=f.duration;return this.manager.start([e].concat(ao(c.reduce(function(a,d,e){if(0===e)return a;var f=d.duration,g=d.easing,h=void 0===g?"ease":g,i=d.style,j=d.properties,k=d.onAnimationEnd,l=e>0?c[e-1]:d,m=j||Object.keys(i);if("function"==typeof h||"spring"===h)return[].concat(ao(a),[b.runJSAnimation.bind(b,{from:l.style,to:i,duration:f,easing:h}),f]);var n=T(m,f,h),o=ar(ar(ar({},l.style),i),{},{transition:n});return[].concat(ao(a),[o,f,k]).filter(R)},[g,Math.max(void 0===h?0:h,d)])),[a.onAnimationEnd]))}},{key:"runAnimation",value:function(a){this.manager||(this.manager=(b=function(){return null},c=!1,d=function a(d){if(!c){if(Array.isArray(d)){if(!d.length)return;var e=function(a){if(Array.isArray(a))return a}(d)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(d)||function(a,b){if(a){if("string"==typeof a)return M(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return M(a,b)}}(d)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),f=e[0],g=e.slice(1);return"number"==typeof f?void K(a.bind(null,g),f):(a(f),void K(a.bind(null,g)))}"object"===L(d)&&b(d),"function"==typeof d&&d()}},{stop:function(){c=!0},start:function(a){c=!1,d(a)},subscribe:function(a){return b=a,function(){b=function(){return null}}}}));var b,c,d,e=a.begin,f=a.duration,g=a.attributeName,h=a.to,i=a.easing,j=a.onAnimationStart,k=a.onAnimationEnd,l=a.steps,m=a.children,n=this.manager;if(this.unSubscribe=n.subscribe(this.handleStyleChange),"function"==typeof i||"function"==typeof m||"spring"===i)return void this.runJSAnimation(a);if(l.length>1)return void this.runStepAnimation(a);var o=g?as({},g,h):h,p=T(Object.keys(o),f,i);n.start([j,e,ar(ar({},o),{},{transition:p}),f,k])}},{key:"render",value:function(){var a=this.props,b=a.children,c=(a.begin,a.duration),f=(a.attributeName,a.easing,a.isActive),g=(a.steps,a.from,a.to,a.canBegin,a.onAnimationEnd,a.shouldReAnimate,a.onAnimationReStart,function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c,d,e={},f=Object.keys(a);for(d=0;d<f.length;d++)c=f[d],b.indexOf(c)>=0||(e[c]=a[c]);return e}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,an)),h=d.Children.count(b),i=this.state.style;if("function"==typeof b)return b(i);if(!f||0===h||c<=0)return b;var j=function(a){var b=a.props,c=b.style,e=b.className;return(0,d.cloneElement)(a,ar(ar({},g),{},{style:ar(ar({},void 0===c?{}:c),i),className:e}))};return 1===h?j(d.Children.only(b)):e().createElement("div",null,d.Children.map(b,function(a){return j(a)}))}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,at(d.key),d)}}(g.prototype,c),Object.defineProperty(g,"prototype",{writable:!1}),g}(d.PureComponent);ay.displayName="Animate",ay.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},ay.propTypes={from:g().oneOfType([g().object,g().string]),to:g().oneOfType([g().object,g().string]),attributeName:g().string,duration:g().number,begin:g().number,easing:g().oneOfType([g().string,g().func]),steps:g().arrayOf(g().shape({duration:g().number.isRequired,style:g().object.isRequired,easing:g().oneOfType([g().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),g().func]),properties:g().arrayOf("string"),onAnimationEnd:g().func})),children:g().oneOfType([g().node,g().func]),isActive:g().bool,canBegin:g().bool,onAnimationEnd:g().func,shouldReAnimate:g().bool,onAnimationStart:g().func,onAnimationReStart:g().func};let az=ay},93780:(a,b,c)=>{"use strict";a.exports=c(66992)},94388:(a,b,c)=>{var d=c(57797);a.exports=function(a){var b=this.__data__,c=d(b,a);return c<0?void 0:b[c][1]}},95308:(a,b,c)=>{var d=c(34772),e=c(36959),f=c(2408);a.exports=d&&1/f(new d([,-0]))[1]==1/0?function(a){return new d(a)}:e},95746:(a,b,c)=>{var d=c(15909),e=c(29205),f=c(29508),g=c(61320),h=c(19976);function i(a){var b=-1,c=null==a?0:a.length;for(this.clear();++b<c;){var d=a[b];this.set(d[0],d[1])}}i.prototype.clear=d,i.prototype.delete=e,i.prototype.get=f,i.prototype.has=g,i.prototype.set=h,a.exports=i},96075:(a,b,c)=>{"use strict";c.d(b,{A3:()=>l,Pu:()=>k});var d=c(20237);function e(a){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}function f(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function g(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?f(Object(c),!0).forEach(function(b){var d,f,g;d=a,f=b,g=c[b],(f=function(a){var b=function(a,b){if("object"!=e(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=e(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==e(b)?b:b+""}(f))in d?Object.defineProperty(d,f,{value:g,enumerable:!0,configurable:!0,writable:!0}):d[f]=g}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):f(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}var h={widthCache:{},cacheCount:0},i={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},j="recharts_measurement_span",k=function(a){var b,c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==a||d.m.isSsr)return{width:0,height:0};var e=(Object.keys(b=g({},c)).forEach(function(a){b[a]||delete b[a]}),b),f=JSON.stringify({text:a,copyStyle:e});if(h.widthCache[f])return h.widthCache[f];try{var k=document.getElementById(j);k||((k=document.createElement("span")).setAttribute("id",j),k.setAttribute("aria-hidden","true"),document.body.appendChild(k));var l=g(g({},i),e);Object.assign(k.style,l),k.textContent="".concat(a);var m=k.getBoundingClientRect(),n={width:m.width,height:m.height};return h.widthCache[f]=n,++h.cacheCount>2e3&&(h.cacheCount=0,h.widthCache={}),n}catch(a){return{width:0,height:0}}},l=function(a){return{top:a.top+window.scrollY-document.documentElement.clientTop,left:a.left+window.scrollX-document.documentElement.clientLeft}}},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96678:(a,b,c)=>{var d=c(91290),e=c(39774),f=c(74610);a.exports=function(a,b,c){return b==b?f(a,b,c):d(a,e,c)}},96882:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},97633:(a,b,c)=>{"use strict";c.d(b,{J:()=>A});var d=c(43210),e=c.n(d),f=c(37456),g=c.n(f),h=c(5231),i=c.n(h),j=c(55048),k=c.n(j),l=c(49384),m=c(23561),n=c(54186),o=c(45370),p=c(19335);function q(a){return(q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a})(a)}var r=["offset"];function s(a,b){(null==b||b>a.length)&&(b=a.length);for(var c=0,d=Array(b);c<b;c++)d[c]=a[c];return d}function t(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function u(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?t(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=q(a)||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=q(d))return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==q(b)?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):t(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function v(){return(v=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var w=function(a){var b=a.value,c=a.formatter,d=g()(a.children)?b:a.children;return i()(c)?c(d):d},x=function(a,b,c){var d,f,h=a.position,i=a.viewBox,j=a.offset,k=a.className,m=i.cx,n=i.cy,q=i.innerRadius,r=i.outerRadius,s=i.startAngle,t=i.endAngle,u=i.clockWise,w=(q+r)/2,x=(0,o.sA)(t-s)*Math.min(Math.abs(t-s),360),y=x>=0?1:-1;"insideStart"===h?(d=s+y*j,f=u):"insideEnd"===h?(d=t-y*j,f=!u):"end"===h&&(d=t+y*j,f=u),f=x<=0?f:!f;var z=(0,p.IZ)(m,n,w,d),A=(0,p.IZ)(m,n,w,d+(f?1:-1)*359),B="M".concat(z.x,",").concat(z.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!f,",\n    ").concat(A.x,",").concat(A.y),C=g()(a.id)?(0,o.NF)("recharts-radial-line-"):a.id;return e().createElement("text",v({},c,{dominantBaseline:"central",className:(0,l.A)("recharts-radial-bar-label",k)}),e().createElement("defs",null,e().createElement("path",{id:C,d:B})),e().createElement("textPath",{xlinkHref:"#".concat(C)},b))},y=function(a){var b=a.viewBox,c=a.offset,d=a.position,e=b.cx,f=b.cy,g=b.innerRadius,h=b.outerRadius,i=(b.startAngle+b.endAngle)/2;if("outside"===d){var j=(0,p.IZ)(e,f,h+c,i),k=j.x;return{x:k,y:j.y,textAnchor:k>=e?"start":"end",verticalAnchor:"middle"}}if("center"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===d)return{x:e,y:f,textAnchor:"middle",verticalAnchor:"end"};var l=(0,p.IZ)(e,f,(g+h)/2,i);return{x:l.x,y:l.y,textAnchor:"middle",verticalAnchor:"middle"}},z=function(a){var b=a.viewBox,c=a.parentViewBox,d=a.offset,e=a.position,f=b.x,g=b.y,h=b.width,i=b.height,j=i>=0?1:-1,l=j*d,m=j>0?"end":"start",n=j>0?"start":"end",p=h>=0?1:-1,q=p*d,r=p>0?"end":"start",s=p>0?"start":"end";if("top"===e)return u(u({},{x:f+h/2,y:g-j*d,textAnchor:"middle",verticalAnchor:m}),c?{height:Math.max(g-c.y,0),width:h}:{});if("bottom"===e)return u(u({},{x:f+h/2,y:g+i+l,textAnchor:"middle",verticalAnchor:n}),c?{height:Math.max(c.y+c.height-(g+i),0),width:h}:{});if("left"===e){var t={x:f-q,y:g+i/2,textAnchor:r,verticalAnchor:"middle"};return u(u({},t),c?{width:Math.max(t.x-c.x,0),height:i}:{})}if("right"===e){var v={x:f+h+q,y:g+i/2,textAnchor:s,verticalAnchor:"middle"};return u(u({},v),c?{width:Math.max(c.x+c.width-v.x,0),height:i}:{})}var w=c?{width:h,height:i}:{};return"insideLeft"===e?u({x:f+q,y:g+i/2,textAnchor:s,verticalAnchor:"middle"},w):"insideRight"===e?u({x:f+h-q,y:g+i/2,textAnchor:r,verticalAnchor:"middle"},w):"insideTop"===e?u({x:f+h/2,y:g+l,textAnchor:"middle",verticalAnchor:n},w):"insideBottom"===e?u({x:f+h/2,y:g+i-l,textAnchor:"middle",verticalAnchor:m},w):"insideTopLeft"===e?u({x:f+q,y:g+l,textAnchor:s,verticalAnchor:n},w):"insideTopRight"===e?u({x:f+h-q,y:g+l,textAnchor:r,verticalAnchor:n},w):"insideBottomLeft"===e?u({x:f+q,y:g+i-l,textAnchor:s,verticalAnchor:m},w):"insideBottomRight"===e?u({x:f+h-q,y:g+i-l,textAnchor:r,verticalAnchor:m},w):k()(e)&&((0,o.Et)(e.x)||(0,o._3)(e.x))&&((0,o.Et)(e.y)||(0,o._3)(e.y))?u({x:f+(0,o.F4)(e.x,h),y:g+(0,o.F4)(e.y,i),textAnchor:"end",verticalAnchor:"end"},w):u({x:f+h/2,y:g+i/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function A(a){var b,c=a.offset,f=u({offset:void 0===c?5:c},function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,r)),h=f.viewBox,j=f.position,k=f.value,p=f.children,q=f.content,s=f.className,t=f.textBreakAll;if(!h||g()(k)&&g()(p)&&!(0,d.isValidElement)(q)&&!i()(q))return null;if((0,d.isValidElement)(q))return(0,d.cloneElement)(q,f);if(i()(q)){if(b=(0,d.createElement)(q,f),(0,d.isValidElement)(b))return b}else b=w(f);var A="cx"in h&&(0,o.Et)(h.cx),B=(0,n.J9)(f,!0);if(A&&("insideStart"===j||"insideEnd"===j||"end"===j))return x(f,b,B);var C=A?y(f):z(f);return e().createElement(m.E,v({className:(0,l.A)("recharts-label",void 0===s?"":s)},B,C,{breakAll:t}),b)}A.displayName="Label";var B=function(a){var b=a.cx,c=a.cy,d=a.angle,e=a.startAngle,f=a.endAngle,g=a.r,h=a.radius,i=a.innerRadius,j=a.outerRadius,k=a.x,l=a.y,m=a.top,n=a.left,p=a.width,q=a.height,r=a.clockWise,s=a.labelViewBox;if(s)return s;if((0,o.Et)(p)&&(0,o.Et)(q)){if((0,o.Et)(k)&&(0,o.Et)(l))return{x:k,y:l,width:p,height:q};if((0,o.Et)(m)&&(0,o.Et)(n))return{x:m,y:n,width:p,height:q}}return(0,o.Et)(k)&&(0,o.Et)(l)?{x:k,y:l,width:0,height:0}:(0,o.Et)(b)&&(0,o.Et)(c)?{cx:b,cy:c,startAngle:e||d||0,endAngle:f||d||0,innerRadius:i||0,outerRadius:j||h||g||0,clockWise:r}:a.viewBox?a.viewBox:{}};A.parseViewBox=B,A.renderCallByParent=function(a,b){var c,f,g=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!a||!a.children&&g&&!a.label)return null;var h=a.children,j=B(a),l=(0,n.aS)(h,A).map(function(a,c){return(0,d.cloneElement)(a,{viewBox:b||j,key:"label-".concat(c)})});if(!g)return l;return[(c=a.label,f=b||j,!c?null:!0===c?e().createElement(A,{key:"label-implicit",viewBox:f}):(0,o.vh)(c)?e().createElement(A,{key:"label-implicit",viewBox:f,value:c}):(0,d.isValidElement)(c)?c.type===A?(0,d.cloneElement)(c,{key:"label-implicit",viewBox:f}):e().createElement(A,{key:"label-implicit",content:c,viewBox:f}):i()(c)?e().createElement(A,{key:"label-implicit",content:c,viewBox:f}):k()(c)?e().createElement(A,v({viewBox:f},c,{key:"label-implicit"})):null)].concat(function(a){if(Array.isArray(a))return s(a)}(l)||function(a){if("undefined"!=typeof Symbol&&null!=a[Symbol.iterator]||null!=a["@@iterator"])return Array.from(a)}(l)||function(a,b){if(a){if("string"==typeof a)return s(a,void 0);var c=Object.prototype.toString.call(a).slice(8,-1);if("Object"===c&&a.constructor&&(c=a.constructor.name),"Map"===c||"Set"===c)return Array.from(a);if("Arguments"===c||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c))return s(a,b)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},98451:(a,b,c)=>{var d=c(29395),e=c(27467);a.exports=function(a){return!0===a||!1===a||e(a)&&"[object Boolean]"==d(a)}},98798:a=>{var b=Math.ceil,c=Math.max;a.exports=function(a,d,e,f){for(var g=-1,h=c(b((d-a)/(e||1)),0),i=Array(h);h--;)i[f?h:++g]=a,a+=e;return i}},98986:(a,b,c)=>{"use strict";c.d(b,{W:()=>j});var d=c(43210),e=c.n(d),f=c(49384),g=c(54186),h=["children","className"];function i(){return(i=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(this,arguments)}var j=e().forwardRef(function(a,b){var c=a.children,d=a.className,j=function(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){if(b.indexOf(d)>=0)continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],!(b.indexOf(c)>=0)&&Object.prototype.propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}(a,h),k=(0,f.A)("recharts-layer",d);return e().createElement("g",i({className:k},(0,g.J9)(j,!0),{ref:b}),c)})},99114:(a,b,c)=>{var d=c(12344),e=c(7651);a.exports=function(a,b){return a&&d(a,b,e)}},99180:a=>{a.exports=function(a,b){return a>b}},99525:a=>{a.exports=function(a,b){for(var c=-1,d=null==a?0:a.length;++c<d;)if(b(a[c],c,a))return!0;return!1}}};