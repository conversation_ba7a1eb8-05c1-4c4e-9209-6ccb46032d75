"use strict";exports.id=7665,exports.ids=[7665],exports.modules={67665:(a,b,c)=>{c.d(b,{RK:()=>E});var d=c(37413);function e(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function f(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?e(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):e(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function g(a){return"span"===a._type&&"text"in a&&"string"==typeof a.text&&(typeof a.marks>"u"||Array.isArray(a.marks)&&a.marks.every(a=>"string"==typeof a))}function h(a){return"string"==typeof a._type&&"@"!==a._type[0]&&(!("markDefs"in a)||!a.markDefs||Array.isArray(a.markDefs)&&a.markDefs.every(a=>"string"==typeof a._key))&&"children"in a&&Array.isArray(a.children)&&a.children.every(a=>"object"==typeof a&&"_type"in a)}function i(a){return h(a)&&"listItem"in a&&"string"==typeof a.listItem&&(typeof a.level>"u"||"number"==typeof a.level)}function j(a){return"@list"===a._type}function k(a){return"@span"===a._type}function l(a){return"@text"===a._type}let m=["strong","em","code","underline","strike-through"];function n(a,b,c){if(!g(a)||!a.marks||!a.marks.length)return[];let d=a.marks.slice(),e={};return d.forEach(a=>{e[a]=1;for(let d=b+1;d<c.length;d++){let b=c[d];if(b&&g(b)&&Array.isArray(b.marks)&&-1!==b.marks.indexOf(a))e[a]++;else break}}),d.sort((a,b)=>(function(a,b,c){let d=a[b],e=a[c];if(d!==e)return e-d;let f=m.indexOf(b),g=m.indexOf(c);return f!==g?f-g:b.localeCompare(c)})(e,a,b))}function o(a,b,c){return{_type:"@list",_key:`${a._key||`${b}`}-parent`,mode:c,level:a.level||1,listItem:a.listItem,children:[a]}}function p(a,b){let c=b.level||1,d=b.listItem||"normal",e="string"==typeof b.listItem;if(j(a)&&(a.level||1)===c&&e&&(a.listItem||"normal")===d)return a;if(!("children"in a))return;let f=a.children[a.children.length-1];return f&&!g(f)?p(f,b):void 0}var q=c(61120);let r=["block","list","listItem","marks","types"],s=["listItem"],t=["_key"];function u(a,b){var c=Object.keys(a);if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(a);b&&(d=d.filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})),c.push.apply(c,d)}return c}function v(a){for(var b=1;b<arguments.length;b++){var c=null!=arguments[b]?arguments[b]:{};b%2?u(Object(c),!0).forEach(function(b){var d,e,f;d=a,e=b,f=c[b],(e=function(a){var b=function(a,b){if("object"!=typeof a||!a)return a;var c=a[Symbol.toPrimitive];if(void 0!==c){var d=c.call(a,b||"default");if("object"!=typeof d)return d;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===b?String:Number)(a)}(a,"string");return"symbol"==typeof b?b:b+""}(e))in d?Object.defineProperty(d,e,{value:f,enumerable:!0,configurable:!0,writable:!0}):d[e]=f}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(c)):u(Object(c)).forEach(function(b){Object.defineProperty(a,b,Object.getOwnPropertyDescriptor(c,b))})}return a}function w(a,b){if(null==a)return{};var c,d,e=function(a,b){if(null==a)return{};var c={};for(var d in a)if(({}).hasOwnProperty.call(a,d)){if(b.includes(d))continue;c[d]=a[d]}return c}(a,b);if(Object.getOwnPropertySymbols){var f=Object.getOwnPropertySymbols(a);for(d=0;d<f.length;d++)c=f[d],b.includes(c)||({}).propertyIsEnumerable.call(a,c)&&(e[c]=a[c])}return e}let x={textDecoration:"underline"},y=(a,b)=>`[@portabletext/react] Unknown ${a}, specify a component for it in the \`components.${b}\` prop`,z=a=>y(`block type "${a}"`,"types");function A(a){console.warn(a)}let B={display:"none"},C={types:{},block:{normal:({children:a})=>(0,d.jsx)("p",{children:a}),blockquote:({children:a})=>(0,d.jsx)("blockquote",{children:a}),h1:({children:a})=>(0,d.jsx)("h1",{children:a}),h2:({children:a})=>(0,d.jsx)("h2",{children:a}),h3:({children:a})=>(0,d.jsx)("h3",{children:a}),h4:({children:a})=>(0,d.jsx)("h4",{children:a}),h5:({children:a})=>(0,d.jsx)("h5",{children:a}),h6:({children:a})=>(0,d.jsx)("h6",{children:a})},marks:{em:({children:a})=>(0,d.jsx)("em",{children:a}),strong:({children:a})=>(0,d.jsx)("strong",{children:a}),code:({children:a})=>(0,d.jsx)("code",{children:a}),underline:({children:a})=>(0,d.jsx)("span",{style:x,children:a}),"strike-through":({children:a})=>(0,d.jsx)("del",{children:a}),link:({children:a,value:b})=>(0,d.jsx)("a",{href:b?.href,children:a})},list:{number:({children:a})=>(0,d.jsx)("ol",{children:a}),bullet:({children:a})=>(0,d.jsx)("ul",{children:a})},listItem:({children:a})=>(0,d.jsx)("li",{children:a}),hardBreak:()=>(0,d.jsx)("br",{}),unknownType:({value:a,isInline:b})=>{let c=z(a._type);return b?(0,d.jsx)("span",{style:B,children:c}):(0,d.jsx)("div",{style:B,children:c})},unknownMark:({markType:a,children:b})=>(0,d.jsx)("span",{className:`unknown__pt__mark__${a}`,children:b}),unknownList:({children:a})=>(0,d.jsx)("ul",{children:a}),unknownListItem:({children:a})=>(0,d.jsx)("li",{children:a}),unknownBlockStyle:({children:a})=>(0,d.jsx)("p",{children:a})};function D(a,b,c){let d=b[c],e=a[c];return"function"==typeof d||d&&"function"==typeof e?d:d?v(v({},e),d):e}function E({value:a,components:b,listNestingMode:c,onMissingComponent:e=A}){let g=e||H,h=function(a,b){let c,d=[];for(let h=0;h<a.length;h++){let j=a[h];if(j){var e,g;if(!i(j)){d.push(j),c=void 0;continue}if(!c){c=o(j,h,b),d.push(c);continue}if(e=j,g=c,(e.level||1)===g.level&&e.listItem===g.listItem){c.children.push(j);continue}if((j.level||1)>c.level){let a=o(j,h,b);if("html"===b){let b=c.children[c.children.length-1],d=f(f({},b),{},{children:[...b.children,a]});c.children[c.children.length-1]=d}else c.children.push(a);c=a;continue}if((j.level||1)<c.level){let a=d[d.length-1],e=a&&p(a,j);if(e){(c=e).children.push(j);continue}c=o(j,h,b),d.push(c);continue}if(j.listItem!==c.listItem){let a=d[d.length-1],e=a&&p(a,{level:j.level||1});if(e&&e.listItem===j.listItem){(c=e).children.push(j);continue}c=o(j,h,b),d.push(c);continue}console.warn("Unknown state encountered for block",j),d.push(j)}}return d}(Array.isArray(a)?a:[a],c||"html"),j=(0,q.useMemo)(()=>b?function(a,b){let{block:c,list:d,listItem:e,marks:f,types:g}=b,h=w(b,r);return v(v({},a),{},{block:D(a,b,"block"),list:D(a,b,"list"),listItem:D(a,b,"listItem"),marks:D(a,b,"marks"),types:D(a,b,"types")},h)}(C,b):C,[b]),k=(0,q.useMemo)(()=>F(j,g),[j,g]),l=h.map((a,b)=>k({node:a,index:b,isInline:!1,renderNode:k}));return(0,d.jsx)(d.Fragment,{children:l})}let F=(a,b)=>function c(e){let{node:f,index:g,isInline:m}=e,n=f._key||`node-${g}`;return j(f)?function(e,f,g){let h=e.children.map((a,b)=>c({node:a._key?a:v(v({},a),{},{_key:`li-${f}-${b}`}),index:b,isInline:!1,renderNode:c})),i=a.list,j=("function"==typeof i?i:i[e.listItem])||a.unknownList;if(j===a.unknownList){let a=e.listItem||"bullet";b(y(`list style "${a}"`,"list"),{nodeType:"listStyle",type:a})}return(0,d.jsx)(j,{value:e,index:f,isInline:!1,renderNode:c,children:h},g)}(f,g,n):i(f)?function(e,f,g){let h=G({node:e,index:f,isInline:!1,renderNode:c}),i=a.listItem,j=("function"==typeof i?i:i[e.listItem])||a.unknownListItem;if(j===a.unknownListItem){let a=e.listItem||"bullet";b(y(`list item style "${a}"`,"listItem"),{type:a,nodeType:"listItemStyle"})}let k=h.children;if(e.style&&"normal"!==e.style){let{listItem:a}=e;k=c({node:w(e,s),index:f,isInline:!1,renderNode:c})}return(0,d.jsx)(j,{value:e,index:f,isInline:!1,renderNode:c,children:k},g)}(f,g,n):k(f)?function(e,f,g){let{markDef:h,markType:i,markKey:j}=e,m=a.marks[i]||a.unknownMark,n=e.children.map((a,b)=>c({node:a,index:b,isInline:!0,renderNode:c}));return m===a.unknownMark&&b(y(`mark type "${i}"`,"marks"),{nodeType:"mark",type:i}),(0,d.jsx)(m,{text:function a(b){let c="";return b.children.forEach(b=>{l(b)?c+=b.text:k(b)&&(c+=a(b))}),c}(e),value:h,markType:i,markKey:j,renderNode:c,children:n},g)}(f,0,n):f._type in a.types?function(b,e,f,g){let h=a.types[b._type];return h?(0,d.jsx)(h,v({},{value:b,isInline:g,index:e,renderNode:c}),f):null}(f,g,n,m):h(f)?function(e,f,g,h){let i=G({node:e,index:f,isInline:h,renderNode:c}),{_key:j}=i,k=w(i,t),l=k.node.style||"normal",m=("function"==typeof a.block?a.block:a.block[l])||a.unknownBlockStyle;return m===a.unknownBlockStyle&&b(y(`block style "${l}"`,"block"),{nodeType:"blockStyle",type:l}),(0,d.jsx)(m,v(v({},k),{},{value:k.node,renderNode:c}),g)}(f,g,n,m):l(f)?function(b,c){if(b.text===`
`){let b=a.hardBreak;return b?(0,d.jsx)(b,{},c):`
`}return b.text}(f,n):function(e,f,g,h){b(z(e._type),{nodeType:"block",type:e._type});let i=a.unknownType;return(0,d.jsx)(i,v({},{value:e,isInline:h,index:f,renderNode:c}),g)}(f,g,n,m)};function G(a){let{node:b,index:c,isInline:d,renderNode:e}=a,f=(function(a){var b,c;let{children:d}=a,e=null!=(b=a.markDefs)?b:[];if(!d||!d.length)return[];let f=d.map(n),h={_type:"@span",children:[],markType:"<unknown>"},i=[h];for(let a=0;a<d.length;a++){let b=d[a];if(!b)continue;let h=f[a]||[],j=1;if(i.length>1)for(;j<i.length;j++){let a=(null==(c=i[j])?void 0:c.markKey)||"",b=h.indexOf(a);if(-1===b)break;h.splice(b,1)}let k=(i=i.slice(0,j))[i.length-1];if(k){for(let a of h){let c=null==e?void 0:e.find(b=>b._key===a),d=c?c._type:a,f={_type:"@span",_key:b._key,children:[],markDef:c,markType:d,markKey:a};k.children.push(f),i.push(f),k=f}if(g(b)){let a=b.text.split(`
`);for(let b=a.length;b-- >1;)a.splice(b,0,`
`);k.children=k.children.concat(a.map(a=>({_type:"@text",text:a})))}else k.children=k.children.concat(b)}}return h.children})(b).map((a,b)=>e({node:a,isInline:!0,index:b,renderNode:e}));return{_key:b._key||`block-${c}`,children:f,index:c,isInline:d,node:b}}function H(){}}};