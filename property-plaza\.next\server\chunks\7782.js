exports.id=7782,exports.ids=[7782],exports.modules={1134:(a,b,c)=>{"use strict";c.d(b,{HF:()=>h,JX:()=>d,cz:()=>f,mZ:()=>e,tf:()=>g});let d=a=>a.toLowerCase().includes("day")?"DAY":a.toLowerCase().includes("week")?"WEEK":a.toLowerCase().includes("month")?"MONTH":a.toLowerCase().includes("year")?"YEAR":"MONTH",e=a=>a.includes("kbps")?"KBPS":a.includes("mbps")?"MBPS":a.includes("gbps")?"GBPS":"MBPS",f=a=>a.includes("day")?"DAY":a.includes("week")?"WEEK":a.includes("month")?"MONTH":"DAY",g=a=>a.includes("Seperated Room")?"SEPERATED_ROOM":"CONNECTED_ROOM",h={leasehold:"LEASEHOLD",freehold:"FREEHOLD",rent:"RENT"}},1695:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\pop-up.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\pop-up.tsx","default")},5943:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>g});var d=c(60687),e=c(24934),f=c(96241);function g({title:a,description:b,action:c,...g}){return(0,d.jsxs)("section",{...g,className:(0,f.cn)("space-y-6",g.className),children:[(0,d.jsxs)("div",{className:"flex justify-between gap-2",children:[(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)("h2",{className:"capitalize text-seekers-text text-2xl font-bold tracking-[0.5%]",children:a}),(0,d.jsx)("p",{className:" text-seekers-text-light text-base font-semibold tracking-[0.5%]",children:b})]}),c&&(0,d.jsx)(e.$,{variant:"link",className:"text-seekers-primary-foreground",onClick:c.action,children:c.title})]}),g.children]})}c(43210)},6809:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return j}});let d=c(37413),e=c(61120),f=c(38922),g=c(95047);function h(a){return{default:a&&"default"in a?a.default:a}}let i={loader:()=>Promise.resolve(h(()=>null)),loading:null,ssr:!0},j=function(a){let b={...i,...a},c=(0,e.lazy)(()=>b.loader().then(h)),j=b.loading;function k(a){let h=j?(0,d.jsx)(j,{isLoading:!0,pastDelay:!0,error:null}):null,i=!b.ssr||!!b.loading,k=i?e.Suspense:e.Fragment,l=b.ssr?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.PreloadChunks,{moduleIds:b.modules}),(0,d.jsx)(c,{...a})]}):(0,d.jsx)(f.BailoutToCSR,{reason:"next/dynamic",children:(0,d.jsx)(c,{...a})});return(0,d.jsx)(k,{...i?{fallback:h}:{},children:l})}return k.displayName="LoadableComponent",k}},7980:(a,b,c)=>{"use strict";c.d(b,{fJ:()=>g,qH:()=>e,rU:()=>f});var d=c(1134);function e(a){let b=a.availability,c=a.detail,e=a.features,f=a.location,[g,i]=h(f.latitude,f.longitude),j=a.images.map(a=>({id:a.id,image:a.image,isHighlight:a.is_highlight,order:a.order,propertyId:a.property_id})),k=j.find(a=>a.isHighlight);k.order=1;let l=[k,...j.filter(a=>!a.isHighlight).map((a,b)=>({...a,order:b+2}))];return{availability:{availableAt:b.available_at,isNegotiable:b.is_negotiable,maxDuration:b?.duration_max||0,minDuration:b?.duration_min||0,price:b.price,type:b.type?.value||"",typeMaximumDuration:(0,d.JX)(b.duration_max_unit?.value||""),typeMinimumDuration:(0,d.JX)(b.duration_min_unit?.value||"")},description:a.description,detail:{bathroomTotal:+c.bathroom_total?.value||0,bedroomTotal:+c.bedroom_total?.value||0,buildingSize:+c.building_size||0,cascoStatus:c.casco_status,cleaningService:+c.cleaning_service?.value||0,garbageFee:c.garbage_fee,gardenSize:+c.garden_size||0,landSize:+c.land_size||0,propertyOfView:c.property_of_view,type:c.option.type,villageFee:c.village_fee,waterFee:c.water_fee,wifiService:+c.wifi_service?.value||0,typeWifiSpeed:(0,d.mZ)(c.wifi_service.suffix||""),yearsOfBuilding:c.years_of_building,typeBedRoom:(0,d.tf)(c.bedroom_total.suffix||""),typeCleaning:(0,d.cz)(c.cleaning_service.suffix||""),title:a.title,excerpt:a.excerpt},excerpt:a.excerpt,features:{amenities:(e.amenities||[]).map(a=>a.value),electricity:+e.electricity,furnishingOption:e.furnishing_option?.value,livingOption:e.living_option?.value||"",parkingOption:e.parking_option?.value||"",poolOption:e.pool_option?.value||"",sellingPoints:(e.selling_points||[]).map(a=>a.value)},id:a.id,images:l,location:{city:f.city,district:f.district,latitude:g,longitude:i,mainAddress:f.main_address,postalCode:f.postal_code,province:f.province,roadSize:+(f.road_size?.value||0),secondAddress:f.second_address,type:f.type.value,banjar:""},propertyId:c.property_id,status:a.status,title:a.title,owner:a.owner?{name:a.owner.full_name,image:a.owner.image,code:a.owner.user.id}:null,middleman:a.middleman?{code:a.middleman.user.id,image:a.middleman.image||"",name:a.middleman.full_name}:null,isFavorite:+(a?._count?.favorites||0)>0,chatCount:a.account?.user._count.chats||0}}function f(a){return a.map(a=>{var b;return{code:a.code,geolocation:h(a.location.latitude,a.location.longitude),location:a.location.district+", "+a.location.city+", "+a.location.province,price:a.availability.price,thumbnail:(b=a.code,a.images.map((a,c)=>({id:b+c,image:a.image,isHighlight:a.is_highlight})).sort((a,b)=>b.isHighlight-a.isHighlight)),title:a.title,listingDetail:{bathRoom:a.detail.bathroom_total,bedRoom:a.detail.bedroom_total,buildingSize:a.detail.building_size,landSize:a.detail.land_size,cascoStatus:a.detail.casco_status,gardenSize:a.detail.garden_size},availability:{availableAt:a.availability.available_at||"",maxDuration:a.availability.duration_max_unit?.value&&a.availability.duration_max?{value:a.availability.duration_max||1,suffix:a.availability.duration_max_unit?.value}:null,minDuration:a.availability.duration_min_unit?.value&&a.availability.duration_min?{value:a.availability.duration_min||1,suffix:a.availability.duration_min_unit?.value}:null,type:a.availability.type.value||""},sellingPoint:a.features.selling_points,category:a.detail.option.type,isFavorite:a?._count?.favorites>0,status:a.status}})}function g(a){return a.map(a=>({title:a.title.replaceAll(/[^a-zA-Z0-9]/g,"-"),id:a.code,updateAt:a.availability.updated_at}))}c(24684);let h=(a,b,c=10)=>{let d=1/111320*c;return[a+.4*d,b+.4*d]}},8723:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(43210);function e(){let[a,b]=(0,d.useState)(!1),[c,e]=(0,d.useState)(!0);return{isVisible:a,sectionRef:(0,d.useRef)(null),firstTimeVisible:c,setFirstTimeVisible:e}}},13344:(a,b,c)=>{"use strict";c.d(b,{Ix:()=>e,Xh:()=>d});let d="tkn",e={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},14233:(a,b,c)=>{"use strict";c.d(b,{W:()=>f});var d=c(1134),e=c(75074);let f=async(a,b,c,f)=>{let g=await (0,e.A)("seeker"),h="",i="",j=(0,d.JX)(c?.suffix||""),k=(0,d.JX)(f?.suffix||"");return(()=>{switch(b){case"LEASEHOLD":let a="MONTH"==k?g("misc.month",{count:f?.value||1}):"YEAR"==k?g("misc.yearWithCount",{count:f?.value||1}):k;i=g("listing.pricing.suffix.leasehold",{count:f?.value||1,durationType:a});return;case"FREEHOLD":h=g("conjuntion.for");return;case"RENT":let c="MONTH"==j?g("misc.month",{count:1}):"YEAR"==j?g("misc.yearWithCount",{count:1}):k;i=`/ ${c}`,h=g("misc.startFrom");return;default:return}})(),{startWord:h,suffix:i,formattedPrice:a}}},19439:(a,b,c)=>{Promise.resolve().then(c.bind(c,1695)),Promise.resolve().then(c.bind(c,28504)),Promise.resolve().then(c.bind(c,67735)),Promise.resolve().then(c.bind(c,71810)),Promise.resolve().then(c.bind(c,18543)),Promise.resolve().then(c.bind(c,3203)),Promise.resolve().then(c.bind(c,2860)),Promise.resolve().then(c.bind(c,94187)),Promise.resolve().then(c.bind(c,25922)),Promise.resolve().then(c.t.bind(c,49603,23)),Promise.resolve().then(c.bind(c,97094))},28504:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\(user)\\\\setup-seekers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\setup-seekers.tsx","default")},28558:(a,b,c)=>{"use strict";c.d(b,{default:()=>e.a});var d=c(67113),e=c.n(d)},32331:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"70b66932d5c4a5b464452baccb955fa54dfdec9631":()=>d.A});var d=c(86962)},35220:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l});var d=c(37413),e=c(29666),f=c(67735),g=c(71810);c(61120);var h=c(28504),i=c(44999),j=c(1695),k=c(18543);async function l({children:a}){let b=await (0,i.UL)(),c=b.get("seekers-settings")?.value||"",l=c?JSON.parse(c):void 0,m=b.get("NEXT_LOCALE")?.value;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.default,{isSeeker:!0}),(0,d.jsx)(h.default,{}),(0,d.jsx)(f.default,{}),(0,d.jsx)("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:(0,d.jsx)(g.default,{currency_:l?.state?.currency,localeId:m})}),(0,d.jsx)("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:a}),(0,d.jsx)("div",{className:"!mt-0",children:(0,d.jsx)(e.A,{})}),(0,d.jsx)(j.default,{})]})}},38922:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\dynamic-bailout-to-csr.js")},40162:(a,b,c)=>{"use strict";c.d(b,{default:()=>o});var d=c(60687),e=c(85814),f=c.n(e),g=c(755),h=c(4e3),i=c(11976),j=c(24934),k=c(33213);function l({open:a,setOpen:b,trigger:c}){let e=(0,k.useTranslations)("universal");return(0,d.jsxs)(i.A,{open:a,setOpen:b,openTrigger:c,children:[(0,d.jsx)(h.A,{children:(0,d.jsx)("h3",{className:"text-base font-bold text-seekers-text",children:e("popup.followInstagram.title")})}),(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:e("popup.followInstagram.description")})}),(0,d.jsx)(g.A,{children:(0,d.jsx)(j.$,{asChild:!0,className:"w-full",variant:"default-seekers",children:(0,d.jsx)(f(),{href:"https://www.instagram.com/join.propertyplaza/",children:e("cta.followUsOnInstagram")})})})]})}var m=c(56605),n=c(43210);function o(){let{successSignUp:a,setSuccessSignUp:b,loading:c}=(0,m.k)(),[e,f]=(0,n.useState)(!1),[g,h]=(0,n.useState)(!0);return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(l,{open:e,setOpen:a=>{b(a),f(a)},trigger:(0,d.jsx)(d.Fragment,{})})})}},53258:(a,b,c)=>{"use strict";c.d(b,{default:()=>g});var d=c(60687),e=c(77273),f=c(66835);function g(){let{setSeekers:a,setRole:b}=(0,f.k)(a=>a);return(0,e.H)(),(0,d.jsx)(d.Fragment,{})}c(43210)},67113:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return e}});let d=c(72639)._(c(6809));function e(a,b){var c;let e={};"function"==typeof a&&(e.loader=a);let f={...e,...b};return(0,d.default)({...f,modules:null==(c=f.loadableGenerated)?void 0:c.modules})}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},67760:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},69327:(a,b,c)=>{"use strict";c.d(b,{$:()=>i,F:()=>h});var d=c(60687),e=c(43210),f=c(55161),g=c(96241);let h=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.bL,{ref:e,className:(0,g.cn)("relative overflow-hidden",a),...c,children:[(0,d.jsx)(f.LM,{className:"h-full w-full rounded-[inherit]",children:b}),(0,d.jsx)(i,{}),(0,d.jsx)(f.OK,{})]}));h.displayName=f.bL.displayName;let i=e.forwardRef(({className:a,orientation:b="vertical",...c},e)=>(0,d.jsx)(f.VM,{ref:e,orientation:b,className:(0,g.cn)("flex touch-none select-none transition-colors","vertical"===b&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===b&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",a),...c,children:(0,d.jsx)(f.lr,{className:"relative flex-1 rounded-full bg-border"})}));i.displayName=f.VM.displayName},72952:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(39916);function e(){(0,d.redirect)("/")}},79271:(a,b,c)=>{Promise.resolve().then(c.bind(c,40162)),Promise.resolve().then(c.bind(c,53258)),Promise.resolve().then(c.bind(c,62881)),Promise.resolve().then(c.bind(c,25842)),Promise.resolve().then(c.bind(c,65994)),Promise.resolve().then(c.bind(c,78377)),Promise.resolve().then(c.bind(c,44150)),Promise.resolve().then(c.bind(c,17995)),Promise.resolve().then(c.bind(c,61408)),Promise.resolve().then(c.t.bind(c,46533,23)),Promise.resolve().then(c.bind(c,15246))},86962:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(67218);c(79130);var e=c(13344),f=c(44999);async function g(a,b,c){let d=(0,f.UL)(),g=d.get(e.Xh)?.value;try{let d=await fetch(a,{method:b,headers:{Authorization:`Bearer ${g}`,"Content-Type":"application/json"},...c});if(!d.ok)return{data:null,meta:void 0,error:{status:d.status,name:d.statusText,message:await d.text()||"Unexpected error",details:{}}};let e=await d.json();if(e.error)return{data:null,meta:void 0,error:e.error};return{data:e.data,meta:e.meta,error:void 0}}catch(a){return{data:null,meta:void 0,error:{status:500,details:{cause:a.cause},message:a.message,name:a.name}}}}(0,c(17478).D)([g]),(0,d.A)(g,"70b66932d5c4a5b464452baccb955fa54dfdec9631",null)},91527:(a,b,c)=>{"use strict";c.d(b,{w:()=>d});let d={get:"GET",post:"POST",put:"PUT",del:"DELETE",patch:"PATCH"}},95047:(a,b,c)=>{let{createProxy:d}=c(39844);a.exports=d("C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\node_modules\\next\\dist\\shared\\lib\\lazy-dynamic\\preload-chunks.js")}};