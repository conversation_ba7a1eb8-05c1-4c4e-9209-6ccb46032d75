"use strict";exports.id=8268,exports.ids=[8268],exports.modules={55161:(a,b,c)=>{c.d(b,{OK:()=>X,bL:()=>V,VM:()=>x,lr:()=>I,LM:()=>W});var d=c(43210),e=c(3416),f=c(98599),g=c(66156),h=a=>{let{present:b,children:c}=a,e=function(a){var b,c;let[e,f]=d.useState(),h=d.useRef({}),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=i(h.current);k.current="mounted"===l?a:"none"},[l]),(0,g.N)(()=>{let b=h.current,c=j.current;if(c!==a){let d=k.current,e=i(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,g.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=i(h.current).includes(c.animationName);if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=i(h.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{a&&(h.current=getComputedStyle(a)),f(a)},[])}}(b),h="function"==typeof c?c({present:e.isPresent}):d.Children.only(c),j=(0,f.s)(e.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(h));return"function"==typeof c||e.isPresent?d.cloneElement(h,{ref:j}):null};function i(a){return a?.animationName||"none"}h.displayName="Presence";var j=c(60687),k=c(13495),l=c(43),m=c(67969),n=c(70569),o="ScrollArea",[p,q]=function(a,b=[]){let c=[],e=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return e.scopeName=a,[function(b,e){let f=d.createContext(e),g=c.length;c=[...c,e];let h=b=>{let{scope:c,children:e,...h}=b,i=c?.[a]?.[g]||f,k=d.useMemo(()=>h,Object.values(h));return(0,j.jsx)(i.Provider,{value:k,children:e})};return h.displayName=b+"Provider",[h,function(c,h){let i=h?.[a]?.[g]||f,j=d.useContext(i);if(j)return j;if(void 0!==e)return e;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(e,...b)]}(o),[r,s]=p(o),t=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,type:g="hover",dir:h,scrollHideDelay:i=600,...k}=a,[m,n]=d.useState(null),[o,p]=d.useState(null),[q,s]=d.useState(null),[t,u]=d.useState(null),[v,w]=d.useState(null),[x,y]=d.useState(0),[z,A]=d.useState(0),[B,C]=d.useState(!1),[D,E]=d.useState(!1),F=(0,f.s)(b,a=>n(a)),G=(0,l.jH)(h);return(0,j.jsx)(r,{scope:c,type:g,dir:G,scrollHideDelay:i,scrollArea:m,viewport:o,onViewportChange:p,content:q,onContentChange:s,scrollbarX:t,onScrollbarXChange:u,scrollbarXEnabled:B,onScrollbarXEnabledChange:C,scrollbarY:v,onScrollbarYChange:w,scrollbarYEnabled:D,onScrollbarYEnabledChange:E,onCornerWidthChange:y,onCornerHeightChange:A,children:(0,j.jsx)(e.sG.div,{dir:G,...k,ref:F,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":z+"px",...a.style}})})});t.displayName=o;var u="ScrollAreaViewport",v=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,children:g,asChild:h,nonce:i,...k}=a,l=s(u,c),m=d.useRef(null),n=(0,f.s)(b,m,l.onViewportChange);return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("style",{dangerouslySetInnerHTML:{__html:`
[data-radix-scroll-area-viewport] {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}
[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: none;
}
:where([data-radix-scroll-area-viewport]) {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
:where([data-radix-scroll-area-content]) {
  flex-grow: 1;
}
`},nonce:i}),(0,j.jsx)(e.sG.div,{"data-radix-scroll-area-viewport":"",...k,asChild:h,ref:n,style:{overflowX:l.scrollbarXEnabled?"scroll":"hidden",overflowY:l.scrollbarYEnabled?"scroll":"hidden",...a.style},children:function(a,b){let{asChild:c,children:e}=a;if(!c)return"function"==typeof b?b(e):b;let f=d.Children.only(e);return d.cloneElement(f,{children:"function"==typeof b?b(f.props.children):b})}({asChild:h,children:g},a=>(0,j.jsx)("div",{"data-radix-scroll-area-content":"",ref:l.onContentChange,style:{minWidth:l.scrollbarXEnabled?"fit-content":void 0},children:a}))})]})});v.displayName=u;var w="ScrollAreaScrollbar",x=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,f=s(w,a.__scopeScrollArea),{onScrollbarXEnabledChange:g,onScrollbarYEnabledChange:h}=f,i="horizontal"===a.orientation;return d.useEffect(()=>(i?g(!0):h(!0),()=>{i?g(!1):h(!1)}),[i,g,h]),"hover"===f.type?(0,j.jsx)(y,{...e,ref:b,forceMount:c}):"scroll"===f.type?(0,j.jsx)(z,{...e,ref:b,forceMount:c}):"auto"===f.type?(0,j.jsx)(A,{...e,ref:b,forceMount:c}):"always"===f.type?(0,j.jsx)(B,{...e,ref:b}):null});x.displayName=w;var y=d.forwardRef((a,b)=>{let{forceMount:c,...e}=a,f=s(w,a.__scopeScrollArea),[g,i]=d.useState(!1);return d.useEffect(()=>{let a=f.scrollArea,b=0;if(a){let c=()=>{window.clearTimeout(b),i(!0)},d=()=>{b=window.setTimeout(()=>i(!1),f.scrollHideDelay)};return a.addEventListener("pointerenter",c),a.addEventListener("pointerleave",d),()=>{window.clearTimeout(b),a.removeEventListener("pointerenter",c),a.removeEventListener("pointerleave",d)}}},[f.scrollArea,f.scrollHideDelay]),(0,j.jsx)(h,{present:c||g,children:(0,j.jsx)(A,{"data-state":g?"visible":"hidden",...e,ref:b})})}),z=d.forwardRef((a,b)=>{var c;let{forceMount:e,...f}=a,g=s(w,a.__scopeScrollArea),i="horizontal"===a.orientation,k=T(()=>m("SCROLL_END"),100),[l,m]=(c={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},d.useReducer((a,b)=>c[a][b]??a,"hidden"));return d.useEffect(()=>{if("idle"===l){let a=window.setTimeout(()=>m("HIDE"),g.scrollHideDelay);return()=>window.clearTimeout(a)}},[l,g.scrollHideDelay,m]),d.useEffect(()=>{let a=g.viewport,b=i?"scrollLeft":"scrollTop";if(a){let c=a[b],d=()=>{let d=a[b];c!==d&&(m("SCROLL"),k()),c=d};return a.addEventListener("scroll",d),()=>a.removeEventListener("scroll",d)}},[g.viewport,i,m,k]),(0,j.jsx)(h,{present:e||"hidden"!==l,children:(0,j.jsx)(B,{"data-state":"hidden"===l?"hidden":"visible",...f,ref:b,onPointerEnter:(0,n.m)(a.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:(0,n.m)(a.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),A=d.forwardRef((a,b)=>{let c=s(w,a.__scopeScrollArea),{forceMount:e,...f}=a,[g,i]=d.useState(!1),k="horizontal"===a.orientation,l=T(()=>{if(c.viewport){let a=c.viewport.offsetWidth<c.viewport.scrollWidth,b=c.viewport.offsetHeight<c.viewport.scrollHeight;i(k?a:b)}},10);return U(c.viewport,l),U(c.content,l),(0,j.jsx)(h,{present:e||g,children:(0,j.jsx)(B,{"data-state":g?"visible":"hidden",...f,ref:b})})}),B=d.forwardRef((a,b)=>{let{orientation:c="vertical",...e}=a,f=s(w,a.__scopeScrollArea),g=d.useRef(null),h=d.useRef(0),[i,k]=d.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),l=O(i.viewport,i.content),m={...e,sizes:i,onSizesChange:k,hasThumb:!!(l>0&&l<1),onThumbChange:a=>g.current=a,onThumbPointerUp:()=>h.current=0,onThumbPointerDown:a=>h.current=a};function n(a,b){return function(a,b,c,d="ltr"){let e=P(c),f=b||e/2,g=c.scrollbar.paddingStart+f,h=c.scrollbar.size-c.scrollbar.paddingEnd-(e-f),i=c.content-c.viewport;return R([g,h],"ltr"===d?[0,i]:[-1*i,0])(a)}(a,h.current,i,b)}return"horizontal"===c?(0,j.jsx)(C,{...m,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=Q(f.viewport.scrollLeft,i,f.dir);g.current.style.transform=`translate3d(${a}px, 0, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollLeft=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollLeft=n(a,f.dir))}}):"vertical"===c?(0,j.jsx)(D,{...m,ref:b,onThumbPositionChange:()=>{if(f.viewport&&g.current){let a=Q(f.viewport.scrollTop,i);g.current.style.transform=`translate3d(0, ${a}px, 0)`}},onWheelScroll:a=>{f.viewport&&(f.viewport.scrollTop=a)},onDragScroll:a=>{f.viewport&&(f.viewport.scrollTop=n(a))}}):null}),C=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...g}=a,h=s(w,a.__scopeScrollArea),[i,k]=d.useState(),l=d.useRef(null),m=(0,f.s)(b,l,h.onScrollbarXChange);return d.useEffect(()=>{l.current&&k(getComputedStyle(l.current))},[l]),(0,j.jsx)(G,{"data-orientation":"horizontal",...g,ref:m,sizes:c,style:{bottom:0,left:"rtl"===h.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===h.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.x),onDragScroll:b=>a.onDragScroll(b.x),onWheelScroll:(b,c)=>{if(h.viewport){let d=h.viewport.scrollLeft+b.deltaX;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{l.current&&h.viewport&&i&&e({content:h.viewport.scrollWidth,viewport:h.viewport.offsetWidth,scrollbar:{size:l.current.clientWidth,paddingStart:N(i.paddingLeft),paddingEnd:N(i.paddingRight)}})}})}),D=d.forwardRef((a,b)=>{let{sizes:c,onSizesChange:e,...g}=a,h=s(w,a.__scopeScrollArea),[i,k]=d.useState(),l=d.useRef(null),m=(0,f.s)(b,l,h.onScrollbarYChange);return d.useEffect(()=>{l.current&&k(getComputedStyle(l.current))},[l]),(0,j.jsx)(G,{"data-orientation":"vertical",...g,ref:m,sizes:c,style:{top:0,right:"ltr"===h.dir?0:void 0,left:"rtl"===h.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":P(c)+"px",...a.style},onThumbPointerDown:b=>a.onThumbPointerDown(b.y),onDragScroll:b=>a.onDragScroll(b.y),onWheelScroll:(b,c)=>{if(h.viewport){let d=h.viewport.scrollTop+b.deltaY;a.onWheelScroll(d),function(a,b){return a>0&&a<b}(d,c)&&b.preventDefault()}},onResize:()=>{l.current&&h.viewport&&i&&e({content:h.viewport.scrollHeight,viewport:h.viewport.offsetHeight,scrollbar:{size:l.current.clientHeight,paddingStart:N(i.paddingTop),paddingEnd:N(i.paddingBottom)}})}})}),[E,F]=p(w),G=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,sizes:g,hasThumb:h,onThumbChange:i,onThumbPointerUp:l,onThumbPointerDown:m,onThumbPositionChange:o,onDragScroll:p,onWheelScroll:q,onResize:r,...t}=a,u=s(w,c),[v,x]=d.useState(null),y=(0,f.s)(b,a=>x(a)),z=d.useRef(null),A=d.useRef(""),B=u.viewport,C=g.content-g.viewport,D=(0,k.c)(q),F=(0,k.c)(o),G=T(r,10);function H(a){z.current&&p({x:a.clientX-z.current.left,y:a.clientY-z.current.top})}return d.useEffect(()=>{let a=a=>{let b=a.target;v?.contains(b)&&D(a,C)};return document.addEventListener("wheel",a,{passive:!1}),()=>document.removeEventListener("wheel",a,{passive:!1})},[B,v,C,D]),d.useEffect(F,[g,F]),U(v,G),U(u.content,G),(0,j.jsx)(E,{scope:c,scrollbar:v,hasThumb:h,onThumbChange:(0,k.c)(i),onThumbPointerUp:(0,k.c)(l),onThumbPositionChange:F,onThumbPointerDown:(0,k.c)(m),children:(0,j.jsx)(e.sG.div,{...t,ref:y,style:{position:"absolute",...t.style},onPointerDown:(0,n.m)(a.onPointerDown,a=>{0===a.button&&(a.target.setPointerCapture(a.pointerId),z.current=v.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",u.viewport&&(u.viewport.style.scrollBehavior="auto"),H(a))}),onPointerMove:(0,n.m)(a.onPointerMove,H),onPointerUp:(0,n.m)(a.onPointerUp,a=>{let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),document.body.style.webkitUserSelect=A.current,u.viewport&&(u.viewport.style.scrollBehavior=""),z.current=null})})})}),H="ScrollAreaThumb",I=d.forwardRef((a,b)=>{let{forceMount:c,...d}=a,e=F(H,a.__scopeScrollArea);return(0,j.jsx)(h,{present:c||e.hasThumb,children:(0,j.jsx)(J,{ref:b,...d})})}),J=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,style:g,...h}=a,i=s(H,c),k=F(H,c),{onThumbPositionChange:l}=k,m=(0,f.s)(b,a=>k.onThumbChange(a)),o=d.useRef(),p=T(()=>{o.current&&(o.current(),o.current=void 0)},100);return d.useEffect(()=>{let a=i.viewport;if(a){let b=()=>{p(),o.current||(o.current=S(a,l),l())};return l(),a.addEventListener("scroll",b),()=>a.removeEventListener("scroll",b)}},[i.viewport,p,l]),(0,j.jsx)(e.sG.div,{"data-state":k.hasThumb?"visible":"hidden",...h,ref:m,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...g},onPointerDownCapture:(0,n.m)(a.onPointerDownCapture,a=>{let b=a.target.getBoundingClientRect(),c=a.clientX-b.left,d=a.clientY-b.top;k.onThumbPointerDown({x:c,y:d})}),onPointerUp:(0,n.m)(a.onPointerUp,k.onThumbPointerUp)})});I.displayName=H;var K="ScrollAreaCorner",L=d.forwardRef((a,b)=>{let c=s(K,a.__scopeScrollArea),d=!!(c.scrollbarX&&c.scrollbarY);return"scroll"!==c.type&&d?(0,j.jsx)(M,{...a,ref:b}):null});L.displayName=K;var M=d.forwardRef((a,b)=>{let{__scopeScrollArea:c,...f}=a,g=s(K,c),[h,i]=d.useState(0),[k,l]=d.useState(0),m=!!(h&&k);return U(g.scrollbarX,()=>{let a=g.scrollbarX?.offsetHeight||0;g.onCornerHeightChange(a),l(a)}),U(g.scrollbarY,()=>{let a=g.scrollbarY?.offsetWidth||0;g.onCornerWidthChange(a),i(a)}),m?(0,j.jsx)(e.sG.div,{...f,ref:b,style:{width:h,height:k,position:"absolute",right:"ltr"===g.dir?0:void 0,left:"rtl"===g.dir?0:void 0,bottom:0,...a.style}}):null});function N(a){return a?parseInt(a,10):0}function O(a,b){let c=a/b;return isNaN(c)?0:c}function P(a){let b=O(a.viewport,a.content),c=a.scrollbar.paddingStart+a.scrollbar.paddingEnd;return Math.max((a.scrollbar.size-c)*b,18)}function Q(a,b,c="ltr"){let d=P(b),e=b.scrollbar.paddingStart+b.scrollbar.paddingEnd,f=b.scrollbar.size-e,g=b.content-b.viewport,h=(0,m.q)(a,"ltr"===c?[0,g]:[-1*g,0]);return R([0,g],[0,f-d])(h)}function R(a,b){return c=>{if(a[0]===a[1]||b[0]===b[1])return b[0];let d=(b[1]-b[0])/(a[1]-a[0]);return b[0]+d*(c-a[0])}}var S=(a,b=()=>{})=>{let c={left:a.scrollLeft,top:a.scrollTop},d=0;return!function e(){let f={left:a.scrollLeft,top:a.scrollTop},g=c.left!==f.left,h=c.top!==f.top;(g||h)&&b(),c=f,d=window.requestAnimationFrame(e)}(),()=>window.cancelAnimationFrame(d)};function T(a,b){let c=(0,k.c)(a),e=d.useRef(0);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),d.useCallback(()=>{window.clearTimeout(e.current),e.current=window.setTimeout(c,b)},[c,b])}function U(a,b){let c=(0,k.c)(b);(0,g.N)(()=>{let b=0;if(a){let d=new ResizeObserver(()=>{cancelAnimationFrame(b),b=window.requestAnimationFrame(c)});return d.observe(a),()=>{window.cancelAnimationFrame(b),d.unobserve(a)}}},[a,c])}var V=t,W=v,X=L},75074:(a,b,c)=>{c.d(b,{A:()=>g});var d=c(61120),e=c(67133),f=c(84757),g=(0,d.cache)(async function(a){let b,c;"string"==typeof a?b=a:a&&(c=a.locale,b=a.namespace);let d=await (0,f.A)(c);return(0,e.HM)({...d,namespace:b,messages:d.messages})})}};