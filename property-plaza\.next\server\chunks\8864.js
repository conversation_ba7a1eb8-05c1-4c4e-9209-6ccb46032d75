"use strict";exports.id=8864,exports.ids=[8864],exports.modules={38864:(a,b,c)=>{c.r(b),c.d(b,{default:()=>h});var d=c(37413),e=c(90388),f=c(34557),g=c(44999);async function h(){let a=(0,g.UL)(),b=a.get("NEXT_LOCALE")?.value,c=await (0,e.Bi)(b?.toLowerCase()||"en");return(0,d.jsx)(f.default,{content:c[0]})}},90388:(a,b,c)=>{c.d(b,{KP:()=>u,Bi:()=>y,gF:()=>x,N7:()=>w,K5:()=>v,xE:()=>A,Dc:()=>z,hD:()=>B,fx:()=>s});var d=c(23777),e=c.n(d),f=c(16664);let g={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var h=c(98810);let i=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`,j=(0,h.A)`*[_type == "post" && author != "hidden"] ${i}`,k=(0,h.A)`*[_type == "post" && author != "hidden"][0...2] ${i}`,l=(0,h.A)`*[_type == "post" && slug.current == $slug][0]  ${i}
  

`;(0,h.A)`*[_type == "post" && $slug in tags[]->slug.current] ${i}`,(0,h.A)`*[_type == "post" && author->slug.current == $slug] ${i}`,(0,h.A)`*[_type == "post" && category->slug.current == $slug] ${i}`;let m=(0,h.A)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${i}
  `,n=(0,h.A)`*[_type == "seoContent" && language == $language]{title,body}`,o=(0,h.A)`*[_type == "termsOfUse" && language == $language]{title,body}`,p=(0,h.A)`*[_type == "privacyPolicy" && language == $language]{title,body}`,q=(0,h.A)`*[_type == "userDataDeletion" && language == $language]{title,body}`,r=(0,f.UU)(g);function s(a){return e()(g).image(a)}async function t({query:a,qParams:b,tags:c}){return r.fetch(a,b,{next:{tags:c,revalidate:3600}})}let u=async()=>await t({query:k,qParams:{},tags:["post","author","category"]}),v=async()=>await t({query:j,qParams:{},tags:["post","author","category"]}),w=async a=>await t({query:l,qParams:{slug:a},tags:["post","author","category"]}),x=async(a,b)=>await t({query:m,qParams:{slug:a,id:b},tags:[]}),y=async a=>await t({query:n,qParams:{language:a},tags:[]}),z=async a=>await t({query:o,qParams:{language:a},tags:[]}),A=async a=>await t({query:p,qParams:{language:a},tags:[]}),B=async a=>await t({query:q,qParams:{language:a},tags:[]})}};