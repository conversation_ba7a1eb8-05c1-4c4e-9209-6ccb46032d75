exports.id=9737,exports.ids=[9737],exports.modules={755:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(41876),f=c(37826),g=c(73037),h=c(96241);function i({children:a,className:b}){return(0,e.U)("(min-width:1024px)")?(0,d.jsx)(f.Es,{className:(0,h.cn)("px-0",b),children:a}):(0,d.jsx)(g.tb,{className:(0,h.cn)("px-0",b),children:a})}},4e3:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687),e=c(41876),f=c(37826),g=c(73037);function h({children:a,className:b}){return(0,e.U)("(min-width:1024px)")?(0,d.jsx)(f.c7,{className:b,children:a}):(0,d.jsx)(g.BE,{className:b,children:a})}},5395:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687),e=c(96241);function f(a){return(0,d.jsx)("div",{...a,ref:a.ref,className:(0,e.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",a.className),children:a.children})}},5698:(a,b,c)=>{"use strict";c.d(b,{d:()=>e});var d=c(43210);let e=(a,b=500)=>{let[c,e]=(0,d.useState)(a);return(0,d.useEffect)(()=>{let c=setTimeout(()=>{e(a)},b);return()=>{clearTimeout(c)}},[a,b]),c}},9216:(a,b,c)=>{"use strict";c.d(b,{i8:()=>g});var d=c(16853),e=c(15405);c(58674);var f=c(27071);async function g(a){try{var b=(await (0,d.jp)(a)).data.data;let c=(0,e.x_)(b.accounts.subscription?.detail.name||"");return{accounts:{about:b.accounts.about,citizenship:b.accounts.citizenship||"",credit:{amount:b.accounts.credit?.amount||0,updatedAt:b.accounts.credit?.updated_at||""},facebookSocial:b.accounts.facebook_social||"",firstName:b.accounts.first_name,image:b.accounts.image,isSubscriber:b.accounts.is_subscriber,language:b.accounts.language,lastName:b.accounts.last_name,membership:c,twitterSocial:b.accounts.twitter_social||"",address:b.accounts.address||"",chat:{current:0,max:(0,e.cQ)(c)},zoomFeature:(0,e.lL)(c)},has2FA:b.is_2fa,email:b.email,code:b.code,isActive:b.is_active,phoneNumber:b.phone_number,phoneCode:b.phone_code,type:b.type,setting:{messageNotif:b.accounts.settings?.message_notif,newsletterNotif:b.accounts.settings?.newsletter_notif,priceAlertNotif:b.accounts.settings?.price_alert_notif,propertyNotif:b.accounts.settings?.property_notif,soundNotif:b.accounts.settings?.sound_notif,specialOfferNotif:b.accounts.settings?.special_offer_notif,surveyNotif:b.accounts.settings?.survey_notif}}}catch(a){throw Error((0,f.Q)(a))}}},11970:(a,b,c)=>{"use strict";c.d(b,{I$:()=>f,KA:()=>k,QS:()=>e,Uw:()=>g,_y:()=>j,b:()=>i,ed:()=>h});var d=c(66595);c(15537);let e=a=>d.apiClient.post("properties/favorite",a),f=a=>(0,d.apiClient)(`/properties/filter-location?search=${a.search}`),g=a=>d.apiClient.post("properties/filter",a),h=()=>d.apiClient.get("filter-parameter"),i=({page:a,per_page:b,search:c,sort_by:e})=>d.apiClient.get(`users/favorite?page=${a}&per_page=${b}&search=${c}&sort_by=${e}`),j=a=>d.apiClient.put("users/filter-setting",a),k=a=>d.apiClient.post("properties/batch-property",a)},11976:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(37826),f=c(41876),g=c(96241);c(43210);var h=c(73037);function i({children:a,openTrigger:b,open:c,setOpen:i,dialogClassName:j,drawerClassName:k,dialogOverlayClassName:l}){return(0,f.U)("(min-width:1024px)")?(0,d.jsxs)(e.lG,{open:c,onOpenChange:i,children:[(0,d.jsx)(e.zM,{asChild:!0,children:b}),(0,d.jsxs)(e.ZJ,{children:[(0,d.jsx)(e.LC,{className:l}),(0,d.jsx)(e.Cf,{className:(0,g.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",j),children:a})]})]}):(0,d.jsxs)(h._s,{open:c,onOpenChange:i,children:[(0,d.jsx)(h.Uz,{asChild:!0,children:b}),(0,d.jsx)(h.zj,{children:(0,d.jsx)("div",{className:(0,g.cn)("p-4 overflow-auto",k),children:a})})]})}},15246:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SuiVK1jFWxjFVjqnREAAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}},15405:(a,b,c)=>{"use strict";c.d(b,{cQ:()=>f,lL:()=>i,vN:()=>h,wJ:()=>g,x_:()=>e});var d=c(62112);function e(a){return d.U$.free.includes(a)?d.U$.free:d.U$.finder.includes(a)?d.U$.finder:d.U$.archiver.includes(a)?d.U$.archiver:d.U$.free}function f(a){return a==d.U$.free?0:a==d.U$.finder?5:10*(a==d.U$.archiver)}let g=10,h={max:13,min:10};function i(a){return a==d.U$.free?h:a==d.U$.finder?{max:14,min:g}:a==d.U$.archiver?{max:15,min:g}:h}},15537:(a,b,c)=>{},16853:(a,b,c)=>{"use strict";c.d(b,{DY:()=>e,jp:()=>g,yN:()=>f});var d=c(66595);let e=async(a,b)=>d.apiClient.post("auth/register",a,{headers:{"g-token":b||""}}),f=async a=>d.apiClient.put("users/update",a),g=async a=>d.apiClient.get("auth/me",a)},19421:(a,b,c)=>{"use strict";c.d(b,{v:()=>f});var d=c(16648),e=c(54050);function f(){return(0,e.n)({mutationFn:a=>(0,d.aH)(a)})}},19743:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(16189),e=c(43210),f=c(61261);function g(){let a=(0,f.useRouter)(),b=(0,d.usePathname)(),c=(0,d.useSearchParams)(),g=(0,e.useCallback)(d=>{let e=new URLSearchParams(c.toString());d.forEach(a=>e.set(a.name,a.value)),a.push(b+"?"+e.toString())},[c,a,b]),h=(0,e.useCallback)((a,b)=>{let d=new URLSearchParams(c.toString());return d.set(a,b),d.toString()},[c]);return{searchParams:c,createQueryString:(d,e)=>{let f=new URLSearchParams(c.toString());f.set(d,e),a.push(b+"?"+f.toString())},generateQueryString:h,removeQueryParam:(b,d)=>{let e=new URLSearchParams(c.toString());b.forEach(a=>{e.delete(a)});let f=`${window.location.pathname}?${e.toString()}`;if(d)return window.location.href=f;a.push(f)},createMultipleQueryString:g,pathname:b,updateQuery:(d,e)=>{let f=new URLSearchParams(c.toString());f.set(d,e),a.push(b+"?"+f.toString())}}}},19791:(a,b,c)=>{"use strict";c.d(b,{DW:()=>h,Eq:()=>i,Nx:()=>k,VZ:()=>o,Zs:()=>n,ch:()=>l,dA:()=>g,gA:()=>j,i1:()=>p,jd:()=>m,yA:()=>q}),c(60687);var d=c(65777),e=c(49212),f=c(94574);let g="/owner/account",h="/profile",i="/s",j="/favorites",k="/message",l="/subscription",m="/plan",n="/billing",o="/notification",p="/security",q="/representative/account";d.A,e.A,f.A,d.A,e.A},25842:(a,b,c)=>{"use strict";c.d(b,{default:()=>aB});var d=c(60687),e=c(87466),f=c(5395),g=c(30474),h=c(15246),i=c(24934),j=c(99270),k=c(12941),l=c(43210),m=c(88920),n=c(97905),o=c(40670),p=c(96241),q=c(63974),r=c(43190);let s=[{id:"1",content:"IDR",value:"IDR"},{id:"2",content:"EUR",value:"EUR"},{id:"3",content:"GBP",value:"GBP"},{id:"4",content:"AUD",value:"AUD"},{id:"5",content:"USD",value:"USD"}],t=(0,l.forwardRef)(({triggerClassName:a,showCaret:b=!1,defaultCurrency:c="IDR",onClick:e},f)=>{let{currency:g,setCurrency:h,isLoading:i}=(0,r.M)(),[j,k]=(0,l.useState)(c),[m,n]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{if(i)return k(c);k(g)},[g,i,c]),(0,d.jsx)("div",{className:"max-sm:w-fit w-full",children:(0,d.jsxs)(q.l6,{defaultValue:c,value:j,onValueChange:h,open:m,onOpenChange:a=>{n(a),a&&e&&e(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,d.jsx)(q.bq,{ref:f,showCaret:b,className:`rounded-full border flex items-center justify-center border-seekers-text-lighter shadow-md h-10 px-2 !w-full ${a}`,onClick:a=>{a.stopPropagation(),e?.(a)},children:(0,d.jsx)(q.yv,{className:"text-xs text-center"})}),(0,d.jsx)(q.gC,{children:s.map(a=>(0,d.jsx)(q.eb,{value:a.value,children:a.content},a.id))})]})})});t.displayName="CurrencyForm";var u=c(2469);function v({code:a}){let b=u[a];return(0,d.jsx)(b,{className:"border border-colortext-foreground rounded-full w-4 h-4 aspect-square my-auto"})}var w=c(33213),x=c(42277),y=c(21466),z=c(26973);(0,y.M6)(async({requestLocale:a})=>{let b=await a;return{locale:await a,messages:(await c(81830)(`./${b}.json`)).default,defaultLocale:z.DT.defaultLocale,locales:z.DT.locales}});let{Link:A,redirect:B,usePathname:C,useRouter:D}=(0,x.xp)({defaultLocale:"en",locales:["en","id"],pathnames:{"/":"/","/pathnames":{en:"/pathnames",de:"/pfadnamen",id:"/nama-jalur"}},localePrefix:"as-needed"});var E=c(19743);let F=(0,l.forwardRef)((a,b)=>{let{triggerClassName:c,showCaret:e=!1,defaultValue:f="en",onClick:g}=a,{changeLanguage:h,locale:i}=function(){let a=(0,w.useLocale)(),b=D(),c=C(),{generateQueryString:d}=(0,E.A)();return{changeLanguage:a=>{let e=d("","");(0,l.startTransition)(()=>{b.replace(c+"?"+e,{locale:a}),b.refresh()})},locale:a}}(),j=[{id:"2",content:(0,d.jsx)(v,{code:"US"}),value:"EN"},{id:"1",content:(0,d.jsx)(v,{code:"ID"}),value:"ID"}],[k,m]=(0,l.useState)(!1);return(0,d.jsx)("div",{className:"max-sm:w-fit w-full",onClick:a=>a.stopPropagation(),children:(0,d.jsxs)(q.l6,{defaultValue:f,onValueChange:h,value:i.toUpperCase(),open:k,onOpenChange:a=>{m(a),a&&g&&g(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,d.jsx)(q.bq,{ref:b,showCaret:e,className:`rounded-full border border-seekers-text-lighter shadow-md h-10 w-14 px-2 flex items-center justify-center ${c}`,onClick:a=>{a.stopPropagation(),g?.(a)},children:(0,d.jsx)(q.yv,{className:"text-xs"})}),(0,d.jsx)(q.gC,{children:j.map(a=>(0,d.jsx)(q.eb,{className:"",value:a.value,children:a.content},a.id))})]})})});F.displayName="SeekersLocaleForm";var G=c(15659),H=c(58674),I=c(66835),J=c(16648),K=c(8693),L=c(54050),M=c(77273),N=c(11976),O=c(4e3),P=c(37826),Q=c(55629),R=c(44824),S=c(72820),T=c(19791),U=c(85814),V=c.n(U);function W({localeId:a="EN",currency_:b="EUR"}){let c=(0,l.useRef)(null),e=(0,l.useRef)(null),f=(0,l.useRef)(null),g=(0,I.k)(a=>a.role),[h,i]=(0,l.useState)(!1),[j,k]=(0,l.useState)(0),[m,o]=(0,l.useState)(null);return(0,d.jsxs)("div",{ref:c,className:"flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]",children:[(0,d.jsx)("div",{className:"w-fit",children:(0,d.jsx)(n.P.div,{className:"overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex",initial:{width:"110px"},animate:{width:h?"166px":"112px"},transition:{duration:.1},children:(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 py-2 w-full",children:[(0,d.jsx)(t,{triggerClassName:(0,p.cn)("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0",h?"w-full":"pl-3 max-w-[48px]"),defaultCurrency:b,ref:f,onClick:a=>{a.stopPropagation(),o("currency"),i(!0)},showCaret:h&&"currency"===m}),(0,d.jsx)("div",{className:"w-[2px] h-[24px] bg-seekers-text-lighter"}),(0,d.jsx)(F,{triggerClassName:(0,p.cn)("rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",h?"w-full":"pl-2 max-w-[32px]"),defaultValue:a,ref:e,onClick:a=>{a.stopPropagation(),o("language"),i(!0)},showCaret:h&&"language"===m})]})})}),(0,d.jsx)(d.Fragment,{children:G.A.get(H.Xh)&&"SEEKER"==g?(0,d.jsx)(X,{trigger:(0,d.jsx)("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10",children:(0,d.jsx)(R.A,{url:""})})}):(0,d.jsx)("div",{children:(0,d.jsx)(S.default,{triggerClassName:(0,p.cn)("!w-10 rounded-full overflow-hidden")})})})]})}function X({trigger:a}){let b=(0,w.useTranslations)("seeker");return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(Q.rI,{modal:!1,children:[(0,d.jsx)(Q.ty,{asChild:!0,children:a}),(0,d.jsxs)(Q.SQ,{align:"end",className:"!w-[256px]",children:[(0,d.jsx)(Q._2,{asChild:!0,children:(0,d.jsx)(V(),{href:T.DW,children:b("accountAndProfile.profile")})}),(0,d.jsx)(Q._2,{asChild:!0,children:(0,d.jsx)(V(),{href:T.gA,children:b("accountAndProfile.favorite")})}),(0,d.jsx)(Q._2,{className:"w-full",asChild:!0,children:(0,d.jsx)(V(),{href:T.Nx,children:(0,d.jsx)("div",{className:"flex justify-between items-center w-full ",children:b("accountAndProfile.message")})})}),(0,d.jsx)(Q._2,{onClick:a=>{a.preventDefault();let b=document.getElementById("open-logout-dialog");b?.click()},children:b("accountAndProfile.logout.title")})]})]}),(0,d.jsx)(Y,{trigger:(0,d.jsx)("button",{id:"open-logout-dialog"})})]})}function Y({trigger:a}){let[b,c]=(0,l.useState)(!1),e=function(a="owner"){let b=(0,K.jE)();return(0,L.n)({mutationFn:()=>(0,J.ri)(),onSuccess:()=>{G.A.remove(H.Xh),G.A.remove("user"),b.invalidateQueries({queryKey:[M.g],refetchType:"none"}),window.location.assign("/")},onError:a=>{G.A.remove(H.Xh),G.A.remove("user"),window.location.assign("/")}})}("seekers"),f=(0,w.useTranslations)("seeker");return(0,d.jsxs)(N.A,{open:b,setOpen:c,openTrigger:a,dialogClassName:"max-w-md",children:[(0,d.jsxs)(O.A,{className:"text-start px-0",children:[(0,d.jsx)("h2",{className:"max-sm:text-center font-semibold",children:f("accountAndProfile.logout.title")}),(0,d.jsx)("p",{className:"max-sm:text-center max-sm:mb-4",children:f("owner.accountAndProfile.logout.description")})]}),(0,d.jsxs)(P.Es,{children:[(0,d.jsx)(i.$,{variant:"default-seekers",loading:e.isPending,className:"min-w-20 max-sm:order-last",onClick:()=>c(!1),children:f("cta.cancel")}),(0,d.jsx)(i.$,{variant:"ghost",onClick:()=>{if(!G.A.get(H.Xh))return void window.location.assign("");e.mutate()},loading:e.isPending,className:"min-w-20",children:f("cta.logout")})]})]})}var Z=c(62692);let $=Z.bL;Z.l9;let _=Z.Mz,aa=l.forwardRef(({className:a,align:b="center",sideOffset:c=4,...e},f)=>(0,d.jsx)(Z.ZL,{children:(0,d.jsx)(Z.UC,{ref:f,align:b,sideOffset:c,className:(0,p.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...e})}));aa.displayName=Z.UC.displayName;var ab=c(39390),ac=c(68988),ad=c(11860),ae=c(54817),af=c(29494),ag=c(5698),ah=c(14952),ai=c(28559),aj=c(13964),ak=c(97992);let al={src:"/_next/static/media/canggu.84e6fbe6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUUeIgAAAACXRSTlMBCyAwTkBmWH4H4C9lAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAMklEQVR4nCXLuQ0AMAzDQFryt//EgRHWR4CI4FJWGvBkTwHl7rJO7KZ0Zkels47wff99FjIAmG4RX6UAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},am={src:"/_next/static/media/ubud.81668090.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlNrAVtEKhuADk09jhuwZCAAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAySURBVHicBcEHAQAwDMMwJ/3jD3gSKo8kMU4rYyiqw3EU2diBDjck2uQ1Yu04PwjYBH0dsQDRzN90KwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},an={src:"/_next/static/media/nusa-dua.9acfd1fe.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAABMaXEAAAAAAAAAAAAAAAAAAABReBoRAAAACXRSTlNsU34AN11hPRckhWnFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAMklEQVR4nB3JQRLAMAwCsSWAnf+/uNPoKmwntvFNm2vMY3QKRWwlicUZafLXwiLmPPMBGmcAtF10IQcAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},ao={src:"/_next/static/media/uluwatu.71df2404.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAABMaXEAAAAAAAAKNf92AAAACnRSTlM5b3xVi2MwACERUm+ZFgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAADNJREFUeJwFwYcBADAMwjBDBun/D1ciESgh17t9IaVdVXie3fEDNbSgzB0u1Aa3mPKMaz4l7wDv+DrveAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},ap={src:"/_next/static/media/seminyak.639fb2f5.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlMCQTd4Wx9LESZpobATef0AAAAJcEhZcwAADsQAAA7EAZUrDhsAAAA1SURBVHicFcm3EcBAEAOxJc+r/4I1jxSApxtAEfEB3s2LApciLoGxUqqXbpVhpO59V5mn+QEcVADSvRMKNQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8};function aq({locationName:a}){switch(a){case"canggu":return(0,d.jsx)(g.default,{src:al,alt:"canggu",width:36,className:"aspect-square "});case"ubud":return(0,d.jsx)(g.default,{src:am,alt:"ubud",width:32,className:"aspect-square "});case"seminyak":return(0,d.jsx)(g.default,{src:ap,alt:"Seminyak",width:32,className:"aspect-square"});case"uluwatu":return(0,d.jsx)(g.default,{src:ao,alt:"uluwatu",width:32,className:"aspect-square "});case"Nusa Dua":return(0,d.jsx)(g.default,{src:an,alt:"nusa dua",width:32,className:"aspect-square "});default:return(0,d.jsx)(d.Fragment,{})}}function ar({showContent:a}){let b=(0,w.useTranslations)("seeker"),{query:c}=(0,o.o)(a=>a),f=function(a){let{search:b}=a;return(0,af.I)({queryKey:["location-suggestion",b],queryFn:async()=>await (0,ae.xn)(a),retry:!1})}({search:(0,ag.d)(c,500)}),{handleSetQuery:g,seekersSearch:h,banjars:i,showBanjars:j,selectedLocation:k,handleSelectLocation:l,handleBackToLocations:m,handleSetBanjar:n,filteredLocations:p,getMatchingBanjars:q}=(0,e.A)();return(0,d.jsxs)(d.Fragment,{children:[j?(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4  px-3 max-sm:px-0",children:[(0,d.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),m()},className:"text-seekers-text-light hover:text-seekers-text",children:(0,d.jsx)(ai.A,{className:"h-4 w-4"})}),(0,d.jsx)("span",{className:"font-medium capitalize",children:k})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4 px-3 max-sm:px-0",children:i[k].map(a=>(0,d.jsx)("div",{className:"relative ",children:(0,d.jsxs)("button",{onClick:b=>{b.preventDefault(),b.stopPropagation(),n(a)},className:`w-full border border-gray-200 rounded-full py-3 px-4 flex items-center gap-3
                                      ${h.query.includes(a)?"bg-gray-50":"bg-white"}`,children:[(0,d.jsx)("div",{className:`w-4 h-4 rounded-full border flex items-center justify-center
                                      ${h.query.includes(a)?"border-seekers-primary bg-seekers-primary":"border-gray-300"}`,children:h.query.includes(a)&&(0,d.jsx)(aj.A,{className:"h-3 w-3 text-white"})}),(0,d.jsx)("span",{className:"text-sm text-seekers-text-light",children:a})]})},a))})]})}):(0,d.jsx)(d.Fragment,{children:p.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{children:b("misc.region")}),p.map(a=>{let b=a.name.replace(", Bali",""),c=i[a.value]?.length>0;return(0,d.jsx)("div",{children:(0,d.jsxs)("button",{className:"w-full flex items-center justify-between p-3 hover:bg-gray-100 transition-colors rounded-lg",onClick:b=>{b.preventDefault(),b.stopPropagation(),c?l(a.value):g(a.value)},children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(aq,{locationName:a.value}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("div",{className:"font-medium",children:b}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.description})]})]}),c&&(0,d.jsx)(ah.A,{className:"h-4 w-4 text-gray-400"})]})},a.value)})]})}),c.length>=3&&!j&&(p.some(a=>q(a.value).length>0)||(f.data?.data?.length||0)>0)&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"text-xs text-gray-500 font-medium mt-4 mb-2 px-3",children:b("misc.areas")}),(0,d.jsxs)("div",{children:[p.map(a=>{let b=q(a.value);return 0===b.length?null:b.map(b=>(0,d.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{n(b,!0)},children:[(0,d.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,d.jsx)(ak.A,{className:"w-4 h-4 text-seekers-primary"})}),(0,d.jsxs)("div",{className:"text-left",children:[(0,d.jsx)("div",{className:"font-medium",children:b}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.name})]})]},`${a.name}-${b}`))}),f.data?.data?.map((a,b)=>(0,d.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{n(a)},children:[(0,d.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,d.jsx)(ak.A,{className:"w-4 h-4 text-seekers-primary"})}),(0,d.jsx)("div",{className:"text-left",children:(0,d.jsx)("div",{className:"font-medium",children:a})})]},b))]})]})]})}function as({customTrigger:a,isUseAnimation:b=!0}){let c=(0,w.useTranslations)("seeker"),[f,g]=(0,l.useState)(!1),{isOpen:h,setLocationInputFocused:j,query:k}=(0,o.o)(a=>a),[m,q]=(0,l.useState)(!0),{handleSetQuery:r,seekersSearch:s,handleSearch:t}=(0,e.A)(),u=(0,l.useRef)(null),v=a=>{g(a),j(a)};return(0,d.jsx)("div",{className:(0,p.cn)(b?h?"w-full":"w-fit":"w-full"),onClick:()=>{v(!0),u.current?.focus()},children:(0,d.jsxs)($,{open:f&&m,onOpenChange:v,children:[a?(0,d.jsx)(_,{asChild:!0,children:a}):(0,d.jsx)(_,{className:"w-full px-4",children:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(ab.J,{className:"text-xs font-medium text-seekers-text",children:c("navbar.search.locationTitle")}),(0,d.jsxs)(n.P.div,{animate:{height:20*!!h,opacity:100*!!h,width:h?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,d.jsx)(ac.p,{ref:u,onFocus:a=>v(!0),onChange:a=>{r(a.target.value),q(!0)},value:k,placeholder:c("form.placeholder.seekersFindPropertyLocation"),onKeyDown:a=>{a.stopPropagation(),"Enter"===a.key&&(t(),g(!1))},className:"border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b w-full rounded-none pb-2 !p-0 h-fit"}),(0,d.jsx)(i.$,{variant:"ghost",onClick:a=>{a.stopPropagation(),a.preventDefault(),s.setQuery("")},size:"icon",className:(0,p.cn)("-mt-2",s.query.length>0?"":"hidden"),children:(0,d.jsx)(ad.A,{})})]})]})}),(0,d.jsx)(aa,{className:"w-full border-seekers-text-lighter/20",align:"start",onOpenAutoFocus:a=>a.preventDefault(),children:(0,d.jsx)(ar,{showContent:q})})]})})}var at=c(78272),au=c(755),av=c(27317),aw=c(89637);function ax(){let a=(0,w.useTranslations)("seeker"),{handleSearch:b}=(0,e.A)(),[c,f]=(0,l.useState)(!1),[g,h]=(0,l.useState)("location"),{query:k}=(0,o.o)(a=>a),{handleSetType:m,seekersSearch:n,propertyType:q,handleSetQuery:r}=(0,e.A)();return(0,ag.d)(k,500),(0,d.jsxs)(N.A,{open:c,setOpen:f,drawerClassName:"relative",openTrigger:(0,d.jsxs)("div",{className:"w-full border h-10 pl-4 pr-1 flex items-center justify-between text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md",children:[(0,d.jsx)("span",{className:"line-clamp-1",children:a("listing.search.placeholder")}),(0,d.jsx)(i.$,{variant:"default-seekers",className:"rounded-full !h-8 !w-[2.25rem]",size:"icon",children:(0,d.jsx)(j.A,{className:"!w-4 !h-4",strokeWidth:3})})]}),children:[(0,d.jsxs)("div",{className:"flex flex-col h-[calc(80vh-24px)] pb-16",children:[(0,d.jsxs)("div",{className:"flex-shrink-0 bg-white z-10 border-b",children:[(0,d.jsxs)(O.A,{className:"px-0 !text-center",children:[(0,d.jsx)(av.A,{className:"font-semibold p-0",children:a("listing.search.title")}),(0,d.jsx)(P.rr,{children:a("misc.findYourPerfectProperty")})]}),(0,d.jsxs)("div",{className:"px-4 mb-4 relative",children:[(0,d.jsx)(ac.p,{type:"text",placeholder:"Search destinations",className:"w-full px-3 py-2 !text-sm !h-10",value:k,onChange:a=>{r(a.target.value)},onKeyDown:a=>{a.stopPropagation(),"Enter"===a.key&&(b(),f(!1))}}),(0,d.jsx)(ad.A,{className:"w-4 h-4 absolute right-7 top-1/2 -translate-y-1/2 text-seekers-text-light",onClick:()=>r("")})]})]}),(0,d.jsx)("div",{className:"flex-grow overflow-y-auto",children:(0,d.jsxs)("div",{className:"p-4 space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>h("location"),children:[(0,d.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:a("navbar.search.locationTitle")}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,d.jsx)(at.A,{className:(0,p.cn)("h-4 w-4 transition-transform","location"==g?"transform rotate-180":"")})})]}),"location"==g&&(0,d.jsx)(ar,{})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>h("category"),children:[(0,d.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:a("navbar.search.category")}),(0,d.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,d.jsx)(at.A,{className:(0,p.cn)("h-4 w-4 transition-transform","category"==g?"transform rotate-180":"")})})]}),"category"==g&&(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:q.map(a=>(0,d.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   p-4   relative   border   rounded-lg   ",onClick:b=>{b.preventDefault(),b.stopPropagation(),m(a.value)},children:[(0,d.jsx)("div",{className:(0,p.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",n.propertyType.includes(a.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,d.jsx)(aj.A,{className:(0,p.cn)(n.propertyType.includes(a.value)?"w-3 h-3 text-white":"hidden")})}),(0,d.jsx)(aw.A,{category:a.value,className:"!w-6 !h-6"}),(0,d.jsx)("span",{className:"text-center",children:a.content})]},a.id))})]})]})})]}),(0,d.jsx)(au.A,{className:"absolute bottom-0 w-[calc(100%-32px)]",children:(0,d.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,d.jsx)(i.$,{variant:"link",className:"px-0 text-seekers-primary",onClick:()=>f(!1),children:a("cta.clearAll")}),(0,d.jsxs)(i.$,{className:"flex gap-2",variant:"default-seekers",onClick:()=>{b(),f(!1)},children:[(0,d.jsx)(j.A,{}),a("cta.search")]})]})})]})}var ay=c(87616);function az({value:a,textOnly:b=!1}){let c=(0,w.useTranslations)("seeker"),{propertyTypeFormatHelper:f}=(0,e.A)(),g=f(a.split(","));if(a.includes("all"))if(b)return c("listing.filter.category.all.title");else return(0,d.jsx)("p",{children:c("listing.filter.category.all.title")});return b?g.toString().replace(",",` ${c("conjuntion.and")} `):(0,d.jsx)(d.Fragment,{children:g.length>2?(0,d.jsx)(ay.A,{trigger:(0,d.jsx)("div",{children:(0,d.jsxs)("p",{children:[g[0]," ",c("conjuntion.and")," ",(0,d.jsxs)("span",{children:["+ ",g.length-1," ",c("misc.more")]})]})}),content:g.toString().replaceAll(",",", "),contentClassName:"text-seekers-text"}):(0,d.jsx)("div",{children:(0,d.jsx)("p",{children:g.toString().replace(",",` ${c("conjuntion.and")} `)})})})}function aA({customTrigger:a}){let b=(0,w.useTranslations)(),[c,f]=(0,l.useState)(!1),{isOpen:g,setCategoryInputFocused:h}=(0,o.o)(a=>a),{handleSetType:j,seekersSearch:k,propertyType:m}=(0,e.A)();return(0,d.jsxs)(Q.rI,{modal:!1,open:c,onOpenChange:a=>{f(a),h(a)},children:[(0,d.jsx)(Q.ty,{asChild:!0,children:a||(0,d.jsxs)("div",{className:(0,p.cn)("px-2",g?"w-full":"w-0"),children:[(0,d.jsx)(ab.J,{className:"text-xs font-medium text-seekers-text",children:b("seeker.navbar.search.category")}),(0,d.jsxs)(n.P.div,{animate:{height:20*!!g,opacity:100*!!g,width:g?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,d.jsx)(i.$,{variant:"ghost",className:"w-full h-fit font-normal p-0 overflow-hidden justify-start hover:bg-transparent",children:k.propertyType.length<1?(0,d.jsx)("p",{className:"text-seekers-text-lighter",children:b("seeker.navbar.search.propertyType")}):(0,d.jsx)(az,{value:k.propertyType.toString()})}),(0,d.jsx)(i.$,{variant:"ghost",onClick:a=>{a.stopPropagation(),a.preventDefault(),k.clearCategory()},size:"icon",className:(0,p.cn)("-mt-2",k.propertyType.length>0?"":"hidden"),children:(0,d.jsx)(ad.A,{})})]})]})}),(0,d.jsx)(Q.SQ,{className:(0,p.cn)("border-seekers-text-lighter/20 grid grid-cols-2 sm:grid-cols-3 gap-3 p-4",g?"w-fit":"w-0"),align:"start",children:m.map(a=>(0,d.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   md:w-28 p-4   relative   border   rounded-lg   ",onClick:b=>{b.preventDefault(),b.stopPropagation(),j(a.value)},"data-inside-dropdown":!0,children:[(0,d.jsx)("div",{className:(0,p.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",k.propertyType.includes(a.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,d.jsx)(aj.A,{className:(0,p.cn)(k.propertyType.includes(a.value)?"w-3 h-3 text-white":"hidden")})}),(0,d.jsx)(aw.A,{category:a.value,className:"!w-6 !h-6"}),(0,d.jsx)("span",{className:"text-center",children:a.content})]},a.id))})]})}function aB({localeId:a="EN",currency_:b="EUR"}){let{handleSearch:c}=(0,e.A)(),[q,r]=(0,l.useState)(!1),s=(0,l.useRef)(null),t=(0,l.useRef)(null),{isOpen:u,setIsOpen:v,categoryInputFocused:w,locationInputFocused:x}=(0,o.o)(a=>a);return(0,d.jsx)(m.N,{children:(0,d.jsxs)("nav",{ref:t,className:"w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]",children:[(0,d.jsx)(f.A,{className:"!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ",children:(0,d.jsxs)("div",{className:"w-full flex justify-between items-center flex-wrap gap-y-6",children:[(0,d.jsx)(A,{href:"/",children:(0,d.jsx)(g.default,{src:h.default,alt:"Property-Plaza",width:164,height:24})}),(0,d.jsxs)(n.P.div,{className:"flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4",initial:{opacity:1,width:"60%"},animate:{width:u?"60%":"30%"},transition:{duration:.3},children:[(0,d.jsxs)("div",{className:"flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter",children:[(0,d.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pr-8",children:(0,d.jsx)(as,{})}),(0,d.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pl-8",children:(0,d.jsx)(aA,{})})]}),(0,d.jsx)(n.P.div,{initial:{height:48,width:48},animate:{height:u?48:36,width:u?48:36},transition:{duration:.3},children:(0,d.jsx)(i.$,{variant:"default-seekers",onClick:()=>c(),className:"rounded-full w-full h-full !aspect-square",size:"icon",children:(0,d.jsx)(j.A,{className:"!w-5 !h-5",strokeWidth:3})})})]}),(0,d.jsx)("div",{className:"lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2",children:(0,d.jsx)(ax,{})}),(0,d.jsx)("div",{className:"md:hidden flex gap-1 w-[164px] justify-end",children:(0,d.jsx)(i.$,{variant:"ghost",className:"px-0 pl-4",onClick:()=>r(a=>!a),children:(0,d.jsx)(k.A,{className:"!h-6 !w-6"})})}),(0,d.jsx)("div",{className:"max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]",children:(0,d.jsx)(W,{currency_:b,localeId:a})})]})}),(0,d.jsx)("div",{className:(0,p.cn)(q?"fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0":"hidden")}),(0,d.jsx)("div",{ref:s,className:`absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ${q?"h-fit  py-4 px-4":"h-0"} overflow-hidden transition-all ease-linear duration-75 transform`,children:(0,d.jsx)(W,{currency_:b,localeId:a})})]})})}},26973:(a,b,c)=>{"use strict";c.d(b,{DT:()=>e,N_:()=>f,a8:()=>h});var d=c(42277);let e={locales:["en","id"],defaultLocale:"en"},{Link:f,redirect:g,usePathname:h,useRouter:i}=(0,d.xp)(e)},27071:(a,b,c)=>{"use strict";c.d(b,{Q:()=>e});var d=c(51060);function e(a){if(d.A.isAxiosError(a))if(a.response?.status===401)throw Error("Unauthorized: Invalid token or missing credentials");else if(a.response?.status===404)throw Error("Not Found: The requested resource could not be found");else if(a.response)throw Error(`Request failed with status code ${a.response.status}: ${a.response.statusText}`);else if(a.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error(`Error during request setup: ${a.message}`);throw Error(a)}},27317:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d=c(60687),e=c(41876),f=c(73037),g=c(37826);function h({children:a,className:b}){return(0,e.U)("(min-width:1024px)")?(0,d.jsx)(g.L3,{className:b,children:a}):(0,d.jsx)(f.gk,{className:b,children:a})}},28462:(a,b,c)=>{"use strict";c.d(b,{h:()=>h});var d=c(16648),e=c(71702),f=c(54050),g=c(33213);function h(a){let{toast:b}=(0,e.dj)(),c=(0,g.useTranslations)("universal");return(0,f.n)({mutationFn:a=>(0,d.eD)(a),onSuccess:b=>{a(b)},onError:d=>{let e=d.response.data;if(e.message.includes("is already sent"))return void a(d);b({title:c("misc.foundError"),description:e.message,variant:"destructive"})}})}},32401:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(37413),e=c(66819);function f(a){return(0,d.jsx)("div",{...a,ref:a.ref,className:(0,e.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",a.className),children:a.children})}},32737:(a,b,c)=>{"use strict";c.d(b,{BT:()=>d,FT:()=>f,MC:()=>e,RX:()=>g,aB:()=>h});let d={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"},e={all:"ANY",mountain:"MOUNTAIN",ocean:"OCEAN",ricefield:"RICEFIELD",jungle:"JUNGLE"},f={anything:"ANY",placeToLive:"PLACE_TO_LIVE",business:"BUSINESS",land:"LAND"},g={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"},h={small:{min:1,max:300,key:"small"},medium:{min:301,max:1e3,key:"medium"},large:{min:1001,max:1e5,key:"large"}}},36866:(a,b,c)=>{"use strict";c.d(b,{h:()=>f});var d=c(33213),e=c(45880);function f(){let a=(0,d.useTranslations)("universal");return e.z.object({email:e.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.email")})}).email()})}},37826:(a,b,c)=>{"use strict";c.d(b,{Cf:()=>m,Es:()=>o,L3:()=>p,LC:()=>l,ZJ:()=>k,c7:()=>n,lG:()=>i,rr:()=>q,zM:()=>j});var d=c(60687),e=c(43210),f=c(86540),g=c(89698),h=c(96241);let i=f.bL,j=f.l9,k=f.ZL;f.bm;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hJ,{ref:c,className:(0,h.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...b}));l.displayName=f.hJ.displayName;let m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(k,{children:[(0,d.jsx)(l,{}),(0,d.jsxs)(f.UC,{ref:e,className:(0,h.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[b,(0,d.jsxs)(f.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,d.jsx)(g.MKb,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=f.UC.displayName;let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col space-y-1.5 text-start sm:text-left",a),...b});n.displayName="DialogHeader";let o=({className:a,...b})=>(0,d.jsx)("div",{className:(0,h.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...b});o.displayName="DialogFooter";let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.hE,{ref:c,className:(0,h.cn)("text-lg font-semibold leading-none tracking-tight",a),...b}));p.displayName=f.hE.displayName;let q=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.VY,{ref:c,className:(0,h.cn)("text-sm text-muted-foreground",a),...b}));q.displayName=f.VY.displayName},40670:(a,b,c)=>{"use strict";c.d(b,{o:()=>i});var d=c(96241),e=c(36248),f=c.n(e),g=c(85665),h=c(59350);let i=(0,g.vt)()((0,h.Zr)(a=>({activeSearch:{propertyType:[],query:""},propertyType:[],query:"",searchHistory:[],isOpen:!0,locationInputFocused:!1,categoryInputFocused:!1,setActiveSearch:b=>a({activeSearch:b}),setPropertyType:b=>a(a=>({propertyType:(0,d.q7)(a.propertyType,b)})),setQuery:b=>a({query:b}),setSearchHistory:b=>a(a=>{let c={...b,validUntil:f()().add(7,"days").format("DD-MMM-YYYY")};if(a.searchHistory.findIndex(a=>a.query==c.query)>=0)return a;let d=[...a.searchHistory,c];return a.searchHistory.length<5?a.searchHistory=d:a.searchHistory=[...d.slice(1,4),c],a}),setIsOpen:b=>a({isOpen:b}),setCategoryInputFocused:b=>a({categoryInputFocused:b}),setLocationInputFocused:b=>a({locationInputFocused:b}),clearSearch:()=>a({query:"",propertyType:[]}),setPropertyTypeFromArray:b=>a({propertyType:b}),clearCategory:()=>a({propertyType:[]})}),{name:"seeker-search",storage:(0,h.KU)(()=>localStorage),onRehydrateStorage(a){if(!a)return;let b=a.searchHistory.filter(a=>{let b=f()(a.validUntil);return f()().isSameOrBefore(b)});a.searchHistory=b}}))},41876:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(43210);function e(a){let[b,c]=(0,d.useState)(!1);return b}},43190:(a,b,c)=>{"use strict";c.d(b,{M:()=>g});var d=c(85665),e=c(59350),f=c(15659);let g=(0,d.vt)()((0,e.Zr)(a=>({currency:"IDR",setCurrency:b=>a({currency:b}),isLoading:!0,setIsLoading:b=>a({isLoading:b})}),{name:"seekers-settings",storage:{getItem:a=>{let b=f.A.get(a);return b?JSON.parse(b):void 0},setItem:(a,b)=>{f.A.set(a,JSON.stringify(b),{expires:7,path:"/"})},removeItem:a=>{f.A.remove(a)}},onRehydrateStorage:()=>a=>{a&&a.setIsLoading(!1)}}))},44824:(a,b,c)=>{"use strict";c.d(b,{A:()=>k});var d=c(60687),e=c(70373),f=c(58869),g=c(66835),h=c(77273),i=c(96241),j=c(33213);function k({url:a,className:b}){(0,h.H)();let{seekers:c}=(0,g.k)(),k=(0,j.useTranslations)("universal");return(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)(e.eu,{className:(0,i.cn)("w-full rounded-full bg-seekers-text-lighter flex justify-center items-center",b),children:[(0,d.jsx)(e.BK,{src:c.accounts?.image,alt:k("misc.profileImageAlt")}),(0,d.jsx)(e.q5,{className:"bg-transparent text-white",children:c.code?(0,d.jsxs)("span",{children:[c.accounts.firstName[0],c.accounts.lastName[0]]}):(0,d.jsx)(f.A,{})})]})})}},53912:(a,b,c)=>{"use strict";c.d(b,{JX:()=>d,rJ:()=>e});let d=a=>a.toLowerCase().includes("day")?"DAY":a.toLowerCase().includes("week")?"WEEK":a.toLowerCase().includes("month")?"MONTH":a.toLowerCase().includes("year")?"YEAR":"MONTH",e=a=>"ONLINE"==a},54817:(a,b,c)=>{"use strict";c.d(b,{yZ:()=>k,Cv:()=>o,Bb:()=>n,lx:()=>l,xn:()=>m});var d=c(66971),e=c(11970);c(53912);var f=c(91842),g=c.n(f),h=c(97100);function i(a){return a.map(a=>{var b;return{code:a.code,geolocation:j(a.location.latitude,a.location.longitude),location:a.location.district+", "+a.location.city+", "+a.location.province,price:a.availability.price,thumbnail:(b=a.code,a.images.map((a,c)=>({id:b+c,image:a.image,isHighlight:a.is_highlight})).sort((a,b)=>b.isHighlight-a.isHighlight)),title:a.title,listingDetail:{bathRoom:a.detail.bathroom_total,bedRoom:a.detail.bedroom_total,buildingSize:a.detail.building_size,landSize:a.detail.land_size,cascoStatus:a.detail.casco_status,gardenSize:a.detail.garden_size},availability:{availableAt:a.availability.available_at||"",maxDuration:a.availability.duration_max_unit?.value&&a.availability.duration_max?{value:a.availability.duration_max||1,suffix:a.availability.duration_max_unit?.value}:null,minDuration:a.availability.duration_min_unit?.value&&a.availability.duration_min?{value:a.availability.duration_min||1,suffix:a.availability.duration_min_unit?.value}:null,type:a.availability.type.value||""},sellingPoint:a.features.selling_points,category:a.detail.option.type,isFavorite:a?._count?.favorites>0,status:a.status}})}let j=(a,b,c=10)=>{let d=1/111320*c;return[a+.4*d,b+.4*d]};async function k(a){try{let b=await (0,e.KA)({property_list:a});return{data:i(b.data.data)}}catch(a){return{error:a.data.error??"An unknown error occurred"}}}async function l(a){try{let b=await (0,e.Uw)(a);try{let b=Object.fromEntries(Object.entries(a).filter(([a,b])=>void 0!==b));2!==Object.keys(b).length&&await (0,e._y)(a)}catch(a){}return{data:i(b.data.data.items),meta:(0,d.w)(b.data.data.meta)}}catch(a){return{error:a.data.error??"An unknown error occurred"}}}async function m(a){if(a.search.length<3)return{data:[]};try{let b=await (0,e.I$)(a);return{data:function(a,b){let c=[];return b.forEach(b=>{Object.values(b).forEach(b=>{(function(a,b){let c=(0,h.A)(a.toLowerCase(),b.toLowerCase());return 1-c/Math.max(a.length,b.length)})(b,a)>0&&c.push(b)})}),g().uniq(c)}(a.search,b.data.data)}}catch(a){return{error:a.data.error??"An unknown error occurred"}}}async function n(){try{var a;return{data:{priceRange:{min:(a=(await (0,e.ed)()).data.data).price_range._min.price,max:a.price_range._max.price},buildingSizeRange:{max:a.size_range._max.building_size,min:a.size_range._min.building_size},gardenSizeRange:{max:a.size_range._max.garden_size,min:a.size_range._min.garden_size},landSizeRange:{max:a.size_range._max.land_size,min:a.size_range._min.land_size},furnishingOptions:a.furnishing_options[0].childrens.map(a=>({title:a.title,value:a.value})),livingOptions:a.living_options[0].childrens.map(a=>({title:a.title,value:a.value})),parkingOptions:a.parking_options[0].childrens.map(a=>({title:a.title,value:a.value})),poolOptions:a.pool_options[0].childrens.map(a=>({title:a.title,value:a.value}))},meta:void 0}}catch(a){return{error:a.data.error??"An unknown error occurred"}}}async function o(a){try{let b=await (0,e.b)({page:+a.page,per_page:+a.per_page,search:a.search||"",sort_by:a.sort_by});return{data:i(b.data.data.items),meta:(0,d.w)(b.data.data.meta)}}catch(a){return{error:a.data.error??"An unknown error occurred"}}}},55629:(a,b,c)=>{"use strict";c.d(b,{SQ:()=>k,_2:()=>l,hO:()=>m,lp:()=>n,mB:()=>o,rI:()=>i,ty:()=>j});var d=c(60687),e=c(43210),f=c(90745),g=c(89698),h=c(96241);let i=f.bL,j=f.l9;f.YJ,f.ZL,f.Pb,f.z6,e.forwardRef(({className:a,inset:b,children:c,...e},i)=>(0,d.jsxs)(f.ZP,{ref:i,className:(0,h.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",b&&"pl-8",a),...e,children:[c,(0,d.jsx)(g.vKP,{className:"ml-auto h-4 w-4"})]})).displayName=f.ZP.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.G5,{ref:c,className:(0,h.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...b})).displayName=f.G5.displayName;let k=e.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(f.ZL,{children:(0,d.jsx)(f.UC,{ref:e,sideOffset:b,className:(0,h.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c})}));k.displayName=f.UC.displayName;let l=e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.q7,{ref:e,className:(0,h.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",b&&"pl-8",a),...c}));l.displayName=f.q7.displayName;let m=e.forwardRef(({className:a,children:b,checked:c,checkboxPosition:e="start",...i},j)=>(0,d.jsxs)(f.H_,{ref:j,className:(0,h.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5  text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50","start"==e?"pl-8 pr-2":"pl-2 pr-8",a),checked:c,...i,children:[(0,d.jsx)("span",{className:(0,h.cn)("absolute flex h-3.5 w-3.5 items-center justify-center","start"==e?"left-2":"right-2"),children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(g.Srz,{className:"h-4 w-4"})})}),b]}));m.displayName=f.H_.displayName,e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.hN,{ref:e,className:(0,h.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(g.RiX,{className:"h-4 w-4 fill-current"})})}),b]})).displayName=f.hN.displayName;let n=e.forwardRef(({className:a,inset:b,...c},e)=>(0,d.jsx)(f.JU,{ref:e,className:(0,h.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c}));n.displayName=f.JU.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,h.cn)("-mx-1 my-1 h-px bg-muted",a),...b}));o.displayName=f.wv.displayName},56605:(a,b,c)=>{"use strict";c.d(b,{k:()=>i});var d=c(36248),e=c.n(d),f=c(85665),g=c(59350);let h={confirm_password:"",first_name:"",last_name:"",otp:"",password:"",type:"SEEKER",email:"",phone_code:"+62",phone_number:""},i=(0,f.vt)()((0,g.Zr)(a=>({register:h,setRegister:b=>a({register:b}),verifyOtpType:"",setVerifyOtpType:b=>a({verifyOtpType:b}),reset:()=>{i.persist.clearStorage(),a({register:h})},validFormUntil:void 0,setValidFormUntil:b=>a({validFormUntil:b}),successSignUp:!1,setSuccessSignUp:b=>a({successSignUp:b}),loading:!0,setLoading:b=>a({loading:b})}),{name:"register-user",storage:(0,g.KU)(()=>localStorage),onRehydrateStorage:()=>a=>{if(a?.validFormUntil){let b=e()(a?.validFormUntil);e()().isAfter(b)&&a.setRegister(h)}a?.setLoading(!1)}}))},61279:(a,b,c)=>{"use strict";c.d(b,{m:()=>h});var d=c(16648),e=c(71702),f=c(54050),g=c(33213);function h(a){let{toast:b}=(0,e.dj)(),c=(0,g.useTranslations)("universal");return(0,f.n)({mutationFn:a=>(0,d.bH)(a),onSuccess:async b=>{await a()},onError:a=>{let d=a.response.data;return b({title:c("misc.foundError"),description:d.message,variant:"destructive"}),d}})}},62112:(a,b,c)=>{"use strict";c.d(b,{U$:()=>d,dF:()=>e});let d={archiver:"Achiever",finder:"Finder",free:"Free"},e={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>k,eb:()=>o,gC:()=>n,l6:()=>i,yv:()=>j});var d=c(60687),e=c(43210),f=c(89698),g=c(51529),h=c(96241);let i=g.bL;g.YJ;let j=g.WT,k=e.forwardRef(({className:a,children:b,showCaret:c=!0,...e},i)=>(0,d.jsxs)(g.l9,{ref:i,className:(0,h.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...e,children:[b,c&&(0,d.jsx)(g.In,{asChild:!0,children:(0,d.jsx)(f.TBE,{className:"h-4 w-4 opacity-50"})})]}));k.displayName=g.l9.displayName;let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.PP,{ref:c,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.Mtm,{})}));l.displayName=g.PP.displayName;let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.wn,{ref:c,className:(0,h.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.D3D,{})}));m.displayName=g.wn.displayName;let n=e.forwardRef(({className:a,children:b,position:c="popper",...e},f)=>(0,d.jsx)(g.ZL,{children:(0,d.jsxs)(g.UC,{ref:f,className:(0,h.cn)("relative z-50 max-h-96 w-fit overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(l,{}),(0,d.jsx)(g.LM,{className:(0,h.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(m,{})]})}));n.displayName=g.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.JU,{ref:c,className:(0,h.cn)("px-2 py-1.5 text-sm font-semibold",a),...b})).displayName=g.JU.displayName;let o=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(g.q7,{ref:e,className:(0,h.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(g.VF,{children:(0,d.jsx)(f.Srz,{className:"h-4 w-4"})})}),(0,d.jsx)(g.p4,{children:b})]}));o.displayName=g.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(g.wv,{ref:c,className:(0,h.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=g.wv.displayName},66819:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f,tT:()=>g});var d=c(75986);c(96002);var e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}let g=a=>a.charAt(0).toUpperCase()+a.slice(1)},66835:(a,b,c)=>{"use strict";c.d(b,{h:()=>j,k:()=>k});var d=c(15405),e=c(85665),f=c(59350),g=c(15659),h=c(62112);let i={getItem:a=>{let b=g.A.get(a);return b?JSON.parse(b):null},setItem:(a,b)=>{g.A.set(a,JSON.stringify(b),{expires:7})},removeItem:a=>{g.A.remove(a)}},j={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:h.U$.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:d.vN},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},k=(0,e.vt)()((0,f.Zr)(a=>({role:void 0,setRole:b=>a({role:b}),seekers:j,setSeekers:b=>a({seekers:b}),tempSubscribtionLevel:0,setTempSubscribtionLevel:b=>a({tempSubscribtionLevel:b}),clearUser:()=>a(()=>({seekers:j}))}),{name:"user",storage:(0,f.KU)(()=>i)}))},66971:(a,b,c)=>{"use strict";function d(a){return{nextPage:a.next_page,page:a.page,pageCount:a.page_count,perPage:a.per_page,prevPage:a.prev_page,total:a.total}}c.d(b,{w:()=>d})},67281:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(58164),f=c(68988),g=c(43713),h=c(96241);function i({form:a,label:b,name:c,placeholder:i,description:j,type:k,inputProps:l,children:m,labelClassName:n,containerClassName:o,inputContainer:p,variant:q="default"}){return(0,d.jsx)(e.zB,{control:a.control,name:c,render:({field:a})=>(0,d.jsx)(g.A,{label:b,description:j,labelClassName:(0,h.cn)("float"==q?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",n),containerClassName:o,variant:q,children:(0,d.jsxs)("div",{className:(0,h.cn)("flex gap-2 w-full overflow-hidden","float"==q?"":"border rounded-sm focus-within:border-neutral-light",p),children:[(0,d.jsx)(f.p,{type:k,placeholder:i,...a,...l,className:(0,h.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==q?"px-0":"",l?.className)}),m]})})})}},70373:(a,b,c)=>{"use strict";c.d(b,{BK:()=>i,eu:()=>h,q5:()=>j});var d=c(60687),e=c(43210),f=c(92951),g=c(96241);let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.bL,{ref:c,className:(0,g.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...b}));h.displayName=f.bL.displayName;let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f._V,{ref:c,className:(0,g.cn)("aspect-square h-full w-full",a),...b}));i.displayName=f._V.displayName;let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.H4,{ref:c,className:(0,g.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...b}));j.displayName=f.H4.displayName},71497:(a,b,c)=>{"use strict";c.d(b,{a:()=>f});var d=c(33213),e=c(45880);function f(){let a=(0,d.useTranslations)("seeker");return e.z.object({otp:e.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.otp")})}).min(5,{message:a("form.utility.enterValidField",{field:a("form.field.otp")})})})}},71810:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\navbar\\\\seekers-navbar-2.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\components\\navbar\\seekers-navbar-2.tsx","default")},72820:(a,b,c)=>{"use strict";c.d(b,{default:()=>Y});var d=c(60687),e=c(11976),f=c(33213),g=c(43210),h=c(44824),i=c(4e3),j=c(63442),k=c(58164),l=c(27605),m=c(67281),n=c(24934),o=c(58674),p=c(45880),q=c(54050),r=c(16648),s=c(71702),t=c(15659),u=c(19791),v=c(9216),w=c(35239),x=c(96241),y=c(73185),z=c(69587),A=c(73253);function B(){let a=(0,q.n)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/google?origin=DEFAULT"}}),b=(0,q.n)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/facebook?origin=DEFAULT"}}),c=async()=>{await a.mutateAsync()},e=async()=>{await b.mutateAsync()};return(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)(n.$,{className:"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 w-full rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:c,type:"button",children:[(0,d.jsx)(A.F4b,{className:"mr-2 h-4 w-4"}),"Google"]}),(0,d.jsxs)(n.$,{className:"inline-flex w-full items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:e,type:"button",children:[(0,d.jsx)(z.iYk,{className:"mr-2 h-4 w-4"}),"Facebook"]})]})}var C=c(78377);function D({isDialog:a,onClickSignUp:b,onClickResetPassword:c}){let e=(0,f.useTranslations)("seeker"),g=function(){let a=(0,f.useTranslations)("seeker");return p.z.object({contact:p.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.email")})}).email({message:a("form.utility.enterValidField",{field:` ${a("form.field.email")}`})}),password:p.z.string({message:a("form.utility.fieldRequired",{field:a("form.field.password")})}).min(o.wz,{message:a("form.utility.minimumLength",{field:a("form.field.password"),length:o.wz})})})}(),h=function(a="seekers"){let b=(0,f.useTranslations)("universal"),{toast:c}=(0,s.dj)(),{executeRecaptcha:d}=(0,w.lw)();return(0,q.n)({mutationFn:async a=>{try{let b=await d("form_submit");return(0,r.iD)(a,b)}catch(a){return console.log(a),{data:null}}},onSuccess:async c=>{let d=c.data,e=await (0,v.i8)({headers:{Authorization:`Bearer ${d.data.access_token}`}}),f=e.type;if(!e)throw Error(b("misc.userNotFound"));if("owner"===a||"middleman"==a){if("SEEKER"==f)throw Error(b("misc.userNotFound"));if(t.A.set(o.Xh,d.data.access_token,{expires:7}),"OWNER"==f)return window.location.assign(u.dA);if("MIDDLEMAN"==f)return window.location.assign(u.yA)}else{if("OWNER"==e.type||"MIDDLEMAN"==e.type)throw Error(b("misc.userNotFound"));t.A.set(o.Xh,d.data.access_token,{expires:7}),window.location.reload()}},onError:a=>{let d=a.response?.data;t.A.remove(o.Xh),c({title:b("misc.foundError"),description:d?.message||"",variant:"destructive"})}})}("seekers"),{toast:i}=(0,s.dj)(),z=(0,l.mN)({resolver:(0,j.u)(g),defaultValues:{contact:"",password:""}});async function A(a){let b=(0,x.gT)(a.contact.replaceAll(/\s+/g,"")),c={username:a.contact.trim(),password:a.password,login_with:b?"DEFAULT":"PHONE_NUMBER"};try{await h.mutateAsync(c)}catch(a){i({title:e("error.failedLogin.title"),description:a?.response?.data.message||"",variant:"destructive"})}}return(0,d.jsx)("div",{className:"grid gap-4",children:(0,d.jsx)(k.lV,{...z,children:(0,d.jsxs)("form",{onSubmit:z.handleSubmit(A),className:"grid gap-4",children:[(0,d.jsxs)("div",{className:"grid gap-4",children:[(0,d.jsx)(m.A,{form:z,label:e("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,d.jsx)(y.A,{form:z,label:e("form.label.password"),name:"password",placeholder:"",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,d.jsxs)("div",{className:"text-xs text-neutral space-x-1 -mt-5",children:[(0,d.jsx)("span",{className:"ml-3",children:e("form.utility.forgotField",{field:e("form.field.password")})}),(0,d.jsx)(n.$,{variant:"link",type:"button",onClick:c,className:"p-0 text-seekers-primary font-medium hover:underline text-xs",children:e("form.utility.resetField",{field:e("form.field.password")})})]})]}),(0,d.jsx)(n.$,{className:"w-full",variant:"default-seekers",loading:h.isPending,children:e("cta.login")}),(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[e("auth.createAccount")," ",(0,d.jsx)(n.$,{variant:"link",onClick:b,className:"p-0 h-9 text-seekers-primary hover:underline",children:e("cta.createAccount")})]})}),(0,d.jsxs)("div",{className:"relative my-6",children:[(0,d.jsx)(C.Separator,{}),(0,d.jsxs)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:[e("conjuntion.or")," ",e("misc.continueWith")]})]}),(0,d.jsx)(B,{})]})})})}var E=c(16413),F=c(56605),G=c(28462),H=c(36248),I=c.n(H),J=c(13964),K=c(11860);let L=({onClickLogin:a,onSuccess:b})=>{let c=(0,f.useTranslations)("seeker"),e=(0,E.Y)(),{setRegister:h,setValidFormUntil:i,register:p}=(0,F.k)(),[q,r]=(0,g.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),{toast:t}=(0,s.dj)(),u=(0,G.h)(a=>{a?.response?.data?.message!=="Email verification code is already sent. Please check your email"&&t({title:c("success.sendVerification.title")+" "+p.email}),b()}),v=(0,l.mN)({resolver:(0,j.u)(e),defaultValues:{confirmPassword:p.confirm_password||"",contact:p.email||"",firstName:p.first_name||"",lastName:p.last_name||"",password:p.password||""}}),w=v.watch("password");async function z(a){let b=I()().add(30,"minutes"),d={email:a.contact||"",password:a.password,confirm_password:a.confirmPassword,first_name:a.firstName,last_name:a.lastName,type:o.Dg,otp:"00000"};h(d),i(b);try{await u.mutateAsync({email:d.email,category:"REGISTRATION"})}catch(a){if(a?.response?.data?.message=="Email verification code is already sent. Please check your email")return;t({title:c("message.otpRequest.failedToast.title"),description:a?.response?.data?.message||"",variant:"destructive"})}}return(0,g.useEffect)(()=>{w&&r({length:w.length>=8,number:/[0-9]/.test(w),special:/[!@#$%^&*()_+]/.test(w),notCommon:!["123456","password","qwerty"].includes(w.toLowerCase()),uppercase:/[A-Z]/.test(w),lowercase:/[a-z]/.test(w)})},[w]),(0,d.jsx)(k.lV,{...v,children:(0,d.jsxs)("form",{onSubmit:v.handleSubmit(z),className:"grid gap-4",children:[(0,d.jsxs)("div",{className:"grid gap-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)(m.A,{form:v,label:c("form.label.firstName"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,d.jsx)(m.A,{form:v,label:c("form.label.lastName"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),(0,d.jsx)(m.A,{form:v,label:c("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,d.jsx)(y.A,{form:v,name:"password",variant:"float",label:c("form.label.password"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,d.jsx)(y.A,{form:v,name:"confirmPassword",variant:"float",label:c("form.label.confirmPassword"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"})]}),w&&(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,d.jsxs)("div",{className:(0,x.cn)(q.length?"text-green-500":"text-red-500"),children:[q.length?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.minimumLength")]}),(0,d.jsxs)("div",{className:(0,x.cn)(q.number?"text-green-500":"text-red-500"),children:[q.number?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.numberRequired")]}),(0,d.jsxs)("div",{className:(0,x.cn)(q.special?"text-green-500":"text-red-500"),children:[q.special?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.specialCharacter")]}),(0,d.jsxs)("div",{className:(0,x.cn)(q.notCommon?"text-green-500":"text-red-500"),children:[q.notCommon?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.notCommonWord")]}),(0,d.jsxs)("div",{className:(0,x.cn)(q.uppercase?"text-green-500":"text-red-500"),children:[q.uppercase?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.uppercaseRequired")]}),(0,d.jsxs)("div",{className:(0,x.cn)(q.lowercase?"text-green-500":"text-red-500"),children:[q.lowercase?(0,d.jsx)(J.A,{className:"inline w-3 h-3 mr-1"}):(0,d.jsx)(K.A,{className:"inline w-3 h-3 mr-1"}),c("form.utility.password.lowercaseRequired")]})]})]}),(0,d.jsx)(n.$,{className:"w-full",variant:"default-seekers",loading:u.isPending,children:c("cta.createAccount")}),(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[c("auth.alreadyHaveAccount")," ",(0,d.jsx)(n.$,{variant:"link",onClick:a,className:"p-0 h-9 text-seekers-primary hover:underline",children:c("cta.login")})]})}),(0,d.jsxs)("div",{className:"relative my-6",children:[(0,d.jsx)(C.Separator,{}),(0,d.jsx)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:c("conjuntion.or")})]}),(0,d.jsx)(B,{})]})})};var M=c(47033),N=c(500),O=c(16853),P=c(61279),Q=c(43713),R=c(99008),S=c(71497);function T(){let{register:a,reset:b,setSuccessSignUp:c}=(0,F.k)(),{toast:e}=(0,s.dj)(),h=(0,f.useTranslations)("seeker"),i=(0,S.a)(),m=function(a,b="owner"){let{toast:c}=(0,s.dj)(),d=(0,f.useTranslations)("universal"),{executeRecaptcha:e}=(0,w.lw)();return(0,q.n)({mutationFn:async a=>{try{let b=await e("form_submit");return(0,O.DY)(a,b)}catch(a){return console.log(a),{data:null}}},onSuccess:c=>{let d=c.data;t.A.set(o.Xh,d.data.access_token,{expires:7}),a?.(),"owner"==b?window.location.assign(u.dA):window.location.reload()},onError:a=>{let b=a.response.data;c({title:d("misc.foundError"),description:b.message,variant:"destructive"})}})}(()=>b(),"seekers"),p=(0,P.m)(async()=>{try{await m.mutateAsync({...a,otp:v.getValues("otp"),register_with:"EMAIL"}),c(!0)}catch(a){}}),r=(0,G.h)(b=>{if("Email verification code is already sent. Please check your email"===b.response.data.message)return void e({title:h("message.otpRequest.failedToast.title"),description:b.response.data.message||"",variant:"destructive"});e({title:h("success.sendVerification.title")+" "+a.email})}),v=(0,l.mN)({resolver:(0,j.u)(i),defaultValues:{otp:""}});async function x(b){let c={otp:b.otp,requested_by:a.email||"",type:"EMAIL"};try{await p.mutateAsync(c)}catch(a){e({title:h("error.signUp.title"),description:a.response.data.message,variant:"destructive"})}}async function y(){r.mutate({email:a.email,category:"REGISTRATION"})}return(0,g.useEffect)(()=>{let a=v.getValues("otp").length,b=document.getElementById("otp-button");a>=5&&b?.click()},[v.getValues("otp")]),(0,d.jsx)(k.lV,{...v,children:(0,d.jsxs)("form",{onSubmit:v.handleSubmit(x),className:"space-y-8",children:[(0,d.jsx)(k.zB,{control:v.control,name:"otp",render:({field:a})=>(0,d.jsx)("div",{className:"flex justify-center",children:(0,d.jsx)(Q.A,{label:"",children:(0,d.jsx)(R.UV,{maxLength:5,...a,pattern:N.UO,required:!0,containerClassName:"flex justify-center max-sm:justify-between",children:(0,d.jsx)(R.NV,{children:Array.from({length:5},(a,b)=>(0,d.jsx)(R.sF,{index:b,className:"w-16 h-20 text-2xl"},b))})})})})}),(0,d.jsxs)("div",{className:"space-y-2 flex flex-col items-center",children:[(0,d.jsxs)(n.$,{id:"otp-button",className:"w-full",variant:"default-seekers",loading:p.isPending,children:[h("cta.verify")," ",h("user.account")]}),(0,d.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),y()},className:"mx-auto text-xs text-seekers-text-light",children:h("otp.resendVerificationCode")})]})]})})}var U=c(19421),V=c(36866);function W({onBack:a}){let b=(0,f.useTranslations)("universal"),{toast:c}=(0,s.dj)(),e=(0,V.h)(),g=(0,U.v)(),h=(0,l.mN)({resolver:(0,j.u)(e),defaultValues:{email:""}});async function i(d){let e={email:d.email};try{await g.mutateAsync(e),c({title:b("success.requestForgotPassword.title"),description:b("success.requestForgotPassword.description")}),a()}catch(a){c({title:b("error.requestForgetPassword.title"),description:a.response.data.message,variant:"destructive"})}}return(0,d.jsx)(k.lV,{...h,children:(0,d.jsxs)("form",{onSubmit:h.handleSubmit(i),className:"grid gap-4 ",children:[(0,d.jsx)(m.A,{form:h,label:b("form.label.email"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light"}),(0,d.jsx)(n.$,{className:"w-full",variant:"default-seekers",loading:g.isPending,children:b("cta.requestChangePassword")}),(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsx)(n.$,{variant:"link",onClick:a,className:"p-0 h-9 text-seekers-primary hover:underline",children:b("cta.goBack")})})]})})}let X={signUp:"SIGN_UP",login:"LOGIN",otp:"OTP",resetPassword:"RESET_PASSWORD"};function Y({triggerClassName:a,customTrigger:b}){let c=(0,f.useTranslations)("seeker"),[j,k]=(0,g.useState)(!1),[l,m]=(0,g.useState)(""),[o,p]=(0,g.useState)(X.signUp);return(0,d.jsxs)(e.A,{open:j,setOpen:k,openTrigger:b||(0,d.jsx)("button",{className:`border relative border-seekers-text-lighter shadow-md rounded-full h-10 w-14 !bg-seekers-text-light ${a}`,children:(0,d.jsx)(h.A,{url:""})}),dialogClassName:"w-full sm:max-w-[500px] p-6",children:[(0,d.jsxs)(i.A,{className:"flex flex-col space-y-1.5 text-center mb-6",children:[(o==X.otp||o==X.resetPassword)&&(0,d.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute top-4 left-4",onClick:()=>p(o==X.otp?X.signUp:X.login),children:(0,d.jsx)(M.A,{className:"h-4 w-4"})}),(0,d.jsxs)("section",{className:"space-y-1.5",children:[(0,d.jsx)("h2",{className:"tracking-tight text-center text-2xl font-bold max-w-xs mx-auto",children:l}),(0,d.jsx)("p",{className:(0,x.cn)(o===X.otp?"hidden":"","text-sm text-muted-foreground text-center"),children:o===X.login?c("auth.login.subtitle"):o===X.signUp?c("auth.register.subtitle"):o===X.resetPassword?c("auth.resetPassword.subtitle"):""})]})]}),o==X.login?(0,d.jsx)(D,{onClickSignUp:()=>p(X.signUp),onClickResetPassword:()=>p(X.resetPassword)}):o==X.signUp?(0,d.jsx)(L,{onSuccess:()=>p(X.otp),onClickLogin:()=>p(X.login)}):o==X.otp?(0,d.jsxs)("section",{children:[o===X.otp&&(0,d.jsxs)("div",{className:"text-seekers-text-light",children:[(0,d.jsx)("p",{children:c("auth.otp.content.title")}),(0,d.jsxs)("ul",{className:"list-disc list-inside",children:[(0,d.jsx)("li",{children:c("auth.otp.item.one")}),(0,d.jsx)("li",{children:c("auth.otp.item.two")}),(0,d.jsx)("li",{children:c("auth.otp.item.three")})]}),(0,d.jsx)("p",{children:c("auth.otp.content.cantFindEmail")})]}),(0,d.jsx)(T,{})]}):o==X.resetPassword?(0,d.jsx)(W,{onBack:()=>p(X.login)}):null]})}},73037:(a,b,c)=>{"use strict";c.d(b,{BE:()=>m,I6:()=>p,Uz:()=>i,_s:()=>h,gk:()=>o,tb:()=>n,zj:()=>l});var d=c(60687),e=c(43210),f=c(77668),g=c(96241);let h=({shouldScaleBackground:a=!0,...b})=>(0,d.jsx)(f._.Root,{shouldScaleBackground:a,...b});h.displayName="Drawer";let i=f._.Trigger,j=f._.Portal;f._.Close;let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f._.Overlay,{ref:c,className:(0,g.cn)("fixed inset-0 z-50 bg-black/80",a),...b}));k.displayName=f._.Overlay.displayName;let l=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(j,{children:[(0,d.jsx)(k,{}),(0,d.jsxs)(f._.Content,{ref:e,className:(0,g.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",a),...c,children:[(0,d.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),b]})]}));l.displayName="DrawerContent";let m=({className:a,...b})=>(0,d.jsx)("div",{className:(0,g.cn)("grid gap-1.5 p-4 text-center sm:text-left",a),...b});m.displayName="DrawerHeader";let n=({className:a,...b})=>(0,d.jsx)("div",{className:(0,g.cn)("mt-auto flex flex-col gap-2 p-4",a),...b});n.displayName="DrawerFooter";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f._.Title,{ref:c,className:(0,g.cn)("font-semibold leading-none tracking-tight",a),...b}));o.displayName=f._.Title.displayName;let p=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f._.Description,{ref:c,className:(0,g.cn)("text-sm text-muted-foreground",a),...b}));p.displayName=f._.Description.displayName},77273:(a,b,c)=>{"use strict";c.d(b,{H:()=>j,g:()=>i});var d=c(9216),e=c(58674),f=c(66835),g=c(29494),h=c(15659);let i="my-detail";function j(a=!0){let{setSeekers:b,clearUser:c,setRole:k}=(0,f.k)(a=>a),l=h.A.get(e.Xh);return(0,g.I)({queryKey:[i,l||"0"],queryFn:async()=>{if(!l)return f.h;try{let a=await (0,d.i8)();return b(a),k("SEEKER"),a}catch(a){return c(),f.h}},refetchOnWindowFocus:!1,retry:!1,enabled:a})}},78377:(a,b,c)=>{"use strict";c.d(b,{Separator:()=>h});var d=c(60687),e=c(43210),f=c(42123),g=c(96241);let h=e.forwardRef(({className:a,orientation:b="horizontal",decorative:c=!0,...e},h)=>(0,d.jsx)(f.b,{ref:h,decorative:c,orientation:b,className:(0,g.cn)("shrink-0 bg-border","horizontal"===b?"h-[1px] w-full":"h-full w-[1px]",a),...e}));h.displayName=f.b.displayName},80189:(a,b,c)=>{"use strict";c.d(b,{Tooltip:()=>i,TooltipContent:()=>k,TooltipProvider:()=>h,TooltipTrigger:()=>j});var d=c(60687),e=c(43210),f=c(36499),g=c(96241);let h=f.Kq,i=f.bL,j=f.l9,k=e.forwardRef(({className:a,sideOffset:b=4,...c},e)=>(0,d.jsx)(f.UC,{ref:e,sideOffset:b,className:(0,g.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...c}));k.displayName=f.UC.displayName},81830:(a,b,c)=>{var d={"./en.json":[46069,6069],"./id.json":[75115,5115]};function e(a){if(!c.o(d,a))return Promise.resolve().then(()=>{var b=Error("Cannot find module '"+a+"'");throw b.code="MODULE_NOT_FOUND",b});var b=d[a],e=b[0];return c.e(b[1]).then(()=>c.t(e,19))}e.keys=()=>Object.keys(d),e.id=81830,a.exports=e},87466:(a,b,c)=>{"use strict";c.d(b,{A:()=>l});var d=c(40670),e=c(43210),f=c(19791),g=c(96241),h=c(61261),i=c(33213),j=c(32737),k=c(58674);function l(){let a=(0,i.useTranslations)(),b=(0,d.o)(a=>a),[c,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)(null),o=(0,h.useRouter)(),p=[{content:a("seeker.listing.category.villa"),id:"1",value:j.BT.villas},{content:a("seeker.listing.category.apartment"),id:"2",value:j.BT.apartment},{content:a("seeker.listing.category.guestHouse"),id:"3",value:j.BT.rooms},{content:a("seeker.listing.category.commercial"),id:"4",value:j.BT.commercialSpace},{content:a("seeker.listing.category.cafeAndRestaurent"),id:"5",value:j.BT.cafeOrRestaurants},{content:a("seeker.listing.category.office"),id:"6",value:j.BT.offices},{content:a("seeker.listing.category.shops"),id:"7",value:j.BT.shops},{content:a("seeker.listing.category.shellAndCore"),id:"8",value:j.BT.shellAndCore},{content:a("seeker.listing.category.land"),id:"9",value:j.BT.lands}],q=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],r={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},s=(0,e.useMemo)(()=>!b.query||c?q:q.filter(a=>{let c=a.name.replace(", Bali","").toLowerCase(),d=b.query.toLowerCase();return!!c.includes(d)||(r[a.value]||[]).some(a=>a.toLowerCase().includes(d))}),[b.query,c]);return{seekersSearch:b,handleSetQuery:a=>{let c=a.split(","),d=a.length;c.length>3&&","==a.charAt(d-1)||b.setQuery(a)},handleSetType:a=>{(!(b.propertyType.length>=3)||b.propertyType.includes(a))&&b.setPropertyType(a)},propertyType:p,handleSearch:(a,c)=>{a&&b.setQuery(a),c&&b.setPropertyTypeFromArray(c);let d=a||b.query,e=c||b.propertyType;""!==b.activeSearch.query&&b.setSearchHistory({propertyType:b.activeSearch.propertyType,query:b.activeSearch.query}),b.setActiveSearch({query:d,propertyType:e});let h=(0,g.jW)(d);o.push(f.Eq+"/"+(h||"all")+"?"+k.Ix.type+"="+(e.toString()||"all"))},propertyTypeFormatHelper:a=>a.map(a=>{let b=p.find(b=>b.value==a);return b?.content}),locations:q,banjars:r,getMatchingBanjars:a=>{let c=b.query;return c?(r[a]||[]).filter(a=>a.toLowerCase().includes(c.toLowerCase())):[]},showBanjars:c,setShowBanjars:l,selectedLocation:m,setSelectedLocation:n,handleSelectLocation:a=>{l(!0),n(a),b.setQuery(a)},handleBackToLocations:()=>{l(!1);let a=b.query.replace(m||"","");b.setQuery(a)},handleSetBanjar:(a,c=!1)=>{let d=b.query.split(",").filter(a=>""!==a.trim()&&a!==m);if(d.includes(a)){let c=d.filter(b=>b!==a);b.setQuery(c.toString());return}if(!(d.length>=3)||""===d[d.length-1]){if(c){let b=d.length;d[b-1]=a}else d.push(a);b.setQuery(d.toString())}},filteredLocations:s}}},87616:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(60687);c(43210);var e=c(80189);function f({content:a,trigger:b,contentClassName:c}){return(0,d.jsx)(e.TooltipProvider,{delayDuration:100,children:(0,d.jsxs)(e.Tooltip,{children:[(0,d.jsx)(e.TooltipTrigger,{asChild:!0,children:b}),(0,d.jsx)(e.TooltipContent,{className:c,children:a})]})})}},89637:(a,b,c)=>{"use strict";c.d(b,{A:()=>q});var d=c(60687),e=c(32737),f=c(96241),g=c(21595),h=c(96202),i=c(32192),j=c(23928),k=c(13166),l=c(550),m=c(28561),n=c(7430),o=c(35607),p=c(42017);function q({category:a,className:b}){switch(a){case e.BT.villa:case e.BT.villas:return(0,d.jsx)(g.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.apartment:return(0,d.jsx)(h.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.homestay:case e.BT.guestHouse:case e.BT.rooms:return(0,d.jsx)(i.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.ruko:case e.BT.commercialSpace:return(0,d.jsx)(j.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.cafeOrRestaurants:return(0,d.jsx)(k.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.offices:return(0,d.jsx)(l.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.shops:return(0,d.jsx)(m.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.shellAndCore:return(0,d.jsx)(n.A,{className:(0,f.cn)("!w-6 !h-6",b)});case e.BT.lands:return(0,d.jsx)(o.A,{className:(0,f.cn)("!w-6 !h-6",b)});default:return(0,d.jsx)(p.A,{className:(0,f.cn)("!w-6 !h-6",b)})}}},98353:(a,b,c)=>{"use strict";c.d(b,{Bu:()=>q,DW:()=>h,Eq:()=>i,KI:()=>v,Nx:()=>k,Rd:()=>g,VZ:()=>o,Zs:()=>n,ch:()=>l,fB:()=>t,gA:()=>j,hT:()=>u,i1:()=>p,ig:()=>s,jd:()=>m,po:()=>r}),c(37413);var d=c(38039),e=c(77942),f=c(87468);let g="/",h="/profile",i="/s",j="/favorites",k="/message",l="/subscription",m="/plan",n="/billing",o="/notification",p="/security",q="/privacy-policy",r="/terms-of-use",s="/contact-us",t="/user-data-deletion",u="/about-us",v="/posts";d.A,e.A,f.A,d.A,e.A},99008:(a,b,c)=>{"use strict";c.d(b,{NV:()=>j,UV:()=>i,sF:()=>k});var d=c(60687),e=c(43210),f=c(89698),g=c(500),h=c(96241);let i=e.forwardRef(({className:a,containerClassName:b,...c},e)=>(0,d.jsx)(g.wE,{ref:e,containerClassName:(0,h.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",b),className:(0,h.cn)("disabled:cursor-not-allowed",a),...c}));i.displayName="InputOTP";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,h.cn)("flex items-center",a),...b}));j.displayName="InputOTPGroup";let k=e.forwardRef(({index:a,className:b,...c},f)=>{let{char:i,hasFakeCaret:j,isActive:k}=e.useContext(g.dK).slots[a];return(0,d.jsxs)("div",{ref:f,className:(0,h.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",k&&"z-10 ring-1 ring-ring",b),...c,children:[i,j&&(0,d.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});k.displayName="InputOTPSlot",e.forwardRef(({...a},b)=>(0,d.jsx)("div",{ref:b,role:"separator",...a,children:(0,d.jsx)(f.YTx,{})})).displayName="InputOTPSeparator"}};