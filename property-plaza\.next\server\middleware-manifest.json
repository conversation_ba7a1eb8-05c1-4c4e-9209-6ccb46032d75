{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ifA+C6SjpX4x2Ho59DIt92I60/XQ2xrjhjhD8id4ytQ="}}}, "functions": {}, "sortedMiddleware": ["/"]}