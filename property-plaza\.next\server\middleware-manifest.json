{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oZNUTQExzFV3ZnLTmrd5oU7ZuUdG7CMBcqFv+uuRocA=", "__NEXT_PREVIEW_MODE_ID": "3cdf982e6946deb9f5c5e8773d91a2f3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c0691696e45133aff68bcad88dfeba0d3cfa0d0e8f446180f591b8453553b8cf", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b6f8605842cf0375d7074550acf5cec8d6884feb05f65a8b544c8bcb225dad42"}}}, "functions": {}, "sortedMiddleware": ["/"]}