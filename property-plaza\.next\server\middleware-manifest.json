{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|id))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/(en|id)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!hooks|_next\\/static|_next\\/image|favicon.ico|icon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|mp3)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!hooks|_next/static|_next/image|favicon.ico|icon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|mp3)$).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "NhhytWpaeJfj-HFgY9tEF", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "M4D+mfVNuoOZYRZ/xX79lV8LDYUY/Ngq2pFjZ5/JePA=", "__NEXT_PREVIEW_MODE_ID": "758d86ecc5562d24884d92b4056c5009", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5a7739e530c89283127752b27de1901ac872634b4a0f686afb38ad7d9db292c5", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f9203319f098f43b4a4bd53adc0c105c0cccf353d91bf04e208fdc206780a260"}}}, "functions": {}, "sortedMiddleware": ["/"]}