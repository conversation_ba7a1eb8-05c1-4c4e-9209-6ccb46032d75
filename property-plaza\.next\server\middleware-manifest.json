{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oHTEHC8i6vShgU1h6qjQPxOwBygR2lrAjr3vmodqlT8=", "__NEXT_PREVIEW_MODE_ID": "435ed4e2af55b0d396e019d0dc69fae5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "44e476cf6d86f97e423cb12c9f5da8a214ff678a4becccf01cbc7a0f8d6ea4bd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a2dc8cf344a9b7263918fe2bacb52bfd39814aaa71a84ca1b5e2f1fc24793124"}}}, "functions": {}, "sortedMiddleware": ["/"]}