self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"ace39bf07124e0ba39e8486050a53bc79b0621b3\": {\n      \"workers\": {\n        \"app/[locale]/(user)/[title]/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Ccore%5C%5Cssr-client.ts%22%2C%5B%22default%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/(user)/page\": \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Ccore%5C%5Cssr-client.ts%22%2C%5B%22default%22%5D%5D%5D&__client_imported__=!\",\n        \"app/[locale]/verify/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Ccore%5C%5Cssr-client.ts%22%2C%5B%22default%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(user)/[title]/page\": \"rsc\",\n        \"app/[locale]/(user)/page\": \"rsc\",\n        \"app/[locale]/verify/page\": \"action-browser\"\n      }\n    },\n    \"23977280e679cbd5490718534d869d8b006b3dfa\": {\n      \"workers\": {\n        \"app/[locale]/(user)/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-sanity%5C%5Cdist%5C%5Cvisual-editing%5C%5Cserver-actions.js%22%2C%5B%22revalidateRootLayout%22%5D%5D%2C%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5C%40sanity%5C%5Cnext-loader%5C%5Cdist%5C%5Cserver-actions.js%22%2C%5B%22revalidateSyncTags%22%2C%22setPerspectiveCookie%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(user)/page\": \"action-browser\"\n      }\n    },\n    \"21a7b89139c1ae9f09080bde8b72017631c6bb15\": {\n      \"workers\": {\n        \"app/[locale]/(user)/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-sanity%5C%5Cdist%5C%5Cvisual-editing%5C%5Cserver-actions.js%22%2C%5B%22revalidateRootLayout%22%5D%5D%2C%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5C%40sanity%5C%5Cnext-loader%5C%5Cdist%5C%5Cserver-actions.js%22%2C%5B%22revalidateSyncTags%22%2C%22setPerspectiveCookie%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(user)/page\": \"action-browser\"\n      }\n    },\n    \"6ebeb7e19dda4f3641cdd24f3ee5eadb0810a726\": {\n      \"workers\": {\n        \"app/[locale]/(user)/page\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5Cnext-sanity%5C%5Cdist%5C%5Cvisual-editing%5C%5Cserver-actions.js%22%2C%5B%22revalidateRootLayout%22%5D%5D%2C%5B%22C%3A%5C%5C_PRIVATE%5C%5CProperty%20Plaza%20-%20Seekers%5C%5Cproperty-plaza%5C%5Cnode_modules%5C%5C%40sanity%5C%5Cnext-loader%5C%5Cdist%5C%5Cserver-actions.js%22%2C%5B%22revalidateSyncTags%22%2C%22setPerspectiveCookie%22%5D%5D%5D&__client_imported__=true!\"\n      },\n      \"layer\": {\n        \"app/[locale]/(user)/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"