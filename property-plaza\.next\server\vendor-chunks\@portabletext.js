"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@portabletext";
exports.ids = ["vendor-chunks/@portabletext"];
exports.modules = {

/***/ "(ssr)/./node_modules/@portabletext/react/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@portabletext/react/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PortableText: () => (/* binding */ PortableText),\n/* harmony export */   defaultComponents: () => (/* binding */ defaultComponents),\n/* harmony export */   mergeComponents: () => (/* binding */ mergeComponents),\n/* harmony export */   toPlainText: () => (/* reexport safe */ _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.toPlainText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @portabletext/toolkit */ \"(ssr)/./node_modules/@portabletext/toolkit/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nconst _excluded = [\"block\", \"list\", \"listItem\", \"marks\", \"types\"],\n  _excluded2 = [\"listItem\"],\n  _excluded3 = [\"_key\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var s = Object.getOwnPropertySymbols(e); for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.includes(n)) continue; t[n] = r[n]; } return t; }\n\n\n\n\nconst defaultLists = {\n    number: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ol\", {\n      children\n    }),\n    bullet: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", {\n      children\n    })\n  },\n  DefaultListItem = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"li\", {\n    children\n  }),\n  link = ({\n    children,\n    value\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n    href: value?.href,\n    children\n  }),\n  underlineStyle = {\n    textDecoration: \"underline\"\n  },\n  defaultMarks = {\n    em: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"em\", {\n      children\n    }),\n    strong: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"strong\", {\n      children\n    }),\n    code: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"code\", {\n      children\n    }),\n    underline: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n      style: underlineStyle,\n      children\n    }),\n    \"strike-through\": ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"del\", {\n      children\n    }),\n    link\n  },\n  getTemplate = (type, prop) => `[@portabletext/react] Unknown ${type}, specify a component for it in the \\`components.${prop}\\` prop`,\n  unknownTypeWarning = typeName => getTemplate(`block type \"${typeName}\"`, \"types\"),\n  unknownMarkWarning = markType => getTemplate(`mark type \"${markType}\"`, \"marks\"),\n  unknownBlockStyleWarning = blockStyle => getTemplate(`block style \"${blockStyle}\"`, \"block\"),\n  unknownListStyleWarning = listStyle => getTemplate(`list style \"${listStyle}\"`, \"list\"),\n  unknownListItemStyleWarning = listStyle => getTemplate(`list item style \"${listStyle}\"`, \"listItem\");\nfunction printWarning(message) {\n  console.warn(message);\n}\nconst hidden = {\n    display: \"none\"\n  },\n  DefaultUnknownType = ({\n    value,\n    isInline\n  }) => {\n    const warning = unknownTypeWarning(value._type);\n    return isInline ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n      style: hidden,\n      children: warning\n    }) : /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n      style: hidden,\n      children: warning\n    });\n  },\n  DefaultUnknownMark = ({\n    markType,\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n    className: `unknown__pt__mark__${markType}`,\n    children\n  }),\n  DefaultUnknownBlockStyle = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n    children\n  }),\n  DefaultUnknownList = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", {\n    children\n  }),\n  DefaultUnknownListItem = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"li\", {\n    children\n  }),\n  DefaultHardBreak = () => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"br\", {}),\n  defaultBlockStyles = {\n    normal: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n      children\n    }),\n    blockquote: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"blockquote\", {\n      children\n    }),\n    h1: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h1\", {\n      children\n    }),\n    h2: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h2\", {\n      children\n    }),\n    h3: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h3\", {\n      children\n    }),\n    h4: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h4\", {\n      children\n    }),\n    h5: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h5\", {\n      children\n    }),\n    h6: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h6\", {\n      children\n    })\n  },\n  defaultComponents = {\n    types: {},\n    block: defaultBlockStyles,\n    marks: defaultMarks,\n    list: defaultLists,\n    listItem: DefaultListItem,\n    hardBreak: DefaultHardBreak,\n    unknownType: DefaultUnknownType,\n    unknownMark: DefaultUnknownMark,\n    unknownList: DefaultUnknownList,\n    unknownListItem: DefaultUnknownListItem,\n    unknownBlockStyle: DefaultUnknownBlockStyle\n  };\nfunction mergeComponents(parent, overrides) {\n  const {\n      block,\n      list,\n      listItem,\n      marks,\n      types\n    } = overrides,\n    rest = _objectWithoutProperties(overrides, _excluded);\n  return _objectSpread(_objectSpread({}, parent), {}, {\n    block: mergeDeeply(parent, overrides, \"block\"),\n    list: mergeDeeply(parent, overrides, \"list\"),\n    listItem: mergeDeeply(parent, overrides, \"listItem\"),\n    marks: mergeDeeply(parent, overrides, \"marks\"),\n    types: mergeDeeply(parent, overrides, \"types\")\n  }, rest);\n}\nfunction mergeDeeply(parent, overrides, key) {\n  const override = overrides[key],\n    parentVal = parent[key];\n  return typeof override == \"function\" || override && typeof parentVal == \"function\" ? override : override ? _objectSpread(_objectSpread({}, parentVal), override) : parentVal;\n}\nfunction PortableText({\n  value: input,\n  components: componentOverrides,\n  listNestingMode,\n  onMissingComponent: missingComponentHandler = printWarning\n}) {\n  const handleMissingComponent = missingComponentHandler || noop,\n    blocks = Array.isArray(input) ? input : [input],\n    nested = (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.nestLists)(blocks, listNestingMode || _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.LIST_NEST_MODE_HTML),\n    components = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => componentOverrides ? mergeComponents(defaultComponents, componentOverrides) : defaultComponents, [componentOverrides]),\n    renderNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => getNodeRenderer(components, handleMissingComponent), [components, handleMissingComponent]),\n    rendered = nested.map((node, index) => renderNode({\n      node,\n      index,\n      isInline: !1,\n      renderNode\n    }));\n  return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n    children: rendered\n  });\n}\nconst getNodeRenderer = (components, handleMissingComponent) => {\n  function renderNode(options) {\n    const {\n        node,\n        index,\n        isInline\n      } = options,\n      key = node._key || `node-${index}`;\n    return (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitList)(node) ? renderList(node, index, key) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextListItemBlock)(node) ? renderListItem(node, index, key) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitSpan)(node) ? renderSpan(node, index, key) : hasCustomComponentForNode(node) ? renderCustomBlock(node, index, key, isInline) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextBlock)(node) ? renderBlock(node, index, key, isInline) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitTextNode)(node) ? renderText(node, key) : renderUnknownType(node, index, key, isInline);\n  }\n  function hasCustomComponentForNode(node) {\n    return node._type in components.types;\n  }\n  function renderListItem(node, index, key) {\n    const tree = serializeBlock({\n        node,\n        index,\n        isInline: !1,\n        renderNode\n      }),\n      renderer = components.listItem,\n      Li = (typeof renderer == \"function\" ? renderer : renderer[node.listItem]) || components.unknownListItem;\n    if (Li === components.unknownListItem) {\n      const style = node.listItem || \"bullet\";\n      handleMissingComponent(unknownListItemStyleWarning(style), {\n        type: style,\n        nodeType: \"listItemStyle\"\n      });\n    }\n    let children = tree.children;\n    if (node.style && node.style !== \"normal\") {\n      const {\n          listItem\n        } = node,\n        blockNode = _objectWithoutProperties(node, _excluded2);\n      children = renderNode({\n        node: blockNode,\n        index,\n        isInline: !1,\n        renderNode\n      });\n    }\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Li, {\n      value: node,\n      index,\n      isInline: !1,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderList(node, index, key) {\n    const children = node.children.map((child, childIndex) => renderNode({\n        node: child._key ? child : _objectSpread(_objectSpread({}, child), {}, {\n          _key: `li-${index}-${childIndex}`\n        }),\n        index: childIndex,\n        isInline: !1,\n        renderNode\n      })),\n      component = components.list,\n      List = (typeof component == \"function\" ? component : component[node.listItem]) || components.unknownList;\n    if (List === components.unknownList) {\n      const style = node.listItem || \"bullet\";\n      handleMissingComponent(unknownListStyleWarning(style), {\n        nodeType: \"listStyle\",\n        type: style\n      });\n    }\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(List, {\n      value: node,\n      index,\n      isInline: !1,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderSpan(node, _index, key) {\n    const {\n        markDef,\n        markType,\n        markKey\n      } = node,\n      Span = components.marks[markType] || components.unknownMark,\n      children = node.children.map((child, childIndex) => renderNode({\n        node: child,\n        index: childIndex,\n        isInline: !0,\n        renderNode\n      }));\n    return Span === components.unknownMark && handleMissingComponent(unknownMarkWarning(markType), {\n      nodeType: \"mark\",\n      type: markType\n    }), /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Span, {\n      text: (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.spanToPlainText)(node),\n      value: markDef,\n      markType,\n      markKey,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderBlock(node, index, key, isInline) {\n    const _serializeBlock = serializeBlock({\n        node,\n        index,\n        isInline,\n        renderNode\n      }),\n      {\n        _key\n      } = _serializeBlock,\n      props = _objectWithoutProperties(_serializeBlock, _excluded3),\n      style = props.node.style || \"normal\",\n      Block = (typeof components.block == \"function\" ? components.block : components.block[style]) || components.unknownBlockStyle;\n    return Block === components.unknownBlockStyle && handleMissingComponent(unknownBlockStyleWarning(style), {\n      nodeType: \"blockStyle\",\n      type: style\n    }), /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Block, _objectSpread(_objectSpread({}, props), {}, {\n      value: props.node,\n      renderNode\n    }), key);\n  }\n  function renderText(node, key) {\n    if (node.text === `\n`) {\n      const HardBreak = components.hardBreak;\n      return HardBreak ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HardBreak, {}, key) : `\n`;\n    }\n    return node.text;\n  }\n  function renderUnknownType(node, index, key, isInline) {\n    const nodeOptions = {\n      value: node,\n      isInline,\n      index,\n      renderNode\n    };\n    handleMissingComponent(unknownTypeWarning(node._type), {\n      nodeType: \"block\",\n      type: node._type\n    });\n    const UnknownType = components.unknownType;\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(UnknownType, _objectSpread({}, nodeOptions), key);\n  }\n  function renderCustomBlock(node, index, key, isInline) {\n    const nodeOptions = {\n        value: node,\n        isInline,\n        index,\n        renderNode\n      },\n      Node = components.types[node._type];\n    return Node ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Node, _objectSpread({}, nodeOptions), key) : null;\n  }\n  return renderNode;\n};\nfunction serializeBlock(options) {\n  const {\n      node,\n      index,\n      isInline,\n      renderNode\n    } = options,\n    children = (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.buildMarksTree)(node).map((child, i) => renderNode({\n      node: child,\n      isInline: !0,\n      index: i,\n      renderNode\n    }));\n  return {\n    _key: node._key || `block-${index}`,\n    children,\n    index,\n    isInline,\n    node\n  };\n}\nfunction noop() {}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@portabletext/react/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@portabletext/toolkit/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@portabletext/toolkit/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LIST_NEST_MODE_DIRECT: () => (/* binding */ LIST_NEST_MODE_DIRECT),\n/* harmony export */   LIST_NEST_MODE_HTML: () => (/* binding */ LIST_NEST_MODE_HTML),\n/* harmony export */   buildMarksTree: () => (/* binding */ buildMarksTree),\n/* harmony export */   isPortableTextBlock: () => (/* binding */ isPortableTextBlock),\n/* harmony export */   isPortableTextListItemBlock: () => (/* binding */ isPortableTextListItemBlock),\n/* harmony export */   isPortableTextSpan: () => (/* binding */ isPortableTextSpan),\n/* harmony export */   isPortableTextToolkitList: () => (/* binding */ isPortableTextToolkitList),\n/* harmony export */   isPortableTextToolkitSpan: () => (/* binding */ isPortableTextToolkitSpan),\n/* harmony export */   isPortableTextToolkitTextNode: () => (/* binding */ isPortableTextToolkitTextNode),\n/* harmony export */   nestLists: () => (/* binding */ nestLists),\n/* harmony export */   sortMarksByOccurences: () => (/* binding */ sortMarksByOccurences),\n/* harmony export */   spanToPlainText: () => (/* binding */ spanToPlainText),\n/* harmony export */   toPlainText: () => (/* binding */ toPlainText)\n/* harmony export */ });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction isPortableTextSpan(node) {\n  return node._type === \"span\" && \"text\" in node && typeof node.text == \"string\" && (typeof node.marks > \"u\" || Array.isArray(node.marks) && node.marks.every(mark => typeof mark == \"string\"));\n}\nfunction isPortableTextBlock(node) {\n  return (\n    // A block doesn't _have_ to be named 'block' - to differentiate between\n    // allowed child types and marks, one might name them differently\n    typeof node._type == \"string\" &&\n    // Toolkit-types like nested spans are @-prefixed\n    node._type[0] !== \"@\" && (\n    // `markDefs` isn't _required_ per say, but if it's there, it needs to be an array\n    !(\"markDefs\" in node) || !node.markDefs || Array.isArray(node.markDefs) &&\n    // Every mark definition needs to have an `_key` to be mappable in child spans\n    node.markDefs.every(def => typeof def._key == \"string\")) &&\n    // `children` is required and needs to be an array\n    \"children\" in node && Array.isArray(node.children) &&\n    // All children are objects with `_type` (usually spans, but can contain other stuff)\n    node.children.every(child => typeof child == \"object\" && \"_type\" in child)\n  );\n}\nfunction isPortableTextListItemBlock(block) {\n  return isPortableTextBlock(block) && \"listItem\" in block && typeof block.listItem == \"string\" && (typeof block.level > \"u\" || typeof block.level == \"number\");\n}\nfunction isPortableTextToolkitList(block) {\n  return block._type === \"@list\";\n}\nfunction isPortableTextToolkitSpan(span) {\n  return span._type === \"@span\";\n}\nfunction isPortableTextToolkitTextNode(node) {\n  return node._type === \"@text\";\n}\nconst knownDecorators = [\"strong\", \"em\", \"code\", \"underline\", \"strike-through\"];\nfunction sortMarksByOccurences(span, index, blockChildren) {\n  if (!isPortableTextSpan(span) || !span.marks) return [];\n  if (!span.marks.length) return [];\n  const marks = span.marks.slice(),\n    occurences = {};\n  return marks.forEach(mark => {\n    occurences[mark] = 1;\n    for (let siblingIndex = index + 1; siblingIndex < blockChildren.length; siblingIndex++) {\n      const sibling = blockChildren[siblingIndex];\n      if (sibling && isPortableTextSpan(sibling) && Array.isArray(sibling.marks) && sibling.marks.indexOf(mark) !== -1) occurences[mark]++;else break;\n    }\n  }), marks.sort((markA, markB) => sortMarks(occurences, markA, markB));\n}\nfunction sortMarks(occurences, markA, markB) {\n  const aOccurences = occurences[markA],\n    bOccurences = occurences[markB];\n  if (aOccurences !== bOccurences) return bOccurences - aOccurences;\n  const aKnownPos = knownDecorators.indexOf(markA),\n    bKnownPos = knownDecorators.indexOf(markB);\n  return aKnownPos !== bKnownPos ? aKnownPos - bKnownPos : markA.localeCompare(markB);\n}\nfunction buildMarksTree(block) {\n  var _a, _b;\n  const {\n      children\n    } = block,\n    markDefs = (_a = block.markDefs) != null ? _a : [];\n  if (!children || !children.length) return [];\n  const sortedMarks = children.map(sortMarksByOccurences),\n    rootNode = {\n      _type: \"@span\",\n      children: [],\n      markType: \"<unknown>\"\n    };\n  let nodeStack = [rootNode];\n  for (let i = 0; i < children.length; i++) {\n    const span = children[i];\n    if (!span) continue;\n    const marksNeeded = sortedMarks[i] || [];\n    let pos = 1;\n    if (nodeStack.length > 1) for (pos; pos < nodeStack.length; pos++) {\n      const mark = ((_b = nodeStack[pos]) == null ? void 0 : _b.markKey) || \"\",\n        index = marksNeeded.indexOf(mark);\n      if (index === -1) break;\n      marksNeeded.splice(index, 1);\n    }\n    nodeStack = nodeStack.slice(0, pos);\n    let currentNode = nodeStack[nodeStack.length - 1];\n    if (currentNode) {\n      for (const markKey of marksNeeded) {\n        const markDef = markDefs == null ? void 0 : markDefs.find(def => def._key === markKey),\n          markType = markDef ? markDef._type : markKey,\n          node = {\n            _type: \"@span\",\n            _key: span._key,\n            children: [],\n            markDef,\n            markType,\n            markKey\n          };\n        currentNode.children.push(node), nodeStack.push(node), currentNode = node;\n      }\n      if (isPortableTextSpan(span)) {\n        const lines = span.text.split(`\n`);\n        for (let line = lines.length; line-- > 1;) lines.splice(line, 0, `\n`);\n        currentNode.children = currentNode.children.concat(lines.map(text => ({\n          _type: \"@text\",\n          text\n        })));\n      } else currentNode.children = currentNode.children.concat(span);\n    }\n  }\n  return rootNode.children;\n}\nfunction nestLists(blocks, mode) {\n  const tree = [];\n  let currentList;\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    if (block) {\n      if (!isPortableTextListItemBlock(block)) {\n        tree.push(block), currentList = void 0;\n        continue;\n      }\n      if (!currentList) {\n        currentList = listFromBlock(block, i, mode), tree.push(currentList);\n        continue;\n      }\n      if (blockMatchesList(block, currentList)) {\n        currentList.children.push(block);\n        continue;\n      }\n      if ((block.level || 1) > currentList.level) {\n        const newList = listFromBlock(block, i, mode);\n        if (mode === \"html\") {\n          const lastListItem = currentList.children[currentList.children.length - 1],\n            newLastChild = _objectSpread(_objectSpread({}, lastListItem), {}, {\n              children: [...lastListItem.children, newList]\n            });\n          currentList.children[currentList.children.length - 1] = newLastChild;\n        } else currentList.children.push(newList);\n        currentList = newList;\n        continue;\n      }\n      if ((block.level || 1) < currentList.level) {\n        const matchingBranch = tree[tree.length - 1],\n          match = matchingBranch && findListMatching(matchingBranch, block);\n        if (match) {\n          currentList = match, currentList.children.push(block);\n          continue;\n        }\n        currentList = listFromBlock(block, i, mode), tree.push(currentList);\n        continue;\n      }\n      if (block.listItem !== currentList.listItem) {\n        const matchingBranch = tree[tree.length - 1],\n          match = matchingBranch && findListMatching(matchingBranch, {\n            level: block.level || 1\n          });\n        if (match && match.listItem === block.listItem) {\n          currentList = match, currentList.children.push(block);\n          continue;\n        } else {\n          currentList = listFromBlock(block, i, mode), tree.push(currentList);\n          continue;\n        }\n      }\n      console.warn(\"Unknown state encountered for block\", block), tree.push(block);\n    }\n  }\n  return tree;\n}\nfunction blockMatchesList(block, list) {\n  return (block.level || 1) === list.level && block.listItem === list.listItem;\n}\nfunction listFromBlock(block, index, mode) {\n  return {\n    _type: \"@list\",\n    _key: `${block._key || `${index}`}-parent`,\n    mode,\n    level: block.level || 1,\n    listItem: block.listItem,\n    children: [block]\n  };\n}\nfunction findListMatching(rootNode, matching) {\n  const level = matching.level || 1,\n    style = matching.listItem || \"normal\",\n    filterOnType = typeof matching.listItem == \"string\";\n  if (isPortableTextToolkitList(rootNode) && (rootNode.level || 1) === level && filterOnType && (rootNode.listItem || \"normal\") === style) return rootNode;\n  if (!(\"children\" in rootNode)) return;\n  const node = rootNode.children[rootNode.children.length - 1];\n  return node && !isPortableTextSpan(node) ? findListMatching(node, matching) : void 0;\n}\nfunction spanToPlainText(span) {\n  let text = \"\";\n  return span.children.forEach(current => {\n    isPortableTextToolkitTextNode(current) ? text += current.text : isPortableTextToolkitSpan(current) && (text += spanToPlainText(current));\n  }), text;\n}\nconst leadingSpace = /^\\s/,\n  trailingSpace = /\\s$/;\nfunction toPlainText(block) {\n  const blocks = Array.isArray(block) ? block : [block];\n  let text = \"\";\n  return blocks.forEach((current, index) => {\n    if (!isPortableTextBlock(current)) return;\n    let pad = !1;\n    current.children.forEach(span => {\n      isPortableTextSpan(span) ? (text += pad && text && !trailingSpace.test(text) && !leadingSpace.test(span.text) ? \" \" : \"\", text += span.text, pad = !1) : pad = !0;\n    }), index !== blocks.length - 1 && (text += `\n\n`);\n  }), text;\n}\nconst LIST_NEST_MODE_HTML = \"html\",\n  LIST_NEST_MODE_DIRECT = \"direct\";\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@portabletext/toolkit/dist/index.js\n");

/***/ })

};
;