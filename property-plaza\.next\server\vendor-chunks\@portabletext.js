"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@portabletext";
exports.ids = ["vendor-chunks/@portabletext"];
exports.modules = {

/***/ "(ssr)/./node_modules/@portabletext/react/dist/index.js":
/*!********************************************************!*\
  !*** ./node_modules/@portabletext/react/dist/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PortableText: () => (/* binding */ PortableText),\n/* harmony export */   defaultComponents: () => (/* binding */ defaultComponents),\n/* harmony export */   mergeComponents: () => (/* binding */ mergeComponents),\n/* harmony export */   toPlainText: () => (/* reexport safe */ _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.toPlainText)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @portabletext/toolkit */ \"(ssr)/./node_modules/@portabletext/toolkit/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nconst _excluded = [\"block\", \"list\", \"listItem\", \"marks\", \"types\"],\n  _excluded2 = [\"listItem\"],\n  _excluded3 = [\"_key\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var s = Object.getOwnPropertySymbols(e); for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.includes(n)) continue; t[n] = r[n]; } return t; }\n\n\n\n\nconst defaultLists = {\n    number: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ol\", {\n      children\n    }),\n    bullet: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", {\n      children\n    })\n  },\n  DefaultListItem = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"li\", {\n    children\n  }),\n  link = ({\n    children,\n    value\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n    href: value?.href,\n    children\n  }),\n  underlineStyle = {\n    textDecoration: \"underline\"\n  },\n  defaultMarks = {\n    em: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"em\", {\n      children\n    }),\n    strong: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"strong\", {\n      children\n    }),\n    code: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"code\", {\n      children\n    }),\n    underline: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n      style: underlineStyle,\n      children\n    }),\n    \"strike-through\": ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"del\", {\n      children\n    }),\n    link\n  },\n  getTemplate = (type, prop) => `[@portabletext/react] Unknown ${type}, specify a component for it in the \\`components.${prop}\\` prop`,\n  unknownTypeWarning = typeName => getTemplate(`block type \"${typeName}\"`, \"types\"),\n  unknownMarkWarning = markType => getTemplate(`mark type \"${markType}\"`, \"marks\"),\n  unknownBlockStyleWarning = blockStyle => getTemplate(`block style \"${blockStyle}\"`, \"block\"),\n  unknownListStyleWarning = listStyle => getTemplate(`list style \"${listStyle}\"`, \"list\"),\n  unknownListItemStyleWarning = listStyle => getTemplate(`list item style \"${listStyle}\"`, \"listItem\");\nfunction printWarning(message) {\n  console.warn(message);\n}\nconst hidden = {\n    display: \"none\"\n  },\n  DefaultUnknownType = ({\n    value,\n    isInline\n  }) => {\n    const warning = unknownTypeWarning(value._type);\n    return isInline ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n      style: hidden,\n      children: warning\n    }) : /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n      style: hidden,\n      children: warning\n    });\n  },\n  DefaultUnknownMark = ({\n    markType,\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n    className: `unknown__pt__mark__${markType}`,\n    children\n  }),\n  DefaultUnknownBlockStyle = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n    children\n  }),\n  DefaultUnknownList = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"ul\", {\n    children\n  }),\n  DefaultUnknownListItem = ({\n    children\n  }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"li\", {\n    children\n  }),\n  DefaultHardBreak = () => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"br\", {}),\n  defaultBlockStyles = {\n    normal: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n      children\n    }),\n    blockquote: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"blockquote\", {\n      children\n    }),\n    h1: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h1\", {\n      children\n    }),\n    h2: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h2\", {\n      children\n    }),\n    h3: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h3\", {\n      children\n    }),\n    h4: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h4\", {\n      children\n    }),\n    h5: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h5\", {\n      children\n    }),\n    h6: ({\n      children\n    }) => /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"h6\", {\n      children\n    })\n  },\n  defaultComponents = {\n    types: {},\n    block: defaultBlockStyles,\n    marks: defaultMarks,\n    list: defaultLists,\n    listItem: DefaultListItem,\n    hardBreak: DefaultHardBreak,\n    unknownType: DefaultUnknownType,\n    unknownMark: DefaultUnknownMark,\n    unknownList: DefaultUnknownList,\n    unknownListItem: DefaultUnknownListItem,\n    unknownBlockStyle: DefaultUnknownBlockStyle\n  };\nfunction mergeComponents(parent, overrides) {\n  const {\n      block,\n      list,\n      listItem,\n      marks,\n      types\n    } = overrides,\n    rest = _objectWithoutProperties(overrides, _excluded);\n  return _objectSpread(_objectSpread({}, parent), {}, {\n    block: mergeDeeply(parent, overrides, \"block\"),\n    list: mergeDeeply(parent, overrides, \"list\"),\n    listItem: mergeDeeply(parent, overrides, \"listItem\"),\n    marks: mergeDeeply(parent, overrides, \"marks\"),\n    types: mergeDeeply(parent, overrides, \"types\")\n  }, rest);\n}\nfunction mergeDeeply(parent, overrides, key) {\n  const override = overrides[key],\n    parentVal = parent[key];\n  return typeof override == \"function\" || override && typeof parentVal == \"function\" ? override : override ? _objectSpread(_objectSpread({}, parentVal), override) : parentVal;\n}\nfunction PortableText({\n  value: input,\n  components: componentOverrides,\n  listNestingMode,\n  onMissingComponent: missingComponentHandler = printWarning\n}) {\n  const handleMissingComponent = missingComponentHandler || noop,\n    blocks = Array.isArray(input) ? input : [input],\n    nested = (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.nestLists)(blocks, listNestingMode || _portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.LIST_NEST_MODE_HTML),\n    components = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => componentOverrides ? mergeComponents(defaultComponents, componentOverrides) : defaultComponents, [componentOverrides]),\n    renderNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => getNodeRenderer(components, handleMissingComponent), [components, handleMissingComponent]),\n    rendered = nested.map((node, index) => renderNode({\n      node,\n      index,\n      isInline: !1,\n      renderNode\n    }));\n  return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n    children: rendered\n  });\n}\nconst getNodeRenderer = (components, handleMissingComponent) => {\n  function renderNode(options) {\n    const {\n        node,\n        index,\n        isInline\n      } = options,\n      key = node._key || `node-${index}`;\n    return (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitList)(node) ? renderList(node, index, key) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextListItemBlock)(node) ? renderListItem(node, index, key) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitSpan)(node) ? renderSpan(node, index, key) : hasCustomComponentForNode(node) ? renderCustomBlock(node, index, key, isInline) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextBlock)(node) ? renderBlock(node, index, key, isInline) : (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.isPortableTextToolkitTextNode)(node) ? renderText(node, key) : renderUnknownType(node, index, key, isInline);\n  }\n  function hasCustomComponentForNode(node) {\n    return node._type in components.types;\n  }\n  function renderListItem(node, index, key) {\n    const tree = serializeBlock({\n        node,\n        index,\n        isInline: !1,\n        renderNode\n      }),\n      renderer = components.listItem,\n      Li = (typeof renderer == \"function\" ? renderer : renderer[node.listItem]) || components.unknownListItem;\n    if (Li === components.unknownListItem) {\n      const style = node.listItem || \"bullet\";\n      handleMissingComponent(unknownListItemStyleWarning(style), {\n        type: style,\n        nodeType: \"listItemStyle\"\n      });\n    }\n    let children = tree.children;\n    if (node.style && node.style !== \"normal\") {\n      const {\n          listItem\n        } = node,\n        blockNode = _objectWithoutProperties(node, _excluded2);\n      children = renderNode({\n        node: blockNode,\n        index,\n        isInline: !1,\n        renderNode\n      });\n    }\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Li, {\n      value: node,\n      index,\n      isInline: !1,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderList(node, index, key) {\n    const children = node.children.map((child, childIndex) => renderNode({\n        node: child._key ? child : _objectSpread(_objectSpread({}, child), {}, {\n          _key: `li-${index}-${childIndex}`\n        }),\n        index: childIndex,\n        isInline: !1,\n        renderNode\n      })),\n      component = components.list,\n      List = (typeof component == \"function\" ? component : component[node.listItem]) || components.unknownList;\n    if (List === components.unknownList) {\n      const style = node.listItem || \"bullet\";\n      handleMissingComponent(unknownListStyleWarning(style), {\n        nodeType: \"listStyle\",\n        type: style\n      });\n    }\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(List, {\n      value: node,\n      index,\n      isInline: !1,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderSpan(node, _index, key) {\n    const {\n        markDef,\n        markType,\n        markKey\n      } = node,\n      Span = components.marks[markType] || components.unknownMark,\n      children = node.children.map((child, childIndex) => renderNode({\n        node: child,\n        index: childIndex,\n        isInline: !0,\n        renderNode\n      }));\n    return Span === components.unknownMark && handleMissingComponent(unknownMarkWarning(markType), {\n      nodeType: \"mark\",\n      type: markType\n    }), /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Span, {\n      text: (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.spanToPlainText)(node),\n      value: markDef,\n      markType,\n      markKey,\n      renderNode,\n      children\n    }, key);\n  }\n  function renderBlock(node, index, key, isInline) {\n    const _serializeBlock = serializeBlock({\n        node,\n        index,\n        isInline,\n        renderNode\n      }),\n      {\n        _key\n      } = _serializeBlock,\n      props = _objectWithoutProperties(_serializeBlock, _excluded3),\n      style = props.node.style || \"normal\",\n      Block = (typeof components.block == \"function\" ? components.block : components.block[style]) || components.unknownBlockStyle;\n    return Block === components.unknownBlockStyle && handleMissingComponent(unknownBlockStyleWarning(style), {\n      nodeType: \"blockStyle\",\n      type: style\n    }), /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Block, _objectSpread(_objectSpread({}, props), {}, {\n      value: props.node,\n      renderNode\n    }), key);\n  }\n  function renderText(node, key) {\n    if (node.text === `\n`) {\n      const HardBreak = components.hardBreak;\n      return HardBreak ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(HardBreak, {}, key) : `\n`;\n    }\n    return node.text;\n  }\n  function renderUnknownType(node, index, key, isInline) {\n    const nodeOptions = {\n      value: node,\n      isInline,\n      index,\n      renderNode\n    };\n    handleMissingComponent(unknownTypeWarning(node._type), {\n      nodeType: \"block\",\n      type: node._type\n    });\n    const UnknownType = components.unknownType;\n    return /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(UnknownType, _objectSpread({}, nodeOptions), key);\n  }\n  function renderCustomBlock(node, index, key, isInline) {\n    const nodeOptions = {\n        value: node,\n        isInline,\n        index,\n        renderNode\n      },\n      Node = components.types[node._type];\n    return Node ? /* @__PURE__ */(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Node, _objectSpread({}, nodeOptions), key) : null;\n  }\n  return renderNode;\n};\nfunction serializeBlock(options) {\n  const {\n      node,\n      index,\n      isInline,\n      renderNode\n    } = options,\n    children = (0,_portabletext_toolkit__WEBPACK_IMPORTED_MODULE_2__.buildMarksTree)(node).map((child, i) => renderNode({\n      node: child,\n      isInline: !0,\n      index: i,\n      renderNode\n    }));\n  return {\n    _key: node._key || `block-${index}`,\n    children,\n    index,\n    isInline,\n    node\n  };\n}\nfunction noop() {}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@portabletext/react/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@portabletext/toolkit/dist/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@portabletext/toolkit/dist/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LIST_NEST_MODE_DIRECT: () => (/* binding */ LIST_NEST_MODE_DIRECT),\n/* harmony export */   LIST_NEST_MODE_HTML: () => (/* binding */ LIST_NEST_MODE_HTML),\n/* harmony export */   buildMarksTree: () => (/* binding */ buildMarksTree),\n/* harmony export */   isPortableTextBlock: () => (/* binding */ isPortableTextBlock),\n/* harmony export */   isPortableTextListItemBlock: () => (/* binding */ isPortableTextListItemBlock),\n/* harmony export */   isPortableTextSpan: () => (/* binding */ isPortableTextSpan),\n/* harmony export */   isPortableTextToolkitList: () => (/* binding */ isPortableTextToolkitList),\n/* harmony export */   isPortableTextToolkitSpan: () => (/* binding */ isPortableTextToolkitSpan),\n/* harmony export */   isPortableTextToolkitTextNode: () => (/* binding */ isPortableTextToolkitTextNode),\n/* harmony export */   nestLists: () => (/* binding */ nestLists),\n/* harmony export */   sortMarksByOccurences: () => (/* binding */ sortMarksByOccurences),\n/* harmony export */   spanToPlainText: () => (/* binding */ spanToPlainText),\n/* harmony export */   toPlainText: () => (/* binding */ toPlainText)\n/* harmony export */ });\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction isPortableTextSpan(node) {\n  return node._type === \"span\" && \"text\" in node && typeof node.text == \"string\" && (typeof node.marks > \"u\" || Array.isArray(node.marks) && node.marks.every(mark => typeof mark == \"string\"));\n}\nfunction isPortableTextBlock(node) {\n  return (\n    // A block doesn't _have_ to be named 'block' - to differentiate between\n    // allowed child types and marks, one might name them differently\n    typeof node._type == \"string\" &&\n    // Toolkit-types like nested spans are @-prefixed\n    node._type[0] !== \"@\" && (\n    // `markDefs` isn't _required_ per say, but if it's there, it needs to be an array\n    !(\"markDefs\" in node) || !node.markDefs || Array.isArray(node.markDefs) &&\n    // Every mark definition needs to have an `_key` to be mappable in child spans\n    node.markDefs.every(def => typeof def._key == \"string\")) &&\n    // `children` is required and needs to be an array\n    \"children\" in node && Array.isArray(node.children) &&\n    // All children are objects with `_type` (usually spans, but can contain other stuff)\n    node.children.every(child => typeof child == \"object\" && \"_type\" in child)\n  );\n}\nfunction isPortableTextListItemBlock(block) {\n  return isPortableTextBlock(block) && \"listItem\" in block && typeof block.listItem == \"string\" && (typeof block.level > \"u\" || typeof block.level == \"number\");\n}\nfunction isPortableTextToolkitList(block) {\n  return block._type === \"@list\";\n}\nfunction isPortableTextToolkitSpan(span) {\n  return span._type === \"@span\";\n}\nfunction isPortableTextToolkitTextNode(node) {\n  return node._type === \"@text\";\n}\nconst knownDecorators = [\"strong\", \"em\", \"code\", \"underline\", \"strike-through\"];\nfunction sortMarksByOccurences(span, index, blockChildren) {\n  if (!isPortableTextSpan(span) || !span.marks) return [];\n  if (!span.marks.length) return [];\n  const marks = span.marks.slice(),\n    occurences = {};\n  return marks.forEach(mark => {\n    occurences[mark] = 1;\n    for (let siblingIndex = index + 1; siblingIndex < blockChildren.length; siblingIndex++) {\n      const sibling = blockChildren[siblingIndex];\n      if (sibling && isPortableTextSpan(sibling) && Array.isArray(sibling.marks) && sibling.marks.indexOf(mark) !== -1) occurences[mark]++;else break;\n    }\n  }), marks.sort((markA, markB) => sortMarks(occurences, markA, markB));\n}\nfunction sortMarks(occurences, markA, markB) {\n  const aOccurences = occurences[markA],\n    bOccurences = occurences[markB];\n  if (aOccurences !== bOccurences) return bOccurences - aOccurences;\n  const aKnownPos = knownDecorators.indexOf(markA),\n    bKnownPos = knownDecorators.indexOf(markB);\n  return aKnownPos !== bKnownPos ? aKnownPos - bKnownPos : markA.localeCompare(markB);\n}\nfunction buildMarksTree(block) {\n  var _a;\n  const {\n    children,\n    markDefs = []\n  } = block;\n  if (!children || !children.length) return [];\n  const sortedMarks = children.map(sortMarksByOccurences),\n    rootNode = {\n      _type: \"@span\",\n      children: [],\n      markType: \"<unknown>\"\n    };\n  let nodeStack = [rootNode];\n  for (let i = 0; i < children.length; i++) {\n    const span = children[i];\n    if (!span) continue;\n    const marksNeeded = sortedMarks[i] || [];\n    let pos = 1;\n    if (nodeStack.length > 1) for (pos; pos < nodeStack.length; pos++) {\n      const mark = ((_a = nodeStack[pos]) == null ? void 0 : _a.markKey) || \"\",\n        index = marksNeeded.indexOf(mark);\n      if (index === -1) break;\n      marksNeeded.splice(index, 1);\n    }\n    nodeStack = nodeStack.slice(0, pos);\n    let currentNode = nodeStack[nodeStack.length - 1];\n    if (currentNode) {\n      for (const markKey of marksNeeded) {\n        const markDef = markDefs.find(def => def._key === markKey),\n          markType = markDef ? markDef._type : markKey,\n          node = {\n            _type: \"@span\",\n            _key: span._key,\n            children: [],\n            markDef,\n            markType,\n            markKey\n          };\n        currentNode.children.push(node), nodeStack.push(node), currentNode = node;\n      }\n      if (isPortableTextSpan(span)) {\n        const lines = span.text.split(`\n`);\n        for (let line = lines.length; line-- > 1;) lines.splice(line, 0, `\n`);\n        currentNode.children = currentNode.children.concat(lines.map(text => ({\n          _type: \"@text\",\n          text\n        })));\n      } else currentNode.children = currentNode.children.concat(span);\n    }\n  }\n  return rootNode.children;\n}\nfunction nestLists(blocks, mode) {\n  const tree = [];\n  let currentList;\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i];\n    if (block) {\n      if (!isPortableTextListItemBlock(block)) {\n        tree.push(block), currentList = void 0;\n        continue;\n      }\n      if (!currentList) {\n        currentList = listFromBlock(block, i, mode), tree.push(currentList);\n        continue;\n      }\n      if (blockMatchesList(block, currentList)) {\n        currentList.children.push(block);\n        continue;\n      }\n      if ((block.level || 1) > currentList.level) {\n        const newList = listFromBlock(block, i, mode);\n        if (mode === \"html\") {\n          const lastListItem = currentList.children[currentList.children.length - 1],\n            newLastChild = _objectSpread(_objectSpread({}, lastListItem), {}, {\n              children: [...lastListItem.children, newList]\n            });\n          currentList.children[currentList.children.length - 1] = newLastChild;\n        } else currentList.children.push(newList);\n        currentList = newList;\n        continue;\n      }\n      if ((block.level || 1) < currentList.level) {\n        const matchingBranch = tree[tree.length - 1],\n          match = matchingBranch && findListMatching(matchingBranch, block);\n        if (match) {\n          currentList = match, currentList.children.push(block);\n          continue;\n        }\n        currentList = listFromBlock(block, i, mode), tree.push(currentList);\n        continue;\n      }\n      if (block.listItem !== currentList.listItem) {\n        const matchingBranch = tree[tree.length - 1],\n          match = matchingBranch && findListMatching(matchingBranch, {\n            level: block.level || 1\n          });\n        if (match && match.listItem === block.listItem) {\n          currentList = match, currentList.children.push(block);\n          continue;\n        } else {\n          currentList = listFromBlock(block, i, mode), tree.push(currentList);\n          continue;\n        }\n      }\n      console.warn(\"Unknown state encountered for block\", block), tree.push(block);\n    }\n  }\n  return tree;\n}\nfunction blockMatchesList(block, list) {\n  return (block.level || 1) === list.level && block.listItem === list.listItem;\n}\nfunction listFromBlock(block, index, mode) {\n  return {\n    _type: \"@list\",\n    _key: `${block._key || `${index}`}-parent`,\n    mode,\n    level: block.level || 1,\n    listItem: block.listItem,\n    children: [block]\n  };\n}\nfunction findListMatching(rootNode, matching) {\n  const level = matching.level || 1,\n    style = matching.listItem || \"normal\",\n    filterOnType = typeof matching.listItem == \"string\";\n  if (isPortableTextToolkitList(rootNode) && (rootNode.level || 1) === level && filterOnType && (rootNode.listItem || \"normal\") === style) return rootNode;\n  if (!(\"children\" in rootNode)) return;\n  const node = rootNode.children[rootNode.children.length - 1];\n  return node && !isPortableTextSpan(node) ? findListMatching(node, matching) : void 0;\n}\nfunction spanToPlainText(span) {\n  let text = \"\";\n  return span.children.forEach(current => {\n    isPortableTextToolkitTextNode(current) ? text += current.text : isPortableTextToolkitSpan(current) && (text += spanToPlainText(current));\n  }), text;\n}\nconst leadingSpace = /^\\s/,\n  trailingSpace = /\\s$/;\nfunction toPlainText(block) {\n  const blocks = Array.isArray(block) ? block : [block];\n  let text = \"\";\n  return blocks.forEach((current, index) => {\n    if (!isPortableTextBlock(current)) return;\n    let pad = !1;\n    current.children.forEach(span => {\n      isPortableTextSpan(span) ? (text += pad && text && !trailingSpace.test(text) && !leadingSpace.test(span.text) ? \" \" : \"\", text += span.text, pad = !1) : pad = !0;\n    }), index !== blocks.length - 1 && (text += `\n\n`);\n  }), text;\n}\nconst LIST_NEST_MODE_HTML = \"html\",\n  LIST_NEST_MODE_DIRECT = \"direct\";\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@portabletext/toolkit/dist/index.js\n");

/***/ })

};
;