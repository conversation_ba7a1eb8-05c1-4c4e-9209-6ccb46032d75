"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/focusManager.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusManager: () => (/* binding */ FocusManager),\n/* harmony export */   focusManager: () => (/* binding */ focusManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/focusManager.ts\n\n\nvar FocusManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onFocus) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup((focused) => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach((listener) => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\n\n//# sourceMappingURL=focusManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasNextPage: () => (/* binding */ hasNextPage),\n/* harmony export */   hasPreviousPage: () => (/* binding */ hasPreviousPage),\n/* harmony export */   infiniteQueryBehavior: () => (/* binding */ infiniteQueryBehavior)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/infiniteQueryBehavior.ts\n\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = { pages: [], pageParams: [] };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = (object) => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.ensureQueryFn)(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(\n            queryFnContext\n          );\n          const { maxPages } = context.options;\n          const addTo = previous ? _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToStart : _utils_js__WEBPACK_IMPORTED_MODULE_0__.addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(\n            fetchFn,\n            {\n              queryKey: context.queryKey,\n              meta: context.options.meta,\n              signal: context.signal\n            },\n            query\n          );\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, { pages, pageParams }) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(\n    pages[lastIndex],\n    pages,\n    pageParams[lastIndex],\n    pageParams\n  ) : void 0;\n}\nfunction getPreviousPageParam(options, { pages, pageParams }) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data)\n    return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam)\n    return false;\n  return getPreviousPageParam(options, data) != null;\n}\n\n//# sourceMappingURL=infiniteQueryBehavior.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutation.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mutation: () => (/* binding */ Mutation),\n/* harmony export */   getDefaultState: () => (/* binding */ getDefaultState)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n// src/mutation.ts\n\n\n\nvar Mutation = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter((x) => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ?? // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_1__.createRetryer)({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (!restored) {\n        this.#dispatch({ type: \"pending\", variables, isPaused });\n        await this.#mutationCache.config.onMutate?.(\n          variables,\n          this\n        );\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(\n        data,\n        variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(\n        data,\n        null,\n        this.state.variables,\n        this.state.context,\n        this\n      );\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({ type: \"success\", data });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(\n          error,\n          variables,\n          this.state.context,\n          this\n        );\n        await this.options.onError?.(\n          error,\n          variables,\n          this.state.context\n        );\n        await this.#mutationCache.config.onSettled?.(\n          void 0,\n          error,\n          this.state.variables,\n          this.state.context,\n          this\n        );\n        await this.options.onSettled?.(\n          void 0,\n          error,\n          variables,\n          this.state.context\n        );\n        throw error;\n      } finally {\n        this.#dispatch({ type: \"error\", error });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.#observers.forEach((observer) => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\n\n//# sourceMappingURL=mutation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL211dGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDbUQ7QUFDUjtBQUNFO0FBQzdDLDZCQUE2QixvREFBUztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDBEQUFhO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSx5QkFBeUIscUNBQXFDO0FBQzlELE9BQU87QUFDUDtBQUNBLHlCQUF5QixlQUFlO0FBQ3hDLE9BQU87QUFDUDtBQUNBLHlCQUF5QixrQkFBa0I7QUFDM0MsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLHNDQUFzQztBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qix1QkFBdUI7QUFDOUM7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUix5QkFBeUIsc0JBQXNCO0FBQy9DO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSw0REFBYTtBQUNqQjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL211dGF0aW9uLmpzPzZlMTciXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL211dGF0aW9uLnRzXG5pbXBvcnQgeyBub3RpZnlNYW5hZ2VyIH0gZnJvbSBcIi4vbm90aWZ5TWFuYWdlci5qc1wiO1xuaW1wb3J0IHsgUmVtb3ZhYmxlIH0gZnJvbSBcIi4vcmVtb3ZhYmxlLmpzXCI7XG5pbXBvcnQgeyBjcmVhdGVSZXRyeWVyIH0gZnJvbSBcIi4vcmV0cnllci5qc1wiO1xudmFyIE11dGF0aW9uID0gY2xhc3MgZXh0ZW5kcyBSZW1vdmFibGUge1xuICAjb2JzZXJ2ZXJzO1xuICAjbXV0YXRpb25DYWNoZTtcbiAgI3JldHJ5ZXI7XG4gIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy5tdXRhdGlvbklkID0gY29uZmlnLm11dGF0aW9uSWQ7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZSA9IGNvbmZpZy5tdXRhdGlvbkNhY2hlO1xuICAgIHRoaXMuI29ic2VydmVycyA9IFtdO1xuICAgIHRoaXMuc3RhdGUgPSBjb25maWcuc3RhdGUgfHwgZ2V0RGVmYXVsdFN0YXRlKCk7XG4gICAgdGhpcy5zZXRPcHRpb25zKGNvbmZpZy5vcHRpb25zKTtcbiAgICB0aGlzLnNjaGVkdWxlR2MoKTtcbiAgfVxuICBzZXRPcHRpb25zKG9wdGlvbnMpIHtcbiAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zO1xuICAgIHRoaXMudXBkYXRlR2NUaW1lKHRoaXMub3B0aW9ucy5nY1RpbWUpO1xuICB9XG4gIGdldCBtZXRhKCkge1xuICAgIHJldHVybiB0aGlzLm9wdGlvbnMubWV0YTtcbiAgfVxuICBhZGRPYnNlcnZlcihvYnNlcnZlcikge1xuICAgIGlmICghdGhpcy4jb2JzZXJ2ZXJzLmluY2x1ZGVzKG9ic2VydmVyKSkge1xuICAgICAgdGhpcy4jb2JzZXJ2ZXJzLnB1c2gob2JzZXJ2ZXIpO1xuICAgICAgdGhpcy5jbGVhckdjVGltZW91dCgpO1xuICAgICAgdGhpcy4jbXV0YXRpb25DYWNoZS5ub3RpZnkoe1xuICAgICAgICB0eXBlOiBcIm9ic2VydmVyQWRkZWRcIixcbiAgICAgICAgbXV0YXRpb246IHRoaXMsXG4gICAgICAgIG9ic2VydmVyXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbiAgcmVtb3ZlT2JzZXJ2ZXIob2JzZXJ2ZXIpIHtcbiAgICB0aGlzLiNvYnNlcnZlcnMgPSB0aGlzLiNvYnNlcnZlcnMuZmlsdGVyKCh4KSA9PiB4ICE9PSBvYnNlcnZlcik7XG4gICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgdGhpcy4jbXV0YXRpb25DYWNoZS5ub3RpZnkoe1xuICAgICAgdHlwZTogXCJvYnNlcnZlclJlbW92ZWRcIixcbiAgICAgIG11dGF0aW9uOiB0aGlzLFxuICAgICAgb2JzZXJ2ZXJcbiAgICB9KTtcbiAgfVxuICBvcHRpb25hbFJlbW92ZSgpIHtcbiAgICBpZiAoIXRoaXMuI29ic2VydmVycy5sZW5ndGgpIHtcbiAgICAgIGlmICh0aGlzLnN0YXRlLnN0YXR1cyA9PT0gXCJwZW5kaW5nXCIpIHtcbiAgICAgICAgdGhpcy5zY2hlZHVsZUdjKCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLiNtdXRhdGlvbkNhY2hlLnJlbW92ZSh0aGlzKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbiAgY29udGludWUoKSB7XG4gICAgcmV0dXJuIHRoaXMuI3JldHJ5ZXI/LmNvbnRpbnVlKCkgPz8gLy8gY29udGludWluZyBhIG11dGF0aW9uIGFzc3VtZXMgdGhhdCB2YXJpYWJsZXMgYXJlIHNldCwgbXV0YXRpb24gbXVzdCBoYXZlIGJlZW4gZGVoeWRyYXRlZCBiZWZvcmVcbiAgICB0aGlzLmV4ZWN1dGUodGhpcy5zdGF0ZS52YXJpYWJsZXMpO1xuICB9XG4gIGFzeW5jIGV4ZWN1dGUodmFyaWFibGVzKSB7XG4gICAgdGhpcy4jcmV0cnllciA9IGNyZWF0ZVJldHJ5ZXIoe1xuICAgICAgZm46ICgpID0+IHtcbiAgICAgICAgaWYgKCF0aGlzLm9wdGlvbnMubXV0YXRpb25Gbikge1xuICAgICAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChuZXcgRXJyb3IoXCJObyBtdXRhdGlvbkZuIGZvdW5kXCIpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5vcHRpb25zLm11dGF0aW9uRm4odmFyaWFibGVzKTtcbiAgICAgIH0sXG4gICAgICBvbkZhaWw6IChmYWlsdXJlQ291bnQsIGVycm9yKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJmYWlsZWRcIiwgZmFpbHVyZUNvdW50LCBlcnJvciB9KTtcbiAgICAgIH0sXG4gICAgICBvblBhdXNlOiAoKSA9PiB7XG4gICAgICAgIHRoaXMuI2Rpc3BhdGNoKHsgdHlwZTogXCJwYXVzZVwiIH0pO1xuICAgICAgfSxcbiAgICAgIG9uQ29udGludWU6ICgpID0+IHtcbiAgICAgICAgdGhpcy4jZGlzcGF0Y2goeyB0eXBlOiBcImNvbnRpbnVlXCIgfSk7XG4gICAgICB9LFxuICAgICAgcmV0cnk6IHRoaXMub3B0aW9ucy5yZXRyeSA/PyAwLFxuICAgICAgcmV0cnlEZWxheTogdGhpcy5vcHRpb25zLnJldHJ5RGVsYXksXG4gICAgICBuZXR3b3JrTW9kZTogdGhpcy5vcHRpb25zLm5ldHdvcmtNb2RlLFxuICAgICAgY2FuUnVuOiAoKSA9PiB0aGlzLiNtdXRhdGlvbkNhY2hlLmNhblJ1bih0aGlzKVxuICAgIH0pO1xuICAgIGNvbnN0IHJlc3RvcmVkID0gdGhpcy5zdGF0ZS5zdGF0dXMgPT09IFwicGVuZGluZ1wiO1xuICAgIGNvbnN0IGlzUGF1c2VkID0gIXRoaXMuI3JldHJ5ZXIuY2FuU3RhcnQoKTtcbiAgICB0cnkge1xuICAgICAgaWYgKCFyZXN0b3JlZCkge1xuICAgICAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwicGVuZGluZ1wiLCB2YXJpYWJsZXMsIGlzUGF1c2VkIH0pO1xuICAgICAgICBhd2FpdCB0aGlzLiNtdXRhdGlvbkNhY2hlLmNvbmZpZy5vbk11dGF0ZT8uKFxuICAgICAgICAgIHZhcmlhYmxlcyxcbiAgICAgICAgICB0aGlzXG4gICAgICAgICk7XG4gICAgICAgIGNvbnN0IGNvbnRleHQgPSBhd2FpdCB0aGlzLm9wdGlvbnMub25NdXRhdGU/Lih2YXJpYWJsZXMpO1xuICAgICAgICBpZiAoY29udGV4dCAhPT0gdGhpcy5zdGF0ZS5jb250ZXh0KSB7XG4gICAgICAgICAgdGhpcy4jZGlzcGF0Y2goe1xuICAgICAgICAgICAgdHlwZTogXCJwZW5kaW5nXCIsXG4gICAgICAgICAgICBjb250ZXh0LFxuICAgICAgICAgICAgdmFyaWFibGVzLFxuICAgICAgICAgICAgaXNQYXVzZWRcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHRoaXMuI3JldHJ5ZXIuc3RhcnQoKTtcbiAgICAgIGF3YWl0IHRoaXMuI211dGF0aW9uQ2FjaGUuY29uZmlnLm9uU3VjY2Vzcz8uKFxuICAgICAgICBkYXRhLFxuICAgICAgICB2YXJpYWJsZXMsXG4gICAgICAgIHRoaXMuc3RhdGUuY29udGV4dCxcbiAgICAgICAgdGhpc1xuICAgICAgKTtcbiAgICAgIGF3YWl0IHRoaXMub3B0aW9ucy5vblN1Y2Nlc3M/LihkYXRhLCB2YXJpYWJsZXMsIHRoaXMuc3RhdGUuY29udGV4dCk7XG4gICAgICBhd2FpdCB0aGlzLiNtdXRhdGlvbkNhY2hlLmNvbmZpZy5vblNldHRsZWQ/LihcbiAgICAgICAgZGF0YSxcbiAgICAgICAgbnVsbCxcbiAgICAgICAgdGhpcy5zdGF0ZS52YXJpYWJsZXMsXG4gICAgICAgIHRoaXMuc3RhdGUuY29udGV4dCxcbiAgICAgICAgdGhpc1xuICAgICAgKTtcbiAgICAgIGF3YWl0IHRoaXMub3B0aW9ucy5vblNldHRsZWQ/LihkYXRhLCBudWxsLCB2YXJpYWJsZXMsIHRoaXMuc3RhdGUuY29udGV4dCk7XG4gICAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwic3VjY2Vzc1wiLCBkYXRhIH0pO1xuICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHRoaXMuI211dGF0aW9uQ2FjaGUuY29uZmlnLm9uRXJyb3I/LihcbiAgICAgICAgICBlcnJvcixcbiAgICAgICAgICB2YXJpYWJsZXMsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5jb250ZXh0LFxuICAgICAgICAgIHRoaXNcbiAgICAgICAgKTtcbiAgICAgICAgYXdhaXQgdGhpcy5vcHRpb25zLm9uRXJyb3I/LihcbiAgICAgICAgICBlcnJvcixcbiAgICAgICAgICB2YXJpYWJsZXMsXG4gICAgICAgICAgdGhpcy5zdGF0ZS5jb250ZXh0XG4gICAgICAgICk7XG4gICAgICAgIGF3YWl0IHRoaXMuI211dGF0aW9uQ2FjaGUuY29uZmlnLm9uU2V0dGxlZD8uKFxuICAgICAgICAgIHZvaWQgMCxcbiAgICAgICAgICBlcnJvcixcbiAgICAgICAgICB0aGlzLnN0YXRlLnZhcmlhYmxlcyxcbiAgICAgICAgICB0aGlzLnN0YXRlLmNvbnRleHQsXG4gICAgICAgICAgdGhpc1xuICAgICAgICApO1xuICAgICAgICBhd2FpdCB0aGlzLm9wdGlvbnMub25TZXR0bGVkPy4oXG4gICAgICAgICAgdm9pZCAwLFxuICAgICAgICAgIGVycm9yLFxuICAgICAgICAgIHZhcmlhYmxlcyxcbiAgICAgICAgICB0aGlzLnN0YXRlLmNvbnRleHRcbiAgICAgICAgKTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICB0aGlzLiNkaXNwYXRjaCh7IHR5cGU6IFwiZXJyb3JcIiwgZXJyb3IgfSk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHRoaXMuI211dGF0aW9uQ2FjaGUucnVuTmV4dCh0aGlzKTtcbiAgICB9XG4gIH1cbiAgI2Rpc3BhdGNoKGFjdGlvbikge1xuICAgIGNvbnN0IHJlZHVjZXIgPSAoc3RhdGUpID0+IHtcbiAgICAgIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICAgICAgY2FzZSBcImZhaWxlZFwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGZhaWx1cmVDb3VudDogYWN0aW9uLmZhaWx1cmVDb3VudCxcbiAgICAgICAgICAgIGZhaWx1cmVSZWFzb246IGFjdGlvbi5lcnJvclxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJwYXVzZVwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGlzUGF1c2VkOiB0cnVlXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcImNvbnRpbnVlXCI6XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlXG4gICAgICAgICAgfTtcbiAgICAgICAgY2FzZSBcInBlbmRpbmdcIjpcbiAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgICAgICBjb250ZXh0OiBhY3Rpb24uY29udGV4dCxcbiAgICAgICAgICAgIGRhdGE6IHZvaWQgMCxcbiAgICAgICAgICAgIGZhaWx1cmVDb3VudDogMCxcbiAgICAgICAgICAgIGZhaWx1cmVSZWFzb246IG51bGwsXG4gICAgICAgICAgICBlcnJvcjogbnVsbCxcbiAgICAgICAgICAgIGlzUGF1c2VkOiBhY3Rpb24uaXNQYXVzZWQsXG4gICAgICAgICAgICBzdGF0dXM6IFwicGVuZGluZ1wiLFxuICAgICAgICAgICAgdmFyaWFibGVzOiBhY3Rpb24udmFyaWFibGVzLFxuICAgICAgICAgICAgc3VibWl0dGVkQXQ6IERhdGUubm93KClcbiAgICAgICAgICB9O1xuICAgICAgICBjYXNlIFwic3VjY2Vzc1wiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGRhdGE6IGFjdGlvbi5kYXRhLFxuICAgICAgICAgICAgZmFpbHVyZUNvdW50OiAwLFxuICAgICAgICAgICAgZmFpbHVyZVJlYXNvbjogbnVsbCxcbiAgICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgICAgc3RhdHVzOiBcInN1Y2Nlc3NcIixcbiAgICAgICAgICAgIGlzUGF1c2VkOiBmYWxzZVxuICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgXCJlcnJvclwiOlxuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAuLi5zdGF0ZSxcbiAgICAgICAgICAgIGRhdGE6IHZvaWQgMCxcbiAgICAgICAgICAgIGVycm9yOiBhY3Rpb24uZXJyb3IsXG4gICAgICAgICAgICBmYWlsdXJlQ291bnQ6IHN0YXRlLmZhaWx1cmVDb3VudCArIDEsXG4gICAgICAgICAgICBmYWlsdXJlUmVhc29uOiBhY3Rpb24uZXJyb3IsXG4gICAgICAgICAgICBpc1BhdXNlZDogZmFsc2UsXG4gICAgICAgICAgICBzdGF0dXM6IFwiZXJyb3JcIlxuICAgICAgICAgIH07XG4gICAgICB9XG4gICAgfTtcbiAgICB0aGlzLnN0YXRlID0gcmVkdWNlcih0aGlzLnN0YXRlKTtcbiAgICBub3RpZnlNYW5hZ2VyLmJhdGNoKCgpID0+IHtcbiAgICAgIHRoaXMuI29ic2VydmVycy5mb3JFYWNoKChvYnNlcnZlcikgPT4ge1xuICAgICAgICBvYnNlcnZlci5vbk11dGF0aW9uVXBkYXRlKGFjdGlvbik7XG4gICAgICB9KTtcbiAgICAgIHRoaXMuI211dGF0aW9uQ2FjaGUubm90aWZ5KHtcbiAgICAgICAgbXV0YXRpb246IHRoaXMsXG4gICAgICAgIHR5cGU6IFwidXBkYXRlZFwiLFxuICAgICAgICBhY3Rpb25cbiAgICAgIH0pO1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gZ2V0RGVmYXVsdFN0YXRlKCkge1xuICByZXR1cm4ge1xuICAgIGNvbnRleHQ6IHZvaWQgMCxcbiAgICBkYXRhOiB2b2lkIDAsXG4gICAgZXJyb3I6IG51bGwsXG4gICAgZmFpbHVyZUNvdW50OiAwLFxuICAgIGZhaWx1cmVSZWFzb246IG51bGwsXG4gICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgIHN0YXR1czogXCJpZGxlXCIsXG4gICAgdmFyaWFibGVzOiB2b2lkIDAsXG4gICAgc3VibWl0dGVkQXQ6IDBcbiAgfTtcbn1cbmV4cG9ydCB7XG4gIE11dGF0aW9uLFxuICBnZXREZWZhdWx0U3RhdGVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tdXRhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationCache.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationCache: () => (/* binding */ MutationCache)\n/* harmony export */ });\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/mutationCache.ts\n\n\n\n\nvar MutationCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */ new Map();\n    this.#mutationId = Date.now();\n  }\n  #mutations;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new _mutation_js__WEBPACK_IMPORTED_MODULE_1__.Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    const scope = scopeFor(mutation);\n    const mutations = this.#mutations.get(scope) ?? [];\n    mutations.push(mutation);\n    this.#mutations.set(scope, mutations);\n    this.notify({ type: \"added\", mutation });\n  }\n  remove(mutation) {\n    const scope = scopeFor(mutation);\n    if (this.#mutations.has(scope)) {\n      const mutations = this.#mutations.get(scope)?.filter((x) => x !== mutation);\n      if (mutations) {\n        if (mutations.length === 0) {\n          this.#mutations.delete(scope);\n        } else {\n          this.#mutations.set(scope, mutations);\n        }\n      }\n    }\n    this.notify({ type: \"removed\", mutation });\n  }\n  canRun(mutation) {\n    const firstPendingMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m.state.status === \"pending\");\n    return !firstPendingMutation || firstPendingMutation === mutation;\n  }\n  runNext(mutation) {\n    const foundMutation = this.#mutations.get(scopeFor(mutation))?.find((m) => m !== mutation && m.state.isPaused);\n    return foundMutation?.continue() ?? Promise.resolve();\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.getAll().forEach((mutation) => {\n        this.remove(mutation);\n      });\n    });\n  }\n  getAll() {\n    return [...this.#mutations.values()].flat();\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(defaultedFilters, mutation)\n    );\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter((mutation) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.matchMutation)(filters, mutation));\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter((x) => x.state.isPaused);\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_2__.notifyManager.batch(\n      () => Promise.all(\n        pausedMutations.map((mutation) => mutation.continue().catch(_utils_js__WEBPACK_IMPORTED_MODULE_3__.noop))\n      )\n    );\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id ?? String(mutation.mutationId);\n}\n\n//# sourceMappingURL=mutationCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/mutationObserver.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutationObserver: () => (/* binding */ MutationObserver)\n/* harmony export */ });\n/* harmony import */ var _mutation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mutation.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutation.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/mutationObserver.ts\n\n\n\n\nvar MutationObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(prevOptions.mutationKey) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashKey)(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? (0,_mutation_js__WEBPACK_IMPORTED_MODULE_2__.getDefaultState)();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(\n            void 0,\n            action.error,\n            variables,\n            context\n          );\n        }\n      }\n      this.listeners.forEach((listener) => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=mutationObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/notifyManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNotifyManager: () => (/* binding */ createNotifyManager),\n/* harmony export */   notifyManager: () => (/* binding */ notifyManager)\n/* harmony export */ });\n// src/notifyManager.ts\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = (callback) => {\n    callback();\n  };\n  let batchNotifyFn = (callback) => {\n    callback();\n  };\n  let scheduleFn = (cb) => setTimeout(cb, 0);\n  const schedule = (callback) => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach((callback) => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: (callback) => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: (callback) => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: (fn) => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: (fn) => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: (fn) => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\n\n//# sourceMappingURL=notifyManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/onlineManager.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnlineManager: () => (/* binding */ OnlineManager),\n/* harmony export */   onlineManager: () => (/* binding */ onlineManager)\n/* harmony export */ });\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/onlineManager.ts\n\n\nvar OnlineManager = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = (onOnline) => {\n      if (!_utils_js__WEBPACK_IMPORTED_MODULE_1__.isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach((listener) => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\n\n//# sourceMappingURL=onlineManager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/query.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Query: () => (/* binding */ Query),\n/* harmony export */   fetchState: () => (/* binding */ fetchState)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _retryer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./retryer.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\");\n/* harmony import */ var _removable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./removable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\");\n// src/query.ts\n\n\n\n\nvar Query = class extends _removable_js__WEBPACK_IMPORTED_MODULE_0__.Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#cache = config.cache;\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = { ...this.#defaultOptions, ...options };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.replaceData)(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({ type: \"setState\", state, setStateOptions });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_1__.noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({ silent: true });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(\n      (observer) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.resolveEnabled)(observer.options.enabled, this) !== false\n    );\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_1__.skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(\n        (observer) => observer.getCurrentResult().isStale\n      );\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.timeUntilStale)(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find((x) => x.shouldFetchOnWindowFocus());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find((x) => x.shouldFetchOnReconnect());\n    observer?.refetch({ cancelRefetch: false });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({ type: \"observerAdded\", query: this, observer });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter((x) => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({ revert: true });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({ type: \"observerRemoved\", query: this, observer });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({ type: \"invalidate\" });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({ silent: true });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find((x) => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (true) {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(\n          `As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`\n        );\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = (object) => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.ensureQueryFn)(this.options, fetchOptions);\n      const queryFnContext = {\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(\n          queryFn,\n          queryFnContext,\n          this\n        );\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(\n      context,\n      this\n    );\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({ type: \"fetch\", meta: context.fetchOptions?.meta });\n    }\n    const onError = (error) => {\n      if (!((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!(0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error)) {\n        this.#cache.config.onError?.(\n          error,\n          this\n        );\n        this.#cache.config.onSettled?.(\n          this.state.data,\n          error,\n          this\n        );\n      }\n      if (!this.isFetchingOptimistic) {\n        this.scheduleGc();\n      }\n      this.isFetchingOptimistic = false;\n    };\n    this.#retryer = (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.createRetryer)({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: (data) => {\n        if (data === void 0) {\n          if (true) {\n            console.error(\n              `Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`\n            );\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(\n          data,\n          this.state.error,\n          this\n        );\n        if (!this.isFetchingOptimistic) {\n          this.scheduleGc();\n        }\n        this.isFetchingOptimistic = false;\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({ type: \"failed\", failureCount, error });\n      },\n      onPause: () => {\n        this.#dispatch({ type: \"pause\" });\n      },\n      onContinue: () => {\n        this.#dispatch({ type: \"continue\" });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = (state) => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            }\n          };\n        case \"error\":\n          const error = action.error;\n          if ((0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.isCancelledError)(error) && error.revert && this.#revertState) {\n            return { ...this.#revertState, fetchStatus: \"idle\" };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.observers.forEach((observer) => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({ query: this, type: \"updated\", action });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: (0,_retryer_js__WEBPACK_IMPORTED_MODULE_2__.canFetch)(options.networkMode) ? \"fetching\" : \"paused\",\n    ...data === void 0 && {\n      error: null,\n      status: \"pending\"\n    }\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\n\n//# sourceMappingURL=query.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryCache.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryCache: () => (/* binding */ QueryCache)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n// src/queryCache.ts\n\n\n\n\nvar QueryCache = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */ new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.hashQueryKeyByOptions)(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new _query_js__WEBPACK_IMPORTED_MODULE_2__.Query({\n        cache: this,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({ type: \"removed\", query });\n    }\n  }\n  clear() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = { exact: true, ...filters };\n    return this.getAll().find(\n      (query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(defaultedFilters, query)\n    );\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter((query) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.matchQuery)(filters, query)) : queries;\n  }\n  notify(event) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.listeners.forEach((listener) => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batch(() => {\n      this.getAll().forEach((query) => {\n        query.onOnline();\n      });\n    });\n  }\n};\n\n//# sourceMappingURL=queryCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryClient.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClient: () => (/* binding */ QueryClient)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _queryCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./queryCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryCache.js\");\n/* harmony import */ var _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mutationCache.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationCache.js\");\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./infiniteQueryBehavior.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js\");\n// src/queryClient.ts\n\n\n\n\n\n\n\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new _queryCache_js__WEBPACK_IMPORTED_MODULE_0__.QueryCache();\n    this.#mutationCache = config.mutationCache || new _mutationCache_js__WEBPACK_IMPORTED_MODULE_1__.MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */ new Map();\n    this.#mutationDefaults = /* @__PURE__ */ new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1)\n      return;\n    this.#unsubscribeFocus = _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = _onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0)\n      return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: \"fetching\" }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({ ...filters, status: \"pending\" }).length;\n  }\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const cachedData = this.getQueryData(options.queryKey);\n    if (cachedData === void 0)\n      return this.fetchQuery(options);\n    else {\n      const defaultedOptions = this.defaultQueryOptions(options);\n      const query = this.#queryCache.build(this, defaultedOptions);\n      if (options.revalidateIfStale && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query))) {\n        void this.prefetchQuery(defaultedOptions);\n      }\n      return Promise.resolve(cachedData);\n    }\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({ queryKey });\n    const query = this.#queryCache.get(\n      defaultedOptions.queryHash\n    );\n    const prevData = query?.state.data;\n    const data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.functionalUpdate)(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, { ...options, manual: true });\n  }\n  setQueriesData(filters, updater, options) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map(({ queryKey }) => [\n        queryKey,\n        this.setQueryData(queryKey, updater, options)\n      ])\n    );\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({ queryKey });\n    return this.#queryCache.get(options.queryHash)?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    const refetchFilters = {\n      type: \"active\",\n      ...filters\n    };\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset();\n      });\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  cancelQueries(filters = {}, cancelOptions = {}) {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).map((query) => query.cancel(defaultedCancelOptions))\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  invalidateQueries(filters = {}, options = {}) {\n    return _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate();\n      });\n      if (filters.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      const refetchFilters = {\n        ...filters,\n        type: filters.refetchType ?? filters.type ?? \"active\"\n      };\n      return this.refetchQueries(refetchFilters, options);\n    });\n  }\n  refetchQueries(filters = {}, options) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options?.cancelRefetch ?? true\n    };\n    const promises = _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(\n      () => this.#queryCache.findAll(filters).filter((query) => !query.isDisabled()).map((query) => {\n        let promise = query.fetch(void 0, fetchOptions);\n        if (!fetchOptions.throwOnError) {\n          promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n        }\n        return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n      })\n    );\n    return Promise.all(promises).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.resolveStaleTime)(defaultedOptions.staleTime, query)\n    ) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = (0,_infiniteQueryBehavior_js__WEBPACK_IMPORTED_MODULE_6__.infiniteQueryBehavior)(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (_onlineManager_js__WEBPACK_IMPORTED_MODULE_3__.onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(queryKey, queryDefault.queryKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashKey)(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    let result = {};\n    defaults.forEach((queryDefault) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.partialMatchKey)(mutationKey, queryDefault.mutationKey)) {\n        result = { ...result, ...queryDefault.defaultOptions };\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.hashQueryKeyByOptions)(\n        defaultedOptions.queryKey,\n        defaultedOptions\n      );\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.enabled !== true && defaultedOptions.queryFn === _utils_js__WEBPACK_IMPORTED_MODULE_4__.skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...options?.mutationKey && this.getMutationDefaults(options.mutationKey),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\n\n//# sourceMappingURL=queryClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/queryObserver.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryObserver: () => (/* binding */ QueryObserver)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notifyManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _query_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./query.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/query.js\");\n/* harmony import */ var _subscribable_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./subscribable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/queryObserver.ts\n\n\n\n\n\n\nvar QueryObserver = class extends _subscribable_js__WEBPACK_IMPORTED_MODULE_0__.Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(\n        new Error(\"experimental_prefetchInRender feature flag is not enabled\")\n      );\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */ new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnReconnect\n    );\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(\n      this.#currentQuery,\n      this.options,\n      this.options.refetchOnWindowFocus\n    );\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options, notifyOptions) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\n        \"Expected enabled to be a boolean or a callback that returns a boolean\"\n      );\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(\n      this.#currentQuery,\n      prevQuery,\n      this.options,\n      prevOptions\n    )) {\n      this.#executeFetch();\n    }\n    this.updateResult(notifyOptions);\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(this.options.staleTime, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) !== (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    const trackedResult = {};\n    Object.keys(result).forEach((key) => {\n      Object.defineProperty(trackedResult, key, {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n          this.trackProp(key);\n          onPropTracked?.(key);\n          return result[key];\n        }\n      });\n    });\n    return trackedResult;\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({ ...options } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    query.isFetchingOptimistic = true;\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(\n      this.options,\n      fetchOptions\n    );\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(_utils_js__WEBPACK_IMPORTED_MODULE_2__.noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(\n      this.options.staleTime,\n      this.#currentQuery\n    );\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || this.#currentResult.isStale || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(staleTime)) {\n      return;\n    }\n    const time = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.timeUntilStale)(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (_utils_js__WEBPACK_IMPORTED_MODULE_2__.isServer || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(this.options.enabled, this.#currentQuery) === false || !(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isValidTimeout)(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || _focusManager_js__WEBPACK_IMPORTED_MODULE_3__.focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const { state } = query;\n    let newState = { ...state };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...(0,_query_js__WEBPACK_IMPORTED_MODULE_4__.fetchState)(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let { error, errorUpdatedAt, status } = newState;\n    if (options.select && newState.data !== void 0) {\n      if (prevResult && newState.data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(newState.data);\n          data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    } else {\n      data = newState.data;\n    }\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(\n          this.#lastQueryWithDefinedData?.state.data,\n          this.#lastQueryWithDefinedData\n        ) : options.placeholderData;\n        if (options.select && placeholderData !== void 0) {\n          try {\n            placeholderData = options.select(placeholderData);\n            this.#selectError = null;\n          } catch (selectError) {\n            this.#selectError = selectError;\n          }\n        }\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceData)(\n          prevResult?.data,\n          placeholderData,\n          options\n        );\n        isPlaceholderData = true;\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable\n    };\n    return result;\n  }\n  updateResult(notifyOptions) {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(nextResult, prevResult)) {\n      return;\n    }\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = (thenable) => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          finalizeThenableIfPossible(prevThenable);\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    this.#currentResult = nextResult;\n    const defaultNotifyOptions = {};\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const { notifyOnChangeProps } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(\n        notifyOnChangePropsValue ?? this.#trackedProps\n      );\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some((key) => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    if (notifyOptions?.listeners !== false && shouldNotifyListeners()) {\n      defaultNotifyOptions.listeners = true;\n    }\n    this.#notify({ ...defaultNotifyOptions, ...notifyOptions });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    _notifyManager_js__WEBPACK_IMPORTED_MODULE_5__.notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach((listener) => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false) {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveEnabled)(options.enabled, query) !== false && query.isStaleByTime((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.resolveStaleTime)(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.shallowEqualObjects)(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\n\n//# sourceMappingURL=queryObserver.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/removable.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Removable: () => (/* binding */ Removable)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/removable.ts\n\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.isValidTimeout)(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(\n      this.gcTime || 0,\n      newGcTime ?? (_utils_js__WEBPACK_IMPORTED_MODULE_0__.isServer ? Infinity : 5 * 60 * 1e3)\n    );\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\n\n//# sourceMappingURL=removable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3JlbW92YWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ3NEO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5REFBYztBQUN0QjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsK0NBQVE7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9xdWVyeS1jb3JlL2J1aWxkL21vZGVybi9yZW1vdmFibGUuanM/ZmI4ZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcmVtb3ZhYmxlLnRzXG5pbXBvcnQgeyBpc1NlcnZlciwgaXNWYWxpZFRpbWVvdXQgfSBmcm9tIFwiLi91dGlscy5qc1wiO1xudmFyIFJlbW92YWJsZSA9IGNsYXNzIHtcbiAgI2djVGltZW91dDtcbiAgZGVzdHJveSgpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG4gIH1cbiAgc2NoZWR1bGVHYygpIHtcbiAgICB0aGlzLmNsZWFyR2NUaW1lb3V0KCk7XG4gICAgaWYgKGlzVmFsaWRUaW1lb3V0KHRoaXMuZ2NUaW1lKSkge1xuICAgICAgdGhpcy4jZ2NUaW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHRoaXMub3B0aW9uYWxSZW1vdmUoKTtcbiAgICAgIH0sIHRoaXMuZ2NUaW1lKTtcbiAgICB9XG4gIH1cbiAgdXBkYXRlR2NUaW1lKG5ld0djVGltZSkge1xuICAgIHRoaXMuZ2NUaW1lID0gTWF0aC5tYXgoXG4gICAgICB0aGlzLmdjVGltZSB8fCAwLFxuICAgICAgbmV3R2NUaW1lID8/IChpc1NlcnZlciA/IEluZmluaXR5IDogNSAqIDYwICogMWUzKVxuICAgICk7XG4gIH1cbiAgY2xlYXJHY1RpbWVvdXQoKSB7XG4gICAgaWYgKHRoaXMuI2djVGltZW91dCkge1xuICAgICAgY2xlYXJUaW1lb3V0KHRoaXMuI2djVGltZW91dCk7XG4gICAgICB0aGlzLiNnY1RpbWVvdXQgPSB2b2lkIDA7XG4gICAgfVxuICB9XG59O1xuZXhwb3J0IHtcbiAgUmVtb3ZhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVtb3ZhYmxlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/removable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/retryer.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CancelledError: () => (/* binding */ CancelledError),\n/* harmony export */   canFetch: () => (/* binding */ canFetch),\n/* harmony export */   createRetryer: () => (/* binding */ createRetryer),\n/* harmony export */   isCancelledError: () => (/* binding */ isCancelledError)\n/* harmony export */ });\n/* harmony import */ var _focusManager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./focusManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/focusManager.js\");\n/* harmony import */ var _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./onlineManager.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _thenable_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./thenable.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n// src/retryer.ts\n\n\n\n\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = (0,_thenable_js__WEBPACK_IMPORTED_MODULE_1__.pendingThenable)();\n  const cancel = (cancelOptions) => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => _focusManager_js__WEBPACK_IMPORTED_MODULE_2__.focusManager.isFocused() && (config.networkMode === \"always\" || _onlineManager_js__WEBPACK_IMPORTED_MODULE_0__.onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = (value) => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise((continueResolve) => {\n      continueFn = (value) => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch((error) => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (_utils_js__WEBPACK_IMPORTED_MODULE_3__.isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.sleep)(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\n\n//# sourceMappingURL=retryer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/retryer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/subscribable.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Subscribable: () => (/* binding */ Subscribable)\n/* harmony export */ });\n// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */ new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {\n  }\n  onUnsubscribe() {\n  }\n};\n\n//# sourceMappingURL=subscribable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3N1YnNjcmliYWJsZS5qcz81ZWUyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zdWJzY3JpYmFibGUudHNcbnZhciBTdWJzY3JpYmFibGUgPSBjbGFzcyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMubGlzdGVuZXJzID0gLyogQF9fUFVSRV9fICovIG5ldyBTZXQoKTtcbiAgICB0aGlzLnN1YnNjcmliZSA9IHRoaXMuc3Vic2NyaWJlLmJpbmQodGhpcyk7XG4gIH1cbiAgc3Vic2NyaWJlKGxpc3RlbmVyKSB7XG4gICAgdGhpcy5saXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICB0aGlzLm9uU3Vic2NyaWJlKCk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHRoaXMubGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gICAgICB0aGlzLm9uVW5zdWJzY3JpYmUoKTtcbiAgICB9O1xuICB9XG4gIGhhc0xpc3RlbmVycygpIHtcbiAgICByZXR1cm4gdGhpcy5saXN0ZW5lcnMuc2l6ZSA+IDA7XG4gIH1cbiAgb25TdWJzY3JpYmUoKSB7XG4gIH1cbiAgb25VbnN1YnNjcmliZSgpIHtcbiAgfVxufTtcbmV4cG9ydCB7XG4gIFN1YnNjcmliYWJsZVxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN1YnNjcmliYWJsZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/subscribable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js":
/*!********************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/thenable.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pendingThenable: () => (/* binding */ pendingThenable)\n/* harmony export */ });\n// src/thenable.ts\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {\n  });\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = (value) => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = (reason) => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\n\n//# sourceMappingURL=thenable.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3RoZW5hYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3F1ZXJ5LWNvcmUvYnVpbGQvbW9kZXJuL3RoZW5hYmxlLmpzP2UyYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3RoZW5hYmxlLnRzXG5mdW5jdGlvbiBwZW5kaW5nVGhlbmFibGUoKSB7XG4gIGxldCByZXNvbHZlO1xuICBsZXQgcmVqZWN0O1xuICBjb25zdCB0aGVuYWJsZSA9IG5ldyBQcm9taXNlKChfcmVzb2x2ZSwgX3JlamVjdCkgPT4ge1xuICAgIHJlc29sdmUgPSBfcmVzb2x2ZTtcbiAgICByZWplY3QgPSBfcmVqZWN0O1xuICB9KTtcbiAgdGhlbmFibGUuc3RhdHVzID0gXCJwZW5kaW5nXCI7XG4gIHRoZW5hYmxlLmNhdGNoKCgpID0+IHtcbiAgfSk7XG4gIGZ1bmN0aW9uIGZpbmFsaXplKGRhdGEpIHtcbiAgICBPYmplY3QuYXNzaWduKHRoZW5hYmxlLCBkYXRhKTtcbiAgICBkZWxldGUgdGhlbmFibGUucmVzb2x2ZTtcbiAgICBkZWxldGUgdGhlbmFibGUucmVqZWN0O1xuICB9XG4gIHRoZW5hYmxlLnJlc29sdmUgPSAodmFsdWUpID0+IHtcbiAgICBmaW5hbGl6ZSh7XG4gICAgICBzdGF0dXM6IFwiZnVsZmlsbGVkXCIsXG4gICAgICB2YWx1ZVxuICAgIH0pO1xuICAgIHJlc29sdmUodmFsdWUpO1xuICB9O1xuICB0aGVuYWJsZS5yZWplY3QgPSAocmVhc29uKSA9PiB7XG4gICAgZmluYWxpemUoe1xuICAgICAgc3RhdHVzOiBcInJlamVjdGVkXCIsXG4gICAgICByZWFzb25cbiAgICB9KTtcbiAgICByZWplY3QocmVhc29uKTtcbiAgfTtcbiAgcmV0dXJuIHRoZW5hYmxlO1xufVxuZXhwb3J0IHtcbiAgcGVuZGluZ1RoZW5hYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dGhlbmFibGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/thenable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@tanstack/query-core/build/modern/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addToEnd: () => (/* binding */ addToEnd),\n/* harmony export */   addToStart: () => (/* binding */ addToStart),\n/* harmony export */   ensureQueryFn: () => (/* binding */ ensureQueryFn),\n/* harmony export */   functionalUpdate: () => (/* binding */ functionalUpdate),\n/* harmony export */   hashKey: () => (/* binding */ hashKey),\n/* harmony export */   hashQueryKeyByOptions: () => (/* binding */ hashQueryKeyByOptions),\n/* harmony export */   isPlainArray: () => (/* binding */ isPlainArray),\n/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject),\n/* harmony export */   isServer: () => (/* binding */ isServer),\n/* harmony export */   isValidTimeout: () => (/* binding */ isValidTimeout),\n/* harmony export */   keepPreviousData: () => (/* binding */ keepPreviousData),\n/* harmony export */   matchMutation: () => (/* binding */ matchMutation),\n/* harmony export */   matchQuery: () => (/* binding */ matchQuery),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   partialMatchKey: () => (/* binding */ partialMatchKey),\n/* harmony export */   replaceData: () => (/* binding */ replaceData),\n/* harmony export */   replaceEqualDeep: () => (/* binding */ replaceEqualDeep),\n/* harmony export */   resolveEnabled: () => (/* binding */ resolveEnabled),\n/* harmony export */   resolveStaleTime: () => (/* binding */ resolveStaleTime),\n/* harmony export */   shallowEqualObjects: () => (/* binding */ shallowEqualObjects),\n/* harmony export */   skipToken: () => (/* binding */ skipToken),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   timeUntilStale: () => (/* binding */ timeUntilStale)\n/* harmony export */ });\n// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {\n  return void 0;\n}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const { exact, status, predicate, mutationKey } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(\n    queryKey,\n    (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n      result[key] = val[key];\n      return result;\n    }, {}) : val\n  );\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return !Object.keys(b).some((key) => !partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise((resolve) => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (true) {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(\n          `Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`\n        );\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (true) {\n    if (options.queryFn === skipToken) {\n      console.error(\n        `Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`\n      );\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>createValue());\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        errorResetBoundary.clearReset();\n    }, [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]);\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/isRestoring.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/isRestoring.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=isRestoring.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9pc1Jlc3RvcmluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ3VCO0FBRXZCLElBQU1DLG1DQUEyQkQsZ0RBQUEsQ0FBYztBQUV4QyxJQUFNRyxpQkFBaUIsSUFBWUgsNkNBQUEsQ0FBV0M7QUFDOUMsSUFBTUksc0JBQXNCSixtQkFBbUJLLFFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uLi8uLi9zcmMvaXNSZXN0b3JpbmcudHM/NjJhNCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuXG5jb25zdCBJc1Jlc3RvcmluZ0NvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KGZhbHNlKVxuXG5leHBvcnQgY29uc3QgdXNlSXNSZXN0b3JpbmcgPSAoKSA9PiBSZWFjdC51c2VDb250ZXh0KElzUmVzdG9yaW5nQ29udGV4dClcbmV4cG9ydCBjb25zdCBJc1Jlc3RvcmluZ1Byb3ZpZGVyID0gSXNSZXN0b3JpbmdDb250ZXh0LlByb3ZpZGVyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJJc1Jlc3RvcmluZ0NvbnRleHQiLCJjcmVhdGVDb250ZXh0IiwidXNlSXNSZXN0b3JpbmciLCJ1c2VDb250ZXh0IiwiSXNSZXN0b3JpbmdQcm92aWRlciIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  if (defaultedOptions.suspense) {\n    if (typeof defaultedOptions.staleTime !== \"number\") {\n      defaultedOptions.staleTime = 1e3;\n    }\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n\n//# sourceMappingURL=suspense.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi9zdXNwZW5zZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQU9DO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL3N1c3BlbnNlLmpzPzAzNzUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3N1c3BlbnNlLnRzXG52YXIgZGVmYXVsdFRocm93T25FcnJvciA9IChfZXJyb3IsIHF1ZXJ5KSA9PiBxdWVyeS5zdGF0ZS5kYXRhID09PSB2b2lkIDA7XG52YXIgZW5zdXJlU3VzcGVuc2VUaW1lcnMgPSAoZGVmYXVsdGVkT3B0aW9ucykgPT4ge1xuICBpZiAoZGVmYXVsdGVkT3B0aW9ucy5zdXNwZW5zZSkge1xuICAgIGlmICh0eXBlb2YgZGVmYXVsdGVkT3B0aW9ucy5zdGFsZVRpbWUgIT09IFwibnVtYmVyXCIpIHtcbiAgICAgIGRlZmF1bHRlZE9wdGlvbnMuc3RhbGVUaW1lID0gMWUzO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGRlZmF1bHRlZE9wdGlvbnMuZ2NUaW1lID09PSBcIm51bWJlclwiKSB7XG4gICAgICBkZWZhdWx0ZWRPcHRpb25zLmdjVGltZSA9IE1hdGgubWF4KGRlZmF1bHRlZE9wdGlvbnMuZ2NUaW1lLCAxZTMpO1xuICAgIH1cbiAgfVxufTtcbnZhciB3aWxsRmV0Y2ggPSAocmVzdWx0LCBpc1Jlc3RvcmluZykgPT4gcmVzdWx0LmlzTG9hZGluZyAmJiByZXN1bHQuaXNGZXRjaGluZyAmJiAhaXNSZXN0b3Jpbmc7XG52YXIgc2hvdWxkU3VzcGVuZCA9IChkZWZhdWx0ZWRPcHRpb25zLCByZXN1bHQpID0+IGRlZmF1bHRlZE9wdGlvbnM/LnN1c3BlbnNlICYmIHJlc3VsdC5pc1BlbmRpbmc7XG52YXIgZmV0Y2hPcHRpbWlzdGljID0gKGRlZmF1bHRlZE9wdGlvbnMsIG9ic2VydmVyLCBlcnJvclJlc2V0Qm91bmRhcnkpID0+IG9ic2VydmVyLmZldGNoT3B0aW1pc3RpYyhkZWZhdWx0ZWRPcHRpb25zKS5jYXRjaCgoKSA9PiB7XG4gIGVycm9yUmVzZXRCb3VuZGFyeS5jbGVhclJlc2V0KCk7XG59KTtcbmV4cG9ydCB7XG4gIGRlZmF1bHRUaHJvd09uRXJyb3IsXG4gIGVuc3VyZVN1c3BlbnNlVGltZXJzLFxuICBmZXRjaE9wdGltaXN0aWMsXG4gIHNob3VsZFN1c3BlbmQsXG4gIHdpbGxGZXRjaFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN1c3BlbnNlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _isRestoring_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRestoring.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/isRestoring.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_isRestoring_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new Observer(client, defaultedOptions));\n    const result = observer.getOptimisticResult(defaultedOptions);\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>{\n        const unsubscribe = isRestoring ? ()=>void 0 : observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange));\n        observer.updateResult();\n        return unsubscribe;\n    }, [\n        observer,\n        isRestoring\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(defaultedOptions, {\n            listeners: false\n        });\n    }, [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash)\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_7__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n        promise?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_8__.noop).finally(()=>{\n            if (!observer.hasListeners()) {\n                observer.updateResult();\n            }\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js":
/*!************************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useMutation.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/mutationObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ // src/useMutation.ts\n\n\n\n\nfunction useMutation(options, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.MutationObserver(client, options));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(options);\n    }, [\n        observer,\n        options\n    ]);\n    const result = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(onStoreChange)), [\n        observer\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((variables, mutateOptions)=>{\n        observer.mutate(variables, mutateOptions).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }, [\n        observer\n    ]);\n    if (result.error && (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(observer.options.throwOnError, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n}\n //# sourceMappingURL=useMutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js":
/*!******************************************************************!*\
  !*** ./node_modules/@tanstack/react-query/build/modern/utils.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\n// src/utils.ts\nfunction shouldThrowError(throwError, params) {\n  if (typeof throwError === \"function\") {\n    return throwError(...params);\n  }\n  return !!throwError;\n}\nfunction noop() {\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5L2J1aWxkL21vZGVybi91dGlscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL3V0aWxzLmpzP2Y3OTYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzLnRzXG5mdW5jdGlvbiBzaG91bGRUaHJvd0Vycm9yKHRocm93RXJyb3IsIHBhcmFtcykge1xuICBpZiAodHlwZW9mIHRocm93RXJyb3IgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiB0aHJvd0Vycm9yKC4uLnBhcmFtcyk7XG4gIH1cbiAgcmV0dXJuICEhdGhyb3dFcnJvcjtcbn1cbmZ1bmN0aW9uIG5vb3AoKSB7XG59XG5leHBvcnQge1xuICBub29wLFxuICBzaG91bGRUaHJvd0Vycm9yXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-query/build/modern/utils.js\n");

/***/ })

};
;