"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vis.gl";
exports.ids = ["vendor-chunks/@vis.gl"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APILoadingStatus: () => (/* binding */ APILoadingStatus),\n/* harmony export */   APIProvider: () => (/* binding */ APIProvider),\n/* harmony export */   APIProviderContext: () => (/* binding */ APIProviderContext),\n/* harmony export */   AdvancedMarker: () => (/* binding */ AdvancedMarker),\n/* harmony export */   AdvancedMarkerAnchorPoint: () => (/* binding */ AdvancedMarkerAnchorPoint),\n/* harmony export */   AdvancedMarkerContext: () => (/* binding */ AdvancedMarkerContext),\n/* harmony export */   CollisionBehavior: () => (/* binding */ CollisionBehavior),\n/* harmony export */   ColorScheme: () => (/* binding */ ColorScheme),\n/* harmony export */   ControlPosition: () => (/* binding */ ControlPosition),\n/* harmony export */   GoogleMapsContext: () => (/* binding */ GoogleMapsContext),\n/* harmony export */   InfoWindow: () => (/* binding */ InfoWindow),\n/* harmony export */   Map: () => (/* binding */ Map),\n/* harmony export */   MapControl: () => (/* binding */ MapControl),\n/* harmony export */   Marker: () => (/* binding */ Marker),\n/* harmony export */   Pin: () => (/* binding */ Pin),\n/* harmony export */   RenderingType: () => (/* binding */ RenderingType),\n/* harmony export */   StaticMap: () => (/* binding */ StaticMap),\n/* harmony export */   createStaticMapsUrl: () => (/* binding */ createStaticMapsUrl),\n/* harmony export */   isAdvancedMarker: () => (/* binding */ isAdvancedMarker),\n/* harmony export */   isLatLngLiteral: () => (/* binding */ isLatLngLiteral),\n/* harmony export */   latLngEquals: () => (/* binding */ latLngEquals),\n/* harmony export */   limitTiltRange: () => (/* binding */ limitTiltRange),\n/* harmony export */   toLatLngLiteral: () => (/* binding */ toLatLngLiteral),\n/* harmony export */   useAdvancedMarkerRef: () => (/* binding */ useAdvancedMarkerRef),\n/* harmony export */   useApiIsLoaded: () => (/* binding */ useApiIsLoaded),\n/* harmony export */   useApiLoadingStatus: () => (/* binding */ useApiLoadingStatus),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMapsLibrary: () => (/* binding */ useMapsLibrary),\n/* harmony export */   useMarkerRef: () => (/* binding */ useMarkerRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n\n\n\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nconst APILoadingStatus = {\n  NOT_LOADED: 'NOT_LOADED',\n  LOADING: 'LOADING',\n  LOADED: 'LOADED',\n  FAILED: 'FAILED',\n  AUTH_FAILURE: 'AUTH_FAILURE'\n};\n\nconst MAPS_API_BASE_URL = 'https://maps.googleapis.com/maps/api/js';\n/**\n * A GoogleMapsApiLoader to reliably load and unload the Google Maps JavaScript API.\n *\n * The actual loading and unloading is delayed into the microtask queue, to\n * allow using the API in an useEffect hook, without worrying about multiple API loads.\n */\nclass GoogleMapsApiLoader {\n  /**\n   * Loads the Maps JavaScript API with the specified parameters.\n   * Since the Maps library can only be loaded once per page, this will\n   * produce a warning when called multiple times with different\n   * parameters.\n   *\n   * The returned promise resolves when loading completes\n   * and rejects in case of an error or when the loading was aborted.\n   */\n  static async load(params, onLoadingStatusChange) {\n    var _window$google;\n    const libraries = params.libraries ? params.libraries.split(',') : [];\n    const serializedParams = this.serializeParams(params);\n    this.listeners.push(onLoadingStatusChange);\n    // Note: if `google.maps.importLibrary` has been defined externally, we\n    //   assume that loading is complete and successful.\n    //   If it was defined by a previous call to this method, a warning\n    //   message is logged if there are differences in api-parameters used\n    //   for both calls.\n    if ((_window$google = window.google) != null && (_window$google = _window$google.maps) != null && _window$google.importLibrary) {\n      // no serialized parameters means it was loaded externally\n      if (!this.serializedApiParams) {\n        this.loadingStatus = APILoadingStatus.LOADED;\n      }\n      this.notifyLoadingStatusListeners();\n    } else {\n      this.serializedApiParams = serializedParams;\n      this.initImportLibrary(params);\n    }\n    if (this.serializedApiParams && this.serializedApiParams !== serializedParams) {\n      console.warn(`[google-maps-api-loader] The maps API has already been loaded ` + `with different parameters and will not be loaded again. Refresh the ` + `page for new values to have effect.`);\n    }\n    const librariesToLoad = ['maps', ...libraries];\n    await Promise.all(librariesToLoad.map(name => google.maps.importLibrary(name)));\n  }\n  /**\n   * Serialize the paramters used to load the library for easier comparison.\n   */\n  static serializeParams(params) {\n    return [params.v, params.key, params.language, params.region, params.authReferrerPolicy, params.solutionChannel].join('/');\n  }\n  /**\n   * Creates the global `google.maps.importLibrary` function for bootstrapping.\n   * This is essentially a formatted version of the dynamic loading script\n   * from the official documentation with some minor adjustments.\n   *\n   * The created importLibrary function will load the Google Maps JavaScript API,\n   * which will then replace the `google.maps.importLibrary` function with the full\n   * implementation.\n   *\n   * @see https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n   */\n  static initImportLibrary(params) {\n    if (!window.google) window.google = {};\n    if (!window.google.maps) window.google.maps = {};\n    if (window.google.maps['importLibrary']) {\n      console.error('[google-maps-api-loader-internal]: initImportLibrary must only be called once');\n      return;\n    }\n    let apiPromise = null;\n    const loadApi = () => {\n      if (apiPromise) return apiPromise;\n      apiPromise = new Promise((resolve, reject) => {\n        var _document$querySelect;\n        const scriptElement = document.createElement('script');\n        const urlParams = new URLSearchParams();\n        for (const [key, value] of Object.entries(params)) {\n          const urlParamName = key.replace(/[A-Z]/g, t => '_' + t[0].toLowerCase());\n          urlParams.set(urlParamName, String(value));\n        }\n        urlParams.set('loading', 'async');\n        urlParams.set('callback', '__googleMapsCallback__');\n        scriptElement.async = true;\n        scriptElement.src = MAPS_API_BASE_URL + `?` + urlParams.toString();\n        scriptElement.nonce = ((_document$querySelect = document.querySelector('script[nonce]')) == null ? void 0 : _document$querySelect.nonce) || '';\n        scriptElement.onerror = () => {\n          this.loadingStatus = APILoadingStatus.FAILED;\n          this.notifyLoadingStatusListeners();\n          reject(new Error('The Google Maps JavaScript API could not load.'));\n        };\n        window.__googleMapsCallback__ = () => {\n          this.loadingStatus = APILoadingStatus.LOADED;\n          this.notifyLoadingStatusListeners();\n          resolve();\n        };\n        window.gm_authFailure = () => {\n          this.loadingStatus = APILoadingStatus.AUTH_FAILURE;\n          this.notifyLoadingStatusListeners();\n        };\n        this.loadingStatus = APILoadingStatus.LOADING;\n        this.notifyLoadingStatusListeners();\n        document.head.append(scriptElement);\n      });\n      return apiPromise;\n    };\n    // for the first load, we declare an importLibrary function that will\n    // be overwritten once the api is loaded.\n    google.maps.importLibrary = libraryName => loadApi().then(() => google.maps.importLibrary(libraryName));\n  }\n  /**\n   * Calls all registered loadingStatusListeners after a status update.\n   */\n  static notifyLoadingStatusListeners() {\n    for (const fn of this.listeners) {\n      fn(this.loadingStatus);\n    }\n  }\n}\n/**\n * The current loadingStatus of the API.\n */\nGoogleMapsApiLoader.loadingStatus = APILoadingStatus.NOT_LOADED;\n/**\n * The parameters used for first loading the API.\n */\nGoogleMapsApiLoader.serializedApiParams = void 0;\n/**\n * A list of functions to be notified when the loading status changes.\n */\nGoogleMapsApiLoader.listeners = [];\n\nconst _excluded$3 = [\"onLoad\", \"onError\", \"apiKey\", \"version\", \"libraries\"],\n  _excluded2$1 = [\"children\"];\nconst DEFAULT_SOLUTION_CHANNEL = 'GMP_visgl_rgmlibrary_v1_default';\nconst APIProviderContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * local hook to set up the map-instance management context.\n */\nfunction useMapInstances() {\n  const [mapInstances, setMapInstances] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const addMapInstance = (mapInstance, id = 'default') => {\n    setMapInstances(instances => _extends({}, instances, {\n      [id]: mapInstance\n    }));\n  };\n  const removeMapInstance = (id = 'default') => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setMapInstances(_ref => {\n      let remaining = _objectWithoutPropertiesLoose(_ref, [id].map(_toPropertyKey));\n      return remaining;\n    });\n  };\n  const clearMapInstances = () => {\n    setMapInstances({});\n  };\n  return {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  };\n}\n/**\n * local hook to handle the loading of the maps API, returns the current loading status\n * @param props\n */\nfunction useGoogleMapsApiLoader(props) {\n  const {\n      onLoad,\n      onError,\n      apiKey,\n      version,\n      libraries = []\n    } = props,\n    otherApiParams = _objectWithoutPropertiesLoose(props, _excluded$3);\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(GoogleMapsApiLoader.loadingStatus);\n  const [loadedLibraries, addLoadedLibrary] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((loadedLibraries, action) => {\n    return loadedLibraries[action.name] ? loadedLibraries : _extends({}, loadedLibraries, {\n      [action.name]: action.value\n    });\n  }, {});\n  const librariesString = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => libraries == null ? void 0 : libraries.join(','), [libraries]);\n  const serializedParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => JSON.stringify(_extends({\n    apiKey,\n    version\n  }, otherApiParams)), [apiKey, version, otherApiParams]);\n  const importLibrary = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async name => {\n    var _google;\n    if (loadedLibraries[name]) {\n      return loadedLibraries[name];\n    }\n    if (!((_google = google) != null && (_google = _google.maps) != null && _google.importLibrary)) {\n      throw new Error('[api-provider-internal] importLibrary was called before ' + 'google.maps.importLibrary was defined.');\n    }\n    const res = await window.google.maps.importLibrary(name);\n    addLoadedLibrary({\n      name,\n      value: res\n    });\n    return res;\n  }, [loadedLibraries]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    (async () => {\n      try {\n        const params = _extends({\n          key: apiKey\n        }, otherApiParams);\n        if (version) params.v = version;\n        if ((librariesString == null ? void 0 : librariesString.length) > 0) params.libraries = librariesString;\n        if (params.channel === undefined || params.channel < 0 || params.channel > 999) delete params.channel;\n        if (params.solutionChannel === undefined) params.solutionChannel = DEFAULT_SOLUTION_CHANNEL;else if (params.solutionChannel === '') delete params.solutionChannel;\n        await GoogleMapsApiLoader.load(params, status => setStatus(status));\n        for (const name of ['core', 'maps', ...libraries]) {\n          await importLibrary(name);\n        }\n        if (onLoad) {\n          onLoad();\n        }\n      } catch (error) {\n        if (onError) {\n          onError(error);\n        } else {\n          console.error('<ApiProvider> failed to load the Google Maps JavaScript API', error);\n        }\n      }\n    })();\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [apiKey, librariesString, serializedParams]);\n  return {\n    status,\n    loadedLibraries,\n    importLibrary\n  };\n}\n/**\n * Component to wrap the components from this library and load the Google Maps JavaScript API\n */\nconst APIProvider = props => {\n  const {\n      children\n    } = props,\n    loaderProps = _objectWithoutPropertiesLoose(props, _excluded2$1);\n  const {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  } = useMapInstances();\n  const {\n    status,\n    loadedLibraries,\n    importLibrary\n  } = useGoogleMapsApiLoader(loaderProps);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances,\n    status,\n    loadedLibraries,\n    importLibrary\n  }), [mapInstances, addMapInstance, removeMapInstance, clearMapInstances, status, loadedLibraries, importLibrary]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(APIProviderContext.Provider, {\n    value: contextValue\n  }, children);\n};\n\n/**\n * Sets up effects to bind event-handlers for all event-props in MapEventProps.\n * @internal\n */\nfunction useMapEvents(map, props) {\n  // note: calling a useEffect hook from within a loop is prohibited by the\n  // rules of hooks, but it's ok here since it's unconditional and the number\n  // and order of iterations is always strictly the same.\n  // (see https://legacy.reactjs.org/docs/hooks-rules.html)\n  for (const propName of eventPropNames) {\n    // fixme: this cast is essentially a 'trust me, bro' for typescript, but\n    //   a proper solution seems way too complicated right now\n    const handler = props[propName];\n    const eventType = propNameToEventType[propName];\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      if (!map) return;\n      if (!handler) return;\n      const listener = google.maps.event.addListener(map, eventType, ev => {\n        handler(createMapEvent(eventType, map, ev));\n      });\n      return () => listener.remove();\n    }, [map, eventType, handler]);\n  }\n}\n/**\n * Create the wrapped map-events used for the event-props.\n * @param type the event type as it is specified to the maps api\n * @param map the map instance the event originates from\n * @param srcEvent the source-event if there is one.\n */\nfunction createMapEvent(type, map, srcEvent) {\n  const ev = {\n    type,\n    map,\n    detail: {},\n    stoppable: false,\n    stop: () => {}\n  };\n  if (cameraEventTypes.includes(type)) {\n    const camEvent = ev;\n    const center = map.getCenter();\n    const zoom = map.getZoom();\n    const heading = map.getHeading() || 0;\n    const tilt = map.getTilt() || 0;\n    const bounds = map.getBounds();\n    if (!center || !bounds || !Number.isFinite(zoom)) {\n      console.warn('[createEvent] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n    }\n    camEvent.detail = {\n      center: (center == null ? void 0 : center.toJSON()) || {\n        lat: 0,\n        lng: 0\n      },\n      zoom: zoom || 0,\n      heading: heading,\n      tilt: tilt,\n      bounds: (bounds == null ? void 0 : bounds.toJSON()) || {\n        north: 90,\n        east: 180,\n        south: -90,\n        west: -180\n      }\n    };\n    return camEvent;\n  } else if (mouseEventTypes.includes(type)) {\n    var _srcEvent$latLng;\n    if (!srcEvent) throw new Error('[createEvent] mouse events must provide a srcEvent');\n    const mouseEvent = ev;\n    mouseEvent.domEvent = srcEvent.domEvent;\n    mouseEvent.stoppable = true;\n    mouseEvent.stop = () => srcEvent.stop();\n    mouseEvent.detail = {\n      latLng: ((_srcEvent$latLng = srcEvent.latLng) == null ? void 0 : _srcEvent$latLng.toJSON()) || null,\n      placeId: srcEvent.placeId\n    };\n    return mouseEvent;\n  }\n  return ev;\n}\n/**\n * maps the camelCased names of event-props to the corresponding event-types\n * used in the maps API.\n */\nconst propNameToEventType = {\n  onBoundsChanged: 'bounds_changed',\n  onCenterChanged: 'center_changed',\n  onClick: 'click',\n  onContextmenu: 'contextmenu',\n  onDblclick: 'dblclick',\n  onDrag: 'drag',\n  onDragend: 'dragend',\n  onDragstart: 'dragstart',\n  onHeadingChanged: 'heading_changed',\n  onIdle: 'idle',\n  onIsFractionalZoomEnabledChanged: 'isfractionalzoomenabled_changed',\n  onMapCapabilitiesChanged: 'mapcapabilities_changed',\n  onMapTypeIdChanged: 'maptypeid_changed',\n  onMousemove: 'mousemove',\n  onMouseout: 'mouseout',\n  onMouseover: 'mouseover',\n  onProjectionChanged: 'projection_changed',\n  onRenderingTypeChanged: 'renderingtype_changed',\n  onTilesLoaded: 'tilesloaded',\n  onTiltChanged: 'tilt_changed',\n  onZoomChanged: 'zoom_changed',\n  // note: onCameraChanged is an alias for the bounds_changed event,\n  // since that is going to be fired in every situation where the camera is\n  // updated.\n  onCameraChanged: 'bounds_changed'\n};\nconst cameraEventTypes = ['bounds_changed', 'center_changed', 'heading_changed', 'tilt_changed', 'zoom_changed'];\nconst mouseEventTypes = ['click', 'contextmenu', 'dblclick', 'mousemove', 'mouseout', 'mouseover'];\nconst eventPropNames = Object.keys(propNameToEventType);\n\nfunction useDeepCompareEffect(effect, deps) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n  if (!ref.current || !fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__(deps, ref.current)) {\n    ref.current = deps;\n  }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, ref.current);\n}\n\nconst mapOptionKeys = new Set(['backgroundColor', 'clickableIcons', 'controlSize', 'disableDefaultUI', 'disableDoubleClickZoom', 'draggable', 'draggableCursor', 'draggingCursor', 'fullscreenControl', 'fullscreenControlOptions', 'gestureHandling', 'headingInteractionEnabled', 'isFractionalZoomEnabled', 'keyboardShortcuts', 'mapTypeControl', 'mapTypeControlOptions', 'mapTypeId', 'maxZoom', 'minZoom', 'noClear', 'panControl', 'panControlOptions', 'restriction', 'rotateControl', 'rotateControlOptions', 'scaleControl', 'scaleControlOptions', 'scrollwheel', 'streetView', 'streetViewControl', 'streetViewControlOptions', 'styles', 'tiltInteractionEnabled', 'zoomControl', 'zoomControlOptions']);\n/**\n * Internal hook to update the map-options when props are changed.\n *\n * @param map the map instance\n * @param mapProps the props to update the map-instance with\n * @internal\n */\nfunction useMapOptions(map, mapProps) {\n  /* eslint-disable react-hooks/exhaustive-deps --\n   *\n   * The following effects aren't triggered when the map is changed.\n   * In that case, the values will be or have been passed to the map\n   * constructor via mapOptions.\n   */\n  const mapOptions = {};\n  const keys = Object.keys(mapProps);\n  for (const key of keys) {\n    if (!mapOptionKeys.has(key)) continue;\n    mapOptions[key] = mapProps[key];\n  }\n  // update the map options when mapOptions is changed\n  // Note: due to the destructuring above, mapOptions will be seen as changed\n  //   with every re-render, so we're assuming the maps-api will properly\n  //   deal with unchanged option-values passed into setOptions.\n  useDeepCompareEffect(() => {\n    if (!map) return;\n    map.setOptions(mapOptions);\n  }, [mapOptions]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}\n\nfunction useApiLoadingStatus() {\n  var _useContext;\n  return ((_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext)) == null ? void 0 : _useContext.status) || APILoadingStatus.NOT_LOADED;\n}\n\n/**\n * Internal hook that updates the camera when deck.gl viewState changes.\n * @internal\n */\nfunction useDeckGLCameraUpdate(map, props) {\n  const {\n    viewport,\n    viewState\n  } = props;\n  const isDeckGlControlled = !!viewport;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !viewState) return;\n    const {\n      latitude,\n      longitude,\n      bearing: heading,\n      pitch: tilt,\n      zoom\n    } = viewState;\n    map.moveCamera({\n      center: {\n        lat: latitude,\n        lng: longitude\n      },\n      heading,\n      tilt,\n      zoom: zoom + 1\n    });\n  }, [map, viewState]);\n  return isDeckGlControlled;\n}\n\nfunction isLatLngLiteral(obj) {\n  if (!obj || typeof obj !== 'object') return false;\n  if (!('lat' in obj && 'lng' in obj)) return false;\n  return Number.isFinite(obj.lat) && Number.isFinite(obj.lng);\n}\nfunction latLngEquals(a, b) {\n  if (!a || !b) return false;\n  const A = toLatLngLiteral(a);\n  const B = toLatLngLiteral(b);\n  if (A.lat !== B.lat || A.lng !== B.lng) return false;\n  return true;\n}\nfunction toLatLngLiteral(obj) {\n  if (isLatLngLiteral(obj)) return obj;\n  return obj.toJSON();\n}\n\nfunction useMapCameraParams(map, cameraStateRef, mapProps) {\n  const center = mapProps.center ? toLatLngLiteral(mapProps.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const zoom = Number.isFinite(mapProps.zoom) ? mapProps.zoom : null;\n  const heading = Number.isFinite(mapProps.heading) ? mapProps.heading : null;\n  const tilt = Number.isFinite(mapProps.tilt) ? mapProps.tilt : null;\n  // the following effect runs for every render of the map component and checks\n  // if there are differences between the known state of the map instance\n  // (cameraStateRef, which is updated by all bounds_changed events) and the\n  // desired state in the props.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map) return;\n    const nextCamera = {};\n    let needsUpdate = false;\n    if (lat !== null && lng !== null && (cameraStateRef.current.center.lat !== lat || cameraStateRef.current.center.lng !== lng)) {\n      nextCamera.center = {\n        lat,\n        lng\n      };\n      needsUpdate = true;\n    }\n    if (zoom !== null && cameraStateRef.current.zoom !== zoom) {\n      nextCamera.zoom = zoom;\n      needsUpdate = true;\n    }\n    if (heading !== null && cameraStateRef.current.heading !== heading) {\n      nextCamera.heading = heading;\n      needsUpdate = true;\n    }\n    if (tilt !== null && cameraStateRef.current.tilt !== tilt) {\n      nextCamera.tilt = tilt;\n      needsUpdate = true;\n    }\n    if (needsUpdate) {\n      map.moveCamera(nextCamera);\n    }\n  });\n}\n\nconst AuthFailureMessage = () => {\n  const style = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    zIndex: 999,\n    display: 'flex',\n    flexFlow: 'column nowrap',\n    textAlign: 'center',\n    justifyContent: 'center',\n    fontSize: '.8rem',\n    color: 'rgba(0,0,0,0.6)',\n    background: '#dddddd',\n    padding: '1rem 1.5rem'\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: style\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h2\", null, \"Error: AuthFailure\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"p\", null, \"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"code\", null, \"APIProvider.apiKey\"), \" prop is correct. Check the error-message in the console for further details.\"));\n};\n\nfunction useCallbackRef() {\n  const [el, setEl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => setEl(value), [setEl]);\n  return [el, ref];\n}\n\n/**\n * Hook to check if the Maps JavaScript API is loaded\n */\nfunction useApiIsLoaded() {\n  const status = useApiLoadingStatus();\n  return status === APILoadingStatus.LOADED;\n}\n\nfunction useForceUpdate() {\n  const [, forceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(x => x + 1, 0);\n  return forceUpdate;\n}\n\nfunction handleBoundsChange(map, ref) {\n  const center = map.getCenter();\n  const zoom = map.getZoom();\n  const heading = map.getHeading() || 0;\n  const tilt = map.getTilt() || 0;\n  const bounds = map.getBounds();\n  if (!center || !bounds || !Number.isFinite(zoom)) {\n    console.warn('[useTrackedCameraState] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n  }\n  // fixme: do we need the `undefined` cases for the camera-params? When are they used in the maps API?\n  Object.assign(ref.current, {\n    center: (center == null ? void 0 : center.toJSON()) || {\n      lat: 0,\n      lng: 0\n    },\n    zoom: zoom || 0,\n    heading: heading,\n    tilt: tilt\n  });\n}\n/**\n * Creates a mutable ref object to track the last known state of the map camera.\n * This is used in `useMapCameraParams` to reduce stuttering in normal operation\n * by avoiding updates of the map camera with values that have already been processed.\n */\nfunction useTrackedCameraStateRef(map) {\n  const forceUpdate = useForceUpdate();\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    center: {\n      lat: 0,\n      lng: 0\n    },\n    heading: 0,\n    tilt: 0,\n    zoom: 0\n  });\n  // Record camera state with every bounds_changed event dispatched by the map.\n  // This data is used to prevent feeding these values back to the\n  // map-instance when a typical \"controlled component\" setup (state variable is\n  // fed into and updated by the map).\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const listener = google.maps.event.addListener(map, 'bounds_changed', () => {\n      handleBoundsChange(map, ref);\n      // When an event is occured, we have to update during the next cycle.\n      // The application could decide to ignore the event and not update any\n      // camera props of the map, meaning that in that case we will have to\n      // 'undo' the change to the camera.\n      forceUpdate();\n    });\n    return () => listener.remove();\n  }, [map, forceUpdate]);\n  return ref;\n}\n\nconst _excluded$2 = [\"id\", \"defaultBounds\", \"defaultCenter\", \"defaultZoom\", \"defaultHeading\", \"defaultTilt\", \"reuseMaps\", \"renderingType\", \"colorScheme\"],\n  _excluded2 = [\"padding\"];\n/**\n * Stores a stack of map-instances for each mapId. Whenever an\n * instance is used, it is removed from the stack while in use,\n * and returned to the stack when the component unmounts.\n * This allows us to correctly implement caching for multiple\n * maps om the same page, while reusing as much as possible.\n *\n * FIXME: while it should in theory be possible to reuse maps solely\n *   based on the mapId (as all other parameters can be changed at\n *   runtime), we don't yet have good enough tracking of options to\n *   reliably unset all the options that have been set.\n */\nclass CachedMapStack {\n  static has(key) {\n    return this.entries[key] && this.entries[key].length > 0;\n  }\n  static pop(key) {\n    if (!this.entries[key]) return null;\n    return this.entries[key].pop() || null;\n  }\n  static push(key, value) {\n    if (!this.entries[key]) this.entries[key] = [];\n    this.entries[key].push(value);\n  }\n}\n/**\n * The main hook takes care of creating map-instances and registering them in\n * the api-provider context.\n * @return a tuple of the map-instance created (or null) and the callback\n *   ref that will be used to pass the map-container into this hook.\n * @internal\n */\nCachedMapStack.entries = {};\nfunction useMapInstance(props, context) {\n  const apiIsLoaded = useApiIsLoaded();\n  const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [container, containerRef] = useCallbackRef();\n  const cameraStateRef = useTrackedCameraStateRef(map);\n  const {\n      id,\n      defaultBounds,\n      defaultCenter,\n      defaultZoom,\n      defaultHeading,\n      defaultTilt,\n      reuseMaps,\n      renderingType,\n      colorScheme\n    } = props,\n    mapOptions = _objectWithoutPropertiesLoose(props, _excluded$2);\n  const hasZoom = props.zoom !== undefined || props.defaultZoom !== undefined;\n  const hasCenter = props.center !== undefined || props.defaultCenter !== undefined;\n  if (!defaultBounds && (!hasZoom || !hasCenter)) {\n    console.warn('<Map> component is missing configuration. ' + 'You have to provide zoom and center (via the `zoom`/`defaultZoom` and ' + '`center`/`defaultCenter` props) or specify the region to show using ' + '`defaultBounds`. See ' + 'https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required');\n  }\n  // apply default camera props if available and not overwritten by controlled props\n  if (!mapOptions.center && defaultCenter) mapOptions.center = defaultCenter;\n  if (!mapOptions.zoom && Number.isFinite(defaultZoom)) mapOptions.zoom = defaultZoom;\n  if (!mapOptions.heading && Number.isFinite(defaultHeading)) mapOptions.heading = defaultHeading;\n  if (!mapOptions.tilt && Number.isFinite(defaultTilt)) mapOptions.tilt = defaultTilt;\n  for (const key of Object.keys(mapOptions)) if (mapOptions[key] === undefined) delete mapOptions[key];\n  const savedMapStateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n  // create the map instance and register it in the context\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!container || !apiIsLoaded) return;\n    const {\n      addMapInstance,\n      removeMapInstance\n    } = context;\n    // note: colorScheme (upcoming feature) isn't yet in the typings, remove once that is fixed:\n    const {\n      mapId\n    } = props;\n    const cacheKey = `${mapId || 'default'}:${renderingType || 'default'}:${colorScheme || 'LIGHT'}`;\n    let mapDiv;\n    let map;\n    if (reuseMaps && CachedMapStack.has(cacheKey)) {\n      map = CachedMapStack.pop(cacheKey);\n      mapDiv = map.getDiv();\n      container.appendChild(mapDiv);\n      map.setOptions(mapOptions);\n      // detaching the element from the DOM lets the map fall back to its default\n      // size, setting the center will trigger reloading the map.\n      setTimeout(() => map.setCenter(map.getCenter()), 0);\n    } else {\n      mapDiv = document.createElement('div');\n      mapDiv.style.height = '100%';\n      container.appendChild(mapDiv);\n      map = new google.maps.Map(mapDiv, _extends({}, mapOptions, renderingType ? {\n        renderingType: renderingType\n      } : {}, colorScheme ? {\n        colorScheme: colorScheme\n      } : {}));\n    }\n    setMap(map);\n    addMapInstance(map, id);\n    if (defaultBounds) {\n      const {\n          padding\n        } = defaultBounds,\n        defBounds = _objectWithoutPropertiesLoose(defaultBounds, _excluded2);\n      map.fitBounds(defBounds, padding);\n    }\n    // prevent map not rendering due to missing configuration\n    else if (!hasZoom || !hasCenter) {\n      map.fitBounds({\n        east: 180,\n        west: -180,\n        south: -90,\n        north: 90\n      });\n    }\n    // the savedMapState is used to restore the camera parameters when the mapId is changed\n    if (savedMapStateRef.current) {\n      const {\n        mapId: savedMapId,\n        cameraState: savedCameraState\n      } = savedMapStateRef.current;\n      if (savedMapId !== mapId) {\n        map.setOptions(savedCameraState);\n      }\n    }\n    return () => {\n      savedMapStateRef.current = {\n        mapId,\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        cameraState: cameraStateRef.current\n      };\n      // detach the map-div from the dom\n      mapDiv.remove();\n      if (reuseMaps) {\n        // push back on the stack\n        CachedMapStack.push(cacheKey, map);\n      } else {\n        // remove all event-listeners to minimize the possibility of memory-leaks\n        google.maps.event.clearInstanceListeners(map);\n      }\n      setMap(null);\n      removeMapInstance(id);\n    };\n  },\n  // some dependencies are ignored in the list below:\n  //  - defaultBounds and the default* camera props will only be used once, and\n  //    changes should be ignored\n  //  - mapOptions has special hooks that take care of updating the options\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [container, apiIsLoaded, id,\n  // these props can't be changed after initialization and require a new\n  // instance to be created\n  props.mapId, props.renderingType, props.colorScheme]);\n  return [map, containerRef, cameraStateRef];\n}\n\nconst GoogleMapsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// ColorScheme and RenderingType are redefined here to make them usable before the\n// maps API has been fully loaded.\nconst ColorScheme = {\n  DARK: 'DARK',\n  LIGHT: 'LIGHT',\n  FOLLOW_SYSTEM: 'FOLLOW_SYSTEM'\n};\nconst RenderingType = {\n  VECTOR: 'VECTOR',\n  RASTER: 'RASTER',\n  UNINITIALIZED: 'UNINITIALIZED'\n};\nconst Map = props => {\n  const {\n    children,\n    id,\n    className,\n    style\n  } = props;\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const loadingStatus = useApiLoadingStatus();\n  if (!context) {\n    throw new Error('<Map> can only be used inside an <ApiProvider> component.');\n  }\n  const [map, mapRef, cameraStateRef] = useMapInstance(props, context);\n  useMapCameraParams(map, cameraStateRef, props);\n  useMapEvents(map, props);\n  useMapOptions(map, props);\n  const isDeckGlControlled = useDeckGLCameraUpdate(map, props);\n  const isControlledExternally = !!props.controlled;\n  // disable interactions with the map for externally controlled maps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    // fixme: this doesn't seem to belong here (and it's mostly there for convenience anyway).\n    //   The reasoning is that a deck.gl canvas will be put on top of the map, rendering\n    //   any default map controls pretty much useless\n    if (isDeckGlControlled) {\n      map.setOptions({\n        disableDefaultUI: true\n      });\n    }\n    // disable all control-inputs when the map is controlled externally\n    if (isDeckGlControlled || isControlledExternally) {\n      map.setOptions({\n        gestureHandling: 'none',\n        keyboardShortcuts: false\n      });\n    }\n    return () => {\n      map.setOptions({\n        gestureHandling: props.gestureHandling,\n        keyboardShortcuts: props.keyboardShortcuts\n      });\n    };\n  }, [map, isDeckGlControlled, isControlledExternally, props.gestureHandling, props.keyboardShortcuts]);\n  // setup a stable cameraOptions object that can be used as dependency\n  const center = props.center ? toLatLngLiteral(props.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const cameraOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _lat, _lng, _props$zoom, _props$heading, _props$tilt;\n    return {\n      center: {\n        lat: (_lat = lat) != null ? _lat : 0,\n        lng: (_lng = lng) != null ? _lng : 0\n      },\n      zoom: (_props$zoom = props.zoom) != null ? _props$zoom : 0,\n      heading: (_props$heading = props.heading) != null ? _props$heading : 0,\n      tilt: (_props$tilt = props.tilt) != null ? _props$tilt : 0\n    };\n  }, [lat, lng, props.zoom, props.heading, props.tilt]);\n  // externally controlled mode: reject all camera changes that don't correspond to changes in props\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !isControlledExternally) return;\n    map.moveCamera(cameraOptions);\n    const listener = map.addListener('bounds_changed', () => {\n      map.moveCamera(cameraOptions);\n    });\n    return () => listener.remove();\n  }, [map, isControlledExternally, cameraOptions]);\n  const combinedStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => _extends({\n    width: '100%',\n    height: '100%',\n    position: 'relative',\n    // when using deckgl, the map should be sent to the back\n    zIndex: isDeckGlControlled ? -1 : 0\n  }, style), [style, isDeckGlControlled]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    map\n  }), [map]);\n  if (loadingStatus === APILoadingStatus.AUTH_FAILURE) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: _extends({\n        position: 'relative'\n      }, className ? {} : combinedStyle),\n      className: className\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AuthFailureMessage, null));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", _extends({\n    ref: mapRef,\n    \"data-testid\": 'map',\n    style: className ? undefined : combinedStyle,\n    className: className\n  }, id ? {\n    id\n  } : {}), map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(GoogleMapsContext.Provider, {\n    value: contextValue\n  }, children) : null);\n};\n// The deckGLViewProps flag here indicates to deck.gl that the Map component is\n// able to handle viewProps from deck.gl when deck.gl is used to control the map.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nMap.deckGLViewProps = true;\n\nconst shownMessages = new Set();\nfunction logErrorOnce(...args) {\n  const key = JSON.stringify(args);\n  if (!shownMessages.has(key)) {\n    shownMessages.add(key);\n    console.error(...args);\n  }\n}\n\n/**\n * Retrieves a map-instance from the context. This is either an instance\n * identified by id or the parent map instance if no id is specified.\n * Returns null if neither can be found.\n */\nconst useMap = (id = null) => {\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const {\n    map\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GoogleMapsContext) || {};\n  if (ctx === null) {\n    logErrorOnce('useMap(): failed to retrieve APIProviderContext. ' + 'Make sure that the <APIProvider> component exists and that the ' + 'component you are calling `useMap()` from is a sibling of the ' + '<APIProvider>.');\n    return null;\n  }\n  const {\n    mapInstances\n  } = ctx;\n  // if an id is specified, the corresponding map or null is returned\n  if (id !== null) return mapInstances[id] || null;\n  // otherwise, return the closest ancestor\n  if (map) return map;\n  // finally, return the default map instance\n  return mapInstances['default'] || null;\n};\n\nfunction useMapsLibrary(name) {\n  const apiIsLoaded = useApiIsLoaded();\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!apiIsLoaded || !ctx) return;\n    // Trigger loading the libraries via our proxy-method.\n    // The returned promise is ignored, since importLibrary will update loadedLibraries\n    // list in the context, triggering a re-render.\n    void ctx.importLibrary(name);\n  }, [apiIsLoaded, ctx, name]);\n  return (ctx == null ? void 0 : ctx.loadedLibraries[name]) || null;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to Maps JavaScript API objects.\n * @internal\n */\nfunction useMapsEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    const listener = google.maps.event.addListener(target, name, callback);\n    return () => listener.remove();\n  }, [target, name, callback]);\n}\n\n/**\n * Internally used to copy values from props into API-Objects\n * whenever they change.\n *\n * @example\n *   usePropBinding(marker, 'position', position);\n *\n * @internal\n */\nfunction usePropBinding(object, prop, value) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!object) return;\n    object[prop] = value;\n  }, [object, prop, value]);\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to DOM nodes.\n * @internal\n */\nfunction useDomEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    target.addEventListener(name, callback);\n    return () => target.removeEventListener(name, callback);\n  }, [target, name, callback]);\n}\n\n/* eslint-disable complexity */\nfunction isAdvancedMarker(marker) {\n  return marker.content !== undefined;\n}\nfunction isElementNode(node) {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Copy of the `google.maps.CollisionBehavior` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst CollisionBehavior = {\n  REQUIRED: 'REQUIRED',\n  REQUIRED_AND_HIDES_OPTIONAL: 'REQUIRED_AND_HIDES_OPTIONAL',\n  OPTIONAL_AND_HIDES_LOWER_PRIORITY: 'OPTIONAL_AND_HIDES_LOWER_PRIORITY'\n};\nconst AdvancedMarkerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// [xPosition, yPosition] when the top left corner is [0, 0]\nconst AdvancedMarkerAnchorPoint = {\n  TOP_LEFT: ['0%', '0%'],\n  TOP_CENTER: ['50%', '0%'],\n  TOP: ['50%', '0%'],\n  TOP_RIGHT: ['100%', '0%'],\n  LEFT_CENTER: ['0%', '50%'],\n  LEFT_TOP: ['0%', '0%'],\n  LEFT: ['0%', '50%'],\n  LEFT_BOTTOM: ['0%', '100%'],\n  RIGHT_TOP: ['100%', '0%'],\n  RIGHT: ['100%', '50%'],\n  RIGHT_CENTER: ['100%', '50%'],\n  RIGHT_BOTTOM: ['100%', '100%'],\n  BOTTOM_LEFT: ['0%', '100%'],\n  BOTTOM_CENTER: ['50%', '100%'],\n  BOTTOM: ['50%', '100%'],\n  BOTTOM_RIGHT: ['100%', '100%'],\n  CENTER: ['50%', '50%']\n};\nconst MarkerContent = ({\n  children,\n  styles,\n  className,\n  anchorPoint\n}) => {\n  const [xTranslation, yTranslation] = anchorPoint != null ? anchorPoint : AdvancedMarkerAnchorPoint['BOTTOM'];\n  // The \"translate(50%, 100%)\" is here to counter and reset the default anchoring of the advanced marker element\n  // that comes from the api\n  const transformStyle = `translate(50%, 100%) translate(-${xTranslation}, -${yTranslation})`;\n  return (\n    /*#__PURE__*/\n    // anchoring container\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: {\n        transform: transformStyle\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      className: className,\n      style: styles\n    }, children))\n  );\n};\nfunction useAdvancedMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [contentContainer, setContentContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const markerLibrary = useMapsLibrary('marker');\n  const {\n    children,\n    onClick,\n    className,\n    onMouseEnter,\n    onMouseLeave,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    collisionBehavior,\n    clickable,\n    draggable,\n    position,\n    title,\n    zIndex\n  } = props;\n  const numChildren = react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children);\n  // create an AdvancedMarkerElement instance and add it to the map once available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map || !markerLibrary) return;\n    const newMarker = new markerLibrary.AdvancedMarkerElement();\n    newMarker.map = map;\n    setMarker(newMarker);\n    // create the container for marker content if there are children\n    let contentElement = null;\n    if (numChildren > 0) {\n      contentElement = document.createElement('div');\n      // We need some kind of flag to identify the custom marker content\n      // in the infowindow component. Choosing a custom property instead of a className\n      // to not encourage users to style the marker content directly.\n      contentElement.isCustomMarker = true;\n      newMarker.content = contentElement;\n      setContentContainer(contentElement);\n    }\n    return () => {\n      var _contentElement;\n      newMarker.map = null;\n      (_contentElement = contentElement) == null || _contentElement.remove();\n      setMarker(null);\n      setContentContainer(null);\n    };\n  }, [map, markerLibrary, numChildren]);\n  // When no children are present we don't have our own wrapper div\n  // which usually gets the user provided className. In this case\n  // we set the className directly on the marker.content element that comes\n  // with the AdvancedMarker.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker || !marker.content || numChildren > 0) return;\n    marker.content.className = className || '';\n  }, [marker, className, numChildren]);\n  // copy other props\n  usePropBinding(marker, 'position', position);\n  usePropBinding(marker, 'title', title != null ? title : '');\n  usePropBinding(marker, 'zIndex', zIndex);\n  usePropBinding(marker, 'collisionBehavior', collisionBehavior);\n  // set gmpDraggable from props (when unspecified, it's true if any drag-event\n  // callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (draggable !== undefined) marker.gmpDraggable = draggable;else if (onDrag || onDragStart || onDragEnd) marker.gmpDraggable = true;else marker.gmpDraggable = false;\n  }, [marker, draggable, onDrag, onDragEnd, onDragStart]);\n  // set gmpClickable from props (when unspecified, it's true if the onClick or one of\n  // the hover events callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const gmpClickable = clickable !== undefined || Boolean(onClick) || Boolean(onMouseEnter) || Boolean(onMouseLeave);\n    // gmpClickable is only available in beta version of the\n    // maps api (as of 2024-10-10)\n    marker.gmpClickable = gmpClickable;\n    // enable pointer events for the markers with custom content\n    if (gmpClickable && marker != null && marker.content && isElementNode(marker.content)) {\n      marker.content.style.pointerEvents = 'none';\n      if (marker.content.firstElementChild) {\n        marker.content.firstElementChild.style.pointerEvents = 'all';\n      }\n    }\n  }, [marker, clickable, onClick, onMouseEnter, onMouseLeave]);\n  useMapsEventListener(marker, 'click', onClick);\n  useMapsEventListener(marker, 'drag', onDrag);\n  useMapsEventListener(marker, 'dragstart', onDragStart);\n  useMapsEventListener(marker, 'dragend', onDragEnd);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseenter', onMouseEnter);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseleave', onMouseLeave);\n  return [marker, contentContainer];\n}\nconst AdvancedMarker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const {\n    children,\n    style,\n    className,\n    anchorPoint\n  } = props;\n  const [marker, contentContainer] = useAdvancedMarker(props);\n  const advancedMarkerContextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => marker ? {\n    marker\n  } : null, [marker]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  if (!contentContainer) return null;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AdvancedMarkerContext.Provider, {\n    value: advancedMarkerContextValue\n  }, (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(MarkerContent, {\n    anchorPoint: anchorPoint,\n    styles: style,\n    className: className\n  }, children), contentContainer));\n});\nfunction useAdvancedMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\nfunction setValueForStyles(element, styles, prevStyles) {\n  if (styles != null && typeof styles !== 'object') {\n    throw new Error('The `style` prop expects a mapping from style properties to values, ' + \"not a string. For example, style={{marginRight: spacing + 'em'}} when \" + 'using JSX.');\n  }\n  const elementStyle = element.style;\n  // without `prevStyles`, just set all values\n  if (prevStyles == null) {\n    if (styles == null) return;\n    for (const styleName in styles) {\n      if (!styles.hasOwnProperty(styleName)) continue;\n      setValueForStyle(elementStyle, styleName, styles[styleName]);\n    }\n    return;\n  }\n  // unset all styles in `prevStyles` that aren't in `styles`\n  for (const styleName in prevStyles) {\n    if (prevStyles.hasOwnProperty(styleName) && (styles == null || !styles.hasOwnProperty(styleName))) {\n      // Clear style\n      const isCustomProperty = styleName.indexOf('--') === 0;\n      if (isCustomProperty) {\n        elementStyle.setProperty(styleName, '');\n      } else if (styleName === 'float') {\n        elementStyle.cssFloat = '';\n      } else {\n        elementStyle[styleName] = '';\n      }\n    }\n  }\n  // only assign values from `styles` that are different from `prevStyles`\n  if (styles == null) return;\n  for (const styleName in styles) {\n    const value = styles[styleName];\n    if (styles.hasOwnProperty(styleName) && prevStyles[styleName] !== value) {\n      setValueForStyle(elementStyle, styleName, value);\n    }\n  }\n}\nfunction setValueForStyle(elementStyle, styleName, value) {\n  const isCustomProperty = styleName.indexOf('--') === 0;\n  // falsy values will unset the style property\n  if (value == null || typeof value === 'boolean' || value === '') {\n    if (isCustomProperty) {\n      elementStyle.setProperty(styleName, '');\n    } else if (styleName === 'float') {\n      elementStyle.cssFloat = '';\n    } else {\n      elementStyle[styleName] = '';\n    }\n  }\n  // custom properties can't be directly assigned\n  else if (isCustomProperty) {\n    elementStyle.setProperty(styleName, value);\n  }\n  // numeric values are treated as 'px' unless the style property expects unitless numbers\n  else if (typeof value === 'number' && value !== 0 && !isUnitlessNumber(styleName)) {\n    elementStyle[styleName] = value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n  }\n  // everything else can just be assigned\n  else {\n    if (styleName === 'float') {\n      elementStyle.cssFloat = value;\n    } else {\n      elementStyle[styleName] = ('' + value).trim();\n    }\n  }\n}\n// CSS properties which accept numbers but are not in units of \"px\".\nconst unitlessNumbers = new Set(['animationIterationCount', 'aspectRatio', 'borderImageOutset', 'borderImageSlice', 'borderImageWidth', 'boxFlex', 'boxFlexGroup', 'boxOrdinalGroup', 'columnCount', 'columns', 'flex', 'flexGrow', 'flexPositive', 'flexShrink', 'flexNegative', 'flexOrder', 'gridArea', 'gridRow', 'gridRowEnd', 'gridRowSpan', 'gridRowStart', 'gridColumn', 'gridColumnEnd', 'gridColumnSpan', 'gridColumnStart', 'fontWeight', 'lineClamp', 'lineHeight', 'opacity', 'order', 'orphans', 'scale', 'tabSize', 'widows', 'zIndex', 'zoom', 'fillOpacity',\n// SVG-related properties\n'floodOpacity', 'stopOpacity', 'strokeDasharray', 'strokeDashoffset', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth']);\nfunction isUnitlessNumber(name) {\n  return unitlessNumbers.has(name);\n}\n\nconst _excluded$1 = [\"children\", \"headerContent\", \"style\", \"className\", \"pixelOffset\", \"anchor\", \"shouldFocus\", \"onClose\", \"onCloseClick\"];\n/**\n * Component to render an Info Window with the Maps JavaScript API\n */\nconst InfoWindow = props => {\n  const {\n      // content options\n      children,\n      headerContent,\n      style,\n      className,\n      pixelOffset,\n      // open options\n      anchor,\n      shouldFocus,\n      // events\n      onClose,\n      onCloseClick\n      // other options\n    } = props,\n    infoWindowOptions = _objectWithoutPropertiesLoose(props, _excluded$1);\n  // ## create infowindow instance once the mapsLibrary is available.\n  const mapsLibrary = useMapsLibrary('maps');\n  const [infoWindow, setInfoWindow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const contentContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!mapsLibrary) return;\n    contentContainerRef.current = document.createElement('div');\n    headerContainerRef.current = document.createElement('div');\n    const opts = infoWindowOptions;\n    if (pixelOffset) {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (headerContent) {\n      // if headerContent is specified as string we can directly forward it,\n      // otherwise we'll pass the element the portal will render into\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    // intentionally shadowing the state variables here\n    const infoWindow = new google.maps.InfoWindow(infoWindowOptions);\n    infoWindow.setContent(contentContainerRef.current);\n    setInfoWindow(infoWindow);\n    // unmount: remove infoWindow and content elements (note: close is called in a different effect-cleanup)\n    return () => {\n      var _contentContainerRef$, _headerContainerRef$c;\n      infoWindow.setContent(null);\n      (_contentContainerRef$ = contentContainerRef.current) == null || _contentContainerRef$.remove();\n      (_headerContainerRef$c = headerContainerRef.current) == null || _headerContainerRef$c.remove();\n      contentContainerRef.current = null;\n      headerContainerRef.current = null;\n      setInfoWindow(null);\n    };\n  },\n  // `infoWindowOptions` and other props are missing from dependencies:\n  //\n  // We don't want to re-create the infowindow instance\n  // when the options change.\n  // Updating the options is handled in the useEffect below.\n  //\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [mapsLibrary]);\n  // ## update className and styles for `contentContainer`\n  // stores previously applied style properties, so they can be removed when unset\n  const prevStyleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!infoWindow || !contentContainerRef.current) return;\n    setValueForStyles(contentContainerRef.current, style || null, prevStyleRef.current);\n    prevStyleRef.current = style || null;\n    if (className !== contentContainerRef.current.className) contentContainerRef.current.className = className || '';\n  }, [infoWindow, className, style]);\n  // ## update options\n  useDeepCompareEffect(() => {\n    if (!infoWindow) return;\n    const opts = infoWindowOptions;\n    if (!pixelOffset) {\n      opts.pixelOffset = null;\n    } else {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (!headerContent) {\n      opts.headerContent = null;\n    } else {\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    infoWindow.setOptions(infoWindowOptions);\n  },\n  // dependency `infoWindow` isn't needed since options are also passed\n  // to the constructor when a new infoWindow is created.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [infoWindowOptions, pixelOffset, headerContent]);\n  // ## bind event handlers\n  useMapsEventListener(infoWindow, 'close', onClose);\n  useMapsEventListener(infoWindow, 'closeclick', onCloseClick);\n  // ## open info window when content and map are available\n  const map = useMap();\n  useDeepCompareEffect(() => {\n    // `anchor === null` means an anchor is defined but not ready yet.\n    if (!map || !infoWindow || anchor === null) return;\n    const isOpenedWithAnchor = !!anchor;\n    const openOptions = {\n      map\n    };\n    if (anchor) {\n      openOptions.anchor = anchor;\n      // Only do the infowindow adjusting when dealing with an AdvancedMarker\n      if (isAdvancedMarker(anchor) && anchor.content instanceof Element) {\n        const wrapper = anchor.content;\n        const wrapperBcr = wrapper == null ? void 0 : wrapper.getBoundingClientRect();\n        // This checks whether or not the anchor has custom content with our own\n        // div wrapper. If not, that means we have a regular AdvancedMarker without any children.\n        // In that case we do not want to adjust the infowindow since it is all handled correctly\n        // by the Google Maps API.\n        if (wrapperBcr && wrapper != null && wrapper.isCustomMarker) {\n          var _anchor$content$first;\n          // We can safely typecast here since we control that element and we know that\n          // it is a div\n          const anchorDomContent = (_anchor$content$first = anchor.content.firstElementChild) == null ? void 0 : _anchor$content$first.firstElementChild;\n          const contentBcr = anchorDomContent == null ? void 0 : anchorDomContent.getBoundingClientRect();\n          // center infowindow above marker\n          const anchorOffsetX = contentBcr.x - wrapperBcr.x + (contentBcr.width - wrapperBcr.width) / 2;\n          const anchorOffsetY = contentBcr.y - wrapperBcr.y;\n          const opts = infoWindowOptions;\n          opts.pixelOffset = new google.maps.Size(pixelOffset ? pixelOffset[0] + anchorOffsetX : anchorOffsetX, pixelOffset ? pixelOffset[1] + anchorOffsetY : anchorOffsetY);\n          infoWindow.setOptions(opts);\n        }\n      }\n    }\n    if (shouldFocus !== undefined) {\n      openOptions.shouldFocus = shouldFocus;\n    }\n    infoWindow.open(openOptions);\n    return () => {\n      // Note: when the infowindow has an anchor, it will automatically show up again when the\n      // anchor was removed from the map before infoWindow.close() is called but the it gets\n      // added back to the map after that.\n      // More information here: https://issuetracker.google.com/issues/343750849\n      if (isOpenedWithAnchor) infoWindow.set('anchor', null);\n      infoWindow.close();\n    };\n  }, [infoWindow, anchor, map, shouldFocus, infoWindowOptions, pixelOffset]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, contentContainerRef.current && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, contentContainerRef.current), headerContainerRef.current !== null && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(headerContent, headerContainerRef.current));\n};\n\n/**\n * Formats a location into a string representation suitable for Google Static Maps API.\n *\n * @param location - The location to format, can be either a string or an object with lat/lng properties\n * @returns A string representation of the location in the format \"lat,lng\" or the original string\n *\n * @example\n * // Returns \"40.714728,-73.998672\"\n * formatLocation({ lat: 40.714728, lng: -73.998672 })\n *\n * @example\n * // Returns \"New York, NY\"\n * formatLocation(\"New York, NY\")\n */\nfunction formatLocation(location) {\n  return typeof location === 'string' ? location : `${location.lat},${location.lng}`;\n}\n// Used for removing the leading pipe from the param string\nfunction formatParam(string) {\n  return string.slice(1);\n}\n\n/**\n * Assembles marker parameters for static maps.\n *\n * This function takes an array of markers and groups them by their style properties.\n * It then creates a string representation of these markers, including their styles and locations,\n * which can be used as parameters for static map APIs.\n *\n * @param {StaticMapsMarker[]} [markers=[]] - An array of markers to be processed. Each marker can have properties such as color, label, size, scale, icon, anchor, and location.\n * @returns {string[]} An array of strings, each representing a group of markers with their styles and locations.\n *\n * @example\n * const markers = [\n *   { color: 'blue', label: 'A', size: 'mid', location: '40.714728,-73.998672' },\n *   { color: 'blue', label: 'B', size: 'mid', location: '40.714728,-73.998672' },\n *   { icon: 'http://example.com/icon.png', location: { lat: 40.714728, lng: -73.998672 } }\n * ];\n * const params = assembleMarkerParams(markers);\n * // Params will be an array of strings representing the marker parameters\n * Example output: [\n *   \"color:blue|label:A|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"color:blue|label:B|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"icon:http://example.com/icon.png|40.714728,-73.998672\"\n * ]\n */\nfunction assembleMarkerParams(markers = []) {\n  const markerParams = [];\n  // Group markers by style\n  const markersByStyle = markers == null ? void 0 : markers.reduce((styles, marker) => {\n    const {\n      color = 'red',\n      label,\n      size,\n      scale,\n      icon,\n      anchor\n    } = marker;\n    // Create a unique style key based on either icon properties or standard marker properties\n    const relevantProps = icon ? [icon, anchor, scale] : [color, label, size];\n    const key = relevantProps.filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(marker);\n    return styles;\n  }, {});\n  Object.values(markersByStyle != null ? markersByStyle : {}).forEach(markers => {\n    let markerParam = '';\n    const {\n      icon\n    } = markers[0];\n    // Create marker style from first marker in group since all markers share the same style.\n    Object.entries(markers[0]).forEach(([key, value]) => {\n      // Determine which properties to include based on whether marker uses custom icon\n      const relevantKeys = icon ? ['icon', 'anchor', 'scale'] : ['color', 'label', 'size'];\n      if (relevantKeys.includes(key)) {\n        markerParam += `|${key}:${value}`;\n      }\n    });\n    // Add location coordinates for each marker in the style group\n    // Handles both string locations and lat/lng object formats.\n    for (const marker of markers) {\n      const location = typeof marker.location === 'string' ? marker.location : `${marker.location.lat},${marker.location.lng}`;\n      markerParam += `|${location}`;\n    }\n    markerParams.push(markerParam);\n  });\n  return markerParams.map(formatParam);\n}\n\n/**\n * Assembles path parameters for the Static Maps Api from an array of paths.\n *\n * This function groups paths by their style properties (color, weight, fillcolor, geodesic)\n * and then constructs a string of path parameters for each group. Each path parameter string\n * includes the style properties and the coordinates of the paths.\n *\n * @param {Array<StaticMapsPath>} [paths=[]] - An array of paths to be assembled into path parameters.\n * @returns {Array<string>} An array of path parameter strings.\n *\n * @example\n * const paths = [\n *   {\n *     color: 'red',\n *     weight: 5,\n *     coordinates: [\n *       { lat: 40.714728, lng: -73.998672 },\n *       { lat: 40.718217, lng: -73.998284 }\n *     ]\n *   }\n * ];\n *\n * const pathParams = assemblePathParams(paths);\n * Output: [\n *    'color:red|weight:5|40.714728,-73.998672|40.718217,-73.998284'\n *  ]\n */\nfunction assemblePathParams(paths = []) {\n  const pathParams = [];\n  // Group paths by their style properties (color, weight, fillcolor, geodesic)\n  // to combine paths with identical styles into single parameter strings\n  const pathsByStyle = paths == null ? void 0 : paths.reduce((styles, path) => {\n    const {\n      color = 'default',\n      weight,\n      fillcolor,\n      geodesic\n    } = path;\n    // Create unique key for this style combination\n    const key = [color, weight, fillcolor, geodesic].filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(path);\n    return styles;\n  }, {});\n  // Process each group of paths with identical styles\n  Object.values(pathsByStyle != null ? pathsByStyle : {}).forEach(paths => {\n    let pathParam = '';\n    // Build style parameter string using properties from first path in group\n    // since all paths in this group share the same style\n    Object.entries(paths[0]).forEach(([key, value]) => {\n      if (['color', 'weight', 'fillcolor', 'geodesic'].includes(key)) {\n        pathParam += `|${key}:${value}`;\n      }\n    });\n    // Add location for all marker in style group\n    for (const path of paths) {\n      if (typeof path.coordinates === 'string') {\n        pathParam += `|${decodeURIComponent(path.coordinates)}`;\n      } else {\n        for (const location of path.coordinates) {\n          pathParam += `|${formatLocation(location)}`;\n        }\n      }\n    }\n    pathParams.push(pathParam);\n  });\n  return pathParams.map(formatParam);\n}\n\n/**\n * Converts an array of Google Maps style objects into an array of style strings\n * compatible with the Google Static Maps API.\n *\n * @param styles - An array of Google Maps MapTypeStyle objects that define the styling rules\n * @returns An array of formatted style strings ready to be used with the Static Maps API\n *\n * @example\n * const styles = [{\n *   featureType: \"road\",\n *   elementType: \"geometry\",\n *   stylers: [{color: \"#ff0000\"}, {weight: 1}]\n * }];\n *\n * const styleStrings = assembleMapTypeStyles(styles);\n * // Returns: [\"|feature:road|element:geometry|color:0xff0000|weight:1\"]\n *\n * Each style string follows the format:\n * \"feature:{featureType}|element:{elementType}|{stylerName}:{stylerValue}\"\n *\n * Note: Color values with hexadecimal notation (#) are automatically converted\n * to the required 0x format for the Static Maps API.\n */\nfunction assembleMapTypeStyles(styles) {\n  return styles.map(mapTypeStyle => {\n    const {\n      featureType,\n      elementType,\n      stylers = []\n    } = mapTypeStyle;\n    let styleString = '';\n    if (featureType) {\n      styleString += `|feature:${featureType}`;\n    }\n    if (elementType) {\n      styleString += `|element:${elementType}`;\n    }\n    for (const styler of stylers) {\n      Object.entries(styler).forEach(([name, value]) => {\n        styleString += `|${name}:${String(value).replace('#', '0x')}`;\n      });\n    }\n    return styleString;\n  }).map(formatParam);\n}\n\nconst STATIC_MAPS_BASE = 'https://maps.googleapis.com/maps/api/staticmap';\n/**\n * Creates a URL for the Google Static Maps API with the specified parameters.\n *\n * @param {Object} options - The configuration options for the static map\n * @param {string} options.apiKey - Your Google Maps API key (required)\n * @param {number} options.width - The width of the map image in pixels (required)\n * @param {number} options.height - The height of the map image in pixels (required)\n * @param {StaticMapsLocation} [options.center] - The center point of the map (lat/lng or address).\n *  Required if no markers or paths or \"visible locations\" are provided.\n * @param {number} [options.zoom] - The zoom level of the map. Required if no markers or paths or \"visible locations\" are provided.\n * @param {1|2|4} [options.scale] - The resolution of the map (1, 2, or 4)\n * @param {string} [options.format] - The image format (png, png8, png32, gif, jpg, jpg-baseline)\n * @param {string} [options.mapType] - The type of map (roadmap, satellite, terrain, hybrid)\n * @param {string} [options.language] - The language of the map labels\n * @param {string} [options.region] - The region code for the map\n * @param {string} [options.map_id] - The Cloud-based map style ID\n * @param {StaticMapsMarker[]} [options.markers=[]] - Array of markers to display on the map\n * @param {StaticMapsPath[]} [options.paths=[]] - Array of paths to display on the map\n * @param {StaticMapsLocation[]} [options.visible=[]] - Array of locations that should be visible on the map\n * @param {MapTypeStyle[]} [options.style=[]] - Array of style objects to customize the map appearance\n *\n * @returns {string} The complete Google Static Maps API URL\n *\n * @throws {Error} If API key is not provided\n * @throws {Error} If width or height is not provided\n *\n * @example\n * const url = createStaticMapsUrl({\n *   apiKey: 'YOUR_API_KEY',\n *   width: 600,\n *   height: 400,\n *   center: { lat: 40.714728, lng: -73.998672 },\n *   zoom: 12,\n *   markers: [\n *     {\n *       location: { lat: 40.714728, lng: -73.998672 },\n *       color: 'red',\n *       label: 'A'\n *     }\n *   ],\n *   paths: [\n *     {\n *       coordinates: [\n *         { lat: 40.714728, lng: -73.998672 },\n *         { lat: 40.719728, lng: -73.991672 }\n *       ],\n *       color: '0x0000ff',\n *       weight: 5\n *     }\n *   ],\n *   style: [\n *     {\n *       featureType: 'road',\n *       elementType: 'geometry',\n *       stylers: [{color: '#00ff00'}]\n *     }\n *   ]\n * });\n *\n * // Results in URL similar to:\n * // https://maps.googleapis.com/maps/api/staticmap?key=YOUR_API_KEY\n * // &size=600x400\n * // &center=40.714728,-73.998672&zoom=12\n * // &markers=color:red|label:A|40.714728,-73.998672\n * // &path=color:0x0000ff|weight:5|40.714728,-73.998672|40.719728,-73.991672\n * // &style=feature:road|element:geometry|color:0x00ff00\n */\nfunction createStaticMapsUrl({\n  apiKey,\n  width,\n  height,\n  center,\n  zoom,\n  scale,\n  format,\n  mapType,\n  language,\n  region,\n  mapId,\n  markers = [],\n  paths = [],\n  visible = [],\n  style = []\n}) {\n  if (!apiKey) {\n    console.warn('API key is required');\n  }\n  if (!width || !height) {\n    console.warn('Width and height are required');\n  }\n  const params = _extends({\n    key: apiKey,\n    size: `${width}x${height}`\n  }, center && {\n    center: formatLocation(center)\n  }, zoom && {\n    zoom\n  }, scale && {\n    scale\n  }, format && {\n    format\n  }, mapType && {\n    maptype: mapType\n  }, language && {\n    language\n  }, region && {\n    region\n  }, mapId && {\n    map_id: mapId\n  });\n  const url = new URL(STATIC_MAPS_BASE);\n  // Params that don't need special handling\n  Object.entries(params).forEach(([key, value]) => {\n    url.searchParams.append(key, String(value));\n  });\n  // Assemble Markers\n  for (const markerParam of assembleMarkerParams(markers)) {\n    url.searchParams.append('markers', markerParam);\n  }\n  // Assemble Paths\n  for (const pathParam of assemblePathParams(paths)) {\n    url.searchParams.append('path', pathParam);\n  }\n  // Assemble visible locations\n  if (visible.length) {\n    url.searchParams.append('visible', visible.map(location => formatLocation(location)).join('|'));\n  }\n  // Assemble Map Type Styles\n  for (const styleString of assembleMapTypeStyles(style)) {\n    url.searchParams.append('style', styleString);\n  }\n  return url.toString();\n}\n\nconst StaticMap = props => {\n  const {\n    url,\n    className\n  } = props;\n  if (!url) throw new Error('URL is required');\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n    className: className,\n    src: url,\n    width: \"100%\"\n  });\n};\n\n/**\n * Copy of the `google.maps.ControlPosition` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst ControlPosition = {\n  TOP_LEFT: 1,\n  TOP_CENTER: 2,\n  TOP: 2,\n  TOP_RIGHT: 3,\n  LEFT_CENTER: 4,\n  LEFT_TOP: 5,\n  LEFT: 5,\n  LEFT_BOTTOM: 6,\n  RIGHT_TOP: 7,\n  RIGHT: 7,\n  RIGHT_CENTER: 8,\n  RIGHT_BOTTOM: 9,\n  BOTTOM_LEFT: 10,\n  BOTTOM_CENTER: 11,\n  BOTTOM: 11,\n  BOTTOM_RIGHT: 12,\n  CENTER: 13,\n  BLOCK_START_INLINE_START: 14,\n  BLOCK_START_INLINE_CENTER: 15,\n  BLOCK_START_INLINE_END: 16,\n  INLINE_START_BLOCK_CENTER: 17,\n  INLINE_START_BLOCK_START: 18,\n  INLINE_START_BLOCK_END: 19,\n  INLINE_END_BLOCK_START: 20,\n  INLINE_END_BLOCK_CENTER: 21,\n  INLINE_END_BLOCK_END: 22,\n  BLOCK_END_INLINE_START: 23,\n  BLOCK_END_INLINE_CENTER: 24,\n  BLOCK_END_INLINE_END: 25\n};\nconst MapControl = ({\n  children,\n  position\n}) => {\n  const controlContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  const map = useMap();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const controls = map.controls[position];\n    controls.push(controlContainer);\n    return () => {\n      const controlsArray = controls.getArray();\n      // controlsArray could be undefined if the map is in an undefined state (e.g. invalid API-key, see #276\n      if (!controlsArray) return;\n      const index = controlsArray.indexOf(controlContainer);\n      controls.removeAt(index);\n    };\n  }, [controlContainer, map, position]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, controlContainer);\n};\n\nconst _excluded = [\"onClick\", \"onDrag\", \"onDragStart\", \"onDragEnd\", \"onMouseOver\", \"onMouseOut\"];\nfunction useMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const {\n      onClick,\n      onDrag,\n      onDragStart,\n      onDragEnd,\n      onMouseOver,\n      onMouseOut\n    } = props,\n    markerOptions = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position,\n    draggable\n  } = markerOptions;\n  // create marker instance and add to the map once the map is available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) {\n      if (map === undefined) console.error('<Marker> has to be inside a Map component.');\n      return;\n    }\n    const newMarker = new google.maps.Marker(markerOptions);\n    newMarker.setMap(map);\n    setMarker(newMarker);\n    return () => {\n      newMarker.setMap(null);\n      setMarker(null);\n    };\n    // We do not want to re-render the whole marker when the options change.\n    // Marker options update is handled in a useEffect below.\n    // Excluding markerOptions from dependency array on purpose here.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [map]);\n  // attach and re-attach event-handlers when any of the properties change\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const m = marker;\n    // Add event listeners\n    const gme = google.maps.event;\n    if (onClick) gme.addListener(m, 'click', onClick);\n    if (onDrag) gme.addListener(m, 'drag', onDrag);\n    if (onDragStart) gme.addListener(m, 'dragstart', onDragStart);\n    if (onDragEnd) gme.addListener(m, 'dragend', onDragEnd);\n    if (onMouseOver) gme.addListener(m, 'mouseover', onMouseOver);\n    if (onMouseOut) gme.addListener(m, 'mouseout', onMouseOut);\n    marker.setDraggable(Boolean(draggable));\n    return () => {\n      gme.clearInstanceListeners(m);\n    };\n  }, [marker, draggable, onClick, onDrag, onDragStart, onDragEnd, onMouseOver, onMouseOut]);\n  // update markerOptions (note the dependencies aren't properly checked\n  // here, we just assume that setOptions is smart enough to not waste a\n  // lot of time updating values that didn't change)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (markerOptions) marker.setOptions(markerOptions);\n  }, [marker, markerOptions]);\n  // update position when changed\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Should not update position when draggable\n    if (draggable || !position || !marker) return;\n    marker.setPosition(position);\n  }, [draggable, position, marker]);\n  return marker;\n}\n/**\n * Component to render a marker on a map\n */\nconst Marker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const marker = useMarker(props);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n});\nfunction useMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\n/**\n * Component to configure the appearance of an AdvancedMarker\n */\nconst Pin = props => {\n  var _useContext;\n  const advancedMarker = (_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(AdvancedMarkerContext)) == null ? void 0 : _useContext.marker;\n  const glyphContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  // Create Pin View instance\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    var _advancedMarker$conte;\n    if (!advancedMarker) {\n      if (advancedMarker === undefined) {\n        console.error('The <Pin> component can only be used inside <AdvancedMarker>.');\n      }\n      return;\n    }\n    if (props.glyph && props.children) {\n      logErrorOnce('The <Pin> component only uses children to render the glyph if both the glyph property and children are present.');\n    }\n    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(props.children) > 1) {\n      logErrorOnce('Passing multiple children to the <Pin> component might lead to unexpected results.');\n    }\n    const pinViewOptions = _extends({}, props);\n    const pinElement = new google.maps.marker.PinElement(pinViewOptions);\n    // Set glyph to glyph container if children are present (rendered via portal).\n    // If both props.glyph and props.children are present, props.children takes priority.\n    if (props.children) {\n      pinElement.glyph = glyphContainer;\n    }\n    // Set content of Advanced Marker View to the Pin View element\n    // Here we are selecting the anchor container.\n    // The hierarchy is as follows:\n    // \"advancedMarker.content\" (from google) -> \"pointer events reset div\" -> \"anchor container\"\n    const markerContent = (_advancedMarker$conte = advancedMarker.content) == null || (_advancedMarker$conte = _advancedMarker$conte.firstChild) == null ? void 0 : _advancedMarker$conte.firstChild;\n    while (markerContent != null && markerContent.firstChild) {\n      markerContent.removeChild(markerContent.firstChild);\n    }\n    if (markerContent) {\n      markerContent.appendChild(pinElement.element);\n    }\n  }, [advancedMarker, glyphContainer, props]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.children, glyphContainer);\n};\n\nconst mapLinear = (x, a1, a2, b1, b2) => b1 + (x - a1) * (b2 - b1) / (a2 - a1);\nconst getMapMaxTilt = zoom => {\n  if (zoom <= 10) {\n    return 30;\n  }\n  if (zoom >= 15.5) {\n    return 67.5;\n  }\n  // range [10...14]\n  if (zoom <= 14) {\n    return mapLinear(zoom, 10, 14, 30, 45);\n  }\n  // range [14...15.5]\n  return mapLinear(zoom, 14, 15.5, 45, 67.5);\n};\n/**\n * Function to limit the tilt range of the Google map when updating the view state\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst limitTiltRange = ({\n  viewState\n}) => {\n  const pitch = viewState.pitch;\n  const gmZoom = viewState.zoom + 1;\n  const maxTilt = getMapMaxTilt(gmZoom);\n  return _extends({}, viewState, {\n    fovy: 25,\n    pitch: Math.min(maxTilt, pitch)\n  });\n};\n\n\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs\n");

/***/ })

};
;