"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vis.gl";
exports.ids = ["vendor-chunks/@vis.gl"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APILoadingStatus: () => (/* binding */ APILoadingStatus),\n/* harmony export */   APIProvider: () => (/* binding */ APIProvider),\n/* harmony export */   APIProviderContext: () => (/* binding */ APIProviderContext),\n/* harmony export */   AdvancedMarker: () => (/* binding */ AdvancedMarker),\n/* harmony export */   AdvancedMarkerAnchorPoint: () => (/* binding */ AdvancedMarkerAnchorPoint),\n/* harmony export */   AdvancedMarkerContext: () => (/* binding */ AdvancedMarkerContext),\n/* harmony export */   CollisionBehavior: () => (/* binding */ CollisionBehavior),\n/* harmony export */   ColorScheme: () => (/* binding */ ColorScheme),\n/* harmony export */   ControlPosition: () => (/* binding */ ControlPosition),\n/* harmony export */   GoogleMapsContext: () => (/* binding */ GoogleMapsContext),\n/* harmony export */   InfoWindow: () => (/* binding */ InfoWindow),\n/* harmony export */   Map: () => (/* binding */ Map),\n/* harmony export */   MapControl: () => (/* binding */ MapControl),\n/* harmony export */   Marker: () => (/* binding */ Marker),\n/* harmony export */   Pin: () => (/* binding */ Pin),\n/* harmony export */   RenderingType: () => (/* binding */ RenderingType),\n/* harmony export */   StaticMap: () => (/* binding */ StaticMap),\n/* harmony export */   createStaticMapsUrl: () => (/* binding */ createStaticMapsUrl),\n/* harmony export */   isAdvancedMarker: () => (/* binding */ isAdvancedMarker),\n/* harmony export */   isLatLngLiteral: () => (/* binding */ isLatLngLiteral),\n/* harmony export */   latLngEquals: () => (/* binding */ latLngEquals),\n/* harmony export */   limitTiltRange: () => (/* binding */ limitTiltRange),\n/* harmony export */   toLatLngLiteral: () => (/* binding */ toLatLngLiteral),\n/* harmony export */   useAdvancedMarkerRef: () => (/* binding */ useAdvancedMarkerRef),\n/* harmony export */   useApiIsLoaded: () => (/* binding */ useApiIsLoaded),\n/* harmony export */   useApiLoadingStatus: () => (/* binding */ useApiLoadingStatus),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMapsLibrary: () => (/* binding */ useMapsLibrary),\n/* harmony export */   useMarkerRef: () => (/* binding */ useMarkerRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fast-deep-equal */ \"(ssr)/./node_modules/fast-deep-equal/index.js\");\n\n\n\n\nfunction _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.includes(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nconst APILoadingStatus = {\n  NOT_LOADED: 'NOT_LOADED',\n  LOADING: 'LOADING',\n  LOADED: 'LOADED',\n  FAILED: 'FAILED',\n  AUTH_FAILURE: 'AUTH_FAILURE'\n};\n\nconst MAPS_API_BASE_URL = 'https://maps.googleapis.com/maps/api/js';\n/**\n * A GoogleMapsApiLoader to reliably load and unload the Google Maps JavaScript API.\n *\n * The actual loading and unloading is delayed into the microtask queue, to\n * allow using the API in an useEffect hook, without worrying about multiple API loads.\n */\nclass GoogleMapsApiLoader {\n  /**\n   * Loads the Maps JavaScript API with the specified parameters.\n   * Since the Maps library can only be loaded once per page, this will\n   * produce a warning when called multiple times with different\n   * parameters.\n   *\n   * The returned promise resolves when loading completes\n   * and rejects in case of an error or when the loading was aborted.\n   */\n  static async load(params, onLoadingStatusChange) {\n    var _window$google;\n    const libraries = params.libraries ? params.libraries.split(',') : [];\n    const serializedParams = this.serializeParams(params);\n    this.listeners.push(onLoadingStatusChange);\n    // Note: if `google.maps.importLibrary` has been defined externally, we\n    //   assume that loading is complete and successful.\n    //   If it was defined by a previous call to this method, a warning\n    //   message is logged if there are differences in api-parameters used\n    //   for both calls.\n    if ((_window$google = window.google) != null && (_window$google = _window$google.maps) != null && _window$google.importLibrary) {\n      // no serialized parameters means it was loaded externally\n      if (!this.serializedApiParams) {\n        this.loadingStatus = APILoadingStatus.LOADED;\n      }\n      this.notifyLoadingStatusListeners();\n    } else {\n      this.serializedApiParams = serializedParams;\n      this.initImportLibrary(params);\n    }\n    if (this.serializedApiParams && this.serializedApiParams !== serializedParams) {\n      console.warn(`[google-maps-api-loader] The maps API has already been loaded ` + `with different parameters and will not be loaded again. Refresh the ` + `page for new values to have effect.`);\n    }\n    const librariesToLoad = ['maps', ...libraries];\n    await Promise.all(librariesToLoad.map(name => google.maps.importLibrary(name)));\n  }\n  /**\n   * Serialize the paramters used to load the library for easier comparison.\n   */\n  static serializeParams(params) {\n    return [params.v, params.key, params.language, params.region, params.authReferrerPolicy, params.solutionChannel].join('/');\n  }\n  /**\n   * Creates the global `google.maps.importLibrary` function for bootstrapping.\n   * This is essentially a formatted version of the dynamic loading script\n   * from the official documentation with some minor adjustments.\n   *\n   * The created importLibrary function will load the Google Maps JavaScript API,\n   * which will then replace the `google.maps.importLibrary` function with the full\n   * implementation.\n   *\n   * @see https://developers.google.com/maps/documentation/javascript/load-maps-js-api#dynamic-library-import\n   */\n  static initImportLibrary(params) {\n    if (!window.google) window.google = {};\n    if (!window.google.maps) window.google.maps = {};\n    if (window.google.maps['importLibrary']) {\n      console.error('[google-maps-api-loader-internal]: initImportLibrary must only be called once');\n      return;\n    }\n    let apiPromise = null;\n    const loadApi = () => {\n      if (apiPromise) return apiPromise;\n      apiPromise = new Promise((resolve, reject) => {\n        var _document$querySelect;\n        const scriptElement = document.createElement('script');\n        const urlParams = new URLSearchParams();\n        for (const [key, value] of Object.entries(params)) {\n          const urlParamName = key.replace(/[A-Z]/g, t => '_' + t[0].toLowerCase());\n          urlParams.set(urlParamName, String(value));\n        }\n        urlParams.set('loading', 'async');\n        urlParams.set('callback', '__googleMapsCallback__');\n        scriptElement.async = true;\n        scriptElement.src = MAPS_API_BASE_URL + `?` + urlParams.toString();\n        scriptElement.nonce = ((_document$querySelect = document.querySelector('script[nonce]')) == null ? void 0 : _document$querySelect.nonce) || '';\n        scriptElement.onerror = () => {\n          this.loadingStatus = APILoadingStatus.FAILED;\n          this.notifyLoadingStatusListeners();\n          reject(new Error('The Google Maps JavaScript API could not load.'));\n        };\n        window.__googleMapsCallback__ = () => {\n          this.loadingStatus = APILoadingStatus.LOADED;\n          this.notifyLoadingStatusListeners();\n          resolve();\n        };\n        window.gm_authFailure = () => {\n          this.loadingStatus = APILoadingStatus.AUTH_FAILURE;\n          this.notifyLoadingStatusListeners();\n        };\n        this.loadingStatus = APILoadingStatus.LOADING;\n        this.notifyLoadingStatusListeners();\n        document.head.append(scriptElement);\n      });\n      return apiPromise;\n    };\n    // for the first load, we declare an importLibrary function that will\n    // be overwritten once the api is loaded.\n    google.maps.importLibrary = libraryName => loadApi().then(() => google.maps.importLibrary(libraryName));\n  }\n  /**\n   * Calls all registered loadingStatusListeners after a status update.\n   */\n  static notifyLoadingStatusListeners() {\n    for (const fn of this.listeners) {\n      fn(this.loadingStatus);\n    }\n  }\n}\n/**\n * The current loadingStatus of the API.\n */\nGoogleMapsApiLoader.loadingStatus = APILoadingStatus.NOT_LOADED;\n/**\n * The parameters used for first loading the API.\n */\nGoogleMapsApiLoader.serializedApiParams = void 0;\n/**\n * A list of functions to be notified when the loading status changes.\n */\nGoogleMapsApiLoader.listeners = [];\n\nconst _excluded$3 = [\"onLoad\", \"onError\", \"apiKey\", \"version\", \"libraries\"],\n  _excluded2$1 = [\"children\"];\nconst DEFAULT_SOLUTION_CHANNEL = 'GMP_visgl_rgmlibrary_v1_default';\nconst APIProviderContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * local hook to set up the map-instance management context.\n */\nfunction useMapInstances() {\n  const [mapInstances, setMapInstances] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n  const addMapInstance = (mapInstance, id = 'default') => {\n    setMapInstances(instances => _extends({}, instances, {\n      [id]: mapInstance\n    }));\n  };\n  const removeMapInstance = (id = 'default') => {\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    setMapInstances(_ref => {\n      let remaining = _objectWithoutPropertiesLoose(_ref, [id].map(_toPropertyKey));\n      return remaining;\n    });\n  };\n  const clearMapInstances = () => {\n    setMapInstances({});\n  };\n  return {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  };\n}\n/**\n * local hook to handle the loading of the maps API, returns the current loading status\n * @param props\n */\nfunction useGoogleMapsApiLoader(props) {\n  const {\n      onLoad,\n      onError,\n      apiKey,\n      version,\n      libraries = []\n    } = props,\n    otherApiParams = _objectWithoutPropertiesLoose(props, _excluded$3);\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(GoogleMapsApiLoader.loadingStatus);\n  const [loadedLibraries, addLoadedLibrary] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((loadedLibraries, action) => {\n    return loadedLibraries[action.name] ? loadedLibraries : _extends({}, loadedLibraries, {\n      [action.name]: action.value\n    });\n  }, {});\n  const librariesString = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => libraries == null ? void 0 : libraries.join(','), [libraries]);\n  const serializedParams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => JSON.stringify(_extends({\n    apiKey,\n    version\n  }, otherApiParams)), [apiKey, version, otherApiParams]);\n  const importLibrary = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async name => {\n    var _google;\n    if (loadedLibraries[name]) {\n      return loadedLibraries[name];\n    }\n    if (!((_google = google) != null && (_google = _google.maps) != null && _google.importLibrary)) {\n      throw new Error('[api-provider-internal] importLibrary was called before ' + 'google.maps.importLibrary was defined.');\n    }\n    const res = await window.google.maps.importLibrary(name);\n    addLoadedLibrary({\n      name,\n      value: res\n    });\n    return res;\n  }, [loadedLibraries]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    (async () => {\n      try {\n        const params = _extends({\n          key: apiKey\n        }, otherApiParams);\n        if (version) params.v = version;\n        if ((librariesString == null ? void 0 : librariesString.length) > 0) params.libraries = librariesString;\n        if (params.channel === undefined || params.channel < 0 || params.channel > 999) delete params.channel;\n        if (params.solutionChannel === undefined) params.solutionChannel = DEFAULT_SOLUTION_CHANNEL;else if (params.solutionChannel === '') delete params.solutionChannel;\n        await GoogleMapsApiLoader.load(params, status => setStatus(status));\n        for (const name of ['core', 'maps', ...libraries]) {\n          await importLibrary(name);\n        }\n        if (onLoad) {\n          onLoad();\n        }\n      } catch (error) {\n        if (onError) {\n          onError(error);\n        } else {\n          console.error('<ApiProvider> failed to load the Google Maps JavaScript API', error);\n        }\n      }\n    })();\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [apiKey, librariesString, serializedParams]);\n  return {\n    status,\n    loadedLibraries,\n    importLibrary\n  };\n}\n/**\n * Component to wrap the components from this library and load the Google Maps JavaScript API\n */\nconst APIProvider = props => {\n  const {\n      children\n    } = props,\n    loaderProps = _objectWithoutPropertiesLoose(props, _excluded2$1);\n  const {\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances\n  } = useMapInstances();\n  const {\n    status,\n    loadedLibraries,\n    importLibrary\n  } = useGoogleMapsApiLoader(loaderProps);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    mapInstances,\n    addMapInstance,\n    removeMapInstance,\n    clearMapInstances,\n    status,\n    loadedLibraries,\n    importLibrary\n  }), [mapInstances, addMapInstance, removeMapInstance, clearMapInstances, status, loadedLibraries, importLibrary]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(APIProviderContext.Provider, {\n    value: contextValue\n  }, children);\n};\n\n/**\n * Sets up effects to bind event-handlers for all event-props in MapEventProps.\n * @internal\n */\nfunction useMapEvents(map, props) {\n  // note: calling a useEffect hook from within a loop is prohibited by the\n  // rules of hooks, but it's ok here since it's unconditional and the number\n  // and order of iterations is always strictly the same.\n  // (see https://legacy.reactjs.org/docs/hooks-rules.html)\n  for (const propName of eventPropNames) {\n    // fixme: this cast is essentially a 'trust me, bro' for typescript, but\n    //   a proper solution seems way too complicated right now\n    const handler = props[propName];\n    const eventType = propNameToEventType[propName];\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      if (!map) return;\n      if (!handler) return;\n      const listener = google.maps.event.addListener(map, eventType, ev => {\n        handler(createMapEvent(eventType, map, ev));\n      });\n      return () => listener.remove();\n    }, [map, eventType, handler]);\n  }\n}\n/**\n * Create the wrapped map-events used for the event-props.\n * @param type the event type as it is specified to the maps api\n * @param map the map instance the event originates from\n * @param srcEvent the source-event if there is one.\n */\nfunction createMapEvent(type, map, srcEvent) {\n  const ev = {\n    type,\n    map,\n    detail: {},\n    stoppable: false,\n    stop: () => {}\n  };\n  if (cameraEventTypes.includes(type)) {\n    const camEvent = ev;\n    const center = map.getCenter();\n    const zoom = map.getZoom();\n    const heading = map.getHeading() || 0;\n    const tilt = map.getTilt() || 0;\n    const bounds = map.getBounds();\n    if (!center || !bounds || !Number.isFinite(zoom)) {\n      console.warn('[createEvent] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n    }\n    camEvent.detail = {\n      center: (center == null ? void 0 : center.toJSON()) || {\n        lat: 0,\n        lng: 0\n      },\n      zoom: zoom || 0,\n      heading: heading,\n      tilt: tilt,\n      bounds: (bounds == null ? void 0 : bounds.toJSON()) || {\n        north: 90,\n        east: 180,\n        south: -90,\n        west: -180\n      }\n    };\n    return camEvent;\n  } else if (mouseEventTypes.includes(type)) {\n    var _srcEvent$latLng;\n    if (!srcEvent) throw new Error('[createEvent] mouse events must provide a srcEvent');\n    const mouseEvent = ev;\n    mouseEvent.domEvent = srcEvent.domEvent;\n    mouseEvent.stoppable = true;\n    mouseEvent.stop = () => srcEvent.stop();\n    mouseEvent.detail = {\n      latLng: ((_srcEvent$latLng = srcEvent.latLng) == null ? void 0 : _srcEvent$latLng.toJSON()) || null,\n      placeId: srcEvent.placeId\n    };\n    return mouseEvent;\n  }\n  return ev;\n}\n/**\n * maps the camelCased names of event-props to the corresponding event-types\n * used in the maps API.\n */\nconst propNameToEventType = {\n  onBoundsChanged: 'bounds_changed',\n  onCenterChanged: 'center_changed',\n  onClick: 'click',\n  onContextmenu: 'contextmenu',\n  onDblclick: 'dblclick',\n  onDrag: 'drag',\n  onDragend: 'dragend',\n  onDragstart: 'dragstart',\n  onHeadingChanged: 'heading_changed',\n  onIdle: 'idle',\n  onIsFractionalZoomEnabledChanged: 'isfractionalzoomenabled_changed',\n  onMapCapabilitiesChanged: 'mapcapabilities_changed',\n  onMapTypeIdChanged: 'maptypeid_changed',\n  onMousemove: 'mousemove',\n  onMouseout: 'mouseout',\n  onMouseover: 'mouseover',\n  onProjectionChanged: 'projection_changed',\n  onRenderingTypeChanged: 'renderingtype_changed',\n  onTilesLoaded: 'tilesloaded',\n  onTiltChanged: 'tilt_changed',\n  onZoomChanged: 'zoom_changed',\n  // note: onCameraChanged is an alias for the bounds_changed event,\n  // since that is going to be fired in every situation where the camera is\n  // updated.\n  onCameraChanged: 'bounds_changed'\n};\nconst cameraEventTypes = ['bounds_changed', 'center_changed', 'heading_changed', 'tilt_changed', 'zoom_changed'];\nconst mouseEventTypes = ['click', 'contextmenu', 'dblclick', 'mousemove', 'mouseout', 'mouseover'];\nconst eventPropNames = Object.keys(propNameToEventType);\n\nfunction useDeepCompareEffect(effect, deps) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n  if (!ref.current || !fast_deep_equal__WEBPACK_IMPORTED_MODULE_2__(deps, ref.current)) {\n    ref.current = deps;\n  }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, ref.current);\n}\n\nconst mapOptionKeys = new Set(['backgroundColor', 'clickableIcons', 'controlSize', 'disableDefaultUI', 'disableDoubleClickZoom', 'draggable', 'draggableCursor', 'draggingCursor', 'fullscreenControl', 'fullscreenControlOptions', 'gestureHandling', 'headingInteractionEnabled', 'isFractionalZoomEnabled', 'keyboardShortcuts', 'mapTypeControl', 'mapTypeControlOptions', 'mapTypeId', 'maxZoom', 'minZoom', 'noClear', 'panControl', 'panControlOptions', 'restriction', 'rotateControl', 'rotateControlOptions', 'scaleControl', 'scaleControlOptions', 'scrollwheel', 'streetView', 'streetViewControl', 'streetViewControlOptions', 'styles', 'tiltInteractionEnabled', 'zoomControl', 'zoomControlOptions']);\n/**\n * Internal hook to update the map-options when props are changed.\n *\n * @param map the map instance\n * @param mapProps the props to update the map-instance with\n * @internal\n */\nfunction useMapOptions(map, mapProps) {\n  /* eslint-disable react-hooks/exhaustive-deps --\n   *\n   * The following effects aren't triggered when the map is changed.\n   * In that case, the values will be or have been passed to the map\n   * constructor via mapOptions.\n   */\n  const mapOptions = {};\n  const keys = Object.keys(mapProps);\n  for (const key of keys) {\n    if (!mapOptionKeys.has(key)) continue;\n    mapOptions[key] = mapProps[key];\n  }\n  // update the map options when mapOptions is changed\n  // Note: due to the destructuring above, mapOptions will be seen as changed\n  //   with every re-render, so we're assuming the maps-api will properly\n  //   deal with unchanged option-values passed into setOptions.\n  useDeepCompareEffect(() => {\n    if (!map) return;\n    map.setOptions(mapOptions);\n  }, [mapOptions]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}\n\nfunction useApiLoadingStatus() {\n  var _useContext;\n  return ((_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext)) == null ? void 0 : _useContext.status) || APILoadingStatus.NOT_LOADED;\n}\n\n/**\n * Internal hook that updates the camera when deck.gl viewState changes.\n * @internal\n */\nfunction useDeckGLCameraUpdate(map, props) {\n  const {\n    viewport,\n    viewState\n  } = props;\n  const isDeckGlControlled = !!viewport;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !viewState) return;\n    const {\n      latitude,\n      longitude,\n      bearing: heading,\n      pitch: tilt,\n      zoom\n    } = viewState;\n    map.moveCamera({\n      center: {\n        lat: latitude,\n        lng: longitude\n      },\n      heading,\n      tilt,\n      zoom: zoom + 1\n    });\n  }, [map, viewState]);\n  return isDeckGlControlled;\n}\n\nfunction isLatLngLiteral(obj) {\n  if (!obj || typeof obj !== 'object') return false;\n  if (!('lat' in obj && 'lng' in obj)) return false;\n  return Number.isFinite(obj.lat) && Number.isFinite(obj.lng);\n}\nfunction latLngEquals(a, b) {\n  if (!a || !b) return false;\n  const A = toLatLngLiteral(a);\n  const B = toLatLngLiteral(b);\n  if (A.lat !== B.lat || A.lng !== B.lng) return false;\n  return true;\n}\nfunction toLatLngLiteral(obj) {\n  if (isLatLngLiteral(obj)) return obj;\n  return obj.toJSON();\n}\n\nfunction useMapCameraParams(map, cameraStateRef, mapProps) {\n  const center = mapProps.center ? toLatLngLiteral(mapProps.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const zoom = Number.isFinite(mapProps.zoom) ? mapProps.zoom : null;\n  const heading = Number.isFinite(mapProps.heading) ? mapProps.heading : null;\n  const tilt = Number.isFinite(mapProps.tilt) ? mapProps.tilt : null;\n  // the following effect runs for every render of the map component and checks\n  // if there are differences between the known state of the map instance\n  // (cameraStateRef, which is updated by all bounds_changed events) and the\n  // desired state in the props.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map) return;\n    const nextCamera = {};\n    let needsUpdate = false;\n    if (lat !== null && lng !== null && (cameraStateRef.current.center.lat !== lat || cameraStateRef.current.center.lng !== lng)) {\n      nextCamera.center = {\n        lat,\n        lng\n      };\n      needsUpdate = true;\n    }\n    if (zoom !== null && cameraStateRef.current.zoom !== zoom) {\n      nextCamera.zoom = zoom;\n      needsUpdate = true;\n    }\n    if (heading !== null && cameraStateRef.current.heading !== heading) {\n      nextCamera.heading = heading;\n      needsUpdate = true;\n    }\n    if (tilt !== null && cameraStateRef.current.tilt !== tilt) {\n      nextCamera.tilt = tilt;\n      needsUpdate = true;\n    }\n    if (needsUpdate) {\n      map.moveCamera(nextCamera);\n    }\n  });\n}\n\nconst AuthFailureMessage = () => {\n  const style = {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    zIndex: 999,\n    display: 'flex',\n    flexFlow: 'column nowrap',\n    textAlign: 'center',\n    justifyContent: 'center',\n    fontSize: '.8rem',\n    color: 'rgba(0,0,0,0.6)',\n    background: '#dddddd',\n    padding: '1rem 1.5rem'\n  };\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n    style: style\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h2\", null, \"Error: AuthFailure\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"p\", null, \"A problem with your API key prevents the map from rendering correctly. Please make sure the value of the \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"code\", null, \"APIProvider.apiKey\"), \" prop is correct. Check the error-message in the console for further details.\"));\n};\n\nfunction useCallbackRef() {\n  const [el, setEl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => setEl(value), [setEl]);\n  return [el, ref];\n}\n\n/**\n * Hook to check if the Maps JavaScript API is loaded\n */\nfunction useApiIsLoaded() {\n  const status = useApiLoadingStatus();\n  return status === APILoadingStatus.LOADED;\n}\n\nfunction useForceUpdate() {\n  const [, forceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(x => x + 1, 0);\n  return forceUpdate;\n}\n\nfunction handleBoundsChange(map, ref) {\n  const center = map.getCenter();\n  const zoom = map.getZoom();\n  const heading = map.getHeading() || 0;\n  const tilt = map.getTilt() || 0;\n  const bounds = map.getBounds();\n  if (!center || !bounds || !Number.isFinite(zoom)) {\n    console.warn('[useTrackedCameraState] at least one of the values from the map ' + 'returned undefined. This is not expected to happen. Please ' + 'report an issue at https://github.com/visgl/react-google-maps/issues/new');\n  }\n  // fixme: do we need the `undefined` cases for the camera-params? When are they used in the maps API?\n  Object.assign(ref.current, {\n    center: (center == null ? void 0 : center.toJSON()) || {\n      lat: 0,\n      lng: 0\n    },\n    zoom: zoom || 0,\n    heading: heading,\n    tilt: tilt\n  });\n}\n/**\n * Creates a mutable ref object to track the last known state of the map camera.\n * This is used in `useMapCameraParams` to reduce stuttering in normal operation\n * by avoiding updates of the map camera with values that have already been processed.\n */\nfunction useTrackedCameraStateRef(map) {\n  const forceUpdate = useForceUpdate();\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    center: {\n      lat: 0,\n      lng: 0\n    },\n    heading: 0,\n    tilt: 0,\n    zoom: 0\n  });\n  // Record camera state with every bounds_changed event dispatched by the map.\n  // This data is used to prevent feeding these values back to the\n  // map-instance when a typical \"controlled component\" setup (state variable is\n  // fed into and updated by the map).\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const listener = google.maps.event.addListener(map, 'bounds_changed', () => {\n      handleBoundsChange(map, ref);\n      // When an event is occured, we have to update during the next cycle.\n      // The application could decide to ignore the event and not update any\n      // camera props of the map, meaning that in that case we will have to\n      // 'undo' the change to the camera.\n      forceUpdate();\n    });\n    return () => listener.remove();\n  }, [map, forceUpdate]);\n  return ref;\n}\n\nconst _excluded$2 = [\"id\", \"defaultBounds\", \"defaultCenter\", \"defaultZoom\", \"defaultHeading\", \"defaultTilt\", \"reuseMaps\", \"renderingType\", \"colorScheme\"],\n  _excluded2 = [\"padding\"];\n/**\n * Stores a stack of map-instances for each mapId. Whenever an\n * instance is used, it is removed from the stack while in use,\n * and returned to the stack when the component unmounts.\n * This allows us to correctly implement caching for multiple\n * maps om the same page, while reusing as much as possible.\n *\n * FIXME: while it should in theory be possible to reuse maps solely\n *   based on the mapId (as all other parameters can be changed at\n *   runtime), we don't yet have good enough tracking of options to\n *   reliably unset all the options that have been set.\n */\nclass CachedMapStack {\n  static has(key) {\n    return this.entries[key] && this.entries[key].length > 0;\n  }\n  static pop(key) {\n    if (!this.entries[key]) return null;\n    return this.entries[key].pop() || null;\n  }\n  static push(key, value) {\n    if (!this.entries[key]) this.entries[key] = [];\n    this.entries[key].push(value);\n  }\n}\n/**\n * The main hook takes care of creating map-instances and registering them in\n * the api-provider context.\n * @return a tuple of the map-instance created (or null) and the callback\n *   ref that will be used to pass the map-container into this hook.\n * @internal\n */\nCachedMapStack.entries = {};\nfunction useMapInstance(props, context) {\n  const apiIsLoaded = useApiIsLoaded();\n  const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [container, containerRef] = useCallbackRef();\n  const cameraStateRef = useTrackedCameraStateRef(map);\n  const {\n      id,\n      defaultBounds,\n      defaultCenter,\n      defaultZoom,\n      defaultHeading,\n      defaultTilt,\n      reuseMaps,\n      renderingType,\n      colorScheme\n    } = props,\n    mapOptions = _objectWithoutPropertiesLoose(props, _excluded$2);\n  const hasZoom = props.zoom !== undefined || props.defaultZoom !== undefined;\n  const hasCenter = props.center !== undefined || props.defaultCenter !== undefined;\n  if (!defaultBounds && (!hasZoom || !hasCenter)) {\n    console.warn('<Map> component is missing configuration. ' + 'You have to provide zoom and center (via the `zoom`/`defaultZoom` and ' + '`center`/`defaultCenter` props) or specify the region to show using ' + '`defaultBounds`. See ' + 'https://visgl.github.io/react-google-maps/docs/api-reference/components/map#required');\n  }\n  // apply default camera props if available and not overwritten by controlled props\n  if (!mapOptions.center && defaultCenter) mapOptions.center = defaultCenter;\n  if (!mapOptions.zoom && Number.isFinite(defaultZoom)) mapOptions.zoom = defaultZoom;\n  if (!mapOptions.heading && Number.isFinite(defaultHeading)) mapOptions.heading = defaultHeading;\n  if (!mapOptions.tilt && Number.isFinite(defaultTilt)) mapOptions.tilt = defaultTilt;\n  for (const key of Object.keys(mapOptions)) if (mapOptions[key] === undefined) delete mapOptions[key];\n  const savedMapStateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n  // create the map instance and register it in the context\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!container || !apiIsLoaded) return;\n    const {\n      addMapInstance,\n      removeMapInstance\n    } = context;\n    // note: colorScheme (upcoming feature) isn't yet in the typings, remove once that is fixed:\n    const {\n      mapId\n    } = props;\n    const cacheKey = `${mapId || 'default'}:${renderingType || 'default'}:${colorScheme || 'LIGHT'}`;\n    let mapDiv;\n    let map;\n    if (reuseMaps && CachedMapStack.has(cacheKey)) {\n      map = CachedMapStack.pop(cacheKey);\n      mapDiv = map.getDiv();\n      container.appendChild(mapDiv);\n      map.setOptions(mapOptions);\n      // detaching the element from the DOM lets the map fall back to its default\n      // size, setting the center will trigger reloading the map.\n      setTimeout(() => map.setCenter(map.getCenter()), 0);\n    } else {\n      mapDiv = document.createElement('div');\n      mapDiv.style.height = '100%';\n      container.appendChild(mapDiv);\n      map = new google.maps.Map(mapDiv, _extends({}, mapOptions, renderingType ? {\n        renderingType: renderingType\n      } : {}, colorScheme ? {\n        colorScheme: colorScheme\n      } : {}));\n    }\n    setMap(map);\n    addMapInstance(map, id);\n    if (defaultBounds) {\n      const {\n          padding\n        } = defaultBounds,\n        defBounds = _objectWithoutPropertiesLoose(defaultBounds, _excluded2);\n      map.fitBounds(defBounds, padding);\n    }\n    // prevent map not rendering due to missing configuration\n    else if (!hasZoom || !hasCenter) {\n      map.fitBounds({\n        east: 180,\n        west: -180,\n        south: -90,\n        north: 90\n      });\n    }\n    // the savedMapState is used to restore the camera parameters when the mapId is changed\n    if (savedMapStateRef.current) {\n      const {\n        mapId: savedMapId,\n        cameraState: savedCameraState\n      } = savedMapStateRef.current;\n      if (savedMapId !== mapId) {\n        map.setOptions(savedCameraState);\n      }\n    }\n    return () => {\n      savedMapStateRef.current = {\n        mapId,\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        cameraState: cameraStateRef.current\n      };\n      // detach the map-div from the dom\n      mapDiv.remove();\n      if (reuseMaps) {\n        // push back on the stack\n        CachedMapStack.push(cacheKey, map);\n      } else {\n        // remove all event-listeners to minimize the possibility of memory-leaks\n        google.maps.event.clearInstanceListeners(map);\n      }\n      setMap(null);\n      removeMapInstance(id);\n    };\n  },\n  // some dependencies are ignored in the list below:\n  //  - defaultBounds and the default* camera props will only be used once, and\n  //    changes should be ignored\n  //  - mapOptions has special hooks that take care of updating the options\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [container, apiIsLoaded, id,\n  // these props can't be changed after initialization and require a new\n  // instance to be created\n  props.mapId, props.renderingType, props.colorScheme]);\n  return [map, containerRef, cameraStateRef];\n}\n\nconst GoogleMapsContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// ColorScheme and RenderingType are redefined here to make them usable before the\n// maps API has been fully loaded.\nconst ColorScheme = {\n  DARK: 'DARK',\n  LIGHT: 'LIGHT',\n  FOLLOW_SYSTEM: 'FOLLOW_SYSTEM'\n};\nconst RenderingType = {\n  VECTOR: 'VECTOR',\n  RASTER: 'RASTER',\n  UNINITIALIZED: 'UNINITIALIZED'\n};\nconst Map = props => {\n  const {\n    children,\n    id,\n    className,\n    style\n  } = props;\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const loadingStatus = useApiLoadingStatus();\n  if (!context) {\n    throw new Error('<Map> can only be used inside an <ApiProvider> component.');\n  }\n  const [map, mapRef, cameraStateRef] = useMapInstance(props, context);\n  useMapCameraParams(map, cameraStateRef, props);\n  useMapEvents(map, props);\n  useMapOptions(map, props);\n  const isDeckGlControlled = useDeckGLCameraUpdate(map, props);\n  const isControlledExternally = !!props.controlled;\n  // disable interactions with the map for externally controlled maps\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    // fixme: this doesn't seem to belong here (and it's mostly there for convenience anyway).\n    //   The reasoning is that a deck.gl canvas will be put on top of the map, rendering\n    //   any default map controls pretty much useless\n    if (isDeckGlControlled) {\n      map.setOptions({\n        disableDefaultUI: true\n      });\n    }\n    // disable all control-inputs when the map is controlled externally\n    if (isDeckGlControlled || isControlledExternally) {\n      map.setOptions({\n        gestureHandling: 'none',\n        keyboardShortcuts: false\n      });\n    }\n    return () => {\n      map.setOptions({\n        gestureHandling: props.gestureHandling,\n        keyboardShortcuts: props.keyboardShortcuts\n      });\n    };\n  }, [map, isDeckGlControlled, isControlledExternally, props.gestureHandling, props.keyboardShortcuts]);\n  // setup a stable cameraOptions object that can be used as dependency\n  const center = props.center ? toLatLngLiteral(props.center) : null;\n  let lat = null;\n  let lng = null;\n  if (center && Number.isFinite(center.lat) && Number.isFinite(center.lng)) {\n    lat = center.lat;\n    lng = center.lng;\n  }\n  const cameraOptions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    var _lat, _lng, _props$zoom, _props$heading, _props$tilt;\n    return {\n      center: {\n        lat: (_lat = lat) != null ? _lat : 0,\n        lng: (_lng = lng) != null ? _lng : 0\n      },\n      zoom: (_props$zoom = props.zoom) != null ? _props$zoom : 0,\n      heading: (_props$heading = props.heading) != null ? _props$heading : 0,\n      tilt: (_props$tilt = props.tilt) != null ? _props$tilt : 0\n    };\n  }, [lat, lng, props.zoom, props.heading, props.tilt]);\n  // externally controlled mode: reject all camera changes that don't correspond to changes in props\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (!map || !isControlledExternally) return;\n    map.moveCamera(cameraOptions);\n    const listener = map.addListener('bounds_changed', () => {\n      map.moveCamera(cameraOptions);\n    });\n    return () => listener.remove();\n  }, [map, isControlledExternally, cameraOptions]);\n  const combinedStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => _extends({\n    width: '100%',\n    height: '100%',\n    position: 'relative',\n    // when using deckgl, the map should be sent to the back\n    zIndex: isDeckGlControlled ? -1 : 0\n  }, style), [style, isDeckGlControlled]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    map\n  }), [map]);\n  if (loadingStatus === APILoadingStatus.AUTH_FAILURE) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: _extends({\n        position: 'relative'\n      }, className ? {} : combinedStyle),\n      className: className\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AuthFailureMessage, null));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", _extends({\n    ref: mapRef,\n    \"data-testid\": 'map',\n    style: className ? undefined : combinedStyle,\n    className: className\n  }, id ? {\n    id\n  } : {}), map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(GoogleMapsContext.Provider, {\n    value: contextValue\n  }, children) : null);\n};\n// The deckGLViewProps flag here indicates to deck.gl that the Map component is\n// able to handle viewProps from deck.gl when deck.gl is used to control the map.\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nMap.deckGLViewProps = true;\n\nconst shownMessages = new Set();\nfunction logErrorOnce(...args) {\n  const key = JSON.stringify(args);\n  if (!shownMessages.has(key)) {\n    shownMessages.add(key);\n    console.error(...args);\n  }\n}\n\n/**\n * Retrieves a map-instance from the context. This is either an instance\n * identified by id or the parent map instance if no id is specified.\n * Returns null if neither can be found.\n */\nconst useMap = (id = null) => {\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  const {\n    map\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(GoogleMapsContext) || {};\n  if (ctx === null) {\n    logErrorOnce('useMap(): failed to retrieve APIProviderContext. ' + 'Make sure that the <APIProvider> component exists and that the ' + 'component you are calling `useMap()` from is a sibling of the ' + '<APIProvider>.');\n    return null;\n  }\n  const {\n    mapInstances\n  } = ctx;\n  // if an id is specified, the corresponding map or null is returned\n  if (id !== null) return mapInstances[id] || null;\n  // otherwise, return the closest ancestor\n  if (map) return map;\n  // finally, return the default map instance\n  return mapInstances['default'] || null;\n};\n\nfunction useMapsLibrary(name) {\n  const apiIsLoaded = useApiIsLoaded();\n  const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(APIProviderContext);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!apiIsLoaded || !ctx) return;\n    // Trigger loading the libraries via our proxy-method.\n    // The returned promise is ignored, since importLibrary will update loadedLibraries\n    // list in the context, triggering a re-render.\n    void ctx.importLibrary(name);\n  }, [apiIsLoaded, ctx, name]);\n  return (ctx == null ? void 0 : ctx.loadedLibraries[name]) || null;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to Maps JavaScript API objects.\n * @internal\n */\nfunction useMapsEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    const listener = google.maps.event.addListener(target, name, callback);\n    return () => listener.remove();\n  }, [target, name, callback]);\n}\n\n/**\n * Internally used to copy values from props into API-Objects\n * whenever they change.\n *\n * @example\n *   usePropBinding(marker, 'position', position);\n *\n * @internal\n */\nfunction usePropBinding(object, prop, value) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!object) return;\n    object[prop] = value;\n  }, [object, prop, value]);\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Internally used to bind events to DOM nodes.\n * @internal\n */\nfunction useDomEventListener(target, name, callback) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!target || !name || !callback) return;\n    target.addEventListener(name, callback);\n    return () => target.removeEventListener(name, callback);\n  }, [target, name, callback]);\n}\n\n/* eslint-disable complexity */\nfunction isAdvancedMarker(marker) {\n  return marker.content !== undefined;\n}\nfunction isElementNode(node) {\n  return node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Copy of the `google.maps.CollisionBehavior` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst CollisionBehavior = {\n  REQUIRED: 'REQUIRED',\n  REQUIRED_AND_HIDES_OPTIONAL: 'REQUIRED_AND_HIDES_OPTIONAL',\n  OPTIONAL_AND_HIDES_LOWER_PRIORITY: 'OPTIONAL_AND_HIDES_LOWER_PRIORITY'\n};\nconst AdvancedMarkerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n// [xPosition, yPosition] when the top left corner is [0, 0]\nconst AdvancedMarkerAnchorPoint = {\n  TOP_LEFT: ['0%', '0%'],\n  TOP_CENTER: ['50%', '0%'],\n  TOP: ['50%', '0%'],\n  TOP_RIGHT: ['100%', '0%'],\n  LEFT_CENTER: ['0%', '50%'],\n  LEFT_TOP: ['0%', '0%'],\n  LEFT: ['0%', '50%'],\n  LEFT_BOTTOM: ['0%', '100%'],\n  RIGHT_TOP: ['100%', '0%'],\n  RIGHT: ['100%', '50%'],\n  RIGHT_CENTER: ['100%', '50%'],\n  RIGHT_BOTTOM: ['100%', '100%'],\n  BOTTOM_LEFT: ['0%', '100%'],\n  BOTTOM_CENTER: ['50%', '100%'],\n  BOTTOM: ['50%', '100%'],\n  BOTTOM_RIGHT: ['100%', '100%'],\n  CENTER: ['50%', '50%']\n};\nconst MarkerContent = ({\n  children,\n  styles,\n  className,\n  anchorPoint\n}) => {\n  const [xTranslation, yTranslation] = anchorPoint != null ? anchorPoint : AdvancedMarkerAnchorPoint['BOTTOM'];\n  // The \"translate(50%, 100%)\" is here to counter and reset the default anchoring of the advanced marker element\n  // that comes from the api\n  const transformStyle = `translate(50%, 100%) translate(-${xTranslation}, -${yTranslation})`;\n  return (\n    /*#__PURE__*/\n    // anchoring container\n    react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      style: {\n        transform: transformStyle\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n      className: className,\n      style: styles\n    }, children))\n  );\n};\nfunction useAdvancedMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [contentContainer, setContentContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const markerLibrary = useMapsLibrary('marker');\n  const {\n    children,\n    onClick,\n    className,\n    onMouseEnter,\n    onMouseLeave,\n    onDrag,\n    onDragStart,\n    onDragEnd,\n    collisionBehavior,\n    clickable,\n    draggable,\n    position,\n    title,\n    zIndex\n  } = props;\n  const numChildren = react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children);\n  // create an AdvancedMarkerElement instance and add it to the map once available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map || !markerLibrary) return;\n    const newMarker = new markerLibrary.AdvancedMarkerElement();\n    newMarker.map = map;\n    setMarker(newMarker);\n    // create the container for marker content if there are children\n    let contentElement = null;\n    if (numChildren > 0) {\n      contentElement = document.createElement('div');\n      // We need some kind of flag to identify the custom marker content\n      // in the infowindow component. Choosing a custom property instead of a className\n      // to not encourage users to style the marker content directly.\n      contentElement.isCustomMarker = true;\n      newMarker.content = contentElement;\n      setContentContainer(contentElement);\n    }\n    return () => {\n      var _contentElement;\n      newMarker.map = null;\n      (_contentElement = contentElement) == null || _contentElement.remove();\n      setMarker(null);\n      setContentContainer(null);\n    };\n  }, [map, markerLibrary, numChildren]);\n  // When no children are present we don't have our own wrapper div\n  // which usually gets the user provided className. In this case\n  // we set the className directly on the marker.content element that comes\n  // with the AdvancedMarker.\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker || !marker.content || numChildren > 0) return;\n    marker.content.className = className || '';\n  }, [marker, className, numChildren]);\n  // copy other props\n  usePropBinding(marker, 'position', position);\n  usePropBinding(marker, 'title', title != null ? title : '');\n  usePropBinding(marker, 'zIndex', zIndex);\n  usePropBinding(marker, 'collisionBehavior', collisionBehavior);\n  // set gmpDraggable from props (when unspecified, it's true if any drag-event\n  // callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (draggable !== undefined) marker.gmpDraggable = draggable;else if (onDrag || onDragStart || onDragEnd) marker.gmpDraggable = true;else marker.gmpDraggable = false;\n  }, [marker, draggable, onDrag, onDragEnd, onDragStart]);\n  // set gmpClickable from props (when unspecified, it's true if the onClick or one of\n  // the hover events callbacks are specified)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const gmpClickable = clickable !== undefined || Boolean(onClick) || Boolean(onMouseEnter) || Boolean(onMouseLeave);\n    // gmpClickable is only available in beta version of the\n    // maps api (as of 2024-10-10)\n    marker.gmpClickable = gmpClickable;\n    // enable pointer events for the markers with custom content\n    if (gmpClickable && marker != null && marker.content && isElementNode(marker.content)) {\n      marker.content.style.pointerEvents = 'none';\n      if (marker.content.firstElementChild) {\n        marker.content.firstElementChild.style.pointerEvents = 'all';\n      }\n    }\n  }, [marker, clickable, onClick, onMouseEnter, onMouseLeave]);\n  useMapsEventListener(marker, 'click', onClick);\n  useMapsEventListener(marker, 'drag', onDrag);\n  useMapsEventListener(marker, 'dragstart', onDragStart);\n  useMapsEventListener(marker, 'dragend', onDragEnd);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseenter', onMouseEnter);\n  useDomEventListener(marker == null ? void 0 : marker.element, 'mouseleave', onMouseLeave);\n  return [marker, contentContainer];\n}\nconst AdvancedMarker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const {\n    children,\n    style,\n    className,\n    anchorPoint\n  } = props;\n  const [marker, contentContainer] = useAdvancedMarker(props);\n  const advancedMarkerContextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => marker ? {\n    marker\n  } : null, [marker]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  if (!contentContainer) return null;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AdvancedMarkerContext.Provider, {\n    value: advancedMarkerContextValue\n  }, (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(MarkerContent, {\n    anchorPoint: anchorPoint,\n    styles: style,\n    className: className\n  }, children), contentContainer));\n});\nfunction useAdvancedMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\nfunction setValueForStyles(element, styles, prevStyles) {\n  if (styles != null && typeof styles !== 'object') {\n    throw new Error('The `style` prop expects a mapping from style properties to values, ' + \"not a string. For example, style={{marginRight: spacing + 'em'}} when \" + 'using JSX.');\n  }\n  const elementStyle = element.style;\n  // without `prevStyles`, just set all values\n  if (prevStyles == null) {\n    if (styles == null) return;\n    for (const styleName in styles) {\n      if (!styles.hasOwnProperty(styleName)) continue;\n      setValueForStyle(elementStyle, styleName, styles[styleName]);\n    }\n    return;\n  }\n  // unset all styles in `prevStyles` that aren't in `styles`\n  for (const styleName in prevStyles) {\n    if (prevStyles.hasOwnProperty(styleName) && (styles == null || !styles.hasOwnProperty(styleName))) {\n      // Clear style\n      const isCustomProperty = styleName.indexOf('--') === 0;\n      if (isCustomProperty) {\n        elementStyle.setProperty(styleName, '');\n      } else if (styleName === 'float') {\n        elementStyle.cssFloat = '';\n      } else {\n        elementStyle[styleName] = '';\n      }\n    }\n  }\n  // only assign values from `styles` that are different from `prevStyles`\n  if (styles == null) return;\n  for (const styleName in styles) {\n    const value = styles[styleName];\n    if (styles.hasOwnProperty(styleName) && prevStyles[styleName] !== value) {\n      setValueForStyle(elementStyle, styleName, value);\n    }\n  }\n}\nfunction setValueForStyle(elementStyle, styleName, value) {\n  const isCustomProperty = styleName.indexOf('--') === 0;\n  // falsy values will unset the style property\n  if (value == null || typeof value === 'boolean' || value === '') {\n    if (isCustomProperty) {\n      elementStyle.setProperty(styleName, '');\n    } else if (styleName === 'float') {\n      elementStyle.cssFloat = '';\n    } else {\n      elementStyle[styleName] = '';\n    }\n  }\n  // custom properties can't be directly assigned\n  else if (isCustomProperty) {\n    elementStyle.setProperty(styleName, value);\n  }\n  // numeric values are treated as 'px' unless the style property expects unitless numbers\n  else if (typeof value === 'number' && value !== 0 && !isUnitlessNumber(styleName)) {\n    elementStyle[styleName] = value + 'px'; // Presumes implicit 'px' suffix for unitless numbers\n  }\n  // everything else can just be assigned\n  else {\n    if (styleName === 'float') {\n      elementStyle.cssFloat = value;\n    } else {\n      elementStyle[styleName] = ('' + value).trim();\n    }\n  }\n}\n// CSS properties which accept numbers but are not in units of \"px\".\nconst unitlessNumbers = new Set(['animationIterationCount', 'aspectRatio', 'borderImageOutset', 'borderImageSlice', 'borderImageWidth', 'boxFlex', 'boxFlexGroup', 'boxOrdinalGroup', 'columnCount', 'columns', 'flex', 'flexGrow', 'flexPositive', 'flexShrink', 'flexNegative', 'flexOrder', 'gridArea', 'gridRow', 'gridRowEnd', 'gridRowSpan', 'gridRowStart', 'gridColumn', 'gridColumnEnd', 'gridColumnSpan', 'gridColumnStart', 'fontWeight', 'lineClamp', 'lineHeight', 'opacity', 'order', 'orphans', 'scale', 'tabSize', 'widows', 'zIndex', 'zoom', 'fillOpacity',\n// SVG-related properties\n'floodOpacity', 'stopOpacity', 'strokeDasharray', 'strokeDashoffset', 'strokeMiterlimit', 'strokeOpacity', 'strokeWidth']);\nfunction isUnitlessNumber(name) {\n  return unitlessNumbers.has(name);\n}\n\nconst _excluded$1 = [\"children\", \"headerContent\", \"style\", \"className\", \"pixelOffset\", \"anchor\", \"shouldFocus\", \"onClose\", \"onCloseClick\"];\n/**\n * Component to render an Info Window with the Maps JavaScript API\n */\nconst InfoWindow = props => {\n  const {\n      // content options\n      children,\n      headerContent,\n      style,\n      className,\n      pixelOffset,\n      // open options\n      anchor,\n      shouldFocus,\n      // events\n      onClose,\n      onCloseClick\n      // other options\n    } = props,\n    infoWindowOptions = _objectWithoutPropertiesLoose(props, _excluded$1);\n  // ## create infowindow instance once the mapsLibrary is available.\n  const mapsLibrary = useMapsLibrary('maps');\n  const [infoWindow, setInfoWindow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const contentContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const headerContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!mapsLibrary) return;\n    contentContainerRef.current = document.createElement('div');\n    headerContainerRef.current = document.createElement('div');\n    const opts = infoWindowOptions;\n    if (pixelOffset) {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (headerContent) {\n      // if headerContent is specified as string we can directly forward it,\n      // otherwise we'll pass the element the portal will render into\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    // intentionally shadowing the state variables here\n    const infoWindow = new google.maps.InfoWindow(infoWindowOptions);\n    infoWindow.setContent(contentContainerRef.current);\n    setInfoWindow(infoWindow);\n    // unmount: remove infoWindow and content elements (note: close is called in a different effect-cleanup)\n    return () => {\n      var _contentContainerRef$, _headerContainerRef$c;\n      infoWindow.setContent(null);\n      (_contentContainerRef$ = contentContainerRef.current) == null || _contentContainerRef$.remove();\n      (_headerContainerRef$c = headerContainerRef.current) == null || _headerContainerRef$c.remove();\n      contentContainerRef.current = null;\n      headerContainerRef.current = null;\n      setInfoWindow(null);\n    };\n  },\n  // `infoWindowOptions` and other props are missing from dependencies:\n  //\n  // We don't want to re-create the infowindow instance\n  // when the options change.\n  // Updating the options is handled in the useEffect below.\n  //\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [mapsLibrary]);\n  // ## update className and styles for `contentContainer`\n  // stores previously applied style properties, so they can be removed when unset\n  const prevStyleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!infoWindow || !contentContainerRef.current) return;\n    setValueForStyles(contentContainerRef.current, style || null, prevStyleRef.current);\n    prevStyleRef.current = style || null;\n    if (className !== contentContainerRef.current.className) contentContainerRef.current.className = className || '';\n  }, [infoWindow, className, style]);\n  // ## update options\n  useDeepCompareEffect(() => {\n    if (!infoWindow) return;\n    const opts = infoWindowOptions;\n    if (!pixelOffset) {\n      opts.pixelOffset = null;\n    } else {\n      opts.pixelOffset = new google.maps.Size(pixelOffset[0], pixelOffset[1]);\n    }\n    if (!headerContent) {\n      opts.headerContent = null;\n    } else {\n      opts.headerContent = typeof headerContent === 'string' ? headerContent : headerContainerRef.current;\n    }\n    infoWindow.setOptions(infoWindowOptions);\n  },\n  // dependency `infoWindow` isn't needed since options are also passed\n  // to the constructor when a new infoWindow is created.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [infoWindowOptions, pixelOffset, headerContent]);\n  // ## bind event handlers\n  useMapsEventListener(infoWindow, 'close', onClose);\n  useMapsEventListener(infoWindow, 'closeclick', onCloseClick);\n  // ## open info window when content and map are available\n  const map = useMap();\n  useDeepCompareEffect(() => {\n    // `anchor === null` means an anchor is defined but not ready yet.\n    if (!map || !infoWindow || anchor === null) return;\n    const isOpenedWithAnchor = !!anchor;\n    const openOptions = {\n      map\n    };\n    if (anchor) {\n      openOptions.anchor = anchor;\n      // Only do the infowindow adjusting when dealing with an AdvancedMarker\n      if (isAdvancedMarker(anchor) && anchor.content instanceof Element) {\n        const wrapper = anchor.content;\n        const wrapperBcr = wrapper == null ? void 0 : wrapper.getBoundingClientRect();\n        // This checks whether or not the anchor has custom content with our own\n        // div wrapper. If not, that means we have a regular AdvancedMarker without any children.\n        // In that case we do not want to adjust the infowindow since it is all handled correctly\n        // by the Google Maps API.\n        if (wrapperBcr && wrapper != null && wrapper.isCustomMarker) {\n          var _anchor$content$first;\n          // We can safely typecast here since we control that element and we know that\n          // it is a div\n          const anchorDomContent = (_anchor$content$first = anchor.content.firstElementChild) == null ? void 0 : _anchor$content$first.firstElementChild;\n          const contentBcr = anchorDomContent == null ? void 0 : anchorDomContent.getBoundingClientRect();\n          // center infowindow above marker\n          const anchorOffsetX = contentBcr.x - wrapperBcr.x + (contentBcr.width - wrapperBcr.width) / 2;\n          const anchorOffsetY = contentBcr.y - wrapperBcr.y;\n          const opts = infoWindowOptions;\n          opts.pixelOffset = new google.maps.Size(pixelOffset ? pixelOffset[0] + anchorOffsetX : anchorOffsetX, pixelOffset ? pixelOffset[1] + anchorOffsetY : anchorOffsetY);\n          infoWindow.setOptions(opts);\n        }\n      }\n    }\n    if (shouldFocus !== undefined) {\n      openOptions.shouldFocus = shouldFocus;\n    }\n    infoWindow.open(openOptions);\n    return () => {\n      // Note: when the infowindow has an anchor, it will automatically show up again when the\n      // anchor was removed from the map before infoWindow.close() is called but the it gets\n      // added back to the map after that.\n      // More information here: https://issuetracker.google.com/issues/343750849\n      if (isOpenedWithAnchor) infoWindow.set('anchor', null);\n      infoWindow.close();\n    };\n  }, [infoWindow, anchor, map, shouldFocus, infoWindowOptions, pixelOffset]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, contentContainerRef.current && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, contentContainerRef.current), headerContainerRef.current !== null && (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(headerContent, headerContainerRef.current));\n};\n\n/**\n * Formats a location into a string representation suitable for Google Static Maps API.\n *\n * @param location - The location to format, can be either a string or an object with lat/lng properties\n * @returns A string representation of the location in the format \"lat,lng\" or the original string\n *\n * @example\n * // Returns \"40.714728,-73.998672\"\n * formatLocation({ lat: 40.714728, lng: -73.998672 })\n *\n * @example\n * // Returns \"New York, NY\"\n * formatLocation(\"New York, NY\")\n */\nfunction formatLocation(location) {\n  return typeof location === 'string' ? location : `${location.lat},${location.lng}`;\n}\n// Used for removing the leading pipe from the param string\nfunction formatParam(string) {\n  return string.slice(1);\n}\n\n/**\n * Assembles marker parameters for static maps.\n *\n * This function takes an array of markers and groups them by their style properties.\n * It then creates a string representation of these markers, including their styles and locations,\n * which can be used as parameters for static map APIs.\n *\n * @param {StaticMapsMarker[]} [markers=[]] - An array of markers to be processed. Each marker can have properties such as color, label, size, scale, icon, anchor, and location.\n * @returns {string[]} An array of strings, each representing a group of markers with their styles and locations.\n *\n * @example\n * const markers = [\n *   { color: 'blue', label: 'A', size: 'mid', location: '40.714728,-73.998672' },\n *   { color: 'blue', label: 'B', size: 'mid', location: '40.714728,-73.998672' },\n *   { icon: 'http://example.com/icon.png', location: { lat: 40.714728, lng: -73.998672 } }\n * ];\n * const params = assembleMarkerParams(markers);\n * // Params will be an array of strings representing the marker parameters\n * Example output: [\n *   \"color:blue|label:A|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"color:blue|label:B|size:mid|40.714728,-73.998672|40.714728,-73.998672\",\n *   \"icon:http://example.com/icon.png|40.714728,-73.998672\"\n * ]\n */\nfunction assembleMarkerParams(markers = []) {\n  const markerParams = [];\n  // Group markers by style\n  const markersByStyle = markers == null ? void 0 : markers.reduce((styles, marker) => {\n    const {\n      color = 'red',\n      label,\n      size,\n      scale,\n      icon,\n      anchor\n    } = marker;\n    // Create a unique style key based on either icon properties or standard marker properties\n    const relevantProps = icon ? [icon, anchor, scale] : [color, label, size];\n    const key = relevantProps.filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(marker);\n    return styles;\n  }, {});\n  Object.values(markersByStyle != null ? markersByStyle : {}).forEach(markers => {\n    let markerParam = '';\n    const {\n      icon\n    } = markers[0];\n    // Create marker style from first marker in group since all markers share the same style.\n    Object.entries(markers[0]).forEach(([key, value]) => {\n      // Determine which properties to include based on whether marker uses custom icon\n      const relevantKeys = icon ? ['icon', 'anchor', 'scale'] : ['color', 'label', 'size'];\n      if (relevantKeys.includes(key)) {\n        markerParam += `|${key}:${value}`;\n      }\n    });\n    // Add location coordinates for each marker in the style group\n    // Handles both string locations and lat/lng object formats.\n    for (const marker of markers) {\n      const location = typeof marker.location === 'string' ? marker.location : `${marker.location.lat},${marker.location.lng}`;\n      markerParam += `|${location}`;\n    }\n    markerParams.push(markerParam);\n  });\n  return markerParams.map(formatParam);\n}\n\n/**\n * Assembles path parameters for the Static Maps Api from an array of paths.\n *\n * This function groups paths by their style properties (color, weight, fillcolor, geodesic)\n * and then constructs a string of path parameters for each group. Each path parameter string\n * includes the style properties and the coordinates of the paths.\n *\n * @param {Array<StaticMapsPath>} [paths=[]] - An array of paths to be assembled into path parameters.\n * @returns {Array<string>} An array of path parameter strings.\n *\n * @example\n * const paths = [\n *   {\n *     color: 'red',\n *     weight: 5,\n *     coordinates: [\n *       { lat: 40.714728, lng: -73.998672 },\n *       { lat: 40.718217, lng: -73.998284 }\n *     ]\n *   }\n * ];\n *\n * const pathParams = assemblePathParams(paths);\n * Output: [\n *    'color:red|weight:5|40.714728,-73.998672|40.718217,-73.998284'\n *  ]\n */\nfunction assemblePathParams(paths = []) {\n  const pathParams = [];\n  // Group paths by their style properties (color, weight, fillcolor, geodesic)\n  // to combine paths with identical styles into single parameter strings\n  const pathsByStyle = paths == null ? void 0 : paths.reduce((styles, path) => {\n    const {\n      color = 'default',\n      weight,\n      fillcolor,\n      geodesic\n    } = path;\n    // Create unique key for this style combination\n    const key = [color, weight, fillcolor, geodesic].filter(Boolean).join('-');\n    styles[key] = styles[key] || [];\n    styles[key].push(path);\n    return styles;\n  }, {});\n  // Process each group of paths with identical styles\n  Object.values(pathsByStyle != null ? pathsByStyle : {}).forEach(paths => {\n    let pathParam = '';\n    // Build style parameter string using properties from first path in group\n    // since all paths in this group share the same style\n    Object.entries(paths[0]).forEach(([key, value]) => {\n      if (['color', 'weight', 'fillcolor', 'geodesic'].includes(key)) {\n        pathParam += `|${key}:${value}`;\n      }\n    });\n    // Add location for all marker in style group\n    for (const path of paths) {\n      if (typeof path.coordinates === 'string') {\n        pathParam += `|${decodeURIComponent(path.coordinates)}`;\n      } else {\n        for (const location of path.coordinates) {\n          pathParam += `|${formatLocation(location)}`;\n        }\n      }\n    }\n    pathParams.push(pathParam);\n  });\n  return pathParams.map(formatParam);\n}\n\n/**\n * Converts an array of Google Maps style objects into an array of style strings\n * compatible with the Google Static Maps API.\n *\n * @param styles - An array of Google Maps MapTypeStyle objects that define the styling rules\n * @returns An array of formatted style strings ready to be used with the Static Maps API\n *\n * @example\n * const styles = [{\n *   featureType: \"road\",\n *   elementType: \"geometry\",\n *   stylers: [{color: \"#ff0000\"}, {weight: 1}]\n * }];\n *\n * const styleStrings = assembleMapTypeStyles(styles);\n * // Returns: [\"|feature:road|element:geometry|color:0xff0000|weight:1\"]\n *\n * Each style string follows the format:\n * \"feature:{featureType}|element:{elementType}|{stylerName}:{stylerValue}\"\n *\n * Note: Color values with hexadecimal notation (#) are automatically converted\n * to the required 0x format for the Static Maps API.\n */\nfunction assembleMapTypeStyles(styles) {\n  return styles.map(mapTypeStyle => {\n    const {\n      featureType,\n      elementType,\n      stylers = []\n    } = mapTypeStyle;\n    let styleString = '';\n    if (featureType) {\n      styleString += `|feature:${featureType}`;\n    }\n    if (elementType) {\n      styleString += `|element:${elementType}`;\n    }\n    for (const styler of stylers) {\n      Object.entries(styler).forEach(([name, value]) => {\n        styleString += `|${name}:${String(value).replace('#', '0x')}`;\n      });\n    }\n    return styleString;\n  }).map(formatParam);\n}\n\nconst STATIC_MAPS_BASE = 'https://maps.googleapis.com/maps/api/staticmap';\n/**\n * Creates a URL for the Google Static Maps API with the specified parameters.\n *\n * @param {Object} options - The configuration options for the static map\n * @param {string} options.apiKey - Your Google Maps API key (required)\n * @param {number} options.width - The width of the map image in pixels (required)\n * @param {number} options.height - The height of the map image in pixels (required)\n * @param {StaticMapsLocation} [options.center] - The center point of the map (lat/lng or address).\n *  Required if no markers or paths or \"visible locations\" are provided.\n * @param {number} [options.zoom] - The zoom level of the map. Required if no markers or paths or \"visible locations\" are provided.\n * @param {1|2|4} [options.scale] - The resolution of the map (1, 2, or 4)\n * @param {string} [options.format] - The image format (png, png8, png32, gif, jpg, jpg-baseline)\n * @param {string} [options.mapType] - The type of map (roadmap, satellite, terrain, hybrid)\n * @param {string} [options.language] - The language of the map labels\n * @param {string} [options.region] - The region code for the map\n * @param {string} [options.map_id] - The Cloud-based map style ID\n * @param {StaticMapsMarker[]} [options.markers=[]] - Array of markers to display on the map\n * @param {StaticMapsPath[]} [options.paths=[]] - Array of paths to display on the map\n * @param {StaticMapsLocation[]} [options.visible=[]] - Array of locations that should be visible on the map\n * @param {MapTypeStyle[]} [options.style=[]] - Array of style objects to customize the map appearance\n *\n * @returns {string} The complete Google Static Maps API URL\n *\n * @throws {Error} If API key is not provided\n * @throws {Error} If width or height is not provided\n *\n * @example\n * const url = createStaticMapsUrl({\n *   apiKey: 'YOUR_API_KEY',\n *   width: 600,\n *   height: 400,\n *   center: { lat: 40.714728, lng: -73.998672 },\n *   zoom: 12,\n *   markers: [\n *     {\n *       location: { lat: 40.714728, lng: -73.998672 },\n *       color: 'red',\n *       label: 'A'\n *     }\n *   ],\n *   paths: [\n *     {\n *       coordinates: [\n *         { lat: 40.714728, lng: -73.998672 },\n *         { lat: 40.719728, lng: -73.991672 }\n *       ],\n *       color: '0x0000ff',\n *       weight: 5\n *     }\n *   ],\n *   style: [\n *     {\n *       featureType: 'road',\n *       elementType: 'geometry',\n *       stylers: [{color: '#00ff00'}]\n *     }\n *   ]\n * });\n *\n * // Results in URL similar to:\n * // https://maps.googleapis.com/maps/api/staticmap?key=YOUR_API_KEY\n * // &size=600x400\n * // &center=40.714728,-73.998672&zoom=12\n * // &markers=color:red|label:A|40.714728,-73.998672\n * // &path=color:0x0000ff|weight:5|40.714728,-73.998672|40.719728,-73.991672\n * // &style=feature:road|element:geometry|color:0x00ff00\n */\nfunction createStaticMapsUrl({\n  apiKey,\n  width,\n  height,\n  center,\n  zoom,\n  scale,\n  format,\n  mapType,\n  language,\n  region,\n  mapId,\n  markers = [],\n  paths = [],\n  visible = [],\n  style = []\n}) {\n  if (!apiKey) {\n    console.warn('API key is required');\n  }\n  if (!width || !height) {\n    console.warn('Width and height are required');\n  }\n  const params = _extends({\n    key: apiKey,\n    size: `${width}x${height}`\n  }, center && {\n    center: formatLocation(center)\n  }, zoom && {\n    zoom\n  }, scale && {\n    scale\n  }, format && {\n    format\n  }, mapType && {\n    maptype: mapType\n  }, language && {\n    language\n  }, region && {\n    region\n  }, mapId && {\n    map_id: mapId\n  });\n  const url = new URL(STATIC_MAPS_BASE);\n  // Params that don't need special handling\n  Object.entries(params).forEach(([key, value]) => {\n    url.searchParams.append(key, String(value));\n  });\n  // Assemble Markers\n  for (const markerParam of assembleMarkerParams(markers)) {\n    url.searchParams.append('markers', markerParam);\n  }\n  // Assemble Paths\n  for (const pathParam of assemblePathParams(paths)) {\n    url.searchParams.append('path', pathParam);\n  }\n  // Assemble visible locations\n  if (visible.length) {\n    url.searchParams.append('visible', visible.map(location => formatLocation(location)).join('|'));\n  }\n  // Assemble Map Type Styles\n  for (const styleString of assembleMapTypeStyles(style)) {\n    url.searchParams.append('style', styleString);\n  }\n  return url.toString();\n}\n\nconst StaticMap = props => {\n  const {\n    url,\n    className\n  } = props;\n  if (!url) throw new Error('URL is required');\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"img\", {\n    className: className,\n    src: url,\n    width: \"100%\"\n  });\n};\n\n/**\n * Copy of the `google.maps.ControlPosition` constants.\n * They have to be duplicated here since we can't wait for the maps API to load to be able to use them.\n */\nconst ControlPosition = {\n  TOP_LEFT: 1,\n  TOP_CENTER: 2,\n  TOP: 2,\n  TOP_RIGHT: 3,\n  LEFT_CENTER: 4,\n  LEFT_TOP: 5,\n  LEFT: 5,\n  LEFT_BOTTOM: 6,\n  RIGHT_TOP: 7,\n  RIGHT: 7,\n  RIGHT_CENTER: 8,\n  RIGHT_BOTTOM: 9,\n  BOTTOM_LEFT: 10,\n  BOTTOM_CENTER: 11,\n  BOTTOM: 11,\n  BOTTOM_RIGHT: 12,\n  CENTER: 13,\n  BLOCK_START_INLINE_START: 14,\n  BLOCK_START_INLINE_CENTER: 15,\n  BLOCK_START_INLINE_END: 16,\n  INLINE_START_BLOCK_CENTER: 17,\n  INLINE_START_BLOCK_START: 18,\n  INLINE_START_BLOCK_END: 19,\n  INLINE_END_BLOCK_START: 20,\n  INLINE_END_BLOCK_CENTER: 21,\n  INLINE_END_BLOCK_END: 22,\n  BLOCK_END_INLINE_START: 23,\n  BLOCK_END_INLINE_CENTER: 24,\n  BLOCK_END_INLINE_END: 25\n};\nconst MapControl = ({\n  children,\n  position\n}) => {\n  const controlContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  const map = useMap();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) return;\n    const controls = map.controls[position];\n    controls.push(controlContainer);\n    return () => {\n      const controlsArray = controls.getArray();\n      // controlsArray could be undefined if the map is in an undefined state (e.g. invalid API-key, see #276\n      if (!controlsArray) return;\n      const index = controlsArray.indexOf(controlContainer);\n      controls.removeAt(index);\n    };\n  }, [controlContainer, map, position]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(children, controlContainer);\n};\n\nconst _excluded = [\"onClick\", \"onDrag\", \"onDragStart\", \"onDragEnd\", \"onMouseOver\", \"onMouseOut\"];\nfunction useMarker(props) {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const map = useMap();\n  const {\n      onClick,\n      onDrag,\n      onDragStart,\n      onDragEnd,\n      onMouseOver,\n      onMouseOut\n    } = props,\n    markerOptions = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    position,\n    draggable\n  } = markerOptions;\n  // create marker instance and add to the map once the map is available\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!map) {\n      if (map === undefined) console.error('<Marker> has to be inside a Map component.');\n      return;\n    }\n    const newMarker = new google.maps.Marker(markerOptions);\n    newMarker.setMap(map);\n    setMarker(newMarker);\n    return () => {\n      newMarker.setMap(null);\n      setMarker(null);\n    };\n    // We do not want to re-render the whole marker when the options change.\n    // Marker options update is handled in a useEffect below.\n    // Excluding markerOptions from dependency array on purpose here.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [map]);\n  // attach and re-attach event-handlers when any of the properties change\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    const m = marker;\n    // Add event listeners\n    const gme = google.maps.event;\n    if (onClick) gme.addListener(m, 'click', onClick);\n    if (onDrag) gme.addListener(m, 'drag', onDrag);\n    if (onDragStart) gme.addListener(m, 'dragstart', onDragStart);\n    if (onDragEnd) gme.addListener(m, 'dragend', onDragEnd);\n    if (onMouseOver) gme.addListener(m, 'mouseover', onMouseOver);\n    if (onMouseOut) gme.addListener(m, 'mouseout', onMouseOut);\n    marker.setDraggable(Boolean(draggable));\n    return () => {\n      gme.clearInstanceListeners(m);\n    };\n  }, [marker, draggable, onClick, onDrag, onDragStart, onDragEnd, onMouseOver, onMouseOut]);\n  // update markerOptions (note the dependencies aren't properly checked\n  // here, we just assume that setOptions is smart enough to not waste a\n  // lot of time updating values that didn't change)\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!marker) return;\n    if (markerOptions) marker.setOptions(markerOptions);\n  }, [marker, markerOptions]);\n  // update position when changed\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Should not update position when draggable\n    if (draggable || !position || !marker) return;\n    marker.setPosition(position);\n  }, [draggable, position, marker]);\n  return marker;\n}\n/**\n * Component to render a marker on a map\n */\nconst Marker = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n  const marker = useMarker(props);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => marker, [marker]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n});\nfunction useMarkerRef() {\n  const [marker, setMarker] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const refCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(m => {\n    setMarker(m);\n  }, []);\n  return [refCallback, marker];\n}\n\n/**\n * Component to configure the appearance of an AdvancedMarker\n */\nconst Pin = props => {\n  var _useContext;\n  const advancedMarker = (_useContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(AdvancedMarkerContext)) == null ? void 0 : _useContext.marker;\n  const glyphContainer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => document.createElement('div'), []);\n  // Create Pin View instance\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    var _advancedMarker$conte;\n    if (!advancedMarker) {\n      if (advancedMarker === undefined) {\n        console.error('The <Pin> component can only be used inside <AdvancedMarker>.');\n      }\n      return;\n    }\n    if (props.glyph && props.children) {\n      logErrorOnce('The <Pin> component only uses children to render the glyph if both the glyph property and children are present.');\n    }\n    if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(props.children) > 1) {\n      logErrorOnce('Passing multiple children to the <Pin> component might lead to unexpected results.');\n    }\n    const pinViewOptions = _extends({}, props);\n    const pinElement = new google.maps.marker.PinElement(pinViewOptions);\n    // Set glyph to glyph container if children are present (rendered via portal).\n    // If both props.glyph and props.children are present, props.children takes priority.\n    if (props.children) {\n      pinElement.glyph = glyphContainer;\n    }\n    // Set content of Advanced Marker View to the Pin View element\n    // Here we are selecting the anchor container.\n    // The hierarchy is as follows:\n    // \"advancedMarker.content\" (from google) -> \"pointer events reset div\" -> \"anchor container\"\n    const markerContent = (_advancedMarker$conte = advancedMarker.content) == null || (_advancedMarker$conte = _advancedMarker$conte.firstChild) == null ? void 0 : _advancedMarker$conte.firstChild;\n    while (markerContent != null && markerContent.firstChild) {\n      markerContent.removeChild(markerContent.firstChild);\n    }\n    if (markerContent) {\n      markerContent.appendChild(pinElement.element);\n    }\n  }, [advancedMarker, glyphContainer, props]);\n  return (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.children, glyphContainer);\n};\n\nconst mapLinear = (x, a1, a2, b1, b2) => b1 + (x - a1) * (b2 - b1) / (a2 - a1);\nconst getMapMaxTilt = zoom => {\n  if (zoom <= 10) {\n    return 30;\n  }\n  if (zoom >= 15.5) {\n    return 67.5;\n  }\n  // range [10...14]\n  if (zoom <= 14) {\n    return mapLinear(zoom, 10, 14, 30, 45);\n  }\n  // range [14...15.5]\n  return mapLinear(zoom, 14, 15.5, 45, 67.5);\n};\n/**\n * Function to limit the tilt range of the Google map when updating the view state\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst limitTiltRange = ({\n  viewState\n}) => {\n  const pitch = viewState.pitch;\n  const gmZoom = viewState.zoom + 1;\n  const maxTilt = getMapMaxTilt(gmZoom);\n  return _extends({}, viewState, {\n    fovy: 25,\n    pitch: Math.min(maxTilt, pitch)\n  });\n};\n\n\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vis.gl/react-google-maps/dist/index.modern.mjs\n");

/***/ })

};
;