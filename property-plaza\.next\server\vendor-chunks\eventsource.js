/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource";
exports.ids = ["vendor-chunks/eventsource"];
exports.modules = {

/***/ "(ssr)/./node_modules/eventsource/lib/eventsource.js":
/*!*****************************************************!*\
  !*** ./node_modules/eventsource/lib/eventsource.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parse = (__webpack_require__(/*! url */ \"url\").parse)\nvar events = __webpack_require__(/*! events */ \"events\")\nvar https = __webpack_require__(/*! https */ \"https\")\nvar http = __webpack_require__(/*! http */ \"http\")\nvar util = __webpack_require__(/*! util */ \"util\")\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/eventsource/lib/eventsource.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/eventsource/lib/eventsource.js":
/*!*****************************************************!*\
  !*** ./node_modules/eventsource/lib/eventsource.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parse = (__webpack_require__(/*! url */ \"url\").parse)\nvar events = __webpack_require__(/*! events */ \"events\")\nvar https = __webpack_require__(/*! https */ \"https\")\nvar http = __webpack_require__(/*! http */ \"http\")\nvar util = __webpack_require__(/*! util */ \"util\")\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/eventsource/lib/eventsource.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/eventsource/lib/eventsource.js":
/*!*****************************************************!*\
  !*** ./node_modules/eventsource/lib/eventsource.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var parse = (__webpack_require__(/*! url */ \"url\").parse)\nvar events = __webpack_require__(/*! events */ \"events\")\nvar https = __webpack_require__(/*! https */ \"https\")\nvar http = __webpack_require__(/*! http */ \"http\")\nvar util = __webpack_require__(/*! util */ \"util\")\n\nvar httpsOptions = [\n  'pfx', 'key', 'passphrase', 'cert', 'ca', 'ciphers',\n  'rejectUnauthorized', 'secureProtocol', 'servername', 'checkServerIdentity'\n]\n\nvar bom = [239, 187, 191]\nvar colon = 58\nvar space = 32\nvar lineFeed = 10\nvar carriageReturn = 13\n// Beyond 256KB we could not observe any gain in performance\nvar maxBufferAheadAllocation = 1024 * 256\n// Headers matching the pattern should be removed when redirecting to different origin\nvar reUnsafeHeader = /^(cookie|authorization)$/i\n\nfunction hasBom (buf) {\n  return bom.every(function (charCode, index) {\n    return buf[index] === charCode\n  })\n}\n\n/**\n * Creates a new EventSource object\n *\n * @param {String} url the URL to which to connect\n * @param {Object} [eventSourceInitDict] extra init params. See README for details.\n * @api public\n **/\nfunction EventSource (url, eventSourceInitDict) {\n  var readyState = EventSource.CONNECTING\n  var headers = eventSourceInitDict && eventSourceInitDict.headers\n  var hasNewOrigin = false\n  Object.defineProperty(this, 'readyState', {\n    get: function () {\n      return readyState\n    }\n  })\n\n  Object.defineProperty(this, 'url', {\n    get: function () {\n      return url\n    }\n  })\n\n  var self = this\n  self.reconnectInterval = 1000\n  self.connectionInProgress = false\n\n  function onConnectionClosed (message) {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CONNECTING\n    _emit('error', new Event('error', {message: message}))\n\n    // The url may have been changed by a temporary redirect. If that's the case,\n    // revert it now, and flag that we are no longer pointing to a new origin\n    if (reconnectUrl) {\n      url = reconnectUrl\n      reconnectUrl = null\n      hasNewOrigin = false\n    }\n    setTimeout(function () {\n      if (readyState !== EventSource.CONNECTING || self.connectionInProgress) {\n        return\n      }\n      self.connectionInProgress = true\n      connect()\n    }, self.reconnectInterval)\n  }\n\n  var req\n  var lastEventId = ''\n  if (headers && headers['Last-Event-ID']) {\n    lastEventId = headers['Last-Event-ID']\n    delete headers['Last-Event-ID']\n  }\n\n  var discardTrailingNewline = false\n  var data = ''\n  var eventName = ''\n\n  var reconnectUrl = null\n\n  function connect () {\n    var options = parse(url)\n    var isSecure = options.protocol === 'https:'\n    options.headers = { 'Cache-Control': 'no-cache', 'Accept': 'text/event-stream' }\n    if (lastEventId) options.headers['Last-Event-ID'] = lastEventId\n    if (headers) {\n      var reqHeaders = hasNewOrigin ? removeUnsafeHeaders(headers) : headers\n      for (var i in reqHeaders) {\n        var header = reqHeaders[i]\n        if (header) {\n          options.headers[i] = header\n        }\n      }\n    }\n\n    // Legacy: this should be specified as `eventSourceInitDict.https.rejectUnauthorized`,\n    // but for now exists as a backwards-compatibility layer\n    options.rejectUnauthorized = !(eventSourceInitDict && !eventSourceInitDict.rejectUnauthorized)\n\n    if (eventSourceInitDict && eventSourceInitDict.createConnection !== undefined) {\n      options.createConnection = eventSourceInitDict.createConnection\n    }\n\n    // If specify http proxy, make the request to sent to the proxy server,\n    // and include the original url in path and Host headers\n    var useProxy = eventSourceInitDict && eventSourceInitDict.proxy\n    if (useProxy) {\n      var proxy = parse(eventSourceInitDict.proxy)\n      isSecure = proxy.protocol === 'https:'\n\n      options.protocol = isSecure ? 'https:' : 'http:'\n      options.path = url\n      options.headers.Host = options.host\n      options.hostname = proxy.hostname\n      options.host = proxy.host\n      options.port = proxy.port\n    }\n\n    // If https options are specified, merge them into the request options\n    if (eventSourceInitDict && eventSourceInitDict.https) {\n      for (var optName in eventSourceInitDict.https) {\n        if (httpsOptions.indexOf(optName) === -1) {\n          continue\n        }\n\n        var option = eventSourceInitDict.https[optName]\n        if (option !== undefined) {\n          options[optName] = option\n        }\n      }\n    }\n\n    // Pass this on to the XHR\n    if (eventSourceInitDict && eventSourceInitDict.withCredentials !== undefined) {\n      options.withCredentials = eventSourceInitDict.withCredentials\n    }\n\n    req = (isSecure ? https : http).request(options, function (res) {\n      self.connectionInProgress = false\n      // Handle HTTP errors\n      if (res.statusCode === 500 || res.statusCode === 502 || res.statusCode === 503 || res.statusCode === 504) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        onConnectionClosed()\n        return\n      }\n\n      // Handle HTTP redirects\n      if (res.statusCode === 301 || res.statusCode === 302 || res.statusCode === 307) {\n        var location = res.headers.location\n        if (!location) {\n          // Server sent redirect response without Location header.\n          _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n          return\n        }\n        var prevOrigin = new URL(url).origin\n        var nextOrigin = new URL(location).origin\n        hasNewOrigin = prevOrigin !== nextOrigin\n        if (res.statusCode === 307) reconnectUrl = url\n        url = location\n        process.nextTick(connect)\n        return\n      }\n\n      if (res.statusCode !== 200) {\n        _emit('error', new Event('error', {status: res.statusCode, message: res.statusMessage}))\n        return self.close()\n      }\n\n      readyState = EventSource.OPEN\n      res.on('close', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n\n      res.on('end', function () {\n        res.removeAllListeners('close')\n        res.removeAllListeners('end')\n        onConnectionClosed()\n      })\n      _emit('open', new Event('open'))\n\n      // text/event-stream parser adapted from webkit's\n      // Source/WebCore/page/EventSource.cpp\n      var buf\n      var newBuffer\n      var startingPos = 0\n      var startingFieldLength = -1\n      var newBufferSize = 0\n      var bytesUsed = 0\n\n      res.on('data', function (chunk) {\n        if (!buf) {\n          buf = chunk\n          if (hasBom(buf)) {\n            buf = buf.slice(bom.length)\n          }\n          bytesUsed = buf.length\n        } else {\n          if (chunk.length > buf.length - bytesUsed) {\n            newBufferSize = (buf.length * 2) + chunk.length\n            if (newBufferSize > maxBufferAheadAllocation) {\n              newBufferSize = buf.length + chunk.length + maxBufferAheadAllocation\n            }\n            newBuffer = Buffer.alloc(newBufferSize)\n            buf.copy(newBuffer, 0, 0, bytesUsed)\n            buf = newBuffer\n          }\n          chunk.copy(buf, bytesUsed)\n          bytesUsed += chunk.length\n        }\n\n        var pos = 0\n        var length = bytesUsed\n\n        while (pos < length) {\n          if (discardTrailingNewline) {\n            if (buf[pos] === lineFeed) {\n              ++pos\n            }\n            discardTrailingNewline = false\n          }\n\n          var lineLength = -1\n          var fieldLength = startingFieldLength\n          var c\n\n          for (var i = startingPos; lineLength < 0 && i < length; ++i) {\n            c = buf[i]\n            if (c === colon) {\n              if (fieldLength < 0) {\n                fieldLength = i - pos\n              }\n            } else if (c === carriageReturn) {\n              discardTrailingNewline = true\n              lineLength = i - pos\n            } else if (c === lineFeed) {\n              lineLength = i - pos\n            }\n          }\n\n          if (lineLength < 0) {\n            startingPos = length - pos\n            startingFieldLength = fieldLength\n            break\n          } else {\n            startingPos = 0\n            startingFieldLength = -1\n          }\n\n          parseEventStreamLine(buf, pos, fieldLength, lineLength)\n\n          pos += lineLength + 1\n        }\n\n        if (pos === length) {\n          buf = void 0\n          bytesUsed = 0\n        } else if (pos > 0) {\n          buf = buf.slice(pos, bytesUsed)\n          bytesUsed = buf.length\n        }\n      })\n    })\n\n    req.on('error', function (err) {\n      self.connectionInProgress = false\n      onConnectionClosed(err.message)\n    })\n\n    if (req.setNoDelay) req.setNoDelay(true)\n    req.end()\n  }\n\n  connect()\n\n  function _emit () {\n    if (self.listeners(arguments[0]).length > 0) {\n      self.emit.apply(self, arguments)\n    }\n  }\n\n  this._close = function () {\n    if (readyState === EventSource.CLOSED) return\n    readyState = EventSource.CLOSED\n    if (req.abort) req.abort()\n    if (req.xhr && req.xhr.abort) req.xhr.abort()\n  }\n\n  function parseEventStreamLine (buf, pos, fieldLength, lineLength) {\n    if (lineLength === 0) {\n      if (data.length > 0) {\n        var type = eventName || 'message'\n        _emit(type, new MessageEvent(type, {\n          data: data.slice(0, -1), // remove trailing newline\n          lastEventId: lastEventId,\n          origin: new URL(url).origin\n        }))\n        data = ''\n      }\n      eventName = void 0\n    } else if (fieldLength > 0) {\n      var noValue = fieldLength < 0\n      var step = 0\n      var field = buf.slice(pos, pos + (noValue ? lineLength : fieldLength)).toString()\n\n      if (noValue) {\n        step = lineLength\n      } else if (buf[pos + fieldLength + 1] !== space) {\n        step = fieldLength + 1\n      } else {\n        step = fieldLength + 2\n      }\n      pos += step\n\n      var valueLength = lineLength - step\n      var value = buf.slice(pos, pos + valueLength).toString()\n\n      if (field === 'data') {\n        data += value + '\\n'\n      } else if (field === 'event') {\n        eventName = value\n      } else if (field === 'id') {\n        lastEventId = value\n      } else if (field === 'retry') {\n        var retry = parseInt(value, 10)\n        if (!Number.isNaN(retry)) {\n          self.reconnectInterval = retry\n        }\n      }\n    }\n  }\n}\n\nmodule.exports = EventSource\n\nutil.inherits(EventSource, events.EventEmitter)\nEventSource.prototype.constructor = EventSource; // make stacktraces readable\n\n['open', 'error', 'message'].forEach(function (method) {\n  Object.defineProperty(EventSource.prototype, 'on' + method, {\n    /**\n     * Returns the current listener\n     *\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    get: function get () {\n      var listener = this.listeners(method)[0]\n      return listener ? (listener._listener ? listener._listener : listener) : undefined\n    },\n\n    /**\n     * Start listening for events\n     *\n     * @param {Function} listener the listener\n     * @return {Mixed} the set function or undefined\n     * @api private\n     */\n    set: function set (listener) {\n      this.removeAllListeners(method)\n      this.addEventListener(method, listener)\n    }\n  })\n})\n\n/**\n * Ready states\n */\nObject.defineProperty(EventSource, 'CONNECTING', {enumerable: true, value: 0})\nObject.defineProperty(EventSource, 'OPEN', {enumerable: true, value: 1})\nObject.defineProperty(EventSource, 'CLOSED', {enumerable: true, value: 2})\n\nEventSource.prototype.CONNECTING = 0\nEventSource.prototype.OPEN = 1\nEventSource.prototype.CLOSED = 2\n\n/**\n * Closes the connection, if one is made, and sets the readyState attribute to 2 (closed)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventSource/close\n * @api public\n */\nEventSource.prototype.close = function () {\n  this._close()\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using addEventListener.\n *\n * @param {String} type A string representing the event type to listen out for\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.addEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.addEventListener = function addEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    // store a reference so we can return the original function again\n    listener._listener = listener\n    this.on(type, listener)\n  }\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using dispatchEvent.\n *\n * @param {Event} event An event to be dispatched\n * @see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/dispatchEvent\n * @api public\n */\nEventSource.prototype.dispatchEvent = function dispatchEvent (event) {\n  if (!event.type) {\n    throw new Error('UNSPECIFIED_EVENT_TYPE_ERR')\n  }\n  // if event is instance of an CustomEvent (or has 'details' property),\n  // send the detail object as the payload for the event\n  this.emit(event.type, event.detail)\n}\n\n/**\n * Emulates the W3C Browser based WebSocket interface using removeEventListener.\n *\n * @param {String} type A string representing the event type to remove\n * @param {Function} listener callback\n * @see https://developer.mozilla.org/en/DOM/element.removeEventListener\n * @see http://dev.w3.org/html5/websockets/#the-websocket-interface\n * @api public\n */\nEventSource.prototype.removeEventListener = function removeEventListener (type, listener) {\n  if (typeof listener === 'function') {\n    listener._listener = undefined\n    this.removeListener(type, listener)\n  }\n}\n\n/**\n * W3C Event\n *\n * @see http://www.w3.org/TR/DOM-Level-3-Events/#interface-Event\n * @api private\n */\nfunction Event (type, optionalProperties) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  if (optionalProperties) {\n    for (var f in optionalProperties) {\n      if (optionalProperties.hasOwnProperty(f)) {\n        Object.defineProperty(this, f, { writable: false, value: optionalProperties[f], enumerable: true })\n      }\n    }\n  }\n}\n\n/**\n * W3C MessageEvent\n *\n * @see http://www.w3.org/TR/webmessaging/#event-definitions\n * @api private\n */\nfunction MessageEvent (type, eventInitDict) {\n  Object.defineProperty(this, 'type', { writable: false, value: type, enumerable: true })\n  for (var f in eventInitDict) {\n    if (eventInitDict.hasOwnProperty(f)) {\n      Object.defineProperty(this, f, { writable: false, value: eventInitDict[f], enumerable: true })\n    }\n  }\n}\n\n/**\n * Returns a new object of headers that does not include any authorization and cookie headers\n *\n * @param {Object} headers An object of headers ({[headerName]: headerValue})\n * @return {Object} a new object of headers\n * @api private\n */\nfunction removeUnsafeHeaders (headers) {\n  var safe = {}\n  for (var key in headers) {\n    if (reUnsafeHeader.test(key)) {\n      continue\n    }\n\n    safe[key] = headers[key]\n  }\n\n  return safe\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource/lib/eventsource.js\n");

/***/ })

};
;