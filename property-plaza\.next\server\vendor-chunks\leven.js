"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/leven";
exports.ids = ["vendor-chunks/leven"];
exports.modules = {

/***/ "(rsc)/./node_modules/leven/index.js":
/*!*************************************!*\
  !*** ./node_modules/leven/index.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leven)\n/* harmony export */ });\nconst array = [];\nconst characterCodeCache = [];\n\nfunction leven(first, second) {\n\tif (first === second) {\n\t\treturn 0;\n\t}\n\n\tconst swap = first;\n\n\t// Swapping the strings if `a` is longer than `b` so we know which one is the\n\t// shortest & which one is the longest\n\tif (first.length > second.length) {\n\t\tfirst = second;\n\t\tsecond = swap;\n\t}\n\n\tlet firstLength = first.length;\n\tlet secondLength = second.length;\n\n\t// Performing suffix trimming:\n\t// We can linearly drop suffix common to both strings since they\n\t// don't increase distance at all\n\t// Note: `~-` is the bitwise way to perform a `- 1` operation\n\twhile (firstLength > 0 && (first.charCodeAt(~-firstLength) === second.charCodeAt(~-secondLength))) {\n\t\tfirstLength--;\n\t\tsecondLength--;\n\t}\n\n\t// Performing prefix trimming\n\t// We can linearly drop prefix common to both strings since they\n\t// don't increase distance at all\n\tlet start = 0;\n\n\twhile (start < firstLength && (first.charCodeAt(start) === second.charCodeAt(start))) {\n\t\tstart++;\n\t}\n\n\tfirstLength -= start;\n\tsecondLength -= start;\n\n\tif (firstLength === 0) {\n\t\treturn secondLength;\n\t}\n\n\tlet bCharacterCode;\n\tlet result;\n\tlet temporary;\n\tlet temporary2;\n\tlet index = 0;\n\tlet index2 = 0;\n\n\twhile (index < firstLength) {\n\t\tcharacterCodeCache[index] = first.charCodeAt(start + index);\n\t\tarray[index] = ++index;\n\t}\n\n\twhile (index2 < secondLength) {\n\t\tbCharacterCode = second.charCodeAt(start + index2);\n\t\ttemporary = index2++;\n\t\tresult = index2;\n\n\t\tfor (index = 0; index < firstLength; index++) {\n\t\t\ttemporary2 = bCharacterCode === characterCodeCache[index] ? temporary : temporary + 1;\n\t\t\ttemporary = array[index];\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tresult = array[index] = temporary > result ? (temporary2 > result ? result + 1 : temporary2) : (temporary2 > temporary ? temporary + 1 : temporary2);\n\t\t}\n\t}\n\n\treturn result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/leven/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/leven/index.js":
/*!*************************************!*\
  !*** ./node_modules/leven/index.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ leven)\n/* harmony export */ });\nconst array = [];\nconst characterCodeCache = [];\n\nfunction leven(first, second) {\n\tif (first === second) {\n\t\treturn 0;\n\t}\n\n\tconst swap = first;\n\n\t// Swapping the strings if `a` is longer than `b` so we know which one is the\n\t// shortest & which one is the longest\n\tif (first.length > second.length) {\n\t\tfirst = second;\n\t\tsecond = swap;\n\t}\n\n\tlet firstLength = first.length;\n\tlet secondLength = second.length;\n\n\t// Performing suffix trimming:\n\t// We can linearly drop suffix common to both strings since they\n\t// don't increase distance at all\n\t// Note: `~-` is the bitwise way to perform a `- 1` operation\n\twhile (firstLength > 0 && (first.charCodeAt(~-firstLength) === second.charCodeAt(~-secondLength))) {\n\t\tfirstLength--;\n\t\tsecondLength--;\n\t}\n\n\t// Performing prefix trimming\n\t// We can linearly drop prefix common to both strings since they\n\t// don't increase distance at all\n\tlet start = 0;\n\n\twhile (start < firstLength && (first.charCodeAt(start) === second.charCodeAt(start))) {\n\t\tstart++;\n\t}\n\n\tfirstLength -= start;\n\tsecondLength -= start;\n\n\tif (firstLength === 0) {\n\t\treturn secondLength;\n\t}\n\n\tlet bCharacterCode;\n\tlet result;\n\tlet temporary;\n\tlet temporary2;\n\tlet index = 0;\n\tlet index2 = 0;\n\n\twhile (index < firstLength) {\n\t\tcharacterCodeCache[index] = first.charCodeAt(start + index);\n\t\tarray[index] = ++index;\n\t}\n\n\twhile (index2 < secondLength) {\n\t\tbCharacterCode = second.charCodeAt(start + index2);\n\t\ttemporary = index2++;\n\t\tresult = index2;\n\n\t\tfor (index = 0; index < firstLength; index++) {\n\t\t\ttemporary2 = bCharacterCode === characterCodeCache[index] ? temporary : temporary + 1;\n\t\t\ttemporary = array[index];\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tresult = array[index] = temporary > result ? (temporary2 > result ? result + 1 : temporary2) : (temporary2 > temporary ? temporary + 1 : temporary2);\n\t\t}\n\t}\n\n\treturn result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/leven/index.js\n");

/***/ })

};
;