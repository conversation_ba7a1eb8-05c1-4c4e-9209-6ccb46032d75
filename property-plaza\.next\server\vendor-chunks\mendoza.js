"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mendoza";
exports.ids = ["vendor-chunks/mendoza"];
exports.modules = {

/***/ "(ssr)/./node_modules/mendoza/dist/index.js":
/*!********************************************!*\
  !*** ./node_modules/mendoza/dist/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyPatch: () => (/* binding */ applyPatch),\n/* harmony export */   incremental: () => (/* binding */ incrementalPatcher)\n/* harmony export */ });\nconst OPS = [\n  \"Value\",\n  \"Copy\",\n  \"Blank\",\n  \"ReturnIntoArray\",\n  \"ReturnIntoObject\",\n  \"ReturnIntoObjectSameKey\",\n  \"PushField\",\n  \"PushElement\",\n  \"PushParent\",\n  \"Pop\",\n  \"PushFieldCopy\",\n  \"PushFieldBlank\",\n  \"PushElementCopy\",\n  \"PushElementBlank\",\n  \"ReturnIntoObjectPop\",\n  \"ReturnIntoObjectSameKeyPop\",\n  \"ReturnIntoArrayPop\",\n  \"ObjectSetFieldValue\",\n  \"ObjectCopyField\",\n  \"ObjectDeleteField\",\n  \"ArrayAppendValue\",\n  \"ArrayAppendSlice\",\n  \"StringAppendString\",\n  \"StringAppendSlice\"\n];\nclass Patcher {\n  model;\n  root;\n  patch;\n  i;\n  inputStack;\n  outputStack;\n  constructor(model, root, patch) {\n    this.model = model, this.root = root, this.patch = patch, this.i = 0, this.inputStack = [], this.outputStack = [];\n  }\n  read() {\n    return this.patch[this.i++];\n  }\n  process() {\n    for (this.inputStack.push({ value: this.root }), this.outputStack.push({ value: this.root }); this.i < this.patch.length; ) {\n      let opcode = this.read(), op = OPS[opcode];\n      if (!op)\n        throw new Error(`Unknown opcode: ${opcode}`);\n      let processor = `process${op}`;\n      this[processor].apply(this);\n    }\n    let entry = this.outputStack.pop();\n    return this.finalizeOutput(entry);\n  }\n  inputEntry() {\n    return this.inputStack[this.inputStack.length - 1];\n  }\n  inputKey(entry, idx) {\n    return entry.keys || (entry.keys = this.model.objectGetKeys(entry.value).sort()), entry.keys[idx];\n  }\n  outputEntry() {\n    return this.outputStack[this.outputStack.length - 1];\n  }\n  outputArray() {\n    let entry = this.outputEntry();\n    return entry.writeValue || (entry.writeValue = this.model.copyArray(entry.value)), entry.writeValue;\n  }\n  outputObject() {\n    let entry = this.outputEntry();\n    return entry.writeValue || (entry.writeValue = this.model.copyObject(entry.value)), entry.writeValue;\n  }\n  outputString() {\n    let entry = this.outputEntry();\n    return entry.writeValue || (entry.writeValue = this.model.copyString(entry.value)), entry.writeValue;\n  }\n  finalizeOutput(entry) {\n    return entry.writeValue ? this.model.finalize(entry.writeValue) : entry.value;\n  }\n  // Processors:\n  processValue() {\n    let value = this.model.wrap(this.read());\n    this.outputStack.push({ value });\n  }\n  processCopy() {\n    let input = this.inputEntry();\n    this.outputStack.push({ value: input.value });\n  }\n  processBlank() {\n    this.outputStack.push({ value: null });\n  }\n  processReturnIntoArray() {\n    let entry = this.outputStack.pop(), result = this.finalizeOutput(entry), arr = this.outputArray();\n    this.model.arrayAppendValue(arr, result);\n  }\n  processReturnIntoObject() {\n    let key = this.read(), entry = this.outputStack.pop(), result = this.finalizeOutput(entry);\n    result = this.model.markChanged(result);\n    let obj = this.outputObject();\n    this.model.objectSetField(obj, key, result);\n  }\n  processReturnIntoObjectSameKey() {\n    let input = this.inputEntry(), entry = this.outputStack.pop(), result = this.finalizeOutput(entry), obj = this.outputObject();\n    this.model.objectSetField(obj, input.key, result);\n  }\n  processPushField() {\n    let idx = this.read(), entry = this.inputEntry(), key = this.inputKey(entry, idx), value = this.model.objectGetField(entry.value, key);\n    this.inputStack.push({ value, key });\n  }\n  processPushElement() {\n    let idx = this.read(), entry = this.inputEntry(), value = this.model.arrayGetElement(entry.value, idx);\n    this.inputStack.push({ value });\n  }\n  processPop() {\n    this.inputStack.pop();\n  }\n  processPushFieldCopy() {\n    this.processPushField(), this.processCopy();\n  }\n  processPushFieldBlank() {\n    this.processPushField(), this.processBlank();\n  }\n  processPushElementCopy() {\n    this.processPushElement(), this.processCopy();\n  }\n  processPushElementBlank() {\n    this.processPushElement(), this.processBlank();\n  }\n  processReturnIntoObjectPop() {\n    this.processReturnIntoObject(), this.processPop();\n  }\n  processReturnIntoObjectSameKeyPop() {\n    this.processReturnIntoObjectSameKey(), this.processPop();\n  }\n  processReturnIntoArrayPop() {\n    this.processReturnIntoArray(), this.processPop();\n  }\n  processObjectSetFieldValue() {\n    this.processValue(), this.processReturnIntoObject();\n  }\n  processObjectCopyField() {\n    this.processPushField(), this.processCopy(), this.processReturnIntoObjectSameKey(), this.processPop();\n  }\n  processObjectDeleteField() {\n    let idx = this.read(), entry = this.inputEntry(), key = this.inputKey(entry, idx), obj = this.outputObject();\n    this.model.objectDeleteField(obj, key);\n  }\n  processArrayAppendValue() {\n    let value = this.model.wrap(this.read()), arr = this.outputArray();\n    this.model.arrayAppendValue(arr, value);\n  }\n  processArrayAppendSlice() {\n    let left = this.read(), right = this.read(), str = this.outputArray(), val = this.inputEntry().value;\n    this.model.arrayAppendSlice(str, val, left, right);\n  }\n  processStringAppendString() {\n    let value = this.model.wrap(this.read()), str = this.outputString();\n    this.model.stringAppendValue(str, value);\n  }\n  processStringAppendSlice() {\n    let left = this.read(), right = this.read(), str = this.outputString(), val = this.inputEntry().value;\n    this.model.stringAppendSlice(str, val, left, right);\n  }\n}\nfunction utf8charSize(code) {\n  return code >> 16 ? 4 : code >> 11 ? 3 : code >> 7 ? 2 : 1;\n}\nfunction utf8stringSize(str) {\n  let b = 0;\n  for (let i = 0; i < str.length; i++) {\n    let code = str.codePointAt(i), size = utf8charSize(code);\n    size == 4 && i++, b += size;\n  }\n  return b;\n}\nfunction utf8resolveIndex(str, idx, start = 0) {\n  let byteCount = start, ucsIdx = 0;\n  for (ucsIdx = start; byteCount < idx; ucsIdx++) {\n    let code = str.codePointAt(ucsIdx), size = utf8charSize(code);\n    size === 4 && ucsIdx++, byteCount += size;\n  }\n  return ucsIdx;\n}\nfunction commonPrefix(str, str2) {\n  let len = Math.min(str.length, str2.length), b = 0;\n  for (let i = 0; i < len; ) {\n    let aPoint = str.codePointAt(i), bPoint = str2.codePointAt(i);\n    if (aPoint !== bPoint)\n      return b;\n    let size = utf8charSize(aPoint);\n    b += size, i += size === 4 ? 2 : 1;\n  }\n  return b;\n}\nfunction commonSuffix(str, str2, prefix = 0) {\n  let len = Math.min(str.length, str2.length) - prefix, b = 0;\n  for (let i = 0; i < len; ) {\n    let aPoint = str.codePointAt(str.length - 1 - i), bPoint = str2.codePointAt(str2.length - 1 - i);\n    if (aPoint !== bPoint)\n      return b;\n    let size = utf8charSize(aPoint);\n    b += size, i += size === 4 ? 2 : 1;\n  }\n  return b;\n}\nclass IncrementalModel {\n  meta;\n  constructor(meta) {\n    this.meta = meta;\n  }\n  wrap(data) {\n    return this.wrapWithMeta(data, this.meta, this.meta);\n  }\n  wrapWithMeta(data, startMeta, endMeta = this.meta) {\n    return { data, startMeta, endMeta };\n  }\n  asObject(value) {\n    if (!value.content) {\n      let fields = {};\n      for (let [key, val] of Object.entries(value.data))\n        fields[key] = this.wrapWithMeta(val, value.startMeta);\n      value.content = { type: \"object\", fields };\n    }\n    return value.content;\n  }\n  asArray(value) {\n    if (!value.content) {\n      let elements = value.data.map(\n        (item) => this.wrapWithMeta(item, value.startMeta)\n      ), metas = elements.map(() => this.meta);\n      value.content = { type: \"array\", elements, metas };\n    }\n    return value.content;\n  }\n  asString(value) {\n    if (!value.content) {\n      let str = value.data, part = {\n        value: str,\n        utf8size: utf8stringSize(str),\n        uses: [],\n        startMeta: value.startMeta,\n        endMeta: value.endMeta\n      };\n      value.content = this.stringFromParts([part]);\n    }\n    return value.content;\n  }\n  stringFromParts(parts) {\n    let str = {\n      type: \"string\",\n      parts\n    };\n    for (let part of parts)\n      part.uses.push(str);\n    return str;\n  }\n  objectGetKeys(value) {\n    return value.content ? Object.keys(value.content.fields) : Object.keys(value.data);\n  }\n  objectGetField(value, key) {\n    return this.asObject(value).fields[key];\n  }\n  arrayGetElement(value, idx) {\n    return this.asArray(value).elements[idx];\n  }\n  finalize(content) {\n    return this.updateEndMeta(content), { content, startMeta: this.meta, endMeta: this.meta };\n  }\n  markChanged(value) {\n    return this.wrap(unwrap(value));\n  }\n  updateEndMeta(content) {\n    if (content.type == \"string\")\n      for (let part of content.parts)\n        part.endMeta = this.meta;\n    else if (content.type === \"array\")\n      for (let val of content.elements)\n        val.content && val.endMeta !== this.meta && this.updateEndMeta(val.content), val.endMeta = this.meta;\n    else\n      for (let val of Object.values(content.fields))\n        val.content && val.endMeta !== this.meta && this.updateEndMeta(val.content), val.endMeta = this.meta;\n  }\n  copyString(value) {\n    if (value) {\n      let other = this.asString(value);\n      return this.stringFromParts(other.parts.slice());\n    } else\n      return {\n        type: \"string\",\n        parts: []\n      };\n  }\n  copyObject(value) {\n    let obj = {\n      type: \"object\",\n      fields: {}\n    };\n    if (value) {\n      let other = this.asObject(value);\n      Object.assign(obj.fields, other.fields);\n    }\n    return obj;\n  }\n  copyArray(value) {\n    let arr = value ? this.asArray(value) : null, elements = arr ? arr.elements : [], metas = arr ? arr.metas : [];\n    return {\n      type: \"array\",\n      elements,\n      metas\n    };\n  }\n  objectSetField(target, key, value) {\n    target.fields[key] = value;\n  }\n  objectDeleteField(target, key) {\n    delete target.fields[key];\n  }\n  arrayAppendValue(target, value) {\n    target.elements.push(value), target.metas.push(this.meta);\n  }\n  arrayAppendSlice(target, source, left, right) {\n    let arr = this.asArray(source), samePosition = arr.elements.length === left;\n    if (target.elements.push(...arr.elements.slice(left, right)), samePosition)\n      target.metas.push(...arr.metas.slice(left, right));\n    else\n      for (let i = left; i < right; i++)\n        target.metas.push(this.meta);\n  }\n  stringAppendValue(target, value) {\n    let str = this.asString(value);\n    for (let part of str.parts)\n      this.stringAppendPart(target, part);\n  }\n  stringAppendPart(target, part) {\n    target.parts.push(part), part.uses.push(target);\n  }\n  resolveStringPart(str, from, len) {\n    if (len === 0)\n      return from;\n    for (let i = from; i < str.parts.length; i++) {\n      let part = str.parts[i];\n      if (len === part.utf8size)\n        return i + 1;\n      if (len < part.utf8size)\n        return this.splitString(part, len), i + 1;\n      len -= part.utf8size;\n    }\n    throw new Error(\"splitting string out of bounds\");\n  }\n  splitString(part, idx) {\n    let leftValue, rightValue, leftSize = idx, rightSize = part.utf8size - leftSize;\n    if (part.utf8size !== part.value.length) {\n      let byteCount = 0;\n      for (idx = 0; byteCount < leftSize; idx++) {\n        let code = part.value.codePointAt(idx), size = utf8charSize(code);\n        size === 4 && idx++, byteCount += size;\n      }\n    }\n    leftValue = part.value.slice(0, idx), rightValue = part.value.slice(idx);\n    let newPart = {\n      value: rightValue,\n      utf8size: rightSize,\n      uses: part.uses.slice(),\n      startMeta: part.startMeta,\n      endMeta: part.endMeta\n    };\n    part.value = leftValue, part.utf8size = leftSize;\n    for (let use of part.uses) {\n      let ndx = use.parts.indexOf(part);\n      if (ndx === -1)\n        throw new Error(\"bug: mismatch between string parts and use.\");\n      use.parts.splice(ndx + 1, 0, newPart);\n    }\n  }\n  stringAppendSlice(target, source, left, right) {\n    let str = this.asString(source), firstPart = this.resolveStringPart(str, 0, left), lastPart = this.resolveStringPart(str, firstPart, right - left);\n    for (let i = firstPart; i < lastPart; i++) {\n      let part = str.parts[i];\n      this.stringAppendPart(target, part);\n    }\n  }\n}\nfunction wrap(data, meta) {\n  return { data, startMeta: meta, endMeta: meta };\n}\nfunction unwrap(value) {\n  if (typeof value.data < \"u\")\n    return value.data;\n  let result, content = value.content;\n  switch (content.type) {\n    case \"string\":\n      result = content.parts.map((part) => part.value).join(\"\");\n      break;\n    case \"array\":\n      result = content.elements.map((val) => unwrap(val));\n      break;\n    case \"object\": {\n      result = {};\n      for (let [key, val] of Object.entries(content.fields))\n        result[key] = unwrap(val);\n    }\n  }\n  return value.data = result, result;\n}\nfunction getType(value) {\n  return value.content ? value.content.type : Array.isArray(value.data) ? \"array\" : value.data === null ? \"null\" : typeof value.data;\n}\nfunction rebaseValue(left, right) {\n  let leftType = getType(left), rightType = getType(right);\n  if (leftType !== rightType)\n    return right;\n  let leftModel = new IncrementalModel(left.endMeta), rightModel = new IncrementalModel(right.endMeta);\n  switch (leftType) {\n    case \"object\": {\n      let leftObj = leftModel.asObject(left), rightObj = rightModel.asObject(right), identicalFieldCount = 0, leftFieldCount = Object.keys(leftObj.fields).length, rightFieldCount = Object.keys(rightObj.fields).length;\n      for (let [key, rightVal] of Object.entries(rightObj.fields)) {\n        let leftVal = leftObj.fields[key];\n        leftVal && (rightObj.fields[key] = rebaseValue(leftVal, rightVal), rightObj.fields[key] === leftVal && identicalFieldCount++);\n      }\n      return leftFieldCount === rightFieldCount && leftFieldCount === identicalFieldCount ? left : right;\n    }\n    case \"array\": {\n      let leftArr = leftModel.asArray(left), rightArr = rightModel.asArray(right);\n      if (leftArr.elements.length !== rightArr.elements.length)\n        break;\n      let numRebased = 0;\n      for (let i = 0; i < rightArr.elements.length; i++)\n        rightArr.elements[i] = rebaseValue(leftArr.elements[i], rightArr.elements[i]), rightArr.elements[i] !== leftArr.elements[i] && numRebased++;\n      return numRebased === 0 ? left : right;\n    }\n    case \"null\":\n    case \"boolean\":\n    case \"number\": {\n      if (unwrap(left) === unwrap(right))\n        return left;\n      break;\n    }\n    case \"string\": {\n      let leftRaw = unwrap(left), rightRaw = unwrap(right);\n      if (leftRaw === rightRaw)\n        return left;\n      let result = rightModel.copyString(null), prefix = commonPrefix(leftRaw, rightRaw), suffix = commonSuffix(leftRaw, rightRaw, prefix), rightLen = utf8stringSize(rightRaw), leftLen = utf8stringSize(leftRaw);\n      0 < prefix && rightModel.stringAppendSlice(result, left, 0, prefix), prefix < rightLen - suffix && rightModel.stringAppendSlice(result, right, prefix, rightLen - suffix), leftLen - suffix < leftLen && rightModel.stringAppendSlice(result, left, leftLen - suffix, leftLen);\n      let value = rightModel.finalize(result);\n      if (unwrap(value) !== rightRaw)\n        throw new Error(\"incorrect string rebase\");\n      return value;\n    }\n  }\n  return right;\n}\nfunction applyPatch$1(left, patch, startMeta) {\n  let model = new IncrementalModel(startMeta);\n  return new Patcher(model, left, patch).process();\n}\nvar incrementalPatcher = /* @__PURE__ */ Object.freeze({\n  __proto__: null,\n  applyPatch: applyPatch$1,\n  getType,\n  rebaseValue,\n  unwrap,\n  wrap\n});\nclass SimpleModel {\n  wrap(data) {\n    return data;\n  }\n  finalize(b) {\n    return Array.isArray(b) ? b : b.data;\n  }\n  markChanged(value) {\n    return value;\n  }\n  objectGetKeys(value) {\n    return Object.keys(value);\n  }\n  objectGetField(value, key) {\n    return value[key];\n  }\n  arrayGetElement(value, idx) {\n    return value[idx];\n  }\n  copyObject(value) {\n    let res = {\n      type: \"object\",\n      data: {}\n    };\n    if (value !== null)\n      for (let [key, val] of Object.entries(value))\n        res.data[key] = val;\n    return res;\n  }\n  copyArray(value) {\n    return value === null ? [] : value.slice();\n  }\n  copyString(value) {\n    return {\n      type: \"string\",\n      data: value === null ? \"\" : value\n    };\n  }\n  objectSetField(target, key, value) {\n    target.data[key] = value;\n  }\n  objectDeleteField(target, key) {\n    delete target.data[key];\n  }\n  arrayAppendValue(target, value) {\n    target.push(value);\n  }\n  arrayAppendSlice(target, source, left, right) {\n    target.push(...source.slice(left, right));\n  }\n  stringAppendSlice(target, source, left, right) {\n    const sourceString = source, leftPos = utf8resolveIndex(sourceString, left), rightPos = utf8resolveIndex(sourceString, right, leftPos);\n    target.data += sourceString.slice(leftPos, rightPos);\n  }\n  stringAppendValue(target, value) {\n    target.data += value;\n  }\n}\nfunction applyPatch(left, patch) {\n  let root = left;\n  return new Patcher(new SimpleModel(), root, patch).process();\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mendoza/dist/index.js\n");

/***/ })

};
;