"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mimic-response";
exports.ids = ["vendor-chunks/mimic-response"];
exports.modules = {

/***/ "(ssr)/./node_modules/mimic-response/index.js":
/*!**********************************************!*\
  !*** ./node_modules/mimic-response/index.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\n// We define these manually to ensure they're always copied\n// even if they would move up the prototype chain\n// https://nodejs.org/api/http.html#http_class_http_incomingmessage\nconst knownProperties = [\n\t'aborted',\n\t'complete',\n\t'headers',\n\t'httpVersion',\n\t'httpVersionMinor',\n\t'httpVersionMajor',\n\t'method',\n\t'rawHeaders',\n\t'rawTrailers',\n\t'setTimeout',\n\t'socket',\n\t'statusCode',\n\t'statusMessage',\n\t'trailers',\n\t'url'\n];\n\nmodule.exports = (fromStream, toStream) => {\n\tif (toStream._readableState.autoDestroy) {\n\t\tthrow new Error('The second stream must have the `autoDestroy` option set to `false`');\n\t}\n\n\tconst fromProperties = new Set(Object.keys(fromStream).concat(knownProperties));\n\n\tconst properties = {};\n\n\tfor (const property of fromProperties) {\n\t\t// Don't overwrite existing properties.\n\t\tif (property in toStream) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tproperties[property] = {\n\t\t\tget() {\n\t\t\t\tconst value = fromStream[property];\n\t\t\t\tconst isFunction = typeof value === 'function';\n\n\t\t\t\treturn isFunction ? value.bind(fromStream) : value;\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tfromStream[property] = value;\n\t\t\t},\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false\n\t\t};\n\t}\n\n\tObject.defineProperties(toStream, properties);\n\n\tfromStream.once('aborted', () => {\n\t\ttoStream.destroy();\n\n\t\ttoStream.emit('aborted');\n\t});\n\n\tfromStream.once('close', () => {\n\t\tif (fromStream.complete) {\n\t\t\tif (toStream.readable) {\n\t\t\t\ttoStream.once('end', () => {\n\t\t\t\t\ttoStream.emit('close');\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\ttoStream.emit('close');\n\t\t\t}\n\t\t} else {\n\t\t\ttoStream.emit('close');\n\t\t}\n\t});\n\n\treturn toStream;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mimic-response/index.js\n");

/***/ })

};
;