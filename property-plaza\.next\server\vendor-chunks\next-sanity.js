"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-sanity";
exports.ids = ["vendor-chunks/next-sanity"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-sanity/dist/_chunks-es/VisualEditing.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next-sanity/dist/_chunks-es/VisualEditing.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisualEditing)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _sanity_visual_editing_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @sanity/visual-editing/react */ \"(ssr)/./node_modules/@sanity/visual-editing/dist/react/index.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_sanity_visual_editing_server_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-sanity/visual-editing/server-actions */ \"(ssr)/./node_modules/next-sanity/dist/visual-editing/server-actions.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\nfunction pathHasPrefix(path, prefix) {\n  if (typeof path != \"string\")\n    return !1;\n  const { pathname } = parsePath(path);\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction parsePath(path) {\n  const hashIndex = path.indexOf(\"#\"), queryIndex = path.indexOf(\"?\"), hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex);\n  return hasQuery || hashIndex > -1 ? {\n    pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n    query: hasQuery ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : void 0) : \"\",\n    hash: hashIndex > -1 ? path.slice(hashIndex) : \"\"\n  } : { pathname: path, query: \"\", hash: \"\" };\n}\nfunction addPathPrefix(path, prefix) {\n  if (!path.startsWith(\"/\") || !prefix)\n    return path;\n  if (path === \"/\" && prefix)\n    return prefix;\n  const { pathname, query, hash } = parsePath(path);\n  return `${prefix}${pathname}${query}${hash}`;\n}\nfunction removePathPrefix(path, prefix) {\n  if (!pathHasPrefix(path, prefix))\n    return path;\n  const withoutPrefix = path.slice(prefix.length);\n  return withoutPrefix.startsWith(\"/\") ? withoutPrefix : `/${withoutPrefix}`;\n}\nconst normalizePathTrailingSlash = (path, trailingSlash) => {\n  const { pathname, query, hash } = parsePath(path);\n  return trailingSlash ? pathname.endsWith(\"/\") ? `${pathname}${query}${hash}` : `${pathname}/${query}${hash}` : `${removeTrailingSlash(pathname)}${query}${hash}`;\n};\nfunction removeTrailingSlash(route) {\n  return route.replace(/\\/$/, \"\") || \"/\";\n}\nfunction VisualEditing(props) {\n  const { basePath = \"\", components, refresh, trailingSlash = !1, zIndex } = props, router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useRouter)(), routerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(router), [navigate, setNavigate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    routerRef.current = router;\n  }, [router]);\n  const history = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(\n    () => ({\n      subscribe: (_navigate) => (setNavigate(() => _navigate), () => setNavigate(void 0)),\n      update: (update) => {\n        switch (update.type) {\n          case \"push\":\n            return routerRef.current.push(removePathPrefix(update.url, basePath));\n          case \"pop\":\n            return routerRef.current.back();\n          case \"replace\":\n            return routerRef.current.replace(removePathPrefix(update.url, basePath));\n          default:\n            throw new Error(`Unknown update type: ${update.type}`);\n        }\n      }\n    }),\n    [basePath]\n  ), pathname = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)(), searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(() => {\n    navigate && navigate({\n      type: \"push\",\n      url: normalizePathTrailingSlash(\n        addPathPrefix(`${pathname}${searchParams?.size ? `?${searchParams}` : \"\"}`, basePath),\n        trailingSlash\n      )\n    });\n  }, [basePath, navigate, pathname, searchParams, trailingSlash]);\n  const handleRefresh = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(\n    (payload) => {\n      if (refresh) return refresh(payload);\n      const manualFastRefresh = () => (console.debug(\n        \"Live preview is setup, calling router.refresh() to refresh the server components without refetching cached data\"\n      ), routerRef.current.refresh(), Promise.resolve()), manualFallbackRefresh = () => (console.debug(\n        \"No loaders in live mode detected, or preview kit setup, revalidating root layout\"\n      ), (0,next_sanity_visual_editing_server_actions__WEBPACK_IMPORTED_MODULE_3__.revalidateRootLayout)()), mutationFastRefresh = () => (console.debug(\n        \"Live preview is setup, mutation is skipped assuming its handled by the live preview\"\n      ), !1), mutationFallbackRefresh = () => (console.debug(\n        \"No loaders in live mode detected, or preview kit setup, revalidating root layout\"\n      ), (0,next_sanity_visual_editing_server_actions__WEBPACK_IMPORTED_MODULE_3__.revalidateRootLayout)());\n      switch (payload.source) {\n        case \"manual\":\n          return payload.livePreviewEnabled ? manualFastRefresh() : manualFallbackRefresh();\n        case \"mutation\":\n          return payload.livePreviewEnabled ? mutationFastRefresh() : mutationFallbackRefresh();\n        default:\n          throw new Error(\"Unknown refresh source\", { cause: payload });\n      }\n    },\n    [refresh]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    _sanity_visual_editing_react__WEBPACK_IMPORTED_MODULE_4__.VisualEditing,\n    {\n      components,\n      history,\n      portal: !0,\n      refresh: handleRefresh,\n      zIndex\n    }\n  );\n}\n\n//# sourceMappingURL=VisualEditing.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1zYW5pdHkvZGlzdC9fY2h1bmtzLWVzL1Zpc3VhbEVkaXRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdDO0FBQ3dDO0FBQ0g7QUFDSTtBQUNQO0FBQzFFO0FBQ0E7QUFDQTtBQUNBLFVBQVUsV0FBVztBQUNyQix1REFBdUQsT0FBTztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksSUFBSTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsd0JBQXdCO0FBQ2xDLFlBQVksT0FBTyxFQUFFLFNBQVMsRUFBRSxNQUFNLEVBQUUsS0FBSztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGNBQWM7QUFDM0U7QUFDQTtBQUNBLFVBQVUsd0JBQXdCO0FBQ2xDLHFEQUFxRCxTQUFTLEVBQUUsTUFBTSxFQUFFLEtBQUssT0FBTyxTQUFTLEdBQUcsTUFBTSxFQUFFLEtBQUssT0FBTyw4QkFBOEIsRUFBRSxNQUFNLEVBQUUsS0FBSztBQUNqSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxpRUFBaUUsa0JBQWtCLDZEQUFTLGdCQUFnQiw2Q0FBTSxvQ0FBb0MsK0NBQVE7QUFDeEssRUFBRSxnREFBUztBQUNYO0FBQ0EsR0FBRztBQUNILGtCQUFrQiw4Q0FBTztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELFlBQVk7QUFDaEU7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLGdCQUFnQiwrREFBVyxtQkFBbUIsbUVBQWU7QUFDN0QsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixTQUFTLEVBQUUseUJBQXlCLGFBQWEsT0FBTztBQUNqRjtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSCx3QkFBd0Isa0RBQVc7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUywrRkFBb0I7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsU0FBUywrRkFBb0I7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELGdCQUFnQjtBQUN0RTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EseUJBQXlCLHNEQUFHO0FBQzVCLElBQUksdUVBQWU7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL25leHQtc2FuaXR5L2Rpc3QvX2NodW5rcy1lcy9WaXN1YWxFZGl0aW5nLmpzPzRlM2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5pbXBvcnQgeyBWaXN1YWxFZGl0aW5nIGFzIFZpc3VhbEVkaXRpbmckMSB9IGZyb20gXCJAc2FuaXR5L3Zpc3VhbC1lZGl0aW5nL3JlYWN0XCI7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lLCB1c2VTZWFyY2hQYXJhbXMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uLmpzXCI7XG5pbXBvcnQgeyByZXZhbGlkYXRlUm9vdExheW91dCB9IGZyb20gXCJuZXh0LXNhbml0eS92aXN1YWwtZWRpdGluZy9zZXJ2ZXItYWN0aW9uc1wiO1xuaW1wb3J0IHsgdXNlUmVmLCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCB1c2VNZW1vLCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gcGF0aEhhc1ByZWZpeChwYXRoLCBwcmVmaXgpIHtcbiAgaWYgKHR5cGVvZiBwYXRoICE9IFwic3RyaW5nXCIpXG4gICAgcmV0dXJuICExO1xuICBjb25zdCB7IHBhdGhuYW1lIH0gPSBwYXJzZVBhdGgocGF0aCk7XG4gIHJldHVybiBwYXRobmFtZSA9PT0gcHJlZml4IHx8IHBhdGhuYW1lLnN0YXJ0c1dpdGgoYCR7cHJlZml4fS9gKTtcbn1cbmZ1bmN0aW9uIHBhcnNlUGF0aChwYXRoKSB7XG4gIGNvbnN0IGhhc2hJbmRleCA9IHBhdGguaW5kZXhPZihcIiNcIiksIHF1ZXJ5SW5kZXggPSBwYXRoLmluZGV4T2YoXCI/XCIpLCBoYXNRdWVyeSA9IHF1ZXJ5SW5kZXggPiAtMSAmJiAoaGFzaEluZGV4IDwgMCB8fCBxdWVyeUluZGV4IDwgaGFzaEluZGV4KTtcbiAgcmV0dXJuIGhhc1F1ZXJ5IHx8IGhhc2hJbmRleCA+IC0xID8ge1xuICAgIHBhdGhuYW1lOiBwYXRoLnN1YnN0cmluZygwLCBoYXNRdWVyeSA/IHF1ZXJ5SW5kZXggOiBoYXNoSW5kZXgpLFxuICAgIHF1ZXJ5OiBoYXNRdWVyeSA/IHBhdGguc3Vic3RyaW5nKHF1ZXJ5SW5kZXgsIGhhc2hJbmRleCA+IC0xID8gaGFzaEluZGV4IDogdm9pZCAwKSA6IFwiXCIsXG4gICAgaGFzaDogaGFzaEluZGV4ID4gLTEgPyBwYXRoLnNsaWNlKGhhc2hJbmRleCkgOiBcIlwiXG4gIH0gOiB7IHBhdGhuYW1lOiBwYXRoLCBxdWVyeTogXCJcIiwgaGFzaDogXCJcIiB9O1xufVxuZnVuY3Rpb24gYWRkUGF0aFByZWZpeChwYXRoLCBwcmVmaXgpIHtcbiAgaWYgKCFwYXRoLnN0YXJ0c1dpdGgoXCIvXCIpIHx8ICFwcmVmaXgpXG4gICAgcmV0dXJuIHBhdGg7XG4gIGlmIChwYXRoID09PSBcIi9cIiAmJiBwcmVmaXgpXG4gICAgcmV0dXJuIHByZWZpeDtcbiAgY29uc3QgeyBwYXRobmFtZSwgcXVlcnksIGhhc2ggfSA9IHBhcnNlUGF0aChwYXRoKTtcbiAgcmV0dXJuIGAke3ByZWZpeH0ke3BhdGhuYW1lfSR7cXVlcnl9JHtoYXNofWA7XG59XG5mdW5jdGlvbiByZW1vdmVQYXRoUHJlZml4KHBhdGgsIHByZWZpeCkge1xuICBpZiAoIXBhdGhIYXNQcmVmaXgocGF0aCwgcHJlZml4KSlcbiAgICByZXR1cm4gcGF0aDtcbiAgY29uc3Qgd2l0aG91dFByZWZpeCA9IHBhdGguc2xpY2UocHJlZml4Lmxlbmd0aCk7XG4gIHJldHVybiB3aXRob3V0UHJlZml4LnN0YXJ0c1dpdGgoXCIvXCIpID8gd2l0aG91dFByZWZpeCA6IGAvJHt3aXRob3V0UHJlZml4fWA7XG59XG5jb25zdCBub3JtYWxpemVQYXRoVHJhaWxpbmdTbGFzaCA9IChwYXRoLCB0cmFpbGluZ1NsYXNoKSA9PiB7XG4gIGNvbnN0IHsgcGF0aG5hbWUsIHF1ZXJ5LCBoYXNoIH0gPSBwYXJzZVBhdGgocGF0aCk7XG4gIHJldHVybiB0cmFpbGluZ1NsYXNoID8gcGF0aG5hbWUuZW5kc1dpdGgoXCIvXCIpID8gYCR7cGF0aG5hbWV9JHtxdWVyeX0ke2hhc2h9YCA6IGAke3BhdGhuYW1lfS8ke3F1ZXJ5fSR7aGFzaH1gIDogYCR7cmVtb3ZlVHJhaWxpbmdTbGFzaChwYXRobmFtZSl9JHtxdWVyeX0ke2hhc2h9YDtcbn07XG5mdW5jdGlvbiByZW1vdmVUcmFpbGluZ1NsYXNoKHJvdXRlKSB7XG4gIHJldHVybiByb3V0ZS5yZXBsYWNlKC9cXC8kLywgXCJcIikgfHwgXCIvXCI7XG59XG5mdW5jdGlvbiBWaXN1YWxFZGl0aW5nKHByb3BzKSB7XG4gIGNvbnN0IHsgYmFzZVBhdGggPSBcIlwiLCBjb21wb25lbnRzLCByZWZyZXNoLCB0cmFpbGluZ1NsYXNoID0gITEsIHpJbmRleCB9ID0gcHJvcHMsIHJvdXRlciA9IHVzZVJvdXRlcigpLCByb3V0ZXJSZWYgPSB1c2VSZWYocm91dGVyKSwgW25hdmlnYXRlLCBzZXROYXZpZ2F0ZV0gPSB1c2VTdGF0ZSgpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHJvdXRlclJlZi5jdXJyZW50ID0gcm91dGVyO1xuICB9LCBbcm91dGVyXSk7XG4gIGNvbnN0IGhpc3RvcnkgPSB1c2VNZW1vKFxuICAgICgpID0+ICh7XG4gICAgICBzdWJzY3JpYmU6IChfbmF2aWdhdGUpID0+IChzZXROYXZpZ2F0ZSgoKSA9PiBfbmF2aWdhdGUpLCAoKSA9PiBzZXROYXZpZ2F0ZSh2b2lkIDApKSxcbiAgICAgIHVwZGF0ZTogKHVwZGF0ZSkgPT4ge1xuICAgICAgICBzd2l0Y2ggKHVwZGF0ZS50eXBlKSB7XG4gICAgICAgICAgY2FzZSBcInB1c2hcIjpcbiAgICAgICAgICAgIHJldHVybiByb3V0ZXJSZWYuY3VycmVudC5wdXNoKHJlbW92ZVBhdGhQcmVmaXgodXBkYXRlLnVybCwgYmFzZVBhdGgpKTtcbiAgICAgICAgICBjYXNlIFwicG9wXCI6XG4gICAgICAgICAgICByZXR1cm4gcm91dGVyUmVmLmN1cnJlbnQuYmFjaygpO1xuICAgICAgICAgIGNhc2UgXCJyZXBsYWNlXCI6XG4gICAgICAgICAgICByZXR1cm4gcm91dGVyUmVmLmN1cnJlbnQucmVwbGFjZShyZW1vdmVQYXRoUHJlZml4KHVwZGF0ZS51cmwsIGJhc2VQYXRoKSk7XG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5rbm93biB1cGRhdGUgdHlwZTogJHt1cGRhdGUudHlwZX1gKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pLFxuICAgIFtiYXNlUGF0aF1cbiAgKSwgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpLCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBuYXZpZ2F0ZSAmJiBuYXZpZ2F0ZSh7XG4gICAgICB0eXBlOiBcInB1c2hcIixcbiAgICAgIHVybDogbm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2goXG4gICAgICAgIGFkZFBhdGhQcmVmaXgoYCR7cGF0aG5hbWV9JHtzZWFyY2hQYXJhbXM/LnNpemUgPyBgPyR7c2VhcmNoUGFyYW1zfWAgOiBcIlwifWAsIGJhc2VQYXRoKSxcbiAgICAgICAgdHJhaWxpbmdTbGFzaFxuICAgICAgKVxuICAgIH0pO1xuICB9LCBbYmFzZVBhdGgsIG5hdmlnYXRlLCBwYXRobmFtZSwgc2VhcmNoUGFyYW1zLCB0cmFpbGluZ1NsYXNoXSk7XG4gIGNvbnN0IGhhbmRsZVJlZnJlc2ggPSB1c2VDYWxsYmFjayhcbiAgICAocGF5bG9hZCkgPT4ge1xuICAgICAgaWYgKHJlZnJlc2gpIHJldHVybiByZWZyZXNoKHBheWxvYWQpO1xuICAgICAgY29uc3QgbWFudWFsRmFzdFJlZnJlc2ggPSAoKSA9PiAoY29uc29sZS5kZWJ1ZyhcbiAgICAgICAgXCJMaXZlIHByZXZpZXcgaXMgc2V0dXAsIGNhbGxpbmcgcm91dGVyLnJlZnJlc2goKSB0byByZWZyZXNoIHRoZSBzZXJ2ZXIgY29tcG9uZW50cyB3aXRob3V0IHJlZmV0Y2hpbmcgY2FjaGVkIGRhdGFcIlxuICAgICAgKSwgcm91dGVyUmVmLmN1cnJlbnQucmVmcmVzaCgpLCBQcm9taXNlLnJlc29sdmUoKSksIG1hbnVhbEZhbGxiYWNrUmVmcmVzaCA9ICgpID0+IChjb25zb2xlLmRlYnVnKFxuICAgICAgICBcIk5vIGxvYWRlcnMgaW4gbGl2ZSBtb2RlIGRldGVjdGVkLCBvciBwcmV2aWV3IGtpdCBzZXR1cCwgcmV2YWxpZGF0aW5nIHJvb3QgbGF5b3V0XCJcbiAgICAgICksIHJldmFsaWRhdGVSb290TGF5b3V0KCkpLCBtdXRhdGlvbkZhc3RSZWZyZXNoID0gKCkgPT4gKGNvbnNvbGUuZGVidWcoXG4gICAgICAgIFwiTGl2ZSBwcmV2aWV3IGlzIHNldHVwLCBtdXRhdGlvbiBpcyBza2lwcGVkIGFzc3VtaW5nIGl0cyBoYW5kbGVkIGJ5IHRoZSBsaXZlIHByZXZpZXdcIlxuICAgICAgKSwgITEpLCBtdXRhdGlvbkZhbGxiYWNrUmVmcmVzaCA9ICgpID0+IChjb25zb2xlLmRlYnVnKFxuICAgICAgICBcIk5vIGxvYWRlcnMgaW4gbGl2ZSBtb2RlIGRldGVjdGVkLCBvciBwcmV2aWV3IGtpdCBzZXR1cCwgcmV2YWxpZGF0aW5nIHJvb3QgbGF5b3V0XCJcbiAgICAgICksIHJldmFsaWRhdGVSb290TGF5b3V0KCkpO1xuICAgICAgc3dpdGNoIChwYXlsb2FkLnNvdXJjZSkge1xuICAgICAgICBjYXNlIFwibWFudWFsXCI6XG4gICAgICAgICAgcmV0dXJuIHBheWxvYWQubGl2ZVByZXZpZXdFbmFibGVkID8gbWFudWFsRmFzdFJlZnJlc2goKSA6IG1hbnVhbEZhbGxiYWNrUmVmcmVzaCgpO1xuICAgICAgICBjYXNlIFwibXV0YXRpb25cIjpcbiAgICAgICAgICByZXR1cm4gcGF5bG9hZC5saXZlUHJldmlld0VuYWJsZWQgPyBtdXRhdGlvbkZhc3RSZWZyZXNoKCkgOiBtdXRhdGlvbkZhbGxiYWNrUmVmcmVzaCgpO1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlVua25vd24gcmVmcmVzaCBzb3VyY2VcIiwgeyBjYXVzZTogcGF5bG9hZCB9KTtcbiAgICAgIH1cbiAgICB9LFxuICAgIFtyZWZyZXNoXVxuICApO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChcbiAgICBWaXN1YWxFZGl0aW5nJDEsXG4gICAge1xuICAgICAgY29tcG9uZW50cyxcbiAgICAgIGhpc3RvcnksXG4gICAgICBwb3J0YWw6ICEwLFxuICAgICAgcmVmcmVzaDogaGFuZGxlUmVmcmVzaCxcbiAgICAgIHpJbmRleFxuICAgIH1cbiAgKTtcbn1cbmV4cG9ydCB7XG4gIFZpc3VhbEVkaXRpbmcgYXMgZGVmYXVsdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVZpc3VhbEVkaXRpbmcuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-sanity/dist/_chunks-es/VisualEditing.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-sanity/dist/visual-editing/client-component.js":
/*!**************************************************************************!*\
  !*** ./node_modules/next-sanity/dist/visual-editing/client-component.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VisualEditingLazyClientComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst VisualEditingClientComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.lazy)(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@sanity\"), __webpack_require__.e(\"vendor-chunks/rxjs\"), __webpack_require__.e(\"vendor-chunks/xstate\"), __webpack_require__.e(\"vendor-chunks/next-sanity\"), __webpack_require__.e(\"vendor-chunks/lodash-es\"), __webpack_require__.e(\"vendor-chunks/valibot\"), __webpack_require__.e(\"vendor-chunks/scroll-into-view-if-needed\"), __webpack_require__.e(\"vendor-chunks/mendoza\"), __webpack_require__.e(\"vendor-chunks/get-random-values-esm\"), __webpack_require__.e(\"vendor-chunks/compute-scroll-into-view\"), __webpack_require__.e(\"vendor-chunks/@vercel\"), __webpack_require__.e(\"vendor-chunks/react-compiler-runtime\")]).then(__webpack_require__.bind(__webpack_require__, /*! ../_chunks-es/VisualEditing.js */ \"(ssr)/./node_modules/next-sanity/dist/_chunks-es/VisualEditing.js\")));\nfunction VisualEditingLazyClientComponent(props) {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: null,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(VisualEditingClientComponent, {\n            ...props\n        })\n    });\n}\n //# sourceMappingURL=client-component.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1zYW5pdHkvZGlzdC92aXN1YWwtZWRpdGluZy9jbGllbnQtY29tcG9uZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBWUEsTUFBTUEsNkNBQStCQywyQ0FBSUEsQ0FBQyxJQUFNLDh6QkFBTztBQUVoRCxTQUFTQyxpQ0FBaUNDLEtBQUE7SUFFN0MsdUJBQUFDLHNEQUFBQSxDQUFDQywyQ0FBUUEsRUFBUjtRQUFTQyxVQUFVO1FBQ2xCQyxVQUFBLGdCQUFBSCxzREFBQUEsQ0FBQ0osOEJBQThCO1lBQUEsR0FBR0csS0FBQTtRQUFBO0lBQ3BDO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uLi8uLi9zcmMvdmlzdWFsLWVkaXRpbmcvY2xpZW50LWNvbXBvbmVudC9WaXN1YWxFZGl0aW5nTGF6eS50c3g/MDAyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqXG4gKiBJZiBwYWdlcyByb3V0ZXIgc3VwcG9ydGVkIGBuZXh0L2R5bmFtaWNgIGltcG9ydHMgKGl0IHdhbnRzIGBuZXh0L2R5bmFtaWMuanNgKSxcbiAqIG9yIGlmIHR1cmJvcGFjayBpbiBhcHAgcm91dGVyIGFsbG93ZWQgYG5leHQvZHluYW1pYy5qc2AgKGl0IGRvZXNuJ3QgeWV0KVxuICogd2UgY291bGQgdXNlIGBkeW5hbWljKCgpID0+IGltcG9ydCgnLi4uKSwge3NzcjogZmFsc2V9KWAgaGVyZS5cbiAqIFNpbmNlIHdlIGNhbid0LCB3ZSBuZWVkIHRvIHVzZSBhIGxhenkgaW1wb3J0IGFuZCBTdXNwZW5zZSBvdXJzZWxmLlxuICovXG5cbmltcG9ydCB7bGF6eSwgU3VzcGVuc2V9IGZyb20gJ3JlYWN0J1xuXG5pbXBvcnQgdHlwZSB7VmlzdWFsRWRpdGluZ1Byb3BzfSBmcm9tICcuL1Zpc3VhbEVkaXRpbmcnXG5cbmNvbnN0IFZpc3VhbEVkaXRpbmdDbGllbnRDb21wb25lbnQgPSBsYXp5KCgpID0+IGltcG9ydCgnLi9WaXN1YWxFZGl0aW5nJykpXG5cbmV4cG9ydCBmdW5jdGlvbiBWaXN1YWxFZGl0aW5nTGF6eUNsaWVudENvbXBvbmVudChwcm9wczogVmlzdWFsRWRpdGluZ1Byb3BzKTogUmVhY3QuUmVhY3ROb2RlIHtcbiAgcmV0dXJuIChcbiAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e251bGx9PlxuICAgICAgPFZpc3VhbEVkaXRpbmdDbGllbnRDb21wb25lbnQgey4uLnByb3BzfSAvPlxuICAgIDwvU3VzcGVuc2U+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJWaXN1YWxFZGl0aW5nQ2xpZW50Q29tcG9uZW50IiwibGF6eSIsIlZpc3VhbEVkaXRpbmdMYXp5Q2xpZW50Q29tcG9uZW50IiwicHJvcHMiLCJqc3giLCJTdXNwZW5zZSIsImZhbGxiYWNrIiwiY2hpbGRyZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-sanity/dist/visual-editing/client-component.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-sanity/dist/visual-editing/server-actions.js":
/*!************************************************************************!*\
  !*** ./node_modules/next-sanity/dist/visual-editing/server-actions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   revalidateRootLayout: () => (/* binding */ revalidateRootLayout)
/* harmony export */ });
/* harmony import */ var next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/client/app-call-server */ "(ssr)/./node_modules/next/dist/client/app-call-server.js");
/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js");



function __build_action__(action, args) {
  return (0,next_dist_client_app_call_server__WEBPACK_IMPORTED_MODULE_0__.callServer)(action.$$id, args)
}

/* __next_internal_action_entry_do_not_use__ {"23977280e679cbd5490718534d869d8b006b3dfa":"revalidateRootLayout"} */ var revalidateRootLayout = (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_1__.createServerReference)("23977280e679cbd5490718534d869d8b006b3dfa");

 //# sourceMappingURL=server-actions.js.map



/***/ }),

/***/ "(action-browser)/./node_modules/next-sanity/dist/visual-editing/server-actions.js":
/*!************************************************************************!*\
  !*** ./node_modules/next-sanity/dist/visual-editing/server-actions.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   revalidateRootLayout: () => (/* binding */ revalidateRootLayout)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(action-browser)/./node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var next_cache_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/cache.js */ \"(action-browser)/./node_modules/next/cache.js\");\n/* harmony import */ var next_headers_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers.js */ \"(action-browser)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"23977280e679cbd5490718534d869d8b006b3dfa\":\"revalidateRootLayout\"} */ \n\n\n\nasync function revalidateRootLayout() {\n    if (!(await (0,next_headers_js__WEBPACK_IMPORTED_MODULE_3__.draftMode)()).isEnabled) {\n        console.warn(\"Skipped revalidatePath request because draft mode is not enabled\");\n        return;\n    }\n    await (0,next_cache_js__WEBPACK_IMPORTED_MODULE_2__.revalidatePath)(\"/\", \"layout\");\n}\n //# sourceMappingURL=server-actions.js.map\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    revalidateRootLayout\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(\"23977280e679cbd5490718534d869d8b006b3dfa\", revalidateRootLayout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LXNhbml0eS9kaXN0L3Zpc3VhbC1lZGl0aW5nL3NlcnZlci1hY3Rpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFJQSxlQUFzQkE7SUFDcEIsSUFBSSxFQUFFLE1BQU1DLDBEQUFTQSxFQUFDLEVBQUdDLFNBQUEsRUFBVztRQUVsQ0MsUUFBUUMsSUFBQSxDQUFLO1FBQ2I7SUFBQTtJQUVJLE1BQUFDLDZEQUFjQSxDQUFDLEtBQUs7QUFDNUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uLi8uLi9zcmMvdmlzdWFsLWVkaXRpbmcvc2VydmVyLWFjdGlvbnMvaW5kZXgudHM/OGVlYyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHNlcnZlcidcbmltcG9ydCB7cmV2YWxpZGF0ZVBhdGh9IGZyb20gJ25leHQvY2FjaGUuanMnXG5pbXBvcnQge2RyYWZ0TW9kZX0gZnJvbSAnbmV4dC9oZWFkZXJzLmpzJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gcmV2YWxpZGF0ZVJvb3RMYXlvdXQoKTogUHJvbWlzZTx2b2lkPiB7XG4gIGlmICghKGF3YWl0IGRyYWZ0TW9kZSgpKS5pc0VuYWJsZWQpIHtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZVxuICAgIGNvbnNvbGUud2FybignU2tpcHBlZCByZXZhbGlkYXRlUGF0aCByZXF1ZXN0IGJlY2F1c2UgZHJhZnQgbW9kZSBpcyBub3QgZW5hYmxlZCcpXG4gICAgcmV0dXJuXG4gIH1cbiAgYXdhaXQgcmV2YWxpZGF0ZVBhdGgoJy8nLCAnbGF5b3V0Jylcbn1cbiJdLCJuYW1lcyI6WyJyZXZhbGlkYXRlUm9vdExheW91dCIsImRyYWZ0TW9kZSIsImlzRW5hYmxlZCIsImNvbnNvbGUiLCJ3YXJuIiwicmV2YWxpZGF0ZVBhdGgiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/next-sanity/dist/visual-editing/server-actions.js\n");

/***/ })

};
;