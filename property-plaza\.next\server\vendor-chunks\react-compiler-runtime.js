/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-compiler-runtime";
exports.ids = ["vendor-chunks/react-compiler-runtime"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-compiler-runtime/dist/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-compiler-runtime/dist/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @lightSyntaxTransform\n * @noflow\n * @nolint\n * @preventMunge\n * @preserve-invariant-messages\n */\n\n\"use no memo\";'use strict';\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar _a, _b;\nconst { useRef, useEffect, isValidElement } = React__namespace;\nconst ReactSecretInternals = (_a = React__namespace.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE) !== null && _a !== void 0 ? _a : React__namespace.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\nconst $empty = Symbol.for('react.memo_cache_sentinel');\nconst c = typeof ((_b = React__namespace.__COMPILER_RUNTIME) === null || _b === void 0 ? void 0 : _b.c) === 'function'\n    ?\n        React__namespace.__COMPILER_RUNTIME.c\n    : function c(size) {\n        return React__namespace.useMemo(() => {\n            const $ = new Array(size);\n            for (let ii = 0; ii < size; ii++) {\n                $[ii] = $empty;\n            }\n            $[$empty] = true;\n            return $;\n        }, []);\n    };\nconst LazyGuardDispatcher = {};\n[\n    'readContext',\n    'useCallback',\n    'useContext',\n    'useEffect',\n    'useImperativeHandle',\n    'useInsertionEffect',\n    'useLayoutEffect',\n    'useMemo',\n    'useReducer',\n    'useRef',\n    'useState',\n    'useDebugValue',\n    'useDeferredValue',\n    'useTransition',\n    'useMutableSource',\n    'useSyncExternalStore',\n    'useId',\n    'unstable_isNewReconciler',\n    'getCacheSignal',\n    'getCacheForType',\n    'useCacheRefresh',\n].forEach(name => {\n    LazyGuardDispatcher[name] = () => {\n        throw new Error(`[React] Unexpected React hook call (${name}) from a React compiled function. ` +\n            \"Check that all hooks are called directly and named according to convention ('use[A-Z]') \");\n    };\n});\nlet originalDispatcher = null;\nLazyGuardDispatcher['useMemoCache'] = (count) => {\n    if (originalDispatcher == null) {\n        throw new Error('React Compiler internal invariant violation: unexpected null dispatcher');\n    }\n    else {\n        return originalDispatcher.useMemoCache(count);\n    }\n};\nvar GuardKind;\n(function (GuardKind) {\n    GuardKind[GuardKind[\"PushGuardContext\"] = 0] = \"PushGuardContext\";\n    GuardKind[GuardKind[\"PopGuardContext\"] = 1] = \"PopGuardContext\";\n    GuardKind[GuardKind[\"PushExpectHook\"] = 2] = \"PushExpectHook\";\n    GuardKind[GuardKind[\"PopExpectHook\"] = 3] = \"PopExpectHook\";\n})(GuardKind || (GuardKind = {}));\nfunction setCurrent(newDispatcher) {\n    ReactSecretInternals.ReactCurrentDispatcher.current = newDispatcher;\n    return ReactSecretInternals.ReactCurrentDispatcher.current;\n}\nconst guardFrames = [];\nfunction $dispatcherGuard(kind) {\n    const curr = ReactSecretInternals.ReactCurrentDispatcher.current;\n    if (kind === GuardKind.PushGuardContext) {\n        guardFrames.push(curr);\n        if (guardFrames.length === 1) {\n            originalDispatcher = curr;\n        }\n        if (curr === LazyGuardDispatcher) {\n            throw new Error(`[React] Unexpected call to custom hook or component from a React compiled function. ` +\n                \"Check that (1) all hooks are called directly and named according to convention ('use[A-Z]') \" +\n                'and (2) components are returned as JSX instead of being directly invoked.');\n        }\n        setCurrent(LazyGuardDispatcher);\n    }\n    else if (kind === GuardKind.PopGuardContext) {\n        const lastFrame = guardFrames.pop();\n        if (lastFrame == null) {\n            throw new Error('React Compiler internal error: unexpected null in guard stack');\n        }\n        if (guardFrames.length === 0) {\n            originalDispatcher = null;\n        }\n        setCurrent(lastFrame);\n    }\n    else if (kind === GuardKind.PushExpectHook) {\n        guardFrames.push(curr);\n        setCurrent(originalDispatcher);\n    }\n    else if (kind === GuardKind.PopExpectHook) {\n        const lastFrame = guardFrames.pop();\n        if (lastFrame == null) {\n            throw new Error('React Compiler internal error: unexpected null in guard stack');\n        }\n        setCurrent(lastFrame);\n    }\n    else {\n        throw new Error('React Compiler internal error: unreachable block' + kind);\n    }\n}\nfunction $reset($) {\n    for (let ii = 0; ii < $.length; ii++) {\n        $[ii] = $empty;\n    }\n}\nfunction $makeReadOnly() {\n    throw new Error('TODO: implement $makeReadOnly in react-compiler-runtime');\n}\nconst renderCounterRegistry = new Map();\nfunction clearRenderCounterRegistry() {\n    for (const counters of renderCounterRegistry.values()) {\n        counters.forEach(counter => {\n            counter.count = 0;\n        });\n    }\n}\nfunction registerRenderCounter(name, val) {\n    let counters = renderCounterRegistry.get(name);\n    if (counters == null) {\n        counters = new Set();\n        renderCounterRegistry.set(name, counters);\n    }\n    counters.add(val);\n}\nfunction removeRenderCounter(name, val) {\n    const counters = renderCounterRegistry.get(name);\n    if (counters == null) {\n        return;\n    }\n    counters.delete(val);\n}\nfunction useRenderCounter(name) {\n    const val = useRef(null);\n    if (val.current != null) {\n        val.current.count += 1;\n    }\n    useEffect(() => {\n        if (val.current == null) {\n            const counter = { count: 0 };\n            registerRenderCounter(name, counter);\n            val.current = counter;\n        }\n        return () => {\n            if (val.current !== null) {\n                removeRenderCounter(name, val.current);\n            }\n        };\n    });\n}\nconst seenErrors = new Set();\nfunction $structuralCheck(oldValue, newValue, variableName, fnName, kind, loc) {\n    function error(l, r, path, depth) {\n        const str = `${fnName}:${loc} [${kind}] ${variableName}${path} changed from ${l} to ${r} at depth ${depth}`;\n        if (seenErrors.has(str)) {\n            return;\n        }\n        seenErrors.add(str);\n        console.error(str);\n    }\n    const depthLimit = 2;\n    function recur(oldValue, newValue, path, depth) {\n        if (depth > depthLimit) {\n            return;\n        }\n        else if (oldValue === newValue) {\n            return;\n        }\n        else if (typeof oldValue !== typeof newValue) {\n            error(`type ${typeof oldValue}`, `type ${typeof newValue}`, path, depth);\n        }\n        else if (typeof oldValue === 'object') {\n            const oldArray = Array.isArray(oldValue);\n            const newArray = Array.isArray(newValue);\n            if (oldValue === null && newValue !== null) {\n                error('null', `type ${typeof newValue}`, path, depth);\n            }\n            else if (newValue === null) {\n                error(`type ${typeof oldValue}`, 'null', path, depth);\n            }\n            else if (oldValue instanceof Map) {\n                if (!(newValue instanceof Map)) {\n                    error(`Map instance`, `other value`, path, depth);\n                }\n                else if (oldValue.size !== newValue.size) {\n                    error(`Map instance with size ${oldValue.size}`, `Map instance with size ${newValue.size}`, path, depth);\n                }\n                else {\n                    for (const [k, v] of oldValue) {\n                        if (!newValue.has(k)) {\n                            error(`Map instance with key ${k}`, `Map instance without key ${k}`, path, depth);\n                        }\n                        else {\n                            recur(v, newValue.get(k), `${path}.get(${k})`, depth + 1);\n                        }\n                    }\n                }\n            }\n            else if (newValue instanceof Map) {\n                error('other value', `Map instance`, path, depth);\n            }\n            else if (oldValue instanceof Set) {\n                if (!(newValue instanceof Set)) {\n                    error(`Set instance`, `other value`, path, depth);\n                }\n                else if (oldValue.size !== newValue.size) {\n                    error(`Set instance with size ${oldValue.size}`, `Set instance with size ${newValue.size}`, path, depth);\n                }\n                else {\n                    for (const v of newValue) {\n                        if (!oldValue.has(v)) {\n                            error(`Set instance without element ${v}`, `Set instance with element ${v}`, path, depth);\n                        }\n                    }\n                }\n            }\n            else if (newValue instanceof Set) {\n                error('other value', `Set instance`, path, depth);\n            }\n            else if (oldArray || newArray) {\n                if (oldArray !== newArray) {\n                    error(`type ${oldArray ? 'array' : 'object'}`, `type ${newArray ? 'array' : 'object'}`, path, depth);\n                }\n                else if (oldValue.length !== newValue.length) {\n                    error(`array with length ${oldValue.length}`, `array with length ${newValue.length}`, path, depth);\n                }\n                else {\n                    for (let ii = 0; ii < oldValue.length; ii++) {\n                        recur(oldValue[ii], newValue[ii], `${path}[${ii}]`, depth + 1);\n                    }\n                }\n            }\n            else if (isValidElement(oldValue) || isValidElement(newValue)) {\n                if (isValidElement(oldValue) !== isValidElement(newValue)) {\n                    error(`type ${isValidElement(oldValue) ? 'React element' : 'object'}`, `type ${isValidElement(newValue) ? 'React element' : 'object'}`, path, depth);\n                }\n                else if (oldValue.type !== newValue.type) {\n                    error(`React element of type ${oldValue.type}`, `React element of type ${newValue.type}`, path, depth);\n                }\n                else {\n                    recur(oldValue.props, newValue.props, `[props of ${path}]`, depth + 1);\n                }\n            }\n            else {\n                for (const key in newValue) {\n                    if (!(key in oldValue)) {\n                        error(`object without key ${key}`, `object with key ${key}`, path, depth);\n                    }\n                }\n                for (const key in oldValue) {\n                    if (!(key in newValue)) {\n                        error(`object with key ${key}`, `object without key ${key}`, path, depth);\n                    }\n                    else {\n                        recur(oldValue[key], newValue[key], `${path}.${key}`, depth + 1);\n                    }\n                }\n            }\n        }\n        else if (typeof oldValue === 'function') {\n            return;\n        }\n        else if (isNaN(oldValue) || isNaN(newValue)) {\n            if (isNaN(oldValue) !== isNaN(newValue)) {\n                error(`${isNaN(oldValue) ? 'NaN' : 'non-NaN value'}`, `${isNaN(newValue) ? 'NaN' : 'non-NaN value'}`, path, depth);\n            }\n        }\n        else if (oldValue !== newValue) {\n            error(oldValue, newValue, path, depth);\n        }\n    }\n    recur(oldValue, newValue, '', 0);\n}\n\nexports.$dispatcherGuard = $dispatcherGuard;\nexports.$makeReadOnly = $makeReadOnly;\nexports.$reset = $reset;\nexports.$structuralCheck = $structuralCheck;\nexports.c = c;\nexports.clearRenderCounterRegistry = clearRenderCounterRegistry;\nexports.renderCounterRegistry = renderCounterRegistry;\nexports.useRenderCounter = useRenderCounter;\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-compiler-runtime/dist/index.js\n");

/***/ })

};
;