"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tunnel-agent";
exports.ids = ["vendor-chunks/tunnel-agent"];
exports.modules = {

/***/ "(ssr)/./node_modules/tunnel-agent/index.js":
/*!********************************************!*\
  !*** ./node_modules/tunnel-agent/index.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar net = __webpack_require__(/*! net */ \"net\")\n  , tls = __webpack_require__(/*! tls */ \"tls\")\n  , http = __webpack_require__(/*! http */ \"http\")\n  , https = __webpack_require__(/*! https */ \"https\")\n  , events = __webpack_require__(/*! events */ \"events\")\n  , assert = __webpack_require__(/*! assert */ \"assert\")\n  , util = __webpack_require__(/*! util */ \"util\")\n  , Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/safe-buffer/index.js\").Buffer)\n  ;\n\nexports.httpOverHttp = httpOverHttp\nexports.httpsOverHttp = httpsOverHttp\nexports.httpOverHttps = httpOverHttps\nexports.httpsOverHttps = httpsOverHttps\n\n\nfunction httpOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  return agent\n}\n\nfunction httpsOverHttp(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = http.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\nfunction httpOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  return agent\n}\n\nfunction httpsOverHttps(options) {\n  var agent = new TunnelingAgent(options)\n  agent.request = https.request\n  agent.createSocket = createSecureSocket\n  agent.defaultPort = 443\n  return agent\n}\n\n\nfunction TunnelingAgent(options) {\n  var self = this\n  self.options = options || {}\n  self.proxyOptions = self.options.proxy || {}\n  self.maxSockets = self.options.maxSockets || http.Agent.defaultMaxSockets\n  self.requests = []\n  self.sockets = []\n\n  self.on('free', function onFree(socket, host, port) {\n    for (var i = 0, len = self.requests.length; i < len; ++i) {\n      var pending = self.requests[i]\n      if (pending.host === host && pending.port === port) {\n        // Detect the request to connect same origin server,\n        // reuse the connection.\n        self.requests.splice(i, 1)\n        pending.request.onSocket(socket)\n        return\n      }\n    }\n    socket.destroy()\n    self.removeSocket(socket)\n  })\n}\nutil.inherits(TunnelingAgent, events.EventEmitter)\n\nTunnelingAgent.prototype.addRequest = function addRequest(req, options) {\n  var self = this\n\n   // Legacy API: addRequest(req, host, port, path)\n  if (typeof options === 'string') {\n    options = {\n      host: options,\n      port: arguments[2],\n      path: arguments[3]\n    };\n  }\n\n  if (self.sockets.length >= this.maxSockets) {\n    // We are over limit so we'll add it to the queue.\n    self.requests.push({host: options.host, port: options.port, request: req})\n    return\n  }\n\n  // If we are under maxSockets create a new one.\n  self.createConnection({host: options.host, port: options.port, request: req})\n}\n\nTunnelingAgent.prototype.createConnection = function createConnection(pending) {\n  var self = this\n\n  self.createSocket(pending, function(socket) {\n    socket.on('free', onFree)\n    socket.on('close', onCloseOrRemove)\n    socket.on('agentRemove', onCloseOrRemove)\n    pending.request.onSocket(socket)\n\n    function onFree() {\n      self.emit('free', socket, pending.host, pending.port)\n    }\n\n    function onCloseOrRemove(err) {\n      self.removeSocket(socket)\n      socket.removeListener('free', onFree)\n      socket.removeListener('close', onCloseOrRemove)\n      socket.removeListener('agentRemove', onCloseOrRemove)\n    }\n  })\n}\n\nTunnelingAgent.prototype.createSocket = function createSocket(options, cb) {\n  var self = this\n  var placeholder = {}\n  self.sockets.push(placeholder)\n\n  var connectOptions = mergeOptions({}, self.proxyOptions,\n    { method: 'CONNECT'\n    , path: options.host + ':' + options.port\n    , agent: false\n    }\n  )\n  if (connectOptions.proxyAuth) {\n    connectOptions.headers = connectOptions.headers || {}\n    connectOptions.headers['Proxy-Authorization'] = 'Basic ' +\n        Buffer.from(connectOptions.proxyAuth).toString('base64')\n  }\n\n  debug('making CONNECT request')\n  var connectReq = self.request(connectOptions)\n  connectReq.useChunkedEncodingByDefault = false // for v0.6\n  connectReq.once('response', onResponse) // for v0.6\n  connectReq.once('upgrade', onUpgrade)   // for v0.6\n  connectReq.once('connect', onConnect)   // for v0.7 or later\n  connectReq.once('error', onError)\n  connectReq.end()\n\n  function onResponse(res) {\n    // Very hacky. This is necessary to avoid http-parser leaks.\n    res.upgrade = true\n  }\n\n  function onUpgrade(res, socket, head) {\n    // Hacky.\n    process.nextTick(function() {\n      onConnect(res, socket, head)\n    })\n  }\n\n  function onConnect(res, socket, head) {\n    connectReq.removeAllListeners()\n    socket.removeAllListeners()\n\n    if (res.statusCode === 200) {\n      assert.equal(head.length, 0)\n      debug('tunneling connection has established')\n      self.sockets[self.sockets.indexOf(placeholder)] = socket\n      cb(socket)\n    } else {\n      debug('tunneling socket could not be established, statusCode=%d', res.statusCode)\n      var error = new Error('tunneling socket could not be established, ' + 'statusCode=' + res.statusCode)\n      error.code = 'ECONNRESET'\n      options.request.emit('error', error)\n      self.removeSocket(placeholder)\n    }\n  }\n\n  function onError(cause) {\n    connectReq.removeAllListeners()\n\n    debug('tunneling socket could not be established, cause=%s\\n', cause.message, cause.stack)\n    var error = new Error('tunneling socket could not be established, ' + 'cause=' + cause.message)\n    error.code = 'ECONNRESET'\n    options.request.emit('error', error)\n    self.removeSocket(placeholder)\n  }\n}\n\nTunnelingAgent.prototype.removeSocket = function removeSocket(socket) {\n  var pos = this.sockets.indexOf(socket)\n  if (pos === -1) return\n\n  this.sockets.splice(pos, 1)\n\n  var pending = this.requests.shift()\n  if (pending) {\n    // If we have pending requests and a socket gets closed a new one\n    // needs to be created to take over in the pool for the one that closed.\n    this.createConnection(pending)\n  }\n}\n\nfunction createSecureSocket(options, cb) {\n  var self = this\n  TunnelingAgent.prototype.createSocket.call(self, options, function(socket) {\n    // 0 is dummy port for v0.6\n    var secureSocket = tls.connect(0, mergeOptions({}, self.options,\n      { servername: options.host\n      , socket: socket\n      }\n    ))\n    self.sockets[self.sockets.indexOf(socket)] = secureSocket\n    cb(secureSocket)\n  })\n}\n\n\nfunction mergeOptions(target) {\n  for (var i = 1, len = arguments.length; i < len; ++i) {\n    var overrides = arguments[i]\n    if (typeof overrides === 'object') {\n      var keys = Object.keys(overrides)\n      for (var j = 0, keyLen = keys.length; j < keyLen; ++j) {\n        var k = keys[j]\n        if (overrides[k] !== undefined) {\n          target[k] = overrides[k]\n        }\n      }\n    }\n  }\n  return target\n}\n\n\nvar debug\nif (process.env.NODE_DEBUG && /\\btunnel\\b/.test(process.env.NODE_DEBUG)) {\n  debug = function() {\n    var args = Array.prototype.slice.call(arguments)\n    if (typeof args[0] === 'string') {\n      args[0] = 'TUNNEL: ' + args[0]\n    } else {\n      args.unshift('TUNNEL:')\n    }\n    console.error.apply(console, args)\n  }\n} else {\n  debug = function() {}\n}\nexports.debug = debug // for test\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tunnel-agent/index.js\n");

/***/ })

};
;