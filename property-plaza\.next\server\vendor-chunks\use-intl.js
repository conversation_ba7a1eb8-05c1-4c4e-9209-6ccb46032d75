"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-intl";
exports.ids = ["vendor-chunks/use-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-intl/dist/_IntlProvider.js":
/*!*****************************************************!*\
  !*** ./node_modules/use-intl/dist/_IntlProvider.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fSW50bFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw2SUFBMEQ7QUFDNUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L19JbnRsUHJvdmlkZXIuanM/NmM0NSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL19JbnRsUHJvdmlkZXIuanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9fSW50bFByb3ZpZGVyLmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/_useLocale.js":
/*!**************************************************!*\
  !*** ./node_modules/use-intl/dist/_useLocale.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/_useLocale.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSx1SUFBdUQ7QUFDekQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L191c2VMb2NhbGUuanM/ZThhZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9wcm9kdWN0aW9uL191c2VMb2NhbGUuanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js":
/*!************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nconst IntlContext = /*#__PURE__*/React.createContext(undefined);\n\nexports.IntlContext = IntlContext;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixZQUFZLG1CQUFPLENBQUMsd0dBQU87O0FBRTNCOztBQUVBLG1CQUFtQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb3BlcnR5LXBsYXphLy4vbm9kZV9tb2R1bGVzL3VzZS1pbnRsL2Rpc3QvZGV2ZWxvcG1lbnQvSW50bENvbnRleHQtQktmc256QnguanM/YjgzMSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBSZWFjdCA9IHJlcXVpcmUoJ3JlYWN0Jyk7XG5cbmNvbnN0IEludGxDb250ZXh0ID0gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUNvbnRleHQodW5kZWZpbmVkKTtcblxuZXhwb3J0cy5JbnRsQ29udGV4dCA9IEludGxDb250ZXh0O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js":
/*!*****************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_IntlProvider.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar React__default = /*#__PURE__*/_interopDefault(React);\n\nfunction IntlProvider(_ref) {\n  let {\n    children,\n    defaultTranslationValues,\n    formats,\n    getMessageFallback,\n    locale,\n    messages,\n    now,\n    onError,\n    timeZone\n  } = _ref;\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = React.useMemo(() => {\n    return initializeConfig.createCache();\n  }, [locale]);\n  const formatters = React.useMemo(() => initializeConfig.createIntlFormatters(cache), [cache]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = React.useMemo(() => ({\n    ...initializeConfig.initializeConfig({\n      locale,\n      defaultTranslationValues,\n      formats,\n      getMessageFallback,\n      messages,\n      now,\n      onError,\n      timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, defaultTranslationValues, formats, formatters, getMessageFallback, locale, messages, now, onError, timeZone]);\n  return /*#__PURE__*/React__default.default.createElement(IntlContext.IntlContext.Provider, {\n    value: value\n  }, children);\n}\n\nexports.IntlProvider = IntlProvider;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js":
/*!***********************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar IntlContext = __webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\nfunction useIntlContext() {\n  const context = React.useContext(IntlContext.IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl-docs.vercel.app/docs/usage/configuration#client-server-components' );\n  }\n  return context;\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nexports.useIntlContext = useIntlContext;\nexports.useLocale = useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLTBSbDl1UjgyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyx3R0FBTztBQUMzQixrQkFBa0IsbUJBQU8sQ0FBQyx5R0FBMkI7O0FBRXJEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0I7QUFDdEIsaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLTBSbDl1UjgyLmpzPzlkNmIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgUmVhY3QgPSByZXF1aXJlKCdyZWFjdCcpO1xudmFyIEludGxDb250ZXh0ID0gcmVxdWlyZSgnLi9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcycpO1xuXG5mdW5jdGlvbiB1c2VJbnRsQ29udGV4dCgpIHtcbiAgY29uc3QgY29udGV4dCA9IFJlYWN0LnVzZUNvbnRleHQoSW50bENvbnRleHQuSW50bENvbnRleHQpO1xuICBpZiAoIWNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vIGludGwgY29udGV4dCBmb3VuZC4gSGF2ZSB5b3UgY29uZmlndXJlZCB0aGUgcHJvdmlkZXI/IFNlZSBodHRwczovL25leHQtaW50bC1kb2NzLnZlcmNlbC5hcHAvZG9jcy91c2FnZS9jb25maWd1cmF0aW9uI2NsaWVudC1zZXJ2ZXItY29tcG9uZW50cycgKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn1cblxuZnVuY3Rpb24gdXNlTG9jYWxlKCkge1xuICByZXR1cm4gdXNlSW50bENvbnRleHQoKS5sb2NhbGU7XG59XG5cbmV4cG9ydHMudXNlSW50bENvbnRleHQgPSB1c2VJbnRsQ29udGV4dDtcbmV4cG9ydHMudXNlTG9jYWxlID0gdXNlTG9jYWxlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/_useLocale.js":
/*!**************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/_useLocale.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9kZXZlbG9wbWVudC9fdXNlTG9jYWxlLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QyxFQUFFLGFBQWEsRUFBQzs7QUFFN0QsaUJBQWlCLG1CQUFPLENBQUMsdUdBQTBCO0FBQ25ELG1CQUFPLENBQUMsd0dBQU87QUFDZixtQkFBTyxDQUFDLHlHQUEyQjs7OztBQUluQyxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9wZXJ0eS1wbGF6YS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2RldmVsb3BtZW50L191c2VMb2NhbGUuanM/MmZmZCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCAnX19lc01vZHVsZScsIHsgdmFsdWU6IHRydWUgfSk7XG5cbnZhciBfdXNlTG9jYWxlID0gcmVxdWlyZSgnLi9fdXNlTG9jYWxlLTBSbDl1UjgyLmpzJyk7XG5yZXF1aXJlKCdyZWFjdCcpO1xucmVxdWlyZSgnLi9JbnRsQ29udGV4dC1CS2ZzbnpCeC5qcycpO1xuXG5cblxuZXhwb3J0cy51c2VMb2NhbGUgPSBfdXNlTG9jYWxlLnVzZUxvY2FsZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/_useLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl-docs.vercel.app/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl-docs.vercel.app/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar core = __webpack_require__(/*! ./core.js */ \"(ssr)/./node_modules/use-intl/dist/development/core.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar react = __webpack_require__(/*! ./react.js */ \"(ssr)/./node_modules/use-intl/dist/development/react.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n\n\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createTranslator = core.createTranslator;\nexports.createFormatter = createFormatter.createFormatter;\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useFormatter = react.useFormatter;\nexports.useMessages = react.useMessages;\nexports.useNow = react.useNow;\nexports.useTimeZone = react.useTimeZone;\nexports.useTranslations = react.useTranslations;\nexports.useLocale = _useLocale.useLocale;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/development/react.js":
/*!*********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/react.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar _IntlProvider = __webpack_require__(/*! ./_IntlProvider.js */ \"(ssr)/./node_modules/use-intl/dist/development/_IntlProvider.js\");\nvar _useLocale = __webpack_require__(/*! ./_useLocale-0Rl9uR82.js */ \"(ssr)/./node_modules/use-intl/dist/development/_useLocale-0Rl9uR82.js\");\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(ssr)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(ssr)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n__webpack_require__(/*! ./IntlContext-BKfsnzBx.js */ \"(ssr)/./node_modules/use-intl/dist/development/IntlContext-BKfsnzBx.js\");\n__webpack_require__(/*! intl-messageformat */ \"(ssr)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = createFormatter.resolveNamespace(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"There is no `timeZone` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl-docs.vercel.app/docs/configuration#time-zone\" ));\n  }\n  const translate = React.useMemo(() => createFormatter.createBaseTranslator({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    defaultTranslationValues,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, defaultTranslationValues, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = _useLocale.useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? \"!.\".concat(namespace) : '!', '!');\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * Reading the current date via `new Date()` in components should be avoided, as\n * it causes components to be impure and can lead to flaky tests. Instead, this\n * hook can be used.\n *\n * By default, it returns the time when the component mounts. If `updateInterval`\n * is specified, the value will be updated based on the interval.\n *\n * You can however also return a static value from this hook, if you\n * configure the `now` parameter on the context provider. Note however,\n * that if `updateInterval` is configured in this case, the component\n * will initialize with the global value, but will afterwards update\n * continuously based on the interval.\n *\n * For unit tests, this can be mocked to a constant value. For end-to-end\n * testing, an environment parameter can be passed to the `now` parameter\n * of the provider to mock this to a static value.\n */\nfunction useNow(options) {\n  const updateInterval = options === null || options === void 0 ? void 0 : options.updateInterval;\n  const {\n    now: globalNow\n  } = _useLocale.useIntlContext();\n  const [now, setNow] = React.useState(globalNow || getNow());\n  React.useEffect(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return _useLocale.useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = _useLocale.useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl-docs.vercel.app/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = _useLocale.useIntlContext();\n  return React.useMemo(() => createFormatter.createFormatter({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\nexports.IntlProvider = _IntlProvider.IntlProvider;\nexports.useLocale = _useLocale.useLocale;\nexports.useFormatter = useFormatter;\nexports.useMessages = useMessages;\nexports.useNow = useNow;\nexports.useTimeZone = useTimeZone;\nexports.useTranslations = useTranslations;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/development/react.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/use-intl/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/use-intl/dist/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./development/index.js */ \"(ssr)/./node_modules/use-intl/dist/development/index.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUUsRUFFMUMsQ0FBQztBQUNGLEVBQUUsNkhBQWtEO0FBQ3BEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJvcGVydHktcGxhemEvLi9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9pbmRleC5qcz8xNmEwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL3Byb2R1Y3Rpb24vaW5kZXguanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kZXZlbG9wbWVudC9pbmRleC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-intl/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/core.js":
/*!********************************************************!*\
  !*** ./node_modules/use-intl/dist/development/core.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\nvar createFormatter = __webpack_require__(/*! ./createFormatter-D2v4ATzl.js */ \"(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\");\n__webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n__webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\n__webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n\nfunction createTranslatorImpl(_ref, namespacePrefix) {\n  let {\n    messages,\n    namespace,\n    ...rest\n  } = _ref;\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = createFormatter.resolveNamespace(namespace, namespacePrefix);\n  return createFormatter.createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator(_ref) {\n  let {\n    _cache = initializeConfig.createCache(),\n    _formatters = initializeConfig.createIntlFormatters(_cache),\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    messages,\n    namespace,\n    onError = initializeConfig.defaultOnError,\n    ...rest\n  } = _ref;\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? \"!.\".concat(namespace) : '!'\n  }, '!');\n}\n\nexports.IntlError = initializeConfig.IntlError;\nexports.IntlErrorCode = initializeConfig.IntlErrorCode;\nexports._createCache = initializeConfig.createCache;\nexports._createIntlFormatters = initializeConfig.createIntlFormatters;\nexports.initializeConfig = initializeConfig.initializeConfig;\nexports.createFormatter = createFormatter.createFormatter;\nexports.createTranslator = createTranslator;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js":
/*!****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar IntlMessageFormat = __webpack_require__(/*! intl-messageformat */ \"(rsc)/./node_modules/intl-messageformat/lib/index.js\");\nvar React = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\nvar initializeConfig = __webpack_require__(/*! ./initializeConfig-BhfMSHP7.js */ \"(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\");\n\nfunction _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }\n\nvar IntlMessageFormat__default = /*#__PURE__*/_interopDefault(IntlMessageFormat);\n\nfunction setTimeZoneInFormats(formats, timeZone) {\n  if (!formats) return formats;\n\n  // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n  // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n  return Object.keys(formats).reduce((acc, key) => {\n    acc[key] = {\n      timeZone,\n      ...formats[key]\n    };\n    return acc;\n  }, {});\n}\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(formats, timeZone) {\n  const formatsWithTimeZone = timeZone ? {\n    ...formats,\n    dateTime: setTimeZoneInFormats(formats.dateTime, timeZone)\n  } : formats;\n  const mfDateDefaults = IntlMessageFormat__default.default.formats.date;\n  const defaultDateFormats = timeZone ? setTimeZoneInFormats(mfDateDefaults, timeZone) : mfDateDefaults;\n  const mfTimeDefaults = IntlMessageFormat__default.default.formats.time;\n  const defaultTimeFormats = timeZone ? setTimeZoneInFormats(mfTimeDefaults, timeZone) : mfTimeDefaults;\n  return {\n    ...formatsWithTimeZone,\n    date: {\n      ...defaultDateFormats,\n      ...formatsWithTimeZone.dateTime\n    },\n    time: {\n      ...defaultTimeFormats,\n      ...formatsWithTimeZone.dateTime\n    }\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = initializeConfig.memoFn(function () {\n    return new IntlMessageFormat__default.default(arguments.length <= 0 ? undefined : arguments[0], arguments.length <= 1 ? undefined : arguments[1], arguments.length <= 2 ? undefined : arguments[2], {\n      formatters: intlFormatters,\n      ...(arguments.length <= 3 ? undefined : arguments[3])\n    });\n  }, cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = initializeConfig.joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(\"No messages available at `\".concat(namespace, \"`.\") );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(\"Could not resolve `\".concat(fullKey, \"` in messages for locale `\").concat(locale, \"`.\") );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  if (Object.keys(values).length === 0) return undefined;\n\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/React.isValidElement(result) ? /*#__PURE__*/React.cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace) {\n  let onError = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : initializeConfig.defaultOnError;\n  try {\n    if (!messages) {\n      throw new Error(\"No messages were configured on the provider.\" );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(\"No messages for namespace `\".concat(namespace, \"` found.\") );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  if (values) return undefined;\n  const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n\n  // Placeholders can be in the message if there are default values,\n  // or if the user has forgotten to provide values. In the latter\n  // case we need to compile the message to receive an error.\n  const hasPlaceholders = /<|{/.test(unescapedMessage);\n  if (!hasPlaceholders) {\n    return unescapedMessage;\n  }\n  return undefined;\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl(_ref) {\n  let {\n    cache,\n    defaultTranslationValues,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback = initializeConfig.defaultGetMessageFallback,\n    locale,\n    messagesOrError,\n    namespace,\n    onError,\n    timeZone\n  } = _ref;\n  const hasMessagesError = messagesOrError instanceof initializeConfig.IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new initializeConfig.IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = initializeConfig.IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an array, but only strings are supported. See https://next-intl-docs.vercel.app/docs/usage/messages#arrays-of-messages\");\n        }\n      } else {\n        code = initializeConfig.IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = \"Message at `\".concat(initializeConfig.joinPath(namespace, key), \"` resolved to an object, but only strings are supported. Use a `.` to retrieve nested messages. See https://next-intl-docs.vercel.app/docs/usage/messages#structuring-messages\");\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat({\n        ...globalFormats,\n        ...formats\n      }, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? \" (\".concat(thrownError.originalMessage, \")\") : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      prepareTranslationValues({\n        ...defaultTranslationValues,\n        ...values\n      }));\n      if (formattedMessage == null) {\n        throw new Error(\"Unable to format `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages') );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/React.isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.INVALID_MESSAGE, \"The message `\".concat(key, \"` in \").concat(namespace ? \"namespace `\".concat(namespace, \"`\") : 'messages', \" didn't resolve to a string. If you want to format rich text, use `t.rich` instead.\") );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n\n    // When only string chunks are provided to the parser, only\n    // strings should be returned here. Note that we need a runtime\n    // check for this since rich text values could be accidentally\n    // inherited from `defaultTranslationValues`.\n    if (typeof result !== 'string') {\n      const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\" );\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, initializeConfig.IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch (_unused) {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(_ref) {\n  let {\n    _cache: cache = initializeConfig.createCache(),\n    _formatters: formatters = initializeConfig.createIntlFormatters(cache),\n    formats,\n    locale,\n    now: globalNow,\n    onError = initializeConfig.defaultOnError,\n    timeZone: globalTimeZone\n  } = _ref;\n  function applyTimeZone(options) {\n    var _options;\n    if (!((_options = options) !== null && _options !== void 0 && _options.timeZone)) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `timeZone` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#time-zone\" ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats === null || typeFormats === void 0 ? void 0 : typeFormats[formatName];\n      if (!options) {\n        const error = new initializeConfig.IntlError(initializeConfig.IntlErrorCode.MISSING_FORMAT, \"Format `\".concat(formatName, \"` is not available. You can configure it on the provider or provide custom options.\") );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions);\n    } catch (_unused) {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  value,\n  /** If a time zone is supplied, the `value` is converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(/** If a number is supplied, this is interpreted as a UTC timestamp. */\n  start, /** If a number is supplied, this is interpreted as a UTC timestamp. */\n  end,\n  /** If a time zone is supplied, the values are converted to that time zone.\n   * Otherwise the user time zone will be used. */\n  formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions) {\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    if (globalNow) {\n      return globalNow;\n    } else {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.ENVIRONMENT_FALLBACK, \"The `now` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl-docs.vercel.app/docs/configuration#now\" ));\n      return new Date();\n    }\n  }\n  function relativeTime(/** The date time that needs to be formatted. */\n  date, /** The reference point in time to which `date` will be formatted in relation to.  */\n  nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new initializeConfig.IntlError(initializeConfig.IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, formats === null || formats === void 0 ? void 0 : formats.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nexports.createBaseTranslator = createBaseTranslator;\nexports.createFormatter = createFormatter;\nexports.resolveNamespace = resolveNamespace;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/createFormatter-D2v4ATzl.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nvar fastMemoize = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != typeof t || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != typeof i) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == typeof i ? i : i + \"\";\n}\n\nlet IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}({});\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    _defineProperty(this, \"code\", void 0);\n    _defineProperty(this, \"originalMessage\", void 0);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nfunction joinPath() {\n  for (var _len = arguments.length, parts = new Array(_len), _key = 0; _key < _len; _key++) {\n    parts[_key] = arguments[_key];\n  }\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return fastMemoize.memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: fastMemoize.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return new ConstructorFn(...args);\n  }, cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(_ref => {\n    let [key, messageOrMessages] = _ref;\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += \" (at \".concat(parentPath, \")\");\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, \"Namespace keys can not contain the character \\\".\\\" as this is used to express nesting. Please remove it or replace it with another character.\\n\\nInvalid \".concat(invalidKeyLabels.length === 1 ? 'key' : 'keys', \": \").concat(invalidKeyLabels.join(', '), \"\\n\\nIf you're migrating from a flat structure, you can convert your messages as follows:\\n\\nimport {set} from \\\"lodash\\\";\\n\\nconst input = {\\n  \\\"one.one\\\": \\\"1.1\\\",\\n  \\\"one.two\\\": \\\"1.2\\\",\\n  \\\"two.one.one\\\": \\\"2.1.1\\\"\\n};\\n\\nconst output = Object.entries(input).reduce(\\n  (acc, [key, value]) => set(acc, key, value),\\n  {}\\n);\\n\\n// Output:\\n//\\n// {\\n//   \\\"one\\\": {\\n//     \\\"one\\\": \\\"1.1\\\",\\n//     \\\"two\\\": \\\"1.2\\\"\\n//   },\\n//   \\\"two\\\": {\\n//     \\\"one\\\": {\\n//       \\\"one\\\": \\\"2.1.1\\\"\\n//     }\\n//   }\\n// }\\n\") ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig(_ref) {\n  let {\n    getMessageFallback,\n    messages,\n    onError,\n    ...rest\n  } = _ref;\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    messages,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexports.IntlError = IntlError;\nexports.IntlErrorCode = IntlErrorCode;\nexports.createCache = createCache;\nexports.createIntlFormatters = createIntlFormatters;\nexports.defaultGetMessageFallback = defaultGetMessageFallback;\nexports.defaultOnError = defaultOnError;\nexports.initializeConfig = initializeConfig;\nexports.joinPath = joinPath;\nexports.memoFn = memoFn;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/use-intl/dist/development/initializeConfig-BhfMSHP7.js\n");

/***/ })

};
;