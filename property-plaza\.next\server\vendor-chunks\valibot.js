"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/valibot";
exports.ids = ["vendor-chunks/valibot"];
exports.modules = {

/***/ "(ssr)/./node_modules/valibot/dist/index.js":
/*!********************************************!*\
  !*** ./node_modules/valibot/dist/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BIC_REGEX: () => (/* binding */ BIC_REGEX),\n/* harmony export */   CUID2_REGEX: () => (/* binding */ CUID2_REGEX),\n/* harmony export */   DECIMAL_REGEX: () => (/* binding */ DECIMAL_REGEX),\n/* harmony export */   EMAIL_REGEX: () => (/* binding */ EMAIL_REGEX),\n/* harmony export */   EMOJI_REGEX: () => (/* binding */ EMOJI_REGEX),\n/* harmony export */   HEXADECIMAL_REGEX: () => (/* binding */ HEXADECIMAL_REGEX),\n/* harmony export */   HEX_COLOR_REGEX: () => (/* binding */ HEX_COLOR_REGEX),\n/* harmony export */   IMEI_REGEX: () => (/* binding */ IMEI_REGEX),\n/* harmony export */   IPV4_REGEX: () => (/* binding */ IPV4_REGEX),\n/* harmony export */   IPV6_REGEX: () => (/* binding */ IPV6_REGEX),\n/* harmony export */   IP_REGEX: () => (/* binding */ IP_REGEX),\n/* harmony export */   ISO_DATE_REGEX: () => (/* binding */ ISO_DATE_REGEX),\n/* harmony export */   ISO_DATE_TIME_REGEX: () => (/* binding */ ISO_DATE_TIME_REGEX),\n/* harmony export */   ISO_TIMESTAMP_REGEX: () => (/* binding */ ISO_TIMESTAMP_REGEX),\n/* harmony export */   ISO_TIME_REGEX: () => (/* binding */ ISO_TIME_REGEX),\n/* harmony export */   ISO_TIME_SECOND_REGEX: () => (/* binding */ ISO_TIME_SECOND_REGEX),\n/* harmony export */   ISO_WEEK_REGEX: () => (/* binding */ ISO_WEEK_REGEX),\n/* harmony export */   MAC48_REGEX: () => (/* binding */ MAC48_REGEX),\n/* harmony export */   MAC64_REGEX: () => (/* binding */ MAC64_REGEX),\n/* harmony export */   MAC_REGEX: () => (/* binding */ MAC_REGEX),\n/* harmony export */   OCTAL_REGEX: () => (/* binding */ OCTAL_REGEX),\n/* harmony export */   ULID_REGEX: () => (/* binding */ ULID_REGEX),\n/* harmony export */   UUID_REGEX: () => (/* binding */ UUID_REGEX),\n/* harmony export */   ValiError: () => (/* binding */ ValiError),\n/* harmony export */   _addIssue: () => (/* binding */ _addIssue),\n/* harmony export */   _isAllowedObjectKey: () => (/* binding */ _isAllowedObjectKey),\n/* harmony export */   _isLuhnAlgo: () => (/* binding */ _isLuhnAlgo),\n/* harmony export */   _stringify: () => (/* binding */ _stringify),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   array: () => (/* binding */ array),\n/* harmony export */   arrayAsync: () => (/* binding */ arrayAsync),\n/* harmony export */   bic: () => (/* binding */ bic),\n/* harmony export */   bigint: () => (/* binding */ bigint),\n/* harmony export */   blob: () => (/* binding */ blob),\n/* harmony export */   boolean: () => (/* binding */ boolean),\n/* harmony export */   brand: () => (/* binding */ brand),\n/* harmony export */   bytes: () => (/* binding */ bytes),\n/* harmony export */   check: () => (/* binding */ check),\n/* harmony export */   checkAsync: () => (/* binding */ checkAsync),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   creditCard: () => (/* binding */ creditCard),\n/* harmony export */   cuid2: () => (/* binding */ cuid2),\n/* harmony export */   custom: () => (/* binding */ custom),\n/* harmony export */   customAsync: () => (/* binding */ customAsync),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   decimal: () => (/* binding */ decimal),\n/* harmony export */   deleteGlobalConfig: () => (/* binding */ deleteGlobalConfig),\n/* harmony export */   deleteGlobalMessage: () => (/* binding */ deleteGlobalMessage),\n/* harmony export */   deleteSchemaMessage: () => (/* binding */ deleteSchemaMessage),\n/* harmony export */   deleteSpecificMessage: () => (/* binding */ deleteSpecificMessage),\n/* harmony export */   email: () => (/* binding */ email),\n/* harmony export */   emoji: () => (/* binding */ emoji),\n/* harmony export */   empty: () => (/* binding */ empty),\n/* harmony export */   endsWith: () => (/* binding */ endsWith),\n/* harmony export */   entriesFromList: () => (/* binding */ entriesFromList),\n/* harmony export */   enum_: () => (/* binding */ enum_),\n/* harmony export */   every: () => (/* binding */ every),\n/* harmony export */   excludes: () => (/* binding */ excludes),\n/* harmony export */   fallback: () => (/* binding */ fallback),\n/* harmony export */   fallbackAsync: () => (/* binding */ fallbackAsync),\n/* harmony export */   finite: () => (/* binding */ finite),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   forward: () => (/* binding */ forward),\n/* harmony export */   forwardAsync: () => (/* binding */ forwardAsync),\n/* harmony export */   getDefault: () => (/* binding */ getDefault),\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   getDefaultsAsync: () => (/* binding */ getDefaultsAsync),\n/* harmony export */   getDotPath: () => (/* binding */ getDotPath),\n/* harmony export */   getFallback: () => (/* binding */ getFallback),\n/* harmony export */   getFallbacks: () => (/* binding */ getFallbacks),\n/* harmony export */   getFallbacksAsync: () => (/* binding */ getFallbacksAsync),\n/* harmony export */   getGlobalConfig: () => (/* binding */ getGlobalConfig),\n/* harmony export */   getGlobalMessage: () => (/* binding */ getGlobalMessage),\n/* harmony export */   getSchemaMessage: () => (/* binding */ getSchemaMessage),\n/* harmony export */   getSpecificMessage: () => (/* binding */ getSpecificMessage),\n/* harmony export */   hash: () => (/* binding */ hash),\n/* harmony export */   hexColor: () => (/* binding */ hexColor),\n/* harmony export */   hexadecimal: () => (/* binding */ hexadecimal),\n/* harmony export */   imei: () => (/* binding */ imei),\n/* harmony export */   includes: () => (/* binding */ includes),\n/* harmony export */   instance: () => (/* binding */ instance),\n/* harmony export */   integer: () => (/* binding */ integer),\n/* harmony export */   intersect: () => (/* binding */ intersect),\n/* harmony export */   intersectAsync: () => (/* binding */ intersectAsync),\n/* harmony export */   ip: () => (/* binding */ ip),\n/* harmony export */   ipv4: () => (/* binding */ ipv4),\n/* harmony export */   ipv6: () => (/* binding */ ipv6),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   isOfKind: () => (/* binding */ isOfKind),\n/* harmony export */   isOfType: () => (/* binding */ isOfType),\n/* harmony export */   isValiError: () => (/* binding */ isValiError),\n/* harmony export */   isoDate: () => (/* binding */ isoDate),\n/* harmony export */   isoDateTime: () => (/* binding */ isoDateTime),\n/* harmony export */   isoTime: () => (/* binding */ isoTime),\n/* harmony export */   isoTimeSecond: () => (/* binding */ isoTimeSecond),\n/* harmony export */   isoTimestamp: () => (/* binding */ isoTimestamp),\n/* harmony export */   isoWeek: () => (/* binding */ isoWeek),\n/* harmony export */   keyof: () => (/* binding */ keyof),\n/* harmony export */   lazy: () => (/* binding */ lazy),\n/* harmony export */   lazyAsync: () => (/* binding */ lazyAsync),\n/* harmony export */   length: () => (/* binding */ length),\n/* harmony export */   literal: () => (/* binding */ literal),\n/* harmony export */   looseObject: () => (/* binding */ looseObject),\n/* harmony export */   looseObjectAsync: () => (/* binding */ looseObjectAsync),\n/* harmony export */   looseTuple: () => (/* binding */ looseTuple),\n/* harmony export */   looseTupleAsync: () => (/* binding */ looseTupleAsync),\n/* harmony export */   mac: () => (/* binding */ mac),\n/* harmony export */   mac48: () => (/* binding */ mac48),\n/* harmony export */   mac64: () => (/* binding */ mac64),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   mapAsync: () => (/* binding */ mapAsync),\n/* harmony export */   maxBytes: () => (/* binding */ maxBytes),\n/* harmony export */   maxLength: () => (/* binding */ maxLength),\n/* harmony export */   maxSize: () => (/* binding */ maxSize),\n/* harmony export */   maxValue: () => (/* binding */ maxValue),\n/* harmony export */   mimeType: () => (/* binding */ mimeType),\n/* harmony export */   minBytes: () => (/* binding */ minBytes),\n/* harmony export */   minLength: () => (/* binding */ minLength),\n/* harmony export */   minSize: () => (/* binding */ minSize),\n/* harmony export */   minValue: () => (/* binding */ minValue),\n/* harmony export */   multipleOf: () => (/* binding */ multipleOf),\n/* harmony export */   nan: () => (/* binding */ nan),\n/* harmony export */   never: () => (/* binding */ never),\n/* harmony export */   nonEmpty: () => (/* binding */ nonEmpty),\n/* harmony export */   nonNullable: () => (/* binding */ nonNullable),\n/* harmony export */   nonNullableAsync: () => (/* binding */ nonNullableAsync),\n/* harmony export */   nonNullish: () => (/* binding */ nonNullish),\n/* harmony export */   nonNullishAsync: () => (/* binding */ nonNullishAsync),\n/* harmony export */   nonOptional: () => (/* binding */ nonOptional),\n/* harmony export */   nonOptionalAsync: () => (/* binding */ nonOptionalAsync),\n/* harmony export */   notBytes: () => (/* binding */ notBytes),\n/* harmony export */   notLength: () => (/* binding */ notLength),\n/* harmony export */   notSize: () => (/* binding */ notSize),\n/* harmony export */   notValue: () => (/* binding */ notValue),\n/* harmony export */   null_: () => (/* binding */ null_),\n/* harmony export */   nullable: () => (/* binding */ nullable),\n/* harmony export */   nullableAsync: () => (/* binding */ nullableAsync),\n/* harmony export */   nullish: () => (/* binding */ nullish),\n/* harmony export */   nullishAsync: () => (/* binding */ nullishAsync),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   object: () => (/* binding */ object),\n/* harmony export */   objectAsync: () => (/* binding */ objectAsync),\n/* harmony export */   objectWithRest: () => (/* binding */ objectWithRest),\n/* harmony export */   objectWithRestAsync: () => (/* binding */ objectWithRestAsync),\n/* harmony export */   octal: () => (/* binding */ octal),\n/* harmony export */   omit: () => (/* binding */ omit),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   optionalAsync: () => (/* binding */ optionalAsync),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseAsync: () => (/* binding */ parseAsync),\n/* harmony export */   parser: () => (/* binding */ parser),\n/* harmony export */   parserAsync: () => (/* binding */ parserAsync),\n/* harmony export */   partial: () => (/* binding */ partial),\n/* harmony export */   partialAsync: () => (/* binding */ partialAsync),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   picklist: () => (/* binding */ picklist),\n/* harmony export */   pipe: () => (/* binding */ pipe),\n/* harmony export */   pipeAsync: () => (/* binding */ pipeAsync),\n/* harmony export */   readonly: () => (/* binding */ readonly),\n/* harmony export */   record: () => (/* binding */ record),\n/* harmony export */   recordAsync: () => (/* binding */ recordAsync),\n/* harmony export */   regex: () => (/* binding */ regex),\n/* harmony export */   required: () => (/* binding */ required),\n/* harmony export */   requiredAsync: () => (/* binding */ requiredAsync),\n/* harmony export */   safeInteger: () => (/* binding */ safeInteger),\n/* harmony export */   safeParse: () => (/* binding */ safeParse),\n/* harmony export */   safeParseAsync: () => (/* binding */ safeParseAsync),\n/* harmony export */   safeParser: () => (/* binding */ safeParser),\n/* harmony export */   safeParserAsync: () => (/* binding */ safeParserAsync),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   setAsync: () => (/* binding */ setAsync),\n/* harmony export */   setGlobalConfig: () => (/* binding */ setGlobalConfig),\n/* harmony export */   setGlobalMessage: () => (/* binding */ setGlobalMessage),\n/* harmony export */   setSchemaMessage: () => (/* binding */ setSchemaMessage),\n/* harmony export */   setSpecificMessage: () => (/* binding */ setSpecificMessage),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   some: () => (/* binding */ some),\n/* harmony export */   startsWith: () => (/* binding */ startsWith),\n/* harmony export */   strictObject: () => (/* binding */ strictObject),\n/* harmony export */   strictObjectAsync: () => (/* binding */ strictObjectAsync),\n/* harmony export */   strictTuple: () => (/* binding */ strictTuple),\n/* harmony export */   strictTupleAsync: () => (/* binding */ strictTupleAsync),\n/* harmony export */   string: () => (/* binding */ string),\n/* harmony export */   symbol: () => (/* binding */ symbol),\n/* harmony export */   toLowerCase: () => (/* binding */ toLowerCase),\n/* harmony export */   toMaxValue: () => (/* binding */ toMaxValue),\n/* harmony export */   toMinValue: () => (/* binding */ toMinValue),\n/* harmony export */   toUpperCase: () => (/* binding */ toUpperCase),\n/* harmony export */   transform: () => (/* binding */ transform),\n/* harmony export */   transformAsync: () => (/* binding */ transformAsync),\n/* harmony export */   trim: () => (/* binding */ trim),\n/* harmony export */   trimEnd: () => (/* binding */ trimEnd),\n/* harmony export */   trimStart: () => (/* binding */ trimStart),\n/* harmony export */   tuple: () => (/* binding */ tuple),\n/* harmony export */   tupleAsync: () => (/* binding */ tupleAsync),\n/* harmony export */   tupleWithRest: () => (/* binding */ tupleWithRest),\n/* harmony export */   tupleWithRestAsync: () => (/* binding */ tupleWithRestAsync),\n/* harmony export */   ulid: () => (/* binding */ ulid),\n/* harmony export */   undefined_: () => (/* binding */ undefined_),\n/* harmony export */   union: () => (/* binding */ union),\n/* harmony export */   unionAsync: () => (/* binding */ unionAsync),\n/* harmony export */   unknown: () => (/* binding */ unknown),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   url: () => (/* binding */ url),\n/* harmony export */   uuid: () => (/* binding */ uuid),\n/* harmony export */   value: () => (/* binding */ value),\n/* harmony export */   variant: () => (/* binding */ variant),\n/* harmony export */   variantAsync: () => (/* binding */ variantAsync),\n/* harmony export */   void_: () => (/* binding */ void_)\n/* harmony export */ });\n// src/regex.ts\nvar BIC_REGEX = /^[A-Z]{6}(?!00)[A-Z\\d]{2}(?:[A-Z\\d]{3})?$/u;\nvar CUID2_REGEX = /^[a-z][\\da-z]*$/u;\nvar DECIMAL_REGEX = /^\\d+$/u;\nvar EMAIL_REGEX = /^[\\w+-]+(?:\\.[\\w+-]+)*@[\\da-z]+(?:[.-][\\da-z]+)*\\.[a-z]{2,}$/iu;\nvar EMOJI_REGEX = /^[\\p{Extended_Pictographic}\\p{Emoji_Component}]+$/u;\nvar HEXADECIMAL_REGEX = /^(?:0h|0x)?[\\da-f]+$/iu;\nvar HEX_COLOR_REGEX = /^#(?:[\\da-f]{3,4}|[\\da-f]{6}|[\\da-f]{8})$/iu;\nvar IMEI_REGEX = /^\\d{15}$|^\\d{2}-\\d{6}-\\d{6}-\\d$/u;\nvar IPV4_REGEX = (\n  // eslint-disable-next-line redos-detector/no-unsafe-regex -- false positive\n  /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$/u\n);\nvar IPV6_REGEX = /^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar IP_REGEX = /^(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])(?:\\.(?:(?:[1-9]|1\\d|2[0-4])?\\d|25[0-5])){3}$|^(?:(?:[\\da-f]{1,4}:){7}[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,7}:|(?:[\\da-f]{1,4}:){1,6}:[\\da-f]{1,4}|(?:[\\da-f]{1,4}:){1,5}(?::[\\da-f]{1,4}){1,2}|(?:[\\da-f]{1,4}:){1,4}(?::[\\da-f]{1,4}){1,3}|(?:[\\da-f]{1,4}:){1,3}(?::[\\da-f]{1,4}){1,4}|(?:[\\da-f]{1,4}:){1,2}(?::[\\da-f]{1,4}){1,5}|[\\da-f]{1,4}:(?::[\\da-f]{1,4}){1,6}|:(?:(?::[\\da-f]{1,4}){1,7}|:)|fe80:(?::[\\da-f]{0,4}){0,4}%[\\da-z]+|::(?:f{4}(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)|(?:[\\da-f]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d)\\.){3}(?:25[0-5]|(?:2[0-4]|1?\\d)?\\d))$/iu;\nvar ISO_DATE_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])$/u;\nvar ISO_DATE_TIME_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_REGEX = /^(?:0\\d|1\\d|2[0-3]):[0-5]\\d$/u;\nvar ISO_TIME_SECOND_REGEX = /^(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}$/u;\nvar ISO_TIMESTAMP_REGEX = /^\\d{4}-(?:0[1-9]|1[0-2])-(?:[12]\\d|0[1-9]|3[01])T(?:0\\d|1\\d|2[0-3])(?::[0-5]\\d){2}(?:\\.\\d{1,9})?(?:Z|[+-](?:0\\d|1\\d|2[0-3])(?::?[0-5]\\d)?)$/u;\nvar ISO_WEEK_REGEX = /^\\d{4}-W(?:0[1-9]|[1-4]\\d|5[0-3])$/u;\nvar MAC48_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$/iu;\nvar MAC64_REGEX = /^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar MAC_REGEX = /^(?:[\\da-f]{2}:){5}[\\da-f]{2}$|^(?:[\\da-f]{2}-){5}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){2}[\\da-f]{4}$|^(?:[\\da-f]{2}:){7}[\\da-f]{2}$|^(?:[\\da-f]{2}-){7}[\\da-f]{2}$|^(?:[\\da-f]{4}\\.){3}[\\da-f]{4}$|^(?:[\\da-f]{4}:){3}[\\da-f]{4}$/iu;\nvar OCTAL_REGEX = /^(?:0o)?[0-7]+$/iu;\nvar ULID_REGEX = /^[\\da-hjkmnp-tv-z]{26}$/iu;\nvar UUID_REGEX = /^[\\da-f]{8}(?:-[\\da-f]{4}){3}-[\\da-f]{12}$/iu;\n\n// src/storages/globalConfig/globalConfig.ts\nvar store;\nfunction setGlobalConfig(config2) {\n  store = { ...store, ...config2 };\n}\nfunction getGlobalConfig(config2) {\n  return {\n    lang: config2?.lang ?? store?.lang,\n    message: config2?.message,\n    abortEarly: config2?.abortEarly ?? store?.abortEarly,\n    abortPipeEarly: config2?.abortPipeEarly ?? store?.abortPipeEarly,\n    skipPipe: config2?.skipPipe\n  };\n}\nfunction deleteGlobalConfig() {\n  store = void 0;\n}\n\n// src/storages/globalMessage/globalMessage.ts\nvar store2;\nfunction setGlobalMessage(message, lang) {\n  if (!store2)\n    store2 = /* @__PURE__ */ new Map();\n  store2.set(lang, message);\n}\nfunction getGlobalMessage(lang) {\n  return store2?.get(lang);\n}\nfunction deleteGlobalMessage(lang) {\n  store2?.delete(lang);\n}\n\n// src/storages/schemaMessage/schemaMessage.ts\nvar store3;\nfunction setSchemaMessage(message, lang) {\n  if (!store3)\n    store3 = /* @__PURE__ */ new Map();\n  store3.set(lang, message);\n}\nfunction getSchemaMessage(lang) {\n  return store3?.get(lang);\n}\nfunction deleteSchemaMessage(lang) {\n  store3?.delete(lang);\n}\n\n// src/storages/specificMessage/specificMessage.ts\nvar store4;\nfunction setSpecificMessage(reference, message, lang) {\n  if (!store4)\n    store4 = /* @__PURE__ */ new Map();\n  if (!store4.get(reference))\n    store4.set(reference, /* @__PURE__ */ new Map());\n  store4.get(reference).set(lang, message);\n}\nfunction getSpecificMessage(reference, lang) {\n  return store4?.get(reference)?.get(lang);\n}\nfunction deleteSpecificMessage(reference, lang) {\n  store4?.get(reference)?.delete(lang);\n}\n\n// src/utils/_stringify/_stringify.ts\nfunction _stringify(input) {\n  let type = typeof input;\n  if (type === \"object\") {\n    type = (input && Object.getPrototypeOf(input)?.constructor?.name) ?? \"null\";\n  }\n  return type === \"string\" ? `\"${input}\"` : type === \"number\" || type === \"bigint\" || type === \"boolean\" ? `${input}` : type;\n}\n\n// src/utils/_addIssue/_addIssue.ts\nfunction _addIssue(context, label, dataset, config2, other) {\n  const input = other && \"input\" in other ? other.input : dataset.value;\n  const expected = other?.expected ?? context.expects;\n  const received = other?.received ?? _stringify(input);\n  const issue = {\n    kind: context.kind,\n    type: context.type,\n    input,\n    expected,\n    received,\n    message: `Invalid ${label}: ${expected ? `Expected ${expected} but r` : \"R\"}eceived ${received}`,\n    // @ts-expect-error\n    requirement: context.requirement,\n    path: other?.path,\n    issues: other?.issues,\n    lang: config2.lang,\n    abortEarly: config2.abortEarly,\n    abortPipeEarly: config2.abortPipeEarly,\n    skipPipe: config2.skipPipe\n  };\n  const isSchema = context.kind === \"schema\";\n  const message = (\n    // @ts-expect-error\n    context.message ?? getSpecificMessage(context.reference, issue.lang) ?? (isSchema ? getSchemaMessage(issue.lang) : null) ?? config2.message ?? getGlobalMessage(issue.lang)\n  );\n  if (message) {\n    issue.message = typeof message === \"function\" ? message(issue) : message;\n  }\n  if (isSchema) {\n    dataset.typed = false;\n  }\n  if (dataset.issues) {\n    dataset.issues.push(issue);\n  } else {\n    dataset.issues = [issue];\n  }\n}\n\n// src/utils/_isAllowedObjectKey/_isAllowedObjectKey.ts\nfunction _isAllowedObjectKey(key) {\n  return key !== \"__proto__\" && key !== \"prototype\" && key !== \"constructor\";\n}\n\n// src/utils/_isLuhnAlgo/_isLuhnAlgo.ts\nvar NON_DIGIT_REGEX = /\\D/gu;\nfunction _isLuhnAlgo(input) {\n  const number2 = input.replace(NON_DIGIT_REGEX, \"\");\n  let length2 = number2.length;\n  let bit = 1;\n  let sum = 0;\n  while (length2) {\n    const value2 = +number2[--length2];\n    bit ^= 1;\n    sum += bit ? [0, 2, 4, 6, 8, 1, 3, 5, 7, 9][value2] : value2;\n  }\n  return sum % 10 === 0;\n}\n\n// src/utils/entriesFromList/entriesFromList.ts\nfunction entriesFromList(list, schema) {\n  const entries = {};\n  for (const key of list) {\n    entries[key] = schema;\n  }\n  return entries;\n}\n\n// src/utils/getDotPath/getDotPath.ts\nfunction getDotPath(issue) {\n  if (issue.path) {\n    let key = \"\";\n    for (const item of issue.path) {\n      if (\"key\" in item && (typeof item.key === \"string\" || typeof item.key === \"number\")) {\n        if (key) {\n          key += `.${item.key}`;\n        } else {\n          key += item.key;\n        }\n      } else {\n        return null;\n      }\n    }\n    return key;\n  }\n  return null;\n}\n\n// src/utils/isOfKind/isOfKind.ts\nfunction isOfKind(kind, object2) {\n  return object2.kind === kind;\n}\n\n// src/utils/isOfType/isOfType.ts\nfunction isOfType(type, object2) {\n  return object2.type === type;\n}\n\n// src/utils/isValiError/isValiError.ts\nfunction isValiError(error) {\n  return error instanceof ValiError;\n}\n\n// src/utils/ValiError/ValiError.ts\nvar ValiError = class extends Error {\n  /**\n   * The error issues.\n   */\n  issues;\n  /**\n   * Creates a Valibot error with useful information.\n   *\n   * @param issues The error issues.\n   */\n  constructor(issues) {\n    super(issues[0].message);\n    this.name = \"ValiError\";\n    this.issues = issues;\n  }\n};\n\n// src/actions/bic/bic.ts\nfunction bic(message) {\n  return {\n    kind: \"validation\",\n    type: \"bic\",\n    reference: bic,\n    async: false,\n    expects: null,\n    requirement: BIC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"BIC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/bytes/bytes.ts\nfunction bytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"bytes\",\n    reference: bytes,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 !== this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/brand/brand.ts\nfunction brand(name) {\n  return {\n    kind: \"transformation\",\n    type: \"brand\",\n    reference: brand,\n    async: false,\n    name,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/check.ts\nfunction check(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: check,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/check/checkAsync.ts\nfunction checkAsync(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"check\",\n    reference: checkAsync,\n    async: true,\n    expects: null,\n    requirement,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.typed && !await this.requirement(dataset.value)) {\n        _addIssue(this, \"input\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/creditCard/creditCard.ts\nvar CREDIT_CARD_REGEX = /^(?:\\d{14,19}|\\d{4}(?: \\d{3,6}){2,4}|\\d{4}(?:-\\d{3,6}){2,4})$/u;\nvar SANITIZE_REGEX = /[- ]/gu;\nvar PROVIDER_REGEX_LIST = [\n  // American Express\n  /^3[47]\\d{13}$/u,\n  // Diners Club\n  /^3(?:0[0-5]|[68]\\d)\\d{11,13}$/u,\n  // Discover\n  /^6(?:011|5\\d{2})\\d{12,15}$/u,\n  // JCB\n  /^(?:2131|1800|35\\d{3})\\d{11}$/u,\n  // Mastercard\n  /^5[1-5]\\d{2}|(?:222\\d|22[3-9]\\d|2[3-6]\\d{2}|27[01]\\d|2720)\\d{12}$/u,\n  // UnionPay\n  /^(?:6[27]\\d{14,17}|81\\d{14,17})$/u,\n  // Visa\n  /^4\\d{12}(?:\\d{3,6})?$/u\n];\nfunction creditCard(message) {\n  return {\n    kind: \"validation\",\n    type: \"credit_card\",\n    reference: creditCard,\n    async: false,\n    expects: null,\n    requirement(input) {\n      let sanitized;\n      return CREDIT_CARD_REGEX.test(input) && // Remove any hyphens and blanks\n      (sanitized = input.replace(SANITIZE_REGEX, \"\")) && // Check if it matches a provider\n      PROVIDER_REGEX_LIST.some((regex2) => regex2.test(sanitized)) && // Check if passes luhn algorithm\n      _isLuhnAlgo(sanitized);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"credit card\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/cuid2/cuid2.ts\nfunction cuid2(message) {\n  return {\n    kind: \"validation\",\n    type: \"cuid2\",\n    reference: cuid2,\n    async: false,\n    expects: null,\n    requirement: CUID2_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"Cuid2\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/decimal/decimal.ts\nfunction decimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"decimal\",\n    reference: decimal,\n    async: false,\n    expects: null,\n    requirement: DECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"decimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/email/email.ts\nfunction email(message) {\n  return {\n    kind: \"validation\",\n    type: \"email\",\n    reference: email,\n    expects: null,\n    async: false,\n    requirement: EMAIL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"email\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/emoji/emoji.ts\nfunction emoji(message) {\n  return {\n    kind: \"validation\",\n    type: \"emoji\",\n    reference: emoji,\n    async: false,\n    expects: null,\n    requirement: EMOJI_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"emoji\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/empty/empty.ts\nfunction empty(message) {\n  return {\n    kind: \"validation\",\n    type: \"empty\",\n    reference: empty,\n    async: false,\n    expects: \"0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/endsWith/endsWith.ts\nfunction endsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"ends_with\",\n    reference: endsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.endsWith(this.requirement)) {\n        _addIssue(this, \"end\", dataset, config2, {\n          received: `\"${dataset.value.slice(-this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/every/every.ts\nfunction every(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"every\",\n    reference: every,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.every(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/excludes/excludes.ts\nfunction excludes(requirement, message) {\n  const received = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"excludes\",\n    reference: excludes,\n    async: false,\n    expects: `!${received}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, { received });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/finite/finite.ts\nfunction finite(message) {\n  return {\n    kind: \"validation\",\n    type: \"finite\",\n    reference: finite,\n    async: false,\n    expects: null,\n    requirement: Number.isFinite,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"finite\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hash/hash.ts\nvar HASH_LENGTHS = {\n  md4: 32,\n  md5: 32,\n  sha1: 40,\n  sha256: 64,\n  sha384: 96,\n  sha512: 128,\n  ripemd128: 32,\n  ripemd160: 40,\n  tiger128: 32,\n  tiger160: 40,\n  tiger192: 48,\n  crc32: 8,\n  crc32b: 8,\n  adler32: 8\n};\nfunction hash(types, message) {\n  return {\n    kind: \"validation\",\n    type: \"hash\",\n    reference: hash,\n    expects: null,\n    async: false,\n    requirement: RegExp(\n      types.map((type) => `^[a-f0-9]{${HASH_LENGTHS[type]}}$`).join(\"|\"),\n      \"iu\"\n    ),\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hash\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexadecimal/hexadecimal.ts\nfunction hexadecimal(message) {\n  return {\n    kind: \"validation\",\n    type: \"hexadecimal\",\n    reference: hexadecimal,\n    async: false,\n    expects: null,\n    requirement: HEXADECIMAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hexadecimal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/hexColor/hexColor.ts\nfunction hexColor(message) {\n  return {\n    kind: \"validation\",\n    type: \"hex_color\",\n    reference: hexColor,\n    async: false,\n    expects: null,\n    requirement: HEX_COLOR_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"hex color\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/imei/imei.ts\nfunction imei(message) {\n  return {\n    kind: \"validation\",\n    type: \"imei\",\n    reference: imei,\n    async: false,\n    expects: null,\n    requirement(input) {\n      return IMEI_REGEX.test(input) && _isLuhnAlgo(input);\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"IMEI\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/includes/includes.ts\nfunction includes(requirement, message) {\n  const expects = _stringify(requirement);\n  return {\n    kind: \"validation\",\n    type: \"includes\",\n    reference: includes,\n    async: false,\n    expects,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.includes(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2, {\n          received: `!${expects}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/integer/integer.ts\nfunction integer(message) {\n  return {\n    kind: \"validation\",\n    type: \"integer\",\n    reference: integer,\n    async: false,\n    expects: null,\n    requirement: Number.isInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ip/ip.ts\nfunction ip(message) {\n  return {\n    kind: \"validation\",\n    type: \"ip\",\n    reference: ip,\n    async: false,\n    expects: null,\n    requirement: IP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IP\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv4/ipv4.ts\nfunction ipv4(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv4\",\n    reference: ipv4,\n    async: false,\n    expects: null,\n    requirement: IPV4_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv4\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ipv6/ipv6.ts\nfunction ipv6(message) {\n  return {\n    kind: \"validation\",\n    type: \"ipv6\",\n    reference: ipv6,\n    async: false,\n    expects: null,\n    requirement: IPV6_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"IPv6\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDate/isoDate.ts\nfunction isoDate(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date\",\n    reference: isoDate,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoDateTime/isoDateTime.ts\nfunction isoDateTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_date_time\",\n    reference: isoDateTime,\n    async: false,\n    expects: null,\n    requirement: ISO_DATE_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"date-time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTime/isoTime.ts\nfunction isoTime(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time\",\n    reference: isoTime,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimeSecond/isoTimeSecond.ts\nfunction isoTimeSecond(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_time_second\",\n    reference: isoTimeSecond,\n    async: false,\n    expects: null,\n    requirement: ISO_TIME_SECOND_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"time-second\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoTimestamp/isoTimestamp.ts\nfunction isoTimestamp(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_timestamp\",\n    reference: isoTimestamp,\n    async: false,\n    expects: null,\n    requirement: ISO_TIMESTAMP_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"timestamp\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/isoWeek/isoWeek.ts\nfunction isoWeek(message) {\n  return {\n    kind: \"validation\",\n    type: \"iso_week\",\n    reference: isoWeek,\n    async: false,\n    expects: null,\n    requirement: ISO_WEEK_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"week\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/length/length.ts\nfunction length(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"length\",\n    reference: length,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length !== this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac/mac.ts\nfunction mac(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac\",\n    reference: mac,\n    async: false,\n    expects: null,\n    requirement: MAC_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac48/mac48.ts\nfunction mac48(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac48\",\n    reference: mac48,\n    async: false,\n    expects: null,\n    requirement: MAC48_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"48-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mac64/mac64.ts\nfunction mac64(message) {\n  return {\n    kind: \"validation\",\n    type: \"mac64\",\n    reference: mac64,\n    async: false,\n    expects: null,\n    requirement: MAC64_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"64-bit MAC\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxBytes/maxBytes.ts\nfunction maxBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_bytes\",\n    reference: maxBytes,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 > this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxLength/maxLength.ts\nfunction maxLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_length\",\n    reference: maxLength,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length > this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxSize/maxSize.ts\nfunction maxSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_size\",\n    reference: maxSize,\n    async: false,\n    expects: `<=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size > this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/maxValue/maxValue.ts\nfunction maxValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"max_value\",\n    reference: maxValue,\n    async: false,\n    expects: `<=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value > this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/mimeType/mimeType.ts\nfunction mimeType(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"mime_type\",\n    reference: mimeType,\n    async: false,\n    expects: requirement.map((option) => `\"${option}\"`).join(\" | \") || \"never\",\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.includes(dataset.value.type)) {\n        _addIssue(this, \"MIME type\", dataset, config2, {\n          received: `\"${dataset.value.type}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minBytes/minBytes.ts\nfunction minBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_bytes\",\n    reference: minBytes,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 < this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minLength/minLength.ts\nfunction minLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_length\",\n    reference: minLength,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length < this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minSize/minSize.ts\nfunction minSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_size\",\n    reference: minSize,\n    async: false,\n    expects: `>=${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size < this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/minValue/minValue.ts\nfunction minValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"min_value\",\n    reference: minValue,\n    async: false,\n    expects: `>=${requirement instanceof Date ? requirement.toJSON() : _stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value < this.requirement) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/multipleOf/multipleOf.ts\nfunction multipleOf(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"multiple_of\",\n    reference: multipleOf,\n    async: false,\n    expects: `%${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value % this.requirement !== 0) {\n        _addIssue(this, \"multiple\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/nonEmpty/nonEmpty.ts\nfunction nonEmpty(message) {\n  return {\n    kind: \"validation\",\n    type: \"non_empty\",\n    reference: nonEmpty,\n    async: false,\n    expects: \"!0\",\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === 0) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: \"0\"\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notBytes/notBytes.ts\nfunction notBytes(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_bytes\",\n    reference: notBytes,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed) {\n        const length2 = new TextEncoder().encode(dataset.value).length;\n        if (length2 === this.requirement) {\n          _addIssue(this, \"bytes\", dataset, config2, {\n            received: `${length2}`\n          });\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notLength/notLength.ts\nfunction notLength(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_length\",\n    reference: notLength,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.length === this.requirement) {\n        _addIssue(this, \"length\", dataset, config2, {\n          received: `${dataset.value.length}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notSize/notSize.ts\nfunction notSize(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_size\",\n    reference: notSize,\n    async: false,\n    expects: `!${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size === this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/notValue/notValue.ts\nfunction notValue(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"not_value\",\n    reference: notValue,\n    async: false,\n    expects: requirement instanceof Date ? `!${requirement.toJSON()}` : `!${_stringify(requirement)}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && this.requirement <= dataset.value && this.requirement >= dataset.value) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/octal/octal.ts\nfunction octal(message) {\n  return {\n    kind: \"validation\",\n    type: \"octal\",\n    reference: octal,\n    async: false,\n    expects: null,\n    requirement: OCTAL_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"octal\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/readonly/readonly.ts\nfunction readonly() {\n  return {\n    kind: \"transformation\",\n    type: \"readonly\",\n    reference: readonly,\n    async: false,\n    _run(dataset) {\n      return dataset;\n    }\n  };\n}\n\n// src/actions/regex/regex.ts\nfunction regex(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"regex\",\n    reference: regex,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"format\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/safeInteger/safeInteger.ts\nfunction safeInteger(message) {\n  return {\n    kind: \"validation\",\n    type: \"safe_integer\",\n    reference: safeInteger,\n    async: false,\n    expects: null,\n    requirement: Number.isSafeInteger,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"safe integer\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/size/size.ts\nfunction size(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"size\",\n    reference: size,\n    async: false,\n    expects: `${requirement}`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && dataset.value.size !== this.requirement) {\n        _addIssue(this, \"size\", dataset, config2, {\n          received: `${dataset.value.size}`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/some/some.ts\nfunction some(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"some\",\n    reference: some,\n    async: false,\n    expects: null,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.some(this.requirement)) {\n        _addIssue(this, \"content\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/startsWith/startsWith.ts\nfunction startsWith(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"starts_with\",\n    reference: startsWith,\n    async: false,\n    expects: `\"${requirement}\"`,\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !dataset.value.startsWith(this.requirement)) {\n        _addIssue(this, \"start\", dataset, config2, {\n          received: `\"${dataset.value.slice(0, this.requirement.length)}\"`\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toLowerCase/toLowerCase.ts\nfunction toLowerCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_lower_case\",\n    reference: toLowerCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toLowerCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMaxValue/toMaxValue.ts\nfunction toMaxValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_max_value\",\n    reference: toMaxValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value > this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toMinValue/toMinValue.ts\nfunction toMinValue(requirement) {\n  return {\n    kind: \"transformation\",\n    type: \"to_min_value\",\n    reference: toMinValue,\n    async: false,\n    requirement,\n    _run(dataset) {\n      dataset.value = dataset.value < this.requirement ? this.requirement : dataset.value;\n      return dataset;\n    }\n  };\n}\n\n// src/actions/toUpperCase/toUpperCase.ts\nfunction toUpperCase() {\n  return {\n    kind: \"transformation\",\n    type: \"to_upper_case\",\n    reference: toUpperCase,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.toUpperCase();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transform.ts\nfunction transform(action) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transform,\n    async: false,\n    action,\n    _run(dataset) {\n      dataset.value = action(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/transform/transformAsync.ts\nfunction transformAsync(action) {\n  return {\n    kind: \"transformation\",\n    type: \"transform\",\n    reference: transformAsync,\n    async: true,\n    action,\n    async _run(dataset) {\n      dataset.value = await action(dataset.value);\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trim/trim.ts\nfunction trim() {\n  return {\n    kind: \"transformation\",\n    type: \"trim\",\n    reference: trim,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trim();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimEnd/trimEnd.ts\nfunction trimEnd() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_end\",\n    reference: trimEnd,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimEnd();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/trimStart/trimStart.ts\nfunction trimStart() {\n  return {\n    kind: \"transformation\",\n    type: \"trim_start\",\n    reference: trimStart,\n    async: false,\n    _run(dataset) {\n      dataset.value = dataset.value.trimStart();\n      return dataset;\n    }\n  };\n}\n\n// src/actions/ulid/ulid.ts\nfunction ulid(message) {\n  return {\n    kind: \"validation\",\n    type: \"ulid\",\n    reference: ulid,\n    async: false,\n    expects: null,\n    requirement: ULID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"ULID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/url/url.ts\nfunction url(message) {\n  return {\n    kind: \"validation\",\n    type: \"url\",\n    reference: url,\n    async: false,\n    expects: null,\n    requirement(input) {\n      try {\n        new URL(input);\n        return true;\n      } catch {\n        return false;\n      }\n    },\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement(dataset.value)) {\n        _addIssue(this, \"URL\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/uuid/uuid.ts\nfunction uuid(message) {\n  return {\n    kind: \"validation\",\n    type: \"uuid\",\n    reference: uuid,\n    async: false,\n    expects: null,\n    requirement: UUID_REGEX,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !this.requirement.test(dataset.value)) {\n        _addIssue(this, \"UUID\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/actions/value/value.ts\nfunction value(requirement, message) {\n  return {\n    kind: \"validation\",\n    type: \"value\",\n    reference: value,\n    async: false,\n    expects: requirement instanceof Date ? requirement.toJSON() : _stringify(requirement),\n    requirement,\n    message,\n    _run(dataset, config2) {\n      if (dataset.typed && !(this.requirement <= dataset.value && this.requirement >= dataset.value)) {\n        _addIssue(this, \"value\", dataset, config2, {\n          received: dataset.value instanceof Date ? dataset.value.toJSON() : _stringify(dataset.value)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/config/config.ts\nfunction config(schema, config2) {\n  return {\n    ...schema,\n    _run(dataset, config_) {\n      return schema._run(dataset, { ...config_, ...config2 });\n    }\n  };\n}\n\n// src/methods/getFallback/getFallback.ts\nfunction getFallback(schema, dataset, config2) {\n  return typeof schema.fallback === \"function\" ? (\n    // @ts-expect-error\n    schema.fallback(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.fallback\n  );\n}\n\n// src/methods/fallback/fallback.ts\nfunction fallback(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    _run(dataset, config2) {\n      schema._run(dataset, config2);\n      return dataset.issues ? { typed: true, value: getFallback(this, dataset, config2) } : dataset;\n    }\n  };\n}\n\n// src/methods/fallback/fallbackAsync.ts\nfunction fallbackAsync(schema, fallback2) {\n  return {\n    ...schema,\n    fallback: fallback2,\n    async: true,\n    async _run(dataset, config2) {\n      schema._run(dataset, config2);\n      return dataset.issues ? (\n        // @ts-expect-error\n        { typed: true, value: await getFallback(this, dataset, config2) }\n      ) : dataset;\n    }\n  };\n}\n\n// src/methods/flatten/flatten.ts\nfunction flatten(issues) {\n  const flatErrors = {};\n  for (const issue of issues) {\n    if (issue.path) {\n      const dotPath = getDotPath(issue);\n      if (dotPath) {\n        if (!flatErrors.nested) {\n          flatErrors.nested = {};\n        }\n        if (flatErrors.nested[dotPath]) {\n          flatErrors.nested[dotPath].push(issue.message);\n        } else {\n          flatErrors.nested[dotPath] = [issue.message];\n        }\n      } else {\n        if (flatErrors.other) {\n          flatErrors.other.push(issue.message);\n        } else {\n          flatErrors.other = [issue.message];\n        }\n      }\n    } else {\n      if (flatErrors.root) {\n        flatErrors.root.push(issue.message);\n      } else {\n        flatErrors.root = [issue.message];\n      }\n    }\n  }\n  return flatErrors;\n}\n\n// src/methods/forward/forward.ts\nfunction forward(action, pathKeys) {\n  return {\n    ...action,\n    _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/forward/forwardAsync.ts\nfunction forwardAsync(action, pathKeys) {\n  return {\n    ...action,\n    async: true,\n    async _run(dataset, config2) {\n      const prevIssues = dataset.issues && [...dataset.issues];\n      await action._run(dataset, config2);\n      if (dataset.issues) {\n        for (const issue of dataset.issues) {\n          if (!prevIssues?.includes(issue)) {\n            let pathInput = dataset.value;\n            for (const key of pathKeys) {\n              const pathValue = pathInput[key];\n              const pathItem = {\n                type: \"unknown\",\n                origin: \"value\",\n                input: pathInput,\n                key,\n                value: pathValue\n              };\n              if (issue.path) {\n                issue.path.push(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              if (!pathValue) {\n                break;\n              }\n              pathInput = pathValue;\n            }\n          }\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/getDefault/getDefault.ts\nfunction getDefault(schema, dataset, config2) {\n  return typeof schema.default === \"function\" ? (\n    // @ts-expect-error\n    schema.default(dataset, config2)\n  ) : (\n    // @ts-expect-error\n    schema.default\n  );\n}\n\n// src/methods/getDefaults/getDefaults.ts\nfunction getDefaults(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getDefaults(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getDefaults);\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getDefaults/getDefaultsAsync.ts\nasync function getDefaultsAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getDefaultsAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getDefaultsAsync));\n  }\n  return getDefault(schema);\n}\n\n// src/methods/getFallbacks/getFallbacks.ts\nfunction getFallbacks(schema) {\n  if (\"entries\" in schema) {\n    const object2 = {};\n    for (const key in schema.entries) {\n      object2[key] = getFallbacks(schema.entries[key]);\n    }\n    return object2;\n  }\n  if (\"items\" in schema) {\n    return schema.items.map(getFallbacks);\n  }\n  return getFallback(schema);\n}\n\n// src/methods/getFallbacks/getFallbacksAsync.ts\nasync function getFallbacksAsync(schema) {\n  if (\"entries\" in schema) {\n    return Object.fromEntries(\n      await Promise.all(\n        Object.entries(schema.entries).map(async ([key, value2]) => [\n          key,\n          await getFallbacksAsync(value2)\n        ])\n      )\n    );\n  }\n  if (\"items\" in schema) {\n    return Promise.all(schema.items.map(getFallbacksAsync));\n  }\n  return getFallback(schema);\n}\n\n// src/methods/is/is.ts\nfunction is(schema, input) {\n  return !schema._run({ typed: false, value: input }, { abortEarly: true }).issues;\n}\n\n// src/schemas/any/any.ts\nfunction any() {\n  return {\n    kind: \"schema\",\n    type: \"any\",\n    reference: any,\n    expects: \"any\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/array.ts\nfunction array(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: array,\n    expects: \"Array\",\n    async: false,\n    item,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < input.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.item._run({ typed: false, value: value2 }, config2);\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/array/arrayAsync.ts\nfunction arrayAsync(item, message) {\n  return {\n    kind: \"schema\",\n    type: \"array\",\n    reference: arrayAsync,\n    expects: \"Array\",\n    async: true,\n    item,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          input.map((value2) => this.item._run({ typed: false, value: value2 }, config2))\n        );\n        for (let key = 0; key < itemDatasets.length; key++) {\n          const itemDataset = itemDatasets[key];\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"array\",\n              origin: \"value\",\n              input,\n              key,\n              value: input[key]\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/bigint/bigint.ts\nfunction bigint(message) {\n  return {\n    kind: \"schema\",\n    type: \"bigint\",\n    reference: bigint,\n    expects: \"bigint\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"bigint\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/blob/blob.ts\nfunction blob(message) {\n  return {\n    kind: \"schema\",\n    type: \"blob\",\n    reference: blob,\n    expects: \"Blob\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Blob) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/boolean/boolean.ts\nfunction boolean(message) {\n  return {\n    kind: \"schema\",\n    type: \"boolean\",\n    reference: boolean,\n    expects: \"boolean\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"boolean\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/custom.ts\nfunction custom(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: custom,\n    expects: \"unknown\",\n    async: false,\n    check: check2,\n    message,\n    _run(dataset, config2) {\n      if (this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/custom/customAsync.ts\nfunction customAsync(check2, message) {\n  return {\n    kind: \"schema\",\n    type: \"custom\",\n    reference: customAsync,\n    expects: \"unknown\",\n    async: true,\n    check: check2,\n    message,\n    async _run(dataset, config2) {\n      if (await this.check(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/date/date.ts\nfunction date(message) {\n  return {\n    kind: \"schema\",\n    type: \"date\",\n    reference: date,\n    expects: \"Date\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof Date && !isNaN(dataset.value.getTime())) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/enum/enum.ts\nfunction enum_(enum__, message) {\n  const options = Object.entries(enum__).filter(([key]) => isNaN(+key)).map(([, value2]) => value2);\n  return {\n    kind: \"schema\",\n    type: \"enum\",\n    reference: enum_,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    enum: enum__,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/instance/instance.ts\nfunction instance(class_, message) {\n  return {\n    kind: \"schema\",\n    type: \"instance\",\n    reference: instance,\n    expects: class_.name,\n    async: false,\n    class: class_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value instanceof this.class) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/utils/_merge/_merge.ts\nfunction _merge(value1, value2) {\n  if (typeof value1 === typeof value2) {\n    if (value1 === value2 || value1 instanceof Date && value2 instanceof Date && +value1 === +value2) {\n      return { value: value1 };\n    }\n    if (value1 && value2 && value1.constructor === Object && value2.constructor === Object) {\n      for (const key in value2) {\n        if (key in value1) {\n          const dataset = _merge(value1[key], value2[key]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[key] = dataset.value;\n        } else {\n          value1[key] = value2[key];\n        }\n      }\n      return { value: value1 };\n    }\n    if (Array.isArray(value1) && Array.isArray(value2)) {\n      if (value1.length === value2.length) {\n        for (let index = 0; index < value1.length; index++) {\n          const dataset = _merge(value1[index], value2[index]);\n          if (dataset.issue) {\n            return dataset;\n          }\n          value1[index] = dataset.value;\n        }\n        return { value: value1 };\n      }\n    }\n  }\n  return { issue: true };\n}\n\n// src/schemas/intersect/intersect.ts\nfunction intersect(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersect,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        for (const schema of this.options) {\n          const optionDataset = schema._run(\n            { typed: false, value: input },\n            config2\n          );\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/intersect/intersectAsync.ts\nfunction intersectAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"intersect\",\n    reference: intersectAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" & \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      if (this.options.length) {\n        const input = dataset.value;\n        let outputs;\n        dataset.typed = true;\n        const optionDatasets = await Promise.all(\n          this.options.map(\n            (schema) => schema._run({ typed: false, value: input }, config2)\n          )\n        );\n        for (const optionDataset of optionDatasets) {\n          if (optionDataset.issues) {\n            if (dataset.issues) {\n              dataset.issues.push(...optionDataset.issues);\n            } else {\n              dataset.issues = optionDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!optionDataset.typed) {\n            dataset.typed = false;\n          }\n          if (dataset.typed) {\n            if (outputs) {\n              outputs.push(optionDataset.value);\n            } else {\n              outputs = [optionDataset.value];\n            }\n          }\n        }\n        if (dataset.typed) {\n          dataset.value = outputs[0];\n          for (let index = 1; index < outputs.length; index++) {\n            const mergeDataset = _merge(dataset.value, outputs[index]);\n            if (mergeDataset.issue) {\n              _addIssue(this, \"type\", dataset, config2, {\n                received: \"unknown\"\n              });\n              break;\n            }\n            dataset.value = mergeDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/lazy/lazy.ts\nfunction lazy(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazy,\n    expects: \"unknown\",\n    async: false,\n    getter,\n    _run(dataset, config2) {\n      return this.getter(dataset.value)._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/lazy/lazyAsync.ts\nfunction lazyAsync(getter) {\n  return {\n    kind: \"schema\",\n    type: \"lazy\",\n    reference: lazyAsync,\n    expects: \"unknown\",\n    async: true,\n    getter,\n    async _run(dataset, config2) {\n      return (await this.getter(dataset.value))._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/literal/literal.ts\nfunction literal(literal_, message) {\n  return {\n    kind: \"schema\",\n    type: \"literal\",\n    reference: literal,\n    expects: _stringify(literal_),\n    async: false,\n    literal: literal_,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === this.literal) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObject.ts\nfunction looseObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseObject/looseObjectAsync.ts\nfunction looseObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_object\",\n    reference: looseObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              dataset.value[key] = input[key];\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTuple.ts\nfunction looseTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/looseTuple/looseTupleAsync.ts\nfunction looseTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"loose_tuple\",\n    reference: looseTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            dataset.value.push(input[key]);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/map.ts\nfunction map(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: map,\n    expects: \"Map\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        for (const [inputKey, inputValue] of input) {\n          const keyDataset = this.key._run(\n            { typed: false, value: inputKey },\n            config2\n          );\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/map/mapAsync.ts\nfunction mapAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"map\",\n    reference: mapAsync,\n    expects: \"Map\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Map) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Map();\n        const datasets = await Promise.all(\n          [...input].map(\n            ([inputKey, inputValue]) => Promise.all([\n              inputKey,\n              inputValue,\n              this.key._run({ typed: false, value: inputKey }, config2),\n              this.value._run({ typed: false, value: inputValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          inputKey,\n          inputValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"key\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of keyDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"map\",\n              origin: \"value\",\n              input,\n              key: inputKey,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.set(keyDataset.value, valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nan/nan.ts\nfunction nan(message) {\n  return {\n    kind: \"schema\",\n    type: \"nan\",\n    reference: nan,\n    expects: \"NaN\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (Number.isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/never/never.ts\nfunction never(message) {\n  return {\n    kind: \"schema\",\n    type: \"never\",\n    reference: never,\n    expects: \"never\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      _addIssue(this, \"type\", dataset, config2);\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullable.ts\nfunction nonNullable(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullable,\n    expects: \"!null\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullable/nonNullableAsync.ts\nfunction nonNullableAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullable\",\n    reference: nonNullableAsync,\n    expects: \"!null\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullish.ts\nfunction nonNullish(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullish,\n    expects: \"!null & !undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonNullish/nonNullishAsync.ts\nfunction nonNullishAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_nullish\",\n    reference: nonNullishAsync,\n    expects: \"!null & !undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptional.ts\nfunction nonOptional(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptional,\n    expects: \"!undefined\",\n    async: false,\n    wrapped,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/nonOptional/nonOptionalAsync.ts\nfunction nonOptionalAsync(wrapped, message) {\n  return {\n    kind: \"schema\",\n    type: \"non_optional\",\n    reference: nonOptionalAsync,\n    expects: \"!undefined\",\n    async: true,\n    wrapped,\n    message,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        _addIssue(this, \"type\", dataset, config2);\n        return dataset;\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n}\n\n// src/schemas/null/null.ts\nfunction null_(message) {\n  return {\n    kind: \"schema\",\n    type: \"null\",\n    reference: null_,\n    expects: \"null\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/nullable/nullable.ts\nfunction nullable(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullable,\n    expects: `${wrapped.expects} | null`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullable/nullableAsync.ts\nfunction nullableAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullable\",\n    reference: nullableAsync,\n    expects: `${wrapped.expects} | null`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullish.ts\nfunction nullish(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullish,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/nullish/nullishAsync.ts\nfunction nullishAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"nullish\",\n    reference: nullishAsync,\n    expects: `${wrapped.expects} | null | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === null || dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === null || dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/number/number.ts\nfunction number(message) {\n  return {\n    kind: \"schema\",\n    type: \"number\",\n    reference: number,\n    expects: \"number\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"number\" && !isNaN(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/object.ts\nfunction object(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: object,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/object/objectAsync.ts\nfunction objectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"object\",\n    reference: objectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRest.ts\nfunction objectWithRest(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRest,\n    expects: \"Object\",\n    async: false,\n    entries,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (_isAllowedObjectKey(key) && !(key in this.entries)) {\n              const value2 = input[key];\n              const valueDataset = this.rest._run(\n                { typed: false, value: value2 },\n                config2\n              );\n              if (valueDataset.issues) {\n                const pathItem = {\n                  type: \"object\",\n                  origin: \"value\",\n                  input,\n                  key,\n                  value: value2\n                };\n                for (const issue of valueDataset.issues) {\n                  if (issue.path) {\n                    issue.path.unshift(pathItem);\n                  } else {\n                    issue.path = [pathItem];\n                  }\n                  dataset.issues?.push(issue);\n                }\n                if (!dataset.issues) {\n                  dataset.issues = valueDataset.issues;\n                }\n                if (config2.abortEarly) {\n                  dataset.typed = false;\n                  break;\n                }\n              }\n              if (!valueDataset.typed) {\n                dataset.typed = false;\n              }\n              dataset.value[key] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/objectWithRest/objectWithRestAsync.ts\nfunction objectWithRestAsync(entries, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"object_with_rest\",\n    reference: objectWithRestAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal entry\n          Promise.all(\n            Object.entries(this.entries).map(async ([key, schema]) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await schema._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other entries with rest schema\n          Promise.all(\n            Object.entries(input).filter(\n              ([key]) => _isAllowedObjectKey(key) && !(key in this.entries)\n            ).map(\n              async ([key, value2]) => [\n                key,\n                value2,\n                await this.rest._run({ typed: false, value: value2 }, config2)\n              ]\n            )\n          )\n        ]);\n        for (const [key, value2, valueDataset] of normalDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, valueDataset] of restDatasets) {\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"object\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!valueDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/optional/optional.ts\nfunction optional(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optional,\n    expects: `${wrapped.expects} | undefined`,\n    async: false,\n    wrapped,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/optional/optionalAsync.ts\nfunction optionalAsync(wrapped, ...args) {\n  const schema = {\n    kind: \"schema\",\n    type: \"optional\",\n    reference: optionalAsync,\n    expects: `${wrapped.expects} | undefined`,\n    async: true,\n    wrapped,\n    async _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        if (\"default\" in this) {\n          dataset.value = await getDefault(\n            this,\n            dataset,\n            config2\n          );\n        }\n        if (dataset.value === void 0) {\n          dataset.typed = true;\n          return dataset;\n        }\n      }\n      return this.wrapped._run(dataset, config2);\n    }\n  };\n  if (0 in args) {\n    schema.default = args[0];\n  }\n  return schema;\n}\n\n// src/schemas/picklist/picklist.ts\nfunction picklist(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"picklist\",\n    reference: picklist,\n    expects: options.map(_stringify).join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      if (this.options.includes(dataset.value)) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/record.ts\nfunction record(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: record,\n    expects: \"Object\",\n    async: false,\n    key,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const entryKey in input) {\n          if (_isAllowedObjectKey(entryKey)) {\n            const entryValue = input[entryKey];\n            const keyDataset = this.key._run(\n              { typed: false, value: entryKey },\n              config2\n            );\n            if (keyDataset.issues) {\n              const pathItem = {\n                type: \"record\",\n                origin: \"key\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of keyDataset.issues) {\n                issue.path = [pathItem];\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = keyDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            const valueDataset = this.value._run(\n              { typed: false, value: entryValue },\n              config2\n            );\n            if (valueDataset.issues) {\n              const pathItem = {\n                type: \"record\",\n                origin: \"value\",\n                input,\n                key: entryKey,\n                value: entryValue\n              };\n              for (const issue of valueDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = valueDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!keyDataset.typed || !valueDataset.typed) {\n              dataset.typed = false;\n            }\n            if (keyDataset.typed) {\n              dataset.value[keyDataset.value] = valueDataset.value;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/record/recordAsync.ts\nfunction recordAsync(key, value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"record\",\n    reference: recordAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const datasets = await Promise.all(\n          Object.entries(input).filter(([key2]) => _isAllowedObjectKey(key2)).map(\n            ([entryKey, entryValue]) => Promise.all([\n              entryKey,\n              entryValue,\n              this.key._run({ typed: false, value: entryKey }, config2),\n              this.value._run({ typed: false, value: entryValue }, config2)\n            ])\n          )\n        );\n        for (const [\n          entryKey,\n          entryValue,\n          keyDataset,\n          valueDataset\n        ] of datasets) {\n          if (keyDataset.issues) {\n            const pathItem = {\n              type: \"record\",\n              origin: \"key\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of keyDataset.issues) {\n              issue.path = [pathItem];\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = keyDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"record\",\n              origin: \"value\",\n              input,\n              key: entryKey,\n              value: entryValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!keyDataset.typed || !valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (keyDataset.typed) {\n            dataset.value[keyDataset.value] = valueDataset.value;\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/set.ts\nfunction set(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: set,\n    expects: \"Set\",\n    async: false,\n    value: value2,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        for (const inputValue of input) {\n          const valueDataset = this.value._run(\n            { typed: false, value: inputValue },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/set/setAsync.ts\nfunction setAsync(value2, message) {\n  return {\n    kind: \"schema\",\n    type: \"set\",\n    reference: setAsync,\n    expects: \"Set\",\n    async: true,\n    value: value2,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input instanceof Set) {\n        dataset.typed = true;\n        dataset.value = /* @__PURE__ */ new Set();\n        const valueDatasets = await Promise.all(\n          [...input].map(\n            async (inputValue) => [\n              inputValue,\n              await this.value._run(\n                { typed: false, value: inputValue },\n                config2\n              )\n            ]\n          )\n        );\n        for (const [inputValue, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"set\",\n              origin: \"value\",\n              input,\n              value: inputValue\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.add(valueDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObject.ts\nfunction strictObject(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObject,\n    expects: \"Object\",\n    async: false,\n    entries,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        for (const key in this.entries) {\n          const value2 = input[key];\n          const valueDataset = this.entries[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictObject/strictObjectAsync.ts\nfunction strictObjectAsync(entries, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_object\",\n    reference: strictObjectAsync,\n    expects: \"Object\",\n    async: true,\n    entries,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        dataset.typed = true;\n        dataset.value = {};\n        const valueDatasets = await Promise.all(\n          Object.entries(this.entries).map(async ([key, schema]) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await schema._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, valueDataset] of valueDatasets) {\n          if (valueDataset.issues) {\n            const pathItem = {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of valueDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = valueDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!valueDataset.typed) {\n            dataset.typed = false;\n          }\n          if (valueDataset.value !== void 0 || key in input) {\n            dataset.value[key] = valueDataset.value;\n          }\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const key in input) {\n            if (!(key in this.entries)) {\n              const value2 = input[key];\n              _addIssue(this, \"type\", dataset, config2, {\n                input: value2,\n                expected: \"never\",\n                path: [\n                  {\n                    type: \"object\",\n                    origin: \"value\",\n                    input,\n                    key,\n                    value: value2\n                  }\n                ]\n              });\n              break;\n            }\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTuple.ts\nfunction strictTuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key: items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/strictTuple/strictTupleAsync.ts\nfunction strictTupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"strict_tuple\",\n    reference: strictTupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!(dataset.issues && config2.abortEarly) && items.length < input.length) {\n          const value2 = input[items.length];\n          _addIssue(this, \"type\", dataset, config2, {\n            input: value2,\n            expected: \"never\",\n            path: [\n              {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key: items.length,\n                value: value2\n              }\n            ]\n          });\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/string/string.ts\nfunction string(message) {\n  return {\n    kind: \"schema\",\n    type: \"string\",\n    reference: string,\n    expects: \"string\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"string\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/symbol/symbol.ts\nfunction symbol(message) {\n  return {\n    kind: \"schema\",\n    type: \"symbol\",\n    reference: symbol,\n    expects: \"symbol\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (typeof dataset.value === \"symbol\") {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tuple.ts\nfunction tuple(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tuple,\n    expects: \"Array\",\n    async: false,\n    items,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tuple/tupleAsync.ts\nfunction tupleAsync(items, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple\",\n    reference: tupleAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const itemDatasets = await Promise.all(\n          items.map(async (item, key) => {\n            const value2 = input[key];\n            return [\n              key,\n              value2,\n              await item._run({ typed: false, value: value2 }, config2)\n            ];\n          })\n        );\n        for (const [key, value2, itemDataset] of itemDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRest.ts\nfunction tupleWithRest(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRest,\n    expects: \"Array\",\n    async: false,\n    items,\n    rest,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        for (let key = 0; key < items.length; key++) {\n          const value2 = input[key];\n          const itemDataset = this.items[key]._run(\n            { typed: false, value: value2 },\n            config2\n          );\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (let key = items.length; key < input.length; key++) {\n            const value2 = input[key];\n            const itemDataset = this.rest._run({ typed: false, value: value2 }, config2);\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/tupleWithRest/tupleWithRestAsync.ts\nfunction tupleWithRestAsync(items, rest, message) {\n  return {\n    kind: \"schema\",\n    type: \"tuple_with_rest\",\n    reference: tupleWithRestAsync,\n    expects: \"Array\",\n    async: true,\n    items,\n    rest,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (Array.isArray(input)) {\n        dataset.typed = true;\n        dataset.value = [];\n        const [normalDatasets, restDatasets] = await Promise.all([\n          // Parse schema of each normal item\n          Promise.all(\n            items.map(async (item, key) => {\n              const value2 = input[key];\n              return [\n                key,\n                value2,\n                await item._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          ),\n          // Parse other items with rest schema\n          Promise.all(\n            input.slice(items.length).map(async (value2, key) => {\n              return [\n                key + items.length,\n                value2,\n                await rest._run({ typed: false, value: value2 }, config2)\n              ];\n            })\n          )\n        ]);\n        for (const [key, value2, itemDataset] of normalDatasets) {\n          if (itemDataset.issues) {\n            const pathItem = {\n              type: \"tuple\",\n              origin: \"value\",\n              input,\n              key,\n              value: value2\n            };\n            for (const issue of itemDataset.issues) {\n              if (issue.path) {\n                issue.path.unshift(pathItem);\n              } else {\n                issue.path = [pathItem];\n              }\n              dataset.issues?.push(issue);\n            }\n            if (!dataset.issues) {\n              dataset.issues = itemDataset.issues;\n            }\n            if (config2.abortEarly) {\n              dataset.typed = false;\n              break;\n            }\n          }\n          if (!itemDataset.typed) {\n            dataset.typed = false;\n          }\n          dataset.value.push(itemDataset.value);\n        }\n        if (!dataset.issues || !config2.abortEarly) {\n          for (const [key, value2, itemDataset] of restDatasets) {\n            if (itemDataset.issues) {\n              const pathItem = {\n                type: \"tuple\",\n                origin: \"value\",\n                input,\n                key,\n                value: value2\n              };\n              for (const issue of itemDataset.issues) {\n                if (issue.path) {\n                  issue.path.unshift(pathItem);\n                } else {\n                  issue.path = [pathItem];\n                }\n                dataset.issues?.push(issue);\n              }\n              if (!dataset.issues) {\n                dataset.issues = itemDataset.issues;\n              }\n              if (config2.abortEarly) {\n                dataset.typed = false;\n                break;\n              }\n            }\n            if (!itemDataset.typed) {\n              dataset.typed = false;\n            }\n            dataset.value.push(itemDataset.value);\n          }\n        }\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/undefined/undefined.ts\nfunction undefined_(message) {\n  return {\n    kind: \"schema\",\n    type: \"undefined\",\n    reference: undefined_,\n    expects: \"undefined\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/utils/_subIssues/_subIssues.ts\nfunction _subIssues(datasets) {\n  let issues;\n  if (datasets) {\n    for (const dataset of datasets) {\n      if (issues) {\n        issues.push(...dataset.issues);\n      } else {\n        issues = dataset.issues;\n      }\n    }\n  }\n  return issues;\n}\n\n// src/schemas/union/union.ts\nfunction union(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: union,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: false,\n    options,\n    message,\n    _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/union/unionAsync.ts\nfunction unionAsync(options, message) {\n  return {\n    kind: \"schema\",\n    type: \"union\",\n    reference: unionAsync,\n    expects: [...new Set(options.map((option) => option.expects))].join(\" | \") || \"never\",\n    async: true,\n    options,\n    message,\n    async _run(dataset, config2) {\n      let validDataset;\n      let typedDatasets;\n      let untypedDatasets;\n      for (const schema of this.options) {\n        const optionDataset = await schema._run(\n          { typed: false, value: dataset.value },\n          config2\n        );\n        if (optionDataset.typed) {\n          if (optionDataset.issues) {\n            if (typedDatasets) {\n              typedDatasets.push(optionDataset);\n            } else {\n              typedDatasets = [optionDataset];\n            }\n          } else {\n            validDataset = optionDataset;\n            break;\n          }\n        } else {\n          if (untypedDatasets) {\n            untypedDatasets.push(optionDataset);\n          } else {\n            untypedDatasets = [optionDataset];\n          }\n        }\n      }\n      if (validDataset) {\n        return validDataset;\n      }\n      if (typedDatasets) {\n        if (typedDatasets.length === 1) {\n          return typedDatasets[0];\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(typedDatasets)\n        });\n        dataset.typed = true;\n      } else if (untypedDatasets?.length === 1) {\n        return untypedDatasets[0];\n      } else {\n        _addIssue(this, \"type\", dataset, config2, {\n          issues: _subIssues(untypedDatasets)\n        });\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/unknown/unknown.ts\nfunction unknown() {\n  return {\n    kind: \"schema\",\n    type: \"unknown\",\n    reference: unknown,\n    expects: \"unknown\",\n    async: false,\n    _run(dataset) {\n      dataset.typed = true;\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/utils/_discriminators/_discriminators.ts\nfunction _discriminators(key, options, set2 = /* @__PURE__ */ new Set()) {\n  for (const schema of options) {\n    if (schema.type === \"variant\") {\n      _discriminators(key, schema.options, set2);\n    } else {\n      set2.add(schema.entries[key].expects);\n    }\n  }\n  return set2;\n}\n\n// src/schemas/variant/variant.ts\nfunction variant(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variant,\n    expects: \"Object\",\n    async: false,\n    key,\n    options,\n    message,\n    _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            ).issues) {\n              const optionDataset = schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/variant/variantAsync.ts\nfunction variantAsync(key, options, message) {\n  let expectedDiscriminators;\n  return {\n    kind: \"schema\",\n    type: \"variant\",\n    reference: variantAsync,\n    expects: \"Object\",\n    async: true,\n    key,\n    options,\n    message,\n    async _run(dataset, config2) {\n      const input = dataset.value;\n      if (input && typeof input === \"object\") {\n        const discriminator = input[this.key];\n        if (this.key in input) {\n          let outputDataset;\n          for (const schema of this.options) {\n            if (schema.type === \"variant\" || !(await schema.entries[this.key]._run(\n              { typed: false, value: discriminator },\n              config2\n            )).issues) {\n              const optionDataset = await schema._run(\n                { typed: false, value: input },\n                config2\n              );\n              if (!optionDataset.issues) {\n                return optionDataset;\n              }\n              if (!outputDataset || !outputDataset.typed && optionDataset.typed) {\n                outputDataset = optionDataset;\n              }\n            }\n          }\n          if (outputDataset) {\n            return outputDataset;\n          }\n        }\n        if (!expectedDiscriminators) {\n          expectedDiscriminators = [..._discriminators(this.key, this.options)].join(\" | \") || \"never\";\n        }\n        _addIssue(this, \"type\", dataset, config2, {\n          input: discriminator,\n          expected: expectedDiscriminators,\n          path: [\n            {\n              type: \"object\",\n              origin: \"value\",\n              input,\n              key: this.key,\n              value: discriminator\n            }\n          ]\n        });\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/schemas/void/void.ts\nfunction void_(message) {\n  return {\n    kind: \"schema\",\n    type: \"void\",\n    reference: void_,\n    expects: \"void\",\n    async: false,\n    message,\n    _run(dataset, config2) {\n      if (dataset.value === void 0) {\n        dataset.typed = true;\n      } else {\n        _addIssue(this, \"type\", dataset, config2);\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/keyof/keyof.ts\nfunction keyof(schema, message) {\n  return picklist(Object.keys(schema.entries), message);\n}\n\n// src/methods/omit/omit.ts\nfunction omit(schema, keys) {\n  const entries = {\n    ...schema.entries\n  };\n  for (const key of keys) {\n    delete entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/parse/parse.ts\nfunction parse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parse/parseAsync.ts\nasync function parseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  if (dataset.issues) {\n    throw new ValiError(dataset.issues);\n  }\n  return dataset.value;\n}\n\n// src/methods/parser/parser.ts\nfunction parser(schema, config2) {\n  const func = (input) => parse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/parser/parserAsync.ts\nfunction parserAsync(schema, config2) {\n  const func = (input) => parseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/partial/partial.ts\nfunction partial(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optional(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/partial/partialAsync.ts\nfunction partialAsync(schema, keys) {\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? optionalAsync(schema.entries[key]) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pick/pick.ts\nfunction pick(schema, keys) {\n  const entries = {};\n  for (const key of keys) {\n    entries[key] = schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/pipe/pipe.ts\nfunction pipe(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    _run(dataset, config2) {\n      for (let index = 0; index < pipe2.length; index++) {\n        dataset = pipe2[index]._run(dataset, config2);\n        const nextAction = pipe2[index + 1];\n        if (config2.skipPipe || dataset.issues && (config2.abortEarly || config2.abortPipeEarly || // TODO: This behavior must be documented!\n        nextAction?.kind === \"schema\" || nextAction?.kind === \"transformation\")) {\n          dataset.typed = false;\n          break;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/pipe/pipeAsync.ts\nfunction pipeAsync(...pipe2) {\n  return {\n    ...pipe2[0],\n    pipe: pipe2,\n    async: true,\n    async _run(dataset, config2) {\n      for (let index = 0; index < pipe2.length; index++) {\n        dataset = await pipe2[index]._run(dataset, config2);\n        const nextAction = pipe2[index + 1];\n        if (config2.skipPipe || dataset.issues && (config2.abortEarly || config2.abortPipeEarly || // TODO: This behavior must be documented!\n        nextAction?.kind === \"schema\" || nextAction?.kind === \"transformation\")) {\n          dataset.typed = false;\n          break;\n        }\n      }\n      return dataset;\n    }\n  };\n}\n\n// src/methods/required/required.ts\nfunction required(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptional(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/required/requiredAsync.ts\nfunction requiredAsync(schema, arg2, arg3) {\n  const keys = Array.isArray(arg2) ? arg2 : void 0;\n  const message = Array.isArray(arg2) ? arg3 : arg2;\n  const entries = {};\n  for (const key in schema.entries) {\n    entries[key] = !keys || keys.includes(key) ? nonOptionalAsync(schema.entries[key], message) : schema.entries[key];\n  }\n  return { ...schema, entries };\n}\n\n// src/methods/safeParse/safeParse.ts\nfunction safeParse(schema, input, config2) {\n  const dataset = schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParse/safeParseAsync.ts\nasync function safeParseAsync(schema, input, config2) {\n  const dataset = await schema._run(\n    { typed: false, value: input },\n    getGlobalConfig(config2)\n  );\n  return {\n    typed: dataset.typed,\n    success: !dataset.issues,\n    output: dataset.value,\n    issues: dataset.issues\n  };\n}\n\n// src/methods/safeParser/safeParser.ts\nfunction safeParser(schema, config2) {\n  const func = (input) => safeParse(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/safeParser/safeParserAsync.ts\nfunction safeParserAsync(schema, config2) {\n  const func = (input) => safeParseAsync(schema, input, config2);\n  func.schema = schema;\n  func.config = config2;\n  return func;\n}\n\n// src/methods/unwrap/unwrap.ts\nfunction unwrap(schema) {\n  return schema.wrapped;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/valibot/dist/index.js\n");

/***/ })

};
;