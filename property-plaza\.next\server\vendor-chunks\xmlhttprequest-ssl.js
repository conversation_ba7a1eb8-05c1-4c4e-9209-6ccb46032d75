/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlhttprequest-ssl";
exports.ids = ["vendor-chunks/xmlhttprequest-ssl"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js":
/*!***************************************************************!*\
  !*** ./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> DeFelippi <<EMAIL>>\n * @contributor David Ellis <<EMAIL>>\n * @license MIT\n */\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar Url = __webpack_require__(/*! url */ \"url\");\nvar spawn = (__webpack_require__(/*! child_process */ \"child_process\").spawn);\n\n/**\n * Module exports.\n */\n\nmodule.exports = XMLHttpRequest;\n\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */\n\nfunction XMLHttpRequest(opts) {\n  \"use strict\";\n\n  opts = opts || {};\n\n  /**\n   * Private variables\n   */\n  var self = this;\n  var http = __webpack_require__(/*! http */ \"http\");\n  var https = __webpack_require__(/*! https */ \"https\");\n\n  // Holds http.js objects\n  var request;\n  var response;\n\n  // Request settings\n  var settings = {};\n\n  // Disable header blacklist.\n  // Not part of XHR specs.\n  var disableHeaderCheck = false;\n\n  // Set some default headers\n  var defaultHeaders = {\n    \"User-Agent\": \"node-XMLHttpRequest\",\n    \"Accept\": \"*/*\"\n  };\n\n  var headers = Object.assign({}, defaultHeaders);\n\n  // These headers are not user setable.\n  // The following are allowed but banned in the spec:\n  // * user-agent\n  var forbiddenRequestHeaders = [\n    \"accept-charset\",\n    \"accept-encoding\",\n    \"access-control-request-headers\",\n    \"access-control-request-method\",\n    \"connection\",\n    \"content-length\",\n    \"content-transfer-encoding\",\n    \"cookie\",\n    \"cookie2\",\n    \"date\",\n    \"expect\",\n    \"host\",\n    \"keep-alive\",\n    \"origin\",\n    \"referer\",\n    \"te\",\n    \"trailer\",\n    \"transfer-encoding\",\n    \"upgrade\",\n    \"via\"\n  ];\n\n  // These request methods are not allowed\n  var forbiddenRequestMethods = [\n    \"TRACE\",\n    \"TRACK\",\n    \"CONNECT\"\n  ];\n\n  // Send flag\n  var sendFlag = false;\n  // Error flag, used when errors occur or abort is called\n  var errorFlag = false;\n  var abortedFlag = false;\n\n  // Event listeners\n  var listeners = {};\n\n  /**\n   * Constants\n   */\n\n  this.UNSENT = 0;\n  this.OPENED = 1;\n  this.HEADERS_RECEIVED = 2;\n  this.LOADING = 3;\n  this.DONE = 4;\n\n  /**\n   * Public vars\n   */\n\n  // Current state\n  this.readyState = this.UNSENT;\n\n  // default ready state change handler in case one is not set or is set late\n  this.onreadystatechange = null;\n\n  // Result & response\n  this.responseText = \"\";\n  this.responseXML = \"\";\n  this.response = Buffer.alloc(0);\n  this.status = null;\n  this.statusText = null;\n\n  /**\n   * Private methods\n   */\n\n  /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpHeader = function(header) {\n    return disableHeaderCheck || (header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1);\n  };\n\n  /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpMethod = function(method) {\n    return (method && forbiddenRequestMethods.indexOf(method) === -1);\n  };\n\n  /**\n   * Public methods\n   */\n\n  /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */\n  this.open = function(method, url, async, user, password) {\n    this.abort();\n    errorFlag = false;\n    abortedFlag = false;\n\n    // Check for valid request method\n    if (!isAllowedHttpMethod(method)) {\n      throw new Error(\"SecurityError: Request method not allowed\");\n    }\n\n    settings = {\n      \"method\": method,\n      \"url\": url.toString(),\n      \"async\": (typeof async !== \"boolean\" ? true : async),\n      \"user\": user || null,\n      \"password\": password || null\n    };\n\n    setState(this.OPENED);\n  };\n\n  /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */\n  this.setDisableHeaderCheck = function(state) {\n    disableHeaderCheck = state;\n  };\n\n  /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */\n  this.setRequestHeader = function(header, value) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n    }\n    if (!isAllowedHttpHeader(header)) {\n      console.warn('Refused to set unsafe header \"' + header + '\"');\n      return false;\n    }\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n    }\n    headers[header] = value;\n    return true;\n  };\n\n  /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */\n  this.getResponseHeader = function(header) {\n    if (typeof header === \"string\"\n      && this.readyState > this.OPENED\n      && response.headers[header.toLowerCase()]\n      && !errorFlag\n    ) {\n      return response.headers[header.toLowerCase()];\n    }\n\n    return null;\n  };\n\n  /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */\n  this.getAllResponseHeaders = function() {\n    if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n      return \"\";\n    }\n    var result = \"\";\n\n    for (var i in response.headers) {\n      // Cookie headers are excluded\n      if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n        result += i + \": \" + response.headers[i] + \"\\r\\n\";\n      }\n    }\n    return result.substr(0, result.length - 2);\n  };\n\n  /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */\n  this.getRequestHeader = function(name) {\n    // @TODO Make this case insensitive\n    if (typeof name === \"string\" && headers[name]) {\n      return headers[name];\n    }\n\n    return \"\";\n  };\n\n  /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */\n  this.send = function(data) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n    }\n\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n    }\n\n    var ssl = false, local = false;\n    var url = Url.parse(settings.url);\n    var host;\n    // Determine the server\n    switch (url.protocol) {\n      case 'https:':\n        ssl = true;\n        // SSL & non-SSL both need host, no break here.\n      case 'http:':\n        host = url.hostname;\n        break;\n\n      case 'file:':\n        local = true;\n        break;\n\n      case undefined:\n      case '':\n        host = \"localhost\";\n        break;\n\n      default:\n        throw new Error(\"Protocol not supported.\");\n    }\n\n    // Load files off the local filesystem (file://)\n    if (local) {\n      if (settings.method !== \"GET\") {\n        throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n      }\n\n      if (settings.async) {\n        fs.readFile(unescape(url.pathname), function(error, data) {\n          if (error) {\n            self.handleError(error, error.errno || -1);\n          } else {\n            self.status = 200;\n            self.responseText = data.toString('utf8');\n            self.response = data;\n            setState(self.DONE);\n          }\n        });\n      } else {\n        try {\n          this.response = fs.readFileSync(unescape(url.pathname));\n          this.responseText = this.response.toString('utf8');\n          this.status = 200;\n          setState(self.DONE);\n        } catch(e) {\n          this.handleError(e, e.errno || -1);\n        }\n      }\n\n      return;\n    }\n\n    // Default to port 80. If accessing localhost on another port be sure\n    // to use http://localhost:port/path\n    var port = url.port || (ssl ? 443 : 80);\n    // Add query string if one is used\n    var uri = url.pathname + (url.search ? url.search : '');\n\n    // Set the Host header or the server may reject the request\n    headers[\"Host\"] = host;\n    if (!((ssl && port === 443) || port === 80)) {\n      headers[\"Host\"] += ':' + url.port;\n    }\n\n    // Set Basic Auth if necessary\n    if (settings.user) {\n      if (typeof settings.password == \"undefined\") {\n        settings.password = \"\";\n      }\n      var authBuf = new Buffer(settings.user + \":\" + settings.password);\n      headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n    }\n\n    // Set content length header\n    if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n      data = null;\n    } else if (data) {\n      headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n\n      if (!headers[\"Content-Type\"]) {\n        headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n      }\n    } else if (settings.method === \"POST\") {\n      // For a post with no data set Content-Length: 0.\n      // This is required by buggy servers that don't meet the specs.\n      headers[\"Content-Length\"] = 0;\n    }\n\n    var agent = opts.agent || false;\n    var options = {\n      host: host,\n      port: port,\n      path: uri,\n      method: settings.method,\n      headers: headers,\n      agent: agent\n    };\n\n    if (ssl) {\n      options.pfx = opts.pfx;\n      options.key = opts.key;\n      options.passphrase = opts.passphrase;\n      options.cert = opts.cert;\n      options.ca = opts.ca;\n      options.ciphers = opts.ciphers;\n      options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n    }\n\n    // Reset error flag\n    errorFlag = false;\n    // Handle async requests\n    if (settings.async) {\n      // Use the proper protocol\n      var doRequest = ssl ? https.request : http.request;\n\n      // Request is being sent, set send flag\n      sendFlag = true;\n\n      // As per spec, this is called here for historical reasons.\n      self.dispatchEvent(\"readystatechange\");\n\n      // Handler for the response\n      var responseHandler = function(resp) {\n        // Set response var to the response we got back\n        // This is so it remains accessable outside this scope\n        response = resp;\n        // Check for redirect\n        // @TODO Prevent looped redirects\n        if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n          // Change URL to the redirect location\n          settings.url = response.headers.location;\n          var url = Url.parse(settings.url);\n          // Set host var in case it's used later\n          host = url.hostname;\n          // Options for the new request\n          var newOptions = {\n            hostname: url.hostname,\n            port: url.port,\n            path: url.path,\n            method: response.statusCode === 303 ? 'GET' : settings.method,\n            headers: headers\n          };\n\n          if (ssl) {\n            newOptions.pfx = opts.pfx;\n            newOptions.key = opts.key;\n            newOptions.passphrase = opts.passphrase;\n            newOptions.cert = opts.cert;\n            newOptions.ca = opts.ca;\n            newOptions.ciphers = opts.ciphers;\n            newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n          }\n\n          // Issue the new request\n          request = doRequest(newOptions, responseHandler).on('error', errorHandler);\n          request.end();\n          // @TODO Check if an XHR event needs to be fired here\n          return;\n        }\n\n        setState(self.HEADERS_RECEIVED);\n        self.status = response.statusCode;\n\n        response.on('data', function(chunk) {\n          // Make sure there's some data\n          if (chunk) {\n            var data = Buffer.from(chunk);\n            self.response = Buffer.concat([self.response, data]);\n          }\n          // Don't emit state changes if the connection has been aborted.\n          if (sendFlag) {\n            setState(self.LOADING);\n          }\n        });\n\n        response.on('end', function() {\n          if (sendFlag) {\n            // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n            // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n            sendFlag = false;\n            // Discard the 'end' event if the connection has been aborted\n            setState(self.DONE);\n            // Construct responseText from response\n            self.responseText = self.response.toString('utf8');\n          }\n        });\n\n        response.on('error', function(error) {\n          self.handleError(error);\n        });\n      }\n\n      // Error handler for the request\n      var errorHandler = function(error) {\n        self.handleError(error);\n      }\n\n      // Create the request\n      request = doRequest(options, responseHandler).on('error', errorHandler);\n\n      if (opts.autoUnref) {\n        request.on('socket', (socket) => {\n          socket.unref();\n        });\n      }\n\n      // Node 0.4 and later won't accept empty data. Make sure it's needed.\n      if (data) {\n        request.write(data);\n      }\n\n      request.end();\n\n      self.dispatchEvent(\"loadstart\");\n    } else { // Synchronous\n      // Create a temporary file for communication with the other Node process\n      var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n      var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n      fs.writeFileSync(syncFile, \"\", \"utf8\");\n      // The async request the other Node process executes\n      var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\"\n        + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\"\n        + \"var options = \" + JSON.stringify(options) + \";\"\n        + \"var responseText = '';\"\n        + \"var responseData = Buffer.alloc(0);\"\n        + \"var req = doRequest(options, function(response) {\"\n        + \"response.on('data', function(chunk) {\"\n        + \"  var data = Buffer.from(chunk);\"\n        + \"  responseText += data.toString('utf8');\"\n        + \"  responseData = Buffer.concat([responseData, data]);\"\n        + \"});\"\n        + \"response.on('end', function() {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"response.on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"}).on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + (data ? \"req.write('\" + JSON.stringify(data).slice(1,-1).replace(/'/g, \"\\\\'\") + \"');\":\"\")\n        + \"req.end();\";\n      // Start the other Node Process, executing this string\n      var syncProc = spawn(process.argv[0], [\"-e\", execString]);\n      var statusText;\n      while(fs.existsSync(syncFile)) {\n        // Wait while the sync file is empty\n      }\n      self.responseText = fs.readFileSync(contentFile, 'utf8');\n      // Kill the child process once the file has data\n      syncProc.stdin.end();\n      // Remove the temporary file\n      fs.unlinkSync(contentFile);\n      if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n        // If the file returned an error, handle it\n        var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n        self.handleError(errorObj, 503);\n      } else {\n        // If the file returned okay, parse its data and move to the DONE state\n        self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n        var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n        response = {\n          statusCode: self.status,\n          headers: resp.data.headers\n        };\n        self.responseText = resp.data.text;\n        self.response = Buffer.from(resp.data.data, 'base64');\n        setState(self.DONE, true);\n      }\n    }\n  };\n\n  /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */\n  this.handleError = function(error, status) {\n    this.status = status || 0;\n    this.statusText = error;\n    this.responseText = error.stack;\n    errorFlag = true;\n    setState(this.DONE);\n  };\n\n  /**\n   * Aborts a request.\n   */\n  this.abort = function() {\n    if (request) {\n      request.abort();\n      request = null;\n    }\n\n    headers = Object.assign({}, defaultHeaders);\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n\n    errorFlag = abortedFlag = true\n    if (this.readyState !== this.UNSENT\n        && (this.readyState !== this.OPENED || sendFlag)\n        && this.readyState !== this.DONE) {\n      sendFlag = false;\n      setState(this.DONE);\n    }\n    this.readyState = this.UNSENT;\n  };\n\n  /**\n   * Adds an event listener. Preferred method of binding to events.\n   */\n  this.addEventListener = function(event, callback) {\n    if (!(event in listeners)) {\n      listeners[event] = [];\n    }\n    // Currently allows duplicate callbacks. Should it?\n    listeners[event].push(callback);\n  };\n\n  /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */\n  this.removeEventListener = function(event, callback) {\n    if (event in listeners) {\n      // Filter will return a new array with the callback removed\n      listeners[event] = listeners[event].filter(function(ev) {\n        return ev !== callback;\n      });\n    }\n  };\n\n  /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */\n  this.dispatchEvent = function (event) {\n    if (typeof self[\"on\" + event] === \"function\") {\n      if (this.readyState === this.DONE && settings.async)\n        setTimeout(function() { self[\"on\" + event]() }, 0)\n      else\n        self[\"on\" + event]()\n    }\n    if (event in listeners) {\n      for (let i = 0, len = listeners[event].length; i < len; i++) {\n        if (this.readyState === this.DONE)\n          setTimeout(function() { listeners[event][i].call(self) }, 0)\n        else\n          listeners[event][i].call(self)\n      }\n    }\n  };\n\n  /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */\n  var setState = function(state) {\n    if ((self.readyState === state) || (self.readyState === self.UNSENT && abortedFlag))\n      return\n\n    self.readyState = state;\n\n    if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n      self.dispatchEvent(\"readystatechange\");\n    }\n\n    if (self.readyState === self.DONE) {\n      let fire\n\n      if (abortedFlag)\n        fire = \"abort\"\n      else if (errorFlag)\n        fire = \"error\"\n      else\n        fire = \"load\"\n\n      self.dispatchEvent(fire)\n\n      // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n      self.dispatchEvent(\"loadend\");\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\n");

/***/ })

};
;