"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1118],{26038:(e,t,r)=>{r.d(t,{_:()=>n});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},45626:(e,t,r)=>{r.d(t,{default:()=>c});var n=r(26038),o=r(6874),l=r.n(o),i=r(35695),a=r(12115),s=r(85808),c=(0,a.forwardRef)(function(e,t){let{defaultLocale:r,href:o,locale:c,localeCookie:u,onClick:d,prefetch:f,unprefixed:p,...h}=e,v=(0,s.A)(),m=c!==v,w=c||v,g=function(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{t(window.location.host)},[]),e}(),b=g&&p&&(p.domains[g]===w||!Object.keys(p.domains).includes(g)&&v===r&&!c)?p.pathname:o,y=(0,i.usePathname)();return m&&(f&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),f=!1),a.createElement(l(),(0,n._)({ref:t,href:b,hrefLang:m?c:void 0,onClick:function(e){(function(e,t,r,n){if(!e||n===r||null==n||!t)return;let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:l,...i}=e;i.path||(i.path=""!==o?o:"/");let a="".concat(l,"=").concat(n,";");for(let[e,t]of Object.entries(i))a+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(a+="="+t),a+=";";document.cookie=a})(u,y,v,c),d&&d(e)},prefetch:f},h))})},46081:(e,t,r)=>{r.d(t,{A:()=>l});var n=r(12115),o=r(95155);function l(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;function s(t){let{scope:r,children:l,...s}=t,c=r?.[e][a]||i,u=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(c.Provider,{value:u,children:l})}return r=[...r,l],s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e][a]||i,c=n.useContext(s);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}},48882:(e,t,r)=>{r.d(t,{default:()=>d});var n=r(26038),o=r(35695),l=r(12115),i=r(85808);function a(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function s(e,t){let r;return"string"==typeof e?r=c(t,e):(r={...e},e.pathname&&(r.pathname=c(t,e.pathname))),r}function c(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}r(87358);var u=r(45626);let d=(0,l.forwardRef)(function(e,t){let{href:r,locale:c,localeCookie:d,localePrefixMode:f,prefix:p,...h}=e,v=(0,o.usePathname)(),m=(0,i.A)(),w=c!==m,[g,b]=(0,l.useState)(()=>a(r)&&("never"!==f||w)?s(r,p):r);return(0,l.useEffect)(()=>{v&&b(function(e,t){var r,n;let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,l=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;if(!a(e))return e;let c=(r=i,(n=l)===r||n.startsWith("".concat(r,"/")));return(t!==o||c)&&null!=i?s(e,i):e}(r,c,m,v,p))},[m,r,c,v,p]),l.createElement(u.default,(0,n._)({ref:t,href:g,locale:c,localeCookie:d},h))});d.displayName="ClientLink"},54416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},68121:(e,t,r)=>{r.d(t,{OK:()=>q,bL:()=>V,VM:()=>E,lr:()=>D,LM:()=>B});var n=r(12115),o=r(63540),l=r(6101),i=r(52712),a=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),a=n.useRef({}),c=n.useRef(e),u=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=s(a.current);u.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=a.current,r=c.current;if(r!==e){let n=u.current,o=s(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=s(a.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(u.current=s(a.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),l(e)},[])}}(t),a="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),c=(0,l.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||o.isPresent?n.cloneElement(a,{ref:c}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence";var c=r(95155),u=r(39033),d=r(94315),f=r(89367),p=r(85185),h="ScrollArea",[v,m]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,s=r?.[e]?.[i]||l,u=n.useMemo(()=>a,Object.values(a));return(0,c.jsx)(s.Provider,{value:u,children:o})};return a.displayName=t+"Provider",[a,function(r,a){let s=a?.[e]?.[i]||l,c=n.useContext(s);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(h),[w,g]=v(h),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:a,scrollHideDelay:s=600,...u}=e,[f,p]=n.useState(null),[h,v]=n.useState(null),[m,g]=n.useState(null),[b,y]=n.useState(null),[S,x]=n.useState(null),[E,N]=n.useState(0),[C,T]=n.useState(0),[R,L]=n.useState(!1),[_,A]=n.useState(!1),P=(0,l.s)(t,e=>p(e)),j=(0,d.jH)(a);return(0,c.jsx)(w,{scope:r,type:i,dir:j,scrollHideDelay:s,scrollArea:f,viewport:h,onViewportChange:v,content:m,onContentChange:g,scrollbarX:b,onScrollbarXChange:y,scrollbarXEnabled:R,onScrollbarXEnabledChange:L,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:_,onScrollbarYEnabledChange:A,onCornerWidthChange:N,onCornerHeightChange:T,children:(0,c.jsx)(o.sG.div,{dir:j,...u,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});b.displayName=h;var y="ScrollAreaViewport",S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,asChild:a,nonce:s,...u}=e,d=g(y,r),f=n.useRef(null),p=(0,l.s)(t,f,d.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n[data-radix-scroll-area-viewport] {\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n}\n[data-radix-scroll-area-viewport]::-webkit-scrollbar {\n  display: none;\n}\n:where([data-radix-scroll-area-viewport]) {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n:where([data-radix-scroll-area-content]) {\n  flex-grow: 1;\n}\n"},nonce:s}),(0,c.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...u,asChild:a,ref:p,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:function(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let l=n.Children.only(o);return n.cloneElement(l,{children:"function"==typeof t?t(l.props.children):t})}({asChild:a,children:i},e=>(0,c.jsx)("div",{"data-radix-scroll-area-content":"",ref:d.onContentChange,style:{minWidth:d.scrollbarXEnabled?"fit-content":void 0},children:e}))})]})});S.displayName=y;var x="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,c.jsx)(N,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,c.jsx)(C,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,c.jsx)(T,{...o,ref:t,forceMount:r}):"always"===l.type?(0,c.jsx)(R,{...o,ref:t}):null});E.displayName=x;var N=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(x,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,c.jsx)(a,{present:r||i,children:(0,c.jsx)(T,{"data-state":i?"visible":"hidden",...o,ref:t})})}),C=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,i=g(x,e.__scopeScrollArea),s="horizontal"===e.orientation,u=$(()=>f("SCROLL_END"),100),[d,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>f("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,i.scrollHideDelay,f]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,f,u]),(0,c.jsx)(a,{present:o||"hidden"!==d,children:(0,c.jsx)(R,{"data-state":"hidden"===d?"hidden":"visible",...l,ref:t,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),T=n.forwardRef((e,t)=>{let r=g(x,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,d=$(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return G(r.viewport,d),G(r.content,d),(0,c.jsx)(a,{present:o||i,children:(0,c.jsx)(R,{"data-state":i?"visible":"hidden",...l,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=g(x,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(s.viewport,s.content),f={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=H(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return F([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,c.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,c.jsx)(_,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),f=(0,l.s)(t,d,a.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(j,{"data-orientation":"horizontal",...i,ref:f,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:W(s.paddingLeft),paddingEnd:W(s.paddingRight)}})}})}),_=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),f=(0,l.s)(t,d,a.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(j,{"data-orientation":"vertical",...i,ref:f,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:W(s.paddingTop),paddingEnd:W(s.paddingBottom)}})}})}),[A,P]=v(x),j=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:a,onThumbChange:s,onThumbPointerUp:d,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:w,...b}=e,y=g(x,r),[S,E]=n.useState(null),N=(0,l.s)(t,e=>E(e)),C=n.useRef(null),T=n.useRef(""),R=y.viewport,L=i.content-i.viewport,_=(0,u.c)(m),P=(0,u.c)(h),j=$(w,10);function O(e){C.current&&v({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&_(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,S,L,_]),n.useEffect(P,[i,P]),G(S,j),G(y.content,j),(0,c.jsx)(A,{scope:r,scrollbar:S,hasThumb:a,onThumbChange:(0,u.c)(s),onThumbPointerUp:(0,u.c)(d),onThumbPositionChange:P,onThumbPointerDown:(0,u.c)(f),children:(0,c.jsx)(o.sG.div,{...b,ref:N,style:{position:"absolute",...b.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=S.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),O(e))}),onPointerMove:(0,p.m)(e.onPointerMove,O),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,y.viewport&&(y.viewport.style.scrollBehavior=""),C.current=null})})})}),O="ScrollAreaThumb",D=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=P(O,e.__scopeScrollArea);return(0,c.jsx)(a,{present:r||o.hasThumb,children:(0,c.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...a}=e,s=g(O,r),u=P(O,r),{onThumbPositionChange:d}=u,f=(0,l.s)(t,e=>u.onThumbChange(e)),h=n.useRef(),v=$(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{v(),h.current||(h.current=Y(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,c.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,p.m)(e.onPointerUp,u.onThumbPointerUp)})});D.displayName=O;var k="ScrollAreaCorner",I=n.forwardRef((e,t)=>{let r=g(k,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,c.jsx)(U,{...e,ref:t}):null});I.displayName=k;var U=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=g(k,r),[a,s]=n.useState(0),[u,d]=n.useState(0),f=!!(a&&u);return G(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),G(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),f?(0,c.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function H(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=H(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,f.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return F([0,i],[0,l-n])(a)}function F(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var Y=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function $(e,t){let r=(0,u.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function G(e,t){let r=(0,u.c)(t);(0,i.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var V=b,B=S,q=I},85808:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(35695),o=r(97526);let l="locale";function i(){let e,t=(0,n.useParams)();try{e=(0,o.useLocale)()}catch(r){if("string"!=typeof(null==t?void 0:t[l]))throw r;e=t[l]}return e}},85977:(e,t,r)=>{r.d(t,{BK:()=>m,H4:()=>S,_V:()=>y,bL:()=>b});var n=r(12115),o=r(46081),l=r(39033),i=r(52712),a=r(63540),s=r(95155),c="Avatar",[u,d]=(0,o.A)(c),[f,p]=u(c),h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[l,i]=n.useState("idle");return(0,s.jsx)(f,{scope:r,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,s.jsx)(a.sG.span,{...o,ref:t})})});h.displayName=c;var v="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:c=()=>{},...u}=e,d=p(v,r),f=function(e){let[t,r]=n.useState("idle");return(0,i.N)(()=>{if(!e)return void r("error");let t=!0,n=new window.Image,o=e=>()=>{t&&r(e)};return r("loading"),n.onload=o("loaded"),n.onerror=o("error"),n.src=e,()=>{t=!1}},[e]),t}(o),h=(0,l.c)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,s.jsx)(a.sG.img,{...u,ref:t,src:o}):null});m.displayName=v;var w="AvatarFallback",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...l}=e,i=p(w,r),[c,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==i.imageLoadingStatus?(0,s.jsx)(a.sG.span,{...l,ref:t}):null});g.displayName=w;var b=h,y=m,S=g},94870:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])}}]);