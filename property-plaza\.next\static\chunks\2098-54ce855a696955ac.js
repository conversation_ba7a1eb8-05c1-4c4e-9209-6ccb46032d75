"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2098],{1184:(e,t,n)=>{n.d(t,{UO:()=>p,dK:()=>d,wE:()=>f});var r=n(12115),l=Object.defineProperty,o=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p="^\\d+$",d=r.createContext({}),f=r.forwardRef((e,t)=>{var n,l,f,v,g,{value:b,onChange:y,maxLength:w,textAlign:E="left",pattern:S=p,inputMode:k="numeric",onComplete:C,pushPasswordManagerStrategy:P="increase-width",containerClassName:x,noScriptCSSFallback:M=m,render:A,children:O}=e,R=((e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n})(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[j,_]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),D=null!=b?b:j,W=function(e){let t=r.useRef();return r.useEffect(()=>{t.current=e}),t.current}(D),T=r.useCallback(e=>{null==y||y(e),_(e)},[y]),B=r.useMemo(()=>S?"string"==typeof S?new RegExp(S):S:null,[S]),I=r.useRef(null),L=r.useRef(null),F=r.useRef({value:D,onChange:T,isIOS:"undefined"!=typeof window&&(null==(l=null==(n=null==window?void 0:window.CSS)?void 0:n.supports)?void 0:l.call(n,"-webkit-touch-callout","none"))}),H=r.useRef({prev:[null==(f=I.current)?void 0:f.selectionStart,null==(v=I.current)?void 0:v.selectionEnd,null==(g=I.current)?void 0:g.selectionDirection]});r.useImperativeHandle(t,()=>I.current,[]),r.useEffect(()=>{let e=I.current,t=L.current;if(!e||!t)return;function n(){if(document.activeElement!==e){U(null),K(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,l=e.maxLength,o=e.value,a=H.current.prev,i=-1,u=-1,c;if(0!==o.length&&null!==t&&null!==n){let e=t===n,r=t===o.length&&o.length<l;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===l)i=t-1,u=t,c="backward";else if(l>1&&o.length>1){let e=0;if(null!==a[0]&&null!==a[1]){c=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<l;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&I.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,p=-1!==u?u:n,d=null!=c?c:r;U(s),K(p),H.current.prev=[s,p,d]}if(F.current.value!==e.value&&F.current.onChange(e.value),H.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&G(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";h(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),h(e.sheet,`[data-input-otp]:autofill { ${t} }`),h(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),h(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),h(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let l=new ResizeObserver(r);return l.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),l.disconnect()}},[]);let[N,$]=r.useState(!1),[z,G]=r.useState(!1),[V,U]=r.useState(null),[q,K]=r.useState(null);r.useEffect(()=>{!function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)}(()=>{var e,t,n,r;null==(e=I.current)||e.dispatchEvent(new Event("input"));let l=null==(t=I.current)?void 0:t.selectionStart,o=null==(n=I.current)?void 0:n.selectionEnd,a=null==(r=I.current)?void 0:r.selectionDirection;null!==l&&null!==o&&(U(l),K(o),H.current.prev=[l,o,a])})},[D,z]),r.useEffect(()=>{void 0!==W&&D!==W&&W.length<w&&D.length===w&&(null==C||C(D))},[w,C,W,D]);let J=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:l}){let o=r.useRef({done:!1,refocused:!1}),[a,i]=r.useState(!1),[u,c]=r.useState(!1),[s,p]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&u,[a,u,n]),f=r.useCallback(()=>{let r=e.current,l=t.current;if(!r||!l||s||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if((0!==document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length||document.elementFromPoint(a-18,u)!==r)&&(i(!0),p(!0),!o.current.refocused&&document.activeElement===l)){let e=[l.selectionStart,l.selectionEnd];l.blur(),l.focus(),l.setSelectionRange(e[0],e[1]),o.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let l=setInterval(r,1e3);return()=>{clearInterval(l)}},[e,n]),r.useEffect(()=>{let e=l||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),o=setTimeout(f,2e3),a=setTimeout(f,5e3),i=setTimeout(()=>{p(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(o),clearTimeout(a),clearTimeout(i)}},[t,l,n,f]),{hasPWMBadge:a,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:L,inputRef:I,pushPasswordManagerStrategy:P,isFocused:z}),Q=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,w);if(t.length>0&&B&&!B.test(t))return void e.preventDefault();"string"==typeof W&&t.length<W.length&&document.dispatchEvent(new Event("selectionchange")),T(t)},[w,T,W,B]),X=r.useCallback(()=>{var e;if(I.current){let t=Math.min(I.current.value.length,w-1),n=I.current.value.length;null==(e=I.current)||e.setSelectionRange(t,n),U(t),K(n)}G(!0)},[w]),Y=r.useCallback(e=>{var t,n;let r=I.current;if(!F.current.isIOS||!e.clipboardData||!r)return;let l=e.clipboardData.getData("text/plain");e.preventDefault();let o=null==(t=I.current)?void 0:t.selectionStart,a=null==(n=I.current)?void 0:n.selectionEnd,i=(o!==a?D.slice(0,o)+l+D.slice(a):D.slice(0,o)+l+D.slice(o)).slice(0,w);if(i.length>0&&B&&!B.test(i))return;r.value=i,T(i);let u=Math.min(i.length,w-1),c=i.length;r.setSelectionRange(u,c),U(u),K(c)},[w,T,B,D]),Z=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),ee=r.useMemo(()=>({position:"absolute",inset:0,width:J.willPushPWMBadge?`calc(100% + ${J.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:J.willPushPWMBadge?`inset(0 ${J.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:E,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[J.PWM_BADGE_SPACE_WIDTH,J.willPushPWMBadge,E]),et=r.useMemo(()=>r.createElement("input",o(((e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e})({autoComplete:R.autoComplete||"one-time-code"},R),a({"data-input-otp":!0,"data-input-otp-mss":V,"data-input-otp-mse":q,inputMode:k,pattern:null==B?void 0:B.source,style:ee,maxLength:w,value:D,ref:I,onPaste:e=>{var t;Y(e),null==(t=R.onPaste)||t.call(R,e)},onChange:Q,onMouseOver:e=>{var t;$(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;$(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;X(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;G(!1),null==(t=R.onBlur)||t.call(R,e)}}))),[Q,X,Y,k,ee,w,q,V,R,null==B?void 0:B.source,D]),en=r.useMemo(()=>({slots:Array.from({length:w}).map((e,t)=>{let n=z&&null!==V&&null!==q&&(V===q&&t===V||t>=V&&t<q),r=void 0!==D[t]?D[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:z,isHovering:!R.disabled&&N}),[z,N,w,q,V,R.disabled,D]),er=r.useMemo(()=>A?A(en):r.createElement(d.Provider,{value:en},O),[O,en,A]);return r.createElement(r.Fragment,null,null!==M&&r.createElement("noscript",null,r.createElement("style",null,M)),r.createElement("div",{ref:L,"data-input-otp-container":!0,style:Z,className:x},er,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},et)))});function h(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}f.displayName="Input";var m=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},26038:(e,t,n)=>{n.d(t,{_:()=>r});function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}},45626:(e,t,n)=>{n.d(t,{default:()=>c});var r=n(26038),l=n(6874),o=n.n(l),a=n(35695),i=n(12115),u=n(85808),c=(0,i.forwardRef)(function(e,t){let{defaultLocale:n,href:l,locale:c,localeCookie:s,onClick:p,prefetch:d,unprefixed:f,...h}=e,m=(0,u.A)(),v=c!==m,g=c||m,b=function(){let[e,t]=(0,i.useState)();return(0,i.useEffect)(()=>{t(window.location.host)},[]),e}(),y=b&&f&&(f.domains[b]===g||!Object.keys(f.domains).includes(b)&&m===n&&!c)?f.pathname:l,w=(0,a.usePathname)();return v&&(d&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),d=!1),i.createElement(o(),(0,r._)({ref:t,href:y,hrefLang:v?c:void 0,onClick:function(e){(function(e,t,n,r){if(!e||r===n||null==r||!t)return;let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:o,...a}=e;a.path||(a.path=""!==l?l:"/");let i="".concat(o,"=").concat(r,";");for(let[e,t]of Object.entries(a))i+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(i+="="+t),i+=";";document.cookie=i})(s,w,m,c),p&&p(e)},prefetch:d},h))})},46767:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},48882:(e,t,n)=>{n.d(t,{default:()=>p});var r=n(26038),l=n(35695),o=n(12115),a=n(85808);function i(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function u(e,t){let n;return"string"==typeof e?n=c(t,e):(n={...e},e.pathname&&(n.pathname=c(t,e.pathname))),n}function c(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}n(87358);var s=n(45626);let p=(0,o.forwardRef)(function(e,t){let{href:n,locale:c,localeCookie:p,localePrefixMode:d,prefix:f,...h}=e,m=(0,l.usePathname)(),v=(0,a.A)(),g=c!==v,[b,y]=(0,o.useState)(()=>i(n)&&("never"!==d||g)?u(n,f):n);return(0,o.useEffect)(()=>{m&&y(function(e,t){var n,r;let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,o=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;if(!i(e))return e;let c=(n=a,(r=o)===n||r.startsWith("".concat(n,"/")));return(t!==l||c)&&null!=a?u(e,a):e}(n,c,v,m,f))},[v,n,c,m,f]),o.createElement(s.default,(0,r._)({ref:t,href:b,locale:c,localeCookie:p},h))});p.displayName="ClientLink"},75525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},78830:(e,t,n)=>{n.d(t,{default:()=>a});var r=n(26038),l=n(12115),o=n(61787);function a(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return l.createElement(o.IntlProvider,(0,r._)({locale:t},n))}},85808:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(35695),l=n(97526);let o="locale";function a(){let e,t=(0,r.useParams)();try{e=(0,l.useLocale)()}catch(n){if("string"!=typeof(null==t?void 0:t[o]))throw n;e=t[o]}return e}},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);