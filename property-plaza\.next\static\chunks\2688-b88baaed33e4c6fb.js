"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2688],{13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},70859:(e,t,n)=>{n.d(t,{UC:()=>ex,Kq:()=>eh,bL:()=>ey,l9:()=>eg});var r,o=n(12115);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return o.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}(...e),e)}var s=n(95155);function u(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,d=o.useMemo(()=>a,Object.values(a));return(0,s.jsx)(u.Provider,{value:d,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let s=a?.[e]?.[l]||i,u=o.useContext(s);if(u)return u;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var d=n(47650),c=n(66634),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=o.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?c.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),f=n(39033),v="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=o.forwardRef((e,t)=>{var n,l;let{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:h,onInteractOutside:x,onDismiss:w,...b}=e,E=o.useContext(m),[C,T]=o.useState(null),N=null!=(l=null==C?void 0:C.ownerDocument)?l:null==(n=globalThis)?void 0:n.document,[,P]=o.useState({}),O=a(t,e=>T(e)),L=Array.from(E.layers),[R]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),j=L.indexOf(R),k=C?L.indexOf(C):-1,A=E.layersWithOutsidePointerEventsDisabled.size>0,D=k>=j,M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){g("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));D&&!n&&(null==c||c(e),null==x||x(e),e.defaultPrevented||null==w||w())},N),S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&g("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==h||h(e),null==x||x(e),e.defaultPrevented||null==w||w())},N);return!function(e,t=globalThis?.document){let n=(0,f.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===E.layers.size-1&&(null==d||d(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},N),o.useEffect(()=>{if(C)return u&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),y(),()=>{u&&1===E.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[C,N,u,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),y())},[C,E]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,s.jsx)(p.div,{...b,ref:O,style:{pointerEvents:A?D?"auto":"none":void 0,...e.style},onFocusCapture:i(e.onFocusCapture,S.onFocusCapture),onBlurCapture:i(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:i(e.onPointerDownCapture,M.onPointerDownCapture)})});function y(){let e=new CustomEvent(v);document.dispatchEvent(e)}function g(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&d.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}h.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(m),r=o.useRef(null),i=a(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var x=n(61285),w=n(84945),b=n(22475),E=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,s.jsx)(p.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});E.displayName="Arrow";var C=n(52712),T=n(11275),N="Popper",[P,O]=u(N),[L,R]=P(N),j=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,s.jsx)(L,{scope:t,anchor:r,onAnchorChange:i,children:n})};j.displayName=N;var k="PopperAnchor",A=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=R(k,n),u=o.useRef(null),d=a(t,u);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||u.current)}),r?null:(0,s.jsx)(p.div,{...i,ref:d})});A.displayName=k;var D="PopperContent",[M,S]=P(D),_=o.forwardRef((e,t)=>{var n,r,i,l,u,d,c,v;let{__scopePopper:m,side:h="bottom",sideOffset:y=0,align:g="center",alignOffset:x=0,arrowPadding:E=0,avoidCollisions:N=!0,collisionBoundary:P=[],collisionPadding:O=0,sticky:L="partial",hideWhenDetached:j=!1,updatePositionStrategy:k="optimized",onPlaced:A,...S}=e,_=R(D,m),[I,U]=o.useState(null),W=a(t,e=>U(e)),[z,X]=o.useState(null),Y=(0,T.X)(z),$=null!=(c=null==Y?void 0:Y.width)?c:0,V=null!=(v=null==Y?void 0:Y.height)?v:0,q="number"==typeof O?O:{top:0,right:0,bottom:0,left:0,...O},K=Array.isArray(P)?P:[P],G=K.length>0,J={padding:q,boundary:K.filter(B),altBoundary:G},{refs:Q,floatingStyles:Z,placement:ee,isPositioned:et,middlewareData:en}=(0,w.we)({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,b.ll)(...t,{animationFrame:"always"===k})},elements:{reference:_.anchor},middleware:[(0,w.cY)({mainAxis:y+V,alignmentAxis:x}),N&&(0,w.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===L?(0,w.ER)():void 0,...J}),N&&(0,w.UU)({...J}),(0,w.Ej)({...J,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),z&&(0,w.UE)({element:z,padding:E}),F({arrowWidth:$,arrowHeight:V}),j&&(0,w.jD)({strategy:"referenceHidden",...J})]}),[er,eo]=H(ee),ei=(0,f.c)(A);(0,C.N)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null==(n=en.arrow)?void 0:n.x,ea=null==(r=en.arrow)?void 0:r.y,es=(null==(i=en.arrow)?void 0:i.centerOffset)!==0,[eu,ed]=o.useState();return(0,C.N)(()=>{I&&ed(window.getComputedStyle(I).zIndex)},[I]),(0,s.jsx)("div",{ref:Q.setFloating,"data-radix-popper-content-wrapper":"",style:{...Z,transform:et?Z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null==(l=en.transformOrigin)?void 0:l.x,null==(u=en.transformOrigin)?void 0:u.y].join(" "),...(null==(d=en.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(M,{scope:m,placedSide:er,onArrowChange:X,arrowX:el,arrowY:ea,shouldHideArrow:es,children:(0,s.jsx)(p.div,{"data-side":er,"data-align":eo,...S,ref:W,style:{...S.style,animation:et?void 0:"none"}})})})});_.displayName=D;var I="PopperArrow",U={top:"bottom",right:"left",bottom:"top",left:"right"},W=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=S(I,n),i=U[o.placedSide];return(0,s.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(E,{...r,ref:t,style:{...r.style,display:"block"}})})});function B(e){return null!==e}W.displayName=I;var F=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,d=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,p=d?0:e.arrowHeight,[f,v]=H(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!=(i=null==(r=u.arrow)?void 0:r.x)?i:0)+c/2,y=(null!=(l=null==(o=u.arrow)?void 0:o.y)?l:0)+p/2,g="",x="";return"bottom"===f?(g=d?m:"".concat(h,"px"),x="".concat(-p,"px")):"top"===f?(g=d?m:"".concat(h,"px"),x="".concat(s.floating.height+p,"px")):"right"===f?(g="".concat(-p,"px"),x=d?m:"".concat(y,"px")):"left"===f&&(g="".concat(s.floating.width+p,"px"),x=d?m:"".concat(y,"px")),{data:{x:g,y:x}}}});function H(e){let[t,n="center"]=e.split("-");return[t,n]}o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);(0,C.N)(()=>u(!0),[]);let c=i||a&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?d.createPortal((0,s.jsx)(p.div,{...l,ref:t}),c):null}).displayName="Portal";var z=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef({}),a=o.useRef(e),s=o.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=X(l.current);s.current="mounted"===u?e:"none"},[u]),(0,C.N)(()=>{let t=l.current,n=a.current;if(n!==e){let r=s.current,o=X(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,C.N)(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=X(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(s.current=X(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=a(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function X(e){return(null==e?void 0:e.animationName)||"none"}z.displayName="Presence";var Y=n(5845),$=o.forwardRef((e,t)=>(0,s.jsx)(p.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));$.displayName="VisuallyHidden";var[V,q]=u("Tooltip",[O]),K=O(),G="TooltipProvider",J="tooltip.open",[Q,Z]=V(G),ee=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:l}=e,[a,u]=o.useState(!0),d=o.useRef(!1),c=o.useRef(0);return o.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,s.jsx)(Q,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(c.current),u(!1)},[]),onClose:o.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u(!0),r)},[r]),isPointerInTransitRef:d,onPointerInTransitChange:o.useCallback(e=>{d.current=e},[]),disableHoverableContent:i,children:l})};ee.displayName=G;var et="Tooltip",[en,er]=V(et),eo=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:u}=e,d=Z(et,e.__scopeTooltip),c=K(t),[p,f]=o.useState(null),v=(0,x.B)(),m=o.useRef(0),h=null!=a?a:d.disableHoverableContent,y=null!=u?u:d.delayDuration,g=o.useRef(!1),[w=!1,b]=(0,Y.i)({prop:r,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(J))):d.onClose(),null==l||l(e)}}),E=o.useMemo(()=>w?g.current?"delayed-open":"instant-open":"closed",[w]),C=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,b(!0)},[b]),T=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),N=o.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,b(!0),m.current=0},y)},[y,b]);return o.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,s.jsx)(j,{...c,children:(0,s.jsx)(en,{scope:t,contentId:v,open:w,stateAttribute:E,trigger:p,onTriggerChange:f,onTriggerEnter:o.useCallback(()=>{d.isOpenDelayed?N():C()},[d.isOpenDelayed,N,C]),onTriggerLeave:o.useCallback(()=>{h?T():(window.clearTimeout(m.current),m.current=0)},[T,h]),onOpen:C,onClose:T,disableHoverableContent:h,children:n})})};eo.displayName=et;var ei="TooltipTrigger",el=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,l=er(ei,n),u=Z(ei,n),d=K(n),c=a(t,o.useRef(null),l.onTriggerChange),f=o.useRef(!1),v=o.useRef(!1),m=o.useCallback(()=>f.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,s.jsx)(A,{asChild:!0,...d,children:(0,s.jsx)(p.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...r,ref:c,onPointerMove:i(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0))}),onPointerLeave:i(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:i(e.onPointerDown,()=>{f.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:i(e.onFocus,()=>{f.current||l.onOpen()}),onBlur:i(e.onBlur,l.onClose),onClick:i(e.onClick,l.onClose)})})});el.displayName=ei;var[ea,es]=V("TooltipPortal",{forceMount:void 0}),eu="TooltipContent",ed=o.forwardRef((e,t)=>{let n=es(eu,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=er(eu,e.__scopeTooltip);return(0,s.jsx)(z,{present:r||l.open,children:l.disableHoverableContent?(0,s.jsx)(ev,{side:o,...i,ref:t}):(0,s.jsx)(ec,{side:o,...i,ref:t})})}),ec=o.forwardRef((e,t)=>{let n=er(eu,e.__scopeTooltip),r=Z(eu,e.__scopeTooltip),i=o.useRef(null),l=a(t,i),[u,d]=o.useState(null),{trigger:c,onClose:p}=n,f=i.current,{onPointerInTransitChange:v}=r,m=o.useCallback(()=>{d(null),v(!1)},[v]),h=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(c&&f){let e=e=>h(e,f),t=e=>h(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,h,m]),o.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>r!=u>r&&n<(s-l)*(r-a)/(u-a)+l&&(o=!o)}return o}(n,u);r?m():o&&(m(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,p,m]),(0,s.jsx)(ev,{...e,ref:l})}),[ep,ef]=V(et,{isInside:!1}),ev=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...u}=e,d=er(eu,n),p=K(n),{onClose:f}=d;return o.useEffect(()=>(document.addEventListener(J,f),()=>document.removeEventListener(J,f)),[f]),o.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,s.jsx)(h,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,s.jsxs)(_,{"data-state":d.stateAttribute,...p,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,s.jsx)(c.xV,{children:r}),(0,s.jsx)(ep,{scope:n,isInside:!0,children:(0,s.jsx)($,{id:d.contentId,role:"tooltip",children:i||r})})]})})});ed.displayName=eu;var em="TooltipArrow";o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=K(n);return ef(em,n).isInside?null:(0,s.jsx)(W,{...o,...r,ref:t})}).displayName=em;var eh=ee,ey=eo,eg=el,ex=ed}}]);