"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2770],{9953:(t,e,s)=>{s.d(e,{Z:()=>function t(e,s,u){return(void 0===u&&(u=1/0),(0,a.T)(s))?t(function(t,i){return(0,n.T)(function(e,n){return s(t,e,i,n)})((0,r.Tg)(e(t,i)))},u):("number"==typeof s&&(u=s),(0,i.N)(function(t,s){var n,i,a,c,l,d,h,p,f;return n=u,a=[],c=0,l=0,d=!1,h=function(){!d||a.length||c||s.complete()},p=function(t){return c<n?f(t):a.push(t)},f=function(t){c++;var u=!1;(0,r.Tg)(e(t,l++)).subscribe((0,o._)(s,function(t){i?p(t):s.next(t)},function(){u=!0},void 0,function(){if(u)try{for(c--;a.length&&c<n;)!function(){var t=a.shift();f(t)}();h()}catch(t){s.error(t)}}))},t.subscribe((0,o._)(s,p,function(){d=!0,h()})),function(){}}))}});var n=s(50793),r=s(57173),i=s(1125),o=(s(74962),s(98175)),a=s(91208)},26959:(t,e,s)=>{s.d(e,{$:()=>$,B:()=>K,C:()=>tx,D:()=>X,E:()=>tE,F:()=>Z,G:()=>tm,H:()=>function t(e,s,n,r){if("string"==typeof s){let t=to(e,s).next(n,r);return t&&t.length?t:e.next(n,r)}if(1===Object.keys(s).length){let i=Object.keys(s),o=t(to(e,i[0]),s[i[0]],n,r);return o&&o.length?o:e.next(n,r)}let i=[];for(let o of Object.keys(s)){let a=s[o];if(!a)continue;let u=t(to(e,o),a,n,r);u&&i.push(...u)}return i.length?i:e.next(n,r)},I:()=>tv,J:()=>d,K:()=>tf,L:()=>ti,M:()=>f,N:()=>i,O:()=>H,P:()=>ta,Q:()=>tq,R:()=>k,S:()=>r,T:()=>a,U:()=>l,V:()=>q,W:()=>t$,X:()=>u,c:()=>j,f:()=>N,g:()=>tu,h:()=>C,k:()=>L,l:()=>g,q:()=>tt,r:()=>tj,t:()=>v,u:()=>b,v:()=>Y,w:()=>B,x:()=>w,y:()=>Q,z:()=>te});class n{constructor(t){this._process=t,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(t){let e={value:t,next:null};if(this._current){this._last.next=e,this._last=e;return}this._current=e,this._last=e,this._active&&this.flush()}flush(){for(;this._current;){let t=this._current;this._process(t.value),this._current=t.next}this._last=null}}let r=".",i="",o="xstate.init",a="xstate.error",u="xstate.stop";function c(t,e){return{type:`xstate.done.state.${t}`,output:e}}function l(t,e){return{type:`xstate.error.actor.${t}`,error:e,actorId:t}}function d(t){return{type:o,input:t}}function h(t){setTimeout(()=>{throw t})}let p="function"==typeof Symbol&&Symbol.observable||"@@observable";function f(t){if(_(t))return t;let e=[],s="";for(let n=0;n<t.length;n++){switch(t.charCodeAt(n)){case 92:s+=t[n+1],n++;continue;case 46:e.push(s),s="";continue}s+=t[n]}return e.push(s),e}function y(t){var e;return(e=t)&&"object"==typeof e&&"machine"in e&&"value"in e?t.value:"string"!=typeof t?t:function(t){if(1===t.length)return t[0];let e={},s=e;for(let e=0;e<t.length-1;e++)if(e===t.length-2)s[t[e]]=t[e+1];else{let n=s;s={},n[t[e]]=s}return e}(f(t))}function g(t,e){let s={},n=Object.keys(t);for(let r=0;r<n.length;r++){let i=n[r];s[i]=e(t[i],i,t,r)}return s}function v(t){var e;return void 0===t?[]:_(e=t)?e:[e]}function m(t,e,s,n){return"function"==typeof t?t({context:e,event:s,self:n}):t}function _(t){return Array.isArray(t)}function b(t){var e;return(_(e=t)?e:[e]).map(t=>void 0===t||"string"==typeof t?{target:t}:t)}function x(t){if(void 0!==t&&""!==t)return v(t)}function S(t,e,s){let n="object"==typeof t,r=n?t:void 0;return{next:(n?t.next:t)?.bind(r),error:(n?t.error:e)?.bind(r),complete:(n?t.complete:s)?.bind(r)}}function w(t,e){return`${e}.${t}`}function k(t,e){let s=e.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!s)return t.implementations.actors[e];let[,n,r]=s,i=t.getStateNodeById(r).config.invoke;return(Array.isArray(i)?i[n]:i).src}function T(t,e){return`${t.sessionId}.${e}`}let I=0,E=!1,$=1,q=function(t){return t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped",t}({}),R={clock:{setTimeout:(t,e)=>setTimeout(t,e),clearTimeout:t=>clearTimeout(t)},logger:console.log.bind(console),devTools:!1};class O{constructor(t,e){this.logic=t,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new n(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=q.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];let s={...R,...e},{clock:r,logger:i,parent:o,syncSnapshot:a,id:u,systemId:c,inspect:l}=s;this.system=o?o.system:function(t,e){let s=new Map,n=new Map,r=new WeakMap,i=new Set,o={},{clock:a,logger:u}=e,c={schedule:(t,e,s,n,r=Math.random().toString(36).slice(2))=>{let i={source:t,target:e,event:s,delay:n,id:r,startedAt:Date.now()},u=T(t,r);l._snapshot._scheduledEvents[u]=i;let c=a.setTimeout(()=>{delete o[u],delete l._snapshot._scheduledEvents[u],l._relay(t,e,s)},n);o[u]=c},cancel:(t,e)=>{let s=T(t,e),n=o[s];delete o[s],delete l._snapshot._scheduledEvents[s],void 0!==n&&a.clearTimeout(n)},cancelAll:t=>{for(let e in l._snapshot._scheduledEvents){let s=l._snapshot._scheduledEvents[e];s.source===t&&c.cancel(t,s.id)}}},l={_snapshot:{_scheduledEvents:(e?.snapshot&&e.snapshot.scheduler)??{}},_bookId:()=>`x:${I++}`,_register:(t,e)=>(s.set(t,e),t),_unregister:t=>{s.delete(t.sessionId);let e=r.get(t);void 0!==e&&(n.delete(e),r.delete(t))},get:t=>n.get(t),_set:(t,e)=>{let s=n.get(t);if(s&&s!==e)throw Error(`Actor with system ID '${t}' already exists.`);n.set(t,e),r.set(e,t)},inspect:t=>{let e=S(t);return i.add(e),{unsubscribe(){i.delete(e)}}},_sendInspectionEvent:e=>{if(!i.size)return;let s={...e,rootId:t.sessionId};i.forEach(t=>t.next?.(s))},_relay:(t,e,s)=>{l._sendInspectionEvent({type:"@xstate.event",sourceRef:t,actorRef:e,event:s}),e._send(s)},scheduler:c,getSnapshot:()=>({_scheduledEvents:{...l._snapshot._scheduledEvents}}),start:()=>{let t=l._snapshot._scheduledEvents;for(let e in l._snapshot._scheduledEvents={},t){let{source:s,target:n,event:r,delay:i,id:o}=t[e];c.schedule(s,n,r,i,o)}},_clock:a,_logger:u};return l}(this,{clock:r,logger:i}),l&&!o&&this.system.inspect(S(l)),this.sessionId=this.system._bookId(),this.id=u??this.sessionId,this.logger=e?.logger??this.system._logger,this.clock=e?.clock??this.system._clock,this._parent=o,this._syncSnapshot=a,this.options=s,this.src=s.src??t,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:t=>{this._deferred.push(t)},system:this.system,stopChild:t=>{if(t._parent!==this)throw Error(`Cannot stop child actor ${t.id} of ${this.id} because it is not a child`);t._stop()},emit:t=>{let e=this.eventListeners.get(t.type),s=this.eventListeners.get("*");if(e||s)for(let n of[...e?e.values():[],...s?s.values():[]])n(t)},actionExecutor:t=>{let e=()=>{if(this._actorScope.system._sendInspectionEvent({type:"@xstate.action",actorRef:this,action:{type:t.type,params:t.params}}),!t.exec)return;let e=E;try{E=!0,t.exec(t.info,t.params)}finally{E=e}};this._processingStatus===q.Running?e():this._deferred.push(e)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),c&&(this._systemId=c,this.system._set(c,this)),this._initState(e?.snapshot??e?.state),c&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(t){try{this._snapshot=t?this.logic.restoreSnapshot?this.logic.restoreSnapshot(t,this._actorScope):t:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(t){this._snapshot={status:"error",output:void 0,error:t}}}update(t,e){let s;for(this._snapshot=t;s=this._deferred.shift();)try{s()}catch(e){this._deferred.length=0,this._snapshot={...t,status:"error",error:e}}switch(this._snapshot.status){case"active":for(let e of this.observers)try{e.next?.(t)}catch(t){h(t)}break;case"done":var n;for(let e of this.observers)try{e.next?.(t)}catch(t){h(t)}this._stopProcedure(),this._complete(),this._doneEvent=(n=this.id,{type:`xstate.done.actor.${n}`,output:this._snapshot.output,actorId:n}),this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:e,snapshot:t})}subscribe(t,e,s){let n=S(t,e,s);if(this._processingStatus!==q.Stopped)this.observers.add(n);else switch(this._snapshot.status){case"done":try{n.complete?.()}catch(t){h(t)}break;case"error":{let t=this._snapshot.error;if(n.error)try{n.error(t)}catch(t){h(t)}else h(t)}}return{unsubscribe:()=>{this.observers.delete(n)}}}on(t,e){let s=this.eventListeners.get(t);s||(s=new Set,this.eventListeners.set(t,s));let n=e.bind(void 0);return s.add(n),{unsubscribe:()=>{s.delete(n)}}}start(){if(this._processingStatus===q.Running)return this;this._syncSnapshot&&this.subscribe({next:t=>{"active"===t.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:t})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=q.Running;let t=d(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:t}),this._snapshot.status){case"done":return this.update(this._snapshot,t),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(t){return this._snapshot={...this._snapshot,status:"error",error:t},this._error(t),this}return this.update(this._snapshot,t),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(t){let e,s;try{e=this.logic.transition(this._snapshot,t,this._actorScope)}catch(t){s={err:t}}if(s){let{err:t}=s;this._snapshot={...this._snapshot,status:"error",error:t},this._error(t);return}this.update(e,t),t.type===u&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===q.Stopped||((this.mailbox.clear(),this._processingStatus===q.NotStarted)?this._processingStatus=q.Stopped:this.mailbox.enqueue({type:u})),this}stop(){if(this._parent)throw Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(let t of this.observers)try{t.complete?.()}catch(t){h(t)}this.observers.clear()}_reportError(t){if(!this.observers.size){this._parent||h(t);return}let e=!1;for(let s of this.observers){let n=s.error;e||=!n;try{n?.(t)}catch(t){h(t)}}this.observers.clear(),e&&h(t)}_error(t){this._stopProcedure(),this._reportError(t),this._parent&&this.system._relay(this,this._parent,l(this.id,t))}_stopProcedure(){return this._processingStatus!==q.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new n(this._process.bind(this)),this._processingStatus=q.Stopped,this.system._unregister(this)),this}_send(t){this._processingStatus!==q.Stopped&&this.mailbox.enqueue(t)}send(t){this.system._relay(void 0,this,t)}attachDevTools(){let{devTools:t}=this.options;t&&("function"==typeof t?t:t=>{if("undefined"==typeof window)return;let e=function(){let t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==s.g?s.g:void 0;if(t.__xstate__)return t.__xstate__}();e&&e.register(t)})(this)}toJSON(){return{xstate$$type:$,id:this.id}}getPersistedSnapshot(t){return this.logic.getPersistedSnapshot(this._snapshot,t)}[p](){return this}getSnapshot(){return this._snapshot}}function j(t,...[e]){return new O(t,e)}function D(t,e,s,n,{sendId:r}){return[e,{sendId:"function"==typeof r?r(s,n):r},void 0]}function A(t,e){t.defer(()=>{t.system.scheduler.cancel(t.self,e.sendId)})}function N(t){function e(t,e){}return e.type="xstate.cancel",e.sendId=t,e.resolve=D,e.execute=A,e}function M(t,e,s,n,{id:r,systemId:i,src:o,input:a,syncSnapshot:u}){let c,l,d="string"==typeof o?k(e.machine,o):o,h="function"==typeof r?r(s):r;return d&&(l="function"==typeof a?a({context:e.context,event:s.event,self:t.self}):a,c=j(d,{id:h,src:o,parent:t.self,syncSnapshot:u,systemId:i,input:l})),[t$(e,{children:{...e.children,[h]:c}}),{id:r,systemId:i,actorRef:c,src:o,input:l},void 0]}function P(t,{actorRef:e}){e&&t.defer(()=>{e._processingStatus!==q.Stopped&&e.start()})}function C(...[t,{id:e,systemId:s,input:n,syncSnapshot:r=!1}={}]){function i(t,e){}return i.type="xstate.spawnChild",i.id=e,i.systemId=s,i.src=t,i.input=n,i.syncSnapshot=r,i.resolve=M,i.execute=P,i}function U(t,e,s,n,{actorRef:r}){let i="function"==typeof r?r(s,n):r,o="string"==typeof i?e.children[i]:i,a=e.children;return o&&(a={...a},delete a[o.id]),[t$(e,{children:a}),o,void 0]}function V(t,e){if(e){if(t.system._unregister(e),e._processingStatus!==q.Running)return void t.stopChild(e);t.defer(()=>{t.stopChild(e)})}}function L(t){function e(t,e){}return e.type="xstate.stopChild",e.actorRef=t,e.resolve=U,e.execute=V,e}function B(t,e,s,n){let{machine:r}=n,i="function"==typeof t,o=i?t:r.implementations.guards["string"==typeof t?t:t.type];if(!i&&!o)throw Error(`Guard '${"string"==typeof t?t:t.type}' is not implemented.'.`);if("function"!=typeof o)return B(o,e,s,n);let a={context:e,event:s},u=i||"string"==typeof t?void 0:"params"in t?"function"==typeof t.params?t.params({context:e,event:s}):t.params:void 0;return"check"in o?o.check(n,a,o):o(a,u)}let J=t=>"atomic"===t.type||"final"===t.type;function W(t){return Object.values(t.states).filter(t=>"history"!==t.type)}function z(t,e){let s=[];if(e===t)return s;let n=t.parent;for(;n&&n!==e;)s.push(n),n=n.parent;return s}function X(t){let e=new Set(t),s=F(e);for(let t of e)if("compound"!==t.type||s.get(t)&&s.get(t).length){if("parallel"===t.type){for(let s of W(t))if("history"!==s.type&&!e.has(s))for(let t of tr(s))e.add(t)}}else tr(t).forEach(t=>e.add(t));for(let t of e){let s=t.parent;for(;s;)e.add(s),s=s.parent}return e}function F(t){let e=new Map;for(let s of t)e.has(s)||e.set(s,[]),s.parent&&(e.has(s.parent)||e.set(s.parent,[]),e.get(s.parent).push(s));return e}function G(t,e){return function t(e,s){let n=s.get(e);if(!n)return{};if("compound"===e.type){let t=n[0];if(!t)return{};if(J(t))return t.key}let r={};for(let e of n)r[e.key]=t(e,s);return r}(t,F(X(e)))}function Z(t,e){return"compound"===e.type?W(e).some(e=>"final"===e.type&&t.has(e)):"parallel"===e.type?W(e).every(e=>Z(t,e)):"final"===e.type}let H=t=>"#"===t[0];function K(t,e){return t.transitions.get(e)||[...t.transitions.keys()].filter(t=>{if("*"===t)return!0;if(!t.endsWith(".*"))return!1;let s=t.split("."),n=e.split(".");for(let t=0;t<s.length;t++){let e=s[t],r=n[t];if("*"===e)return t===s.length-1;if(e!==r)return!1}return!0}).sort((t,e)=>e.length-t.length).flatMap(e=>t.transitions.get(e))}function Q(t){let e=t.config.after;return e?Object.keys(e).flatMap(s=>{let n=e[s],r=Number.isNaN(+s)?s:+s,i=(e=>{var s;let n=(s=t.id,{type:`xstate.after.${e}.${s}`}),r=n.type;return t.entry.push(tj(n,{id:r,delay:e})),t.exit.push(N(r)),r})(r);return v("string"==typeof n?{target:n}:n).map(t=>({...t,event:i,delay:r}))}).map(e=>{let{delay:s}=e;return{...Y(t,e.event,e),delay:s}}):[]}function Y(t,e,s){let n=x(s.target),i=s.reenter??!1,o=function(t,e){if(void 0!==e)return e.map(e=>{if("string"!=typeof e)return e;if(H(e))return t.machine.getStateNodeById(e);let s=e[0]===r;if(s&&!t.parent)return ta(t,e.slice(1));let n=s?t.key+e:e;if(t.parent)try{return ta(t.parent,n)}catch(e){throw Error(`Invalid transition definition for state node '${t.id}':
${e.message}`)}throw Error(`Invalid target: "${e}" is not a valid target from the root node. Did you mean ".${e}"?`)})}(t,n),a={...s,actions:v(s.actions),guard:s.guard,target:o,source:t,reenter:i,eventType:e,toJSON:()=>({...a,source:`#${t.id}`,target:o?o.map(t=>`#${t.id}`):void 0})};return a}function tt(t){let e=new Map;if(t.config.on)for(let s of Object.keys(t.config.on)){if(s===i)throw Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');let n=t.config.on[s];e.set(s,b(n).map(e=>Y(t,s,e)))}if(t.config.onDone){let s=`xstate.done.state.${t.id}`;e.set(s,b(t.config.onDone).map(e=>Y(t,s,e)))}for(let s of t.invoke){if(s.onDone){let n=`xstate.done.actor.${s.id}`;e.set(n,b(s.onDone).map(e=>Y(t,n,e)))}if(s.onError){let n=`xstate.error.actor.${s.id}`;e.set(n,b(s.onError).map(e=>Y(t,n,e)))}if(s.onSnapshot){let n=`xstate.snapshot.${s.id}`;e.set(n,b(s.onSnapshot).map(e=>Y(t,n,e)))}}for(let s of t.after){let t=e.get(s.eventType);t||(t=[],e.set(s.eventType,t)),t.push(s)}return e}function te(t,e){let s="string"==typeof e?t.states[e]:e?t.states[e.target]:void 0;if(!s&&e)throw Error(`Initial state node "${e}" not found on parent state node #${t.id}`);let n={source:t,actions:e&&"string"!=typeof e?v(e.actions):[],eventType:null,reenter:!1,target:s?[s]:[],toJSON:()=>({...n,source:`#${t.id}`,target:s?[`#${s.id}`]:[]})};return n}function ts(t){let e=x(t.config.target);return e?{target:e.map(e=>"string"==typeof e?ta(t.parent,e):e)}:t.parent.initial}function tn(t){return"history"===t.type}function tr(t){let e=ti(t);for(let s of e)for(let n of z(s,t))e.add(n);return e}function ti(t){let e=new Set;return!function t(s){if(!e.has(s)){if(e.add(s),"compound"===s.type)t(s.initial.target[0]);else if("parallel"===s.type)for(let e of W(s))t(e)}}(t),e}function to(t,e){if(H(e))return t.machine.getStateNodeById(e);if(!t.states)throw Error(`Unable to retrieve child state '${e}' from '${t.id}'; no child states exist.`);let s=t.states[e];if(!s)throw Error(`Child state '${e}' does not exist on '${t.id}'`);return s}function ta(t,e){if("string"==typeof e&&H(e))try{return t.machine.getStateNodeById(e)}catch{}let s=f(e).slice(),n=t;for(;s.length;){let t=s.shift();if(!t.length)break;n=to(n,t)}return n}function tu(t,e){if("string"==typeof e){let s=t.states[e];if(!s)throw Error(`State '${e}' does not exist on '${t.id}'`);return[t,s]}let s=Object.keys(e),n=s.map(e=>to(t,e)).filter(Boolean);return[t.machine.root,t].concat(n,s.reduce((s,n)=>{let r=to(t,n);if(!r)return s;let i=tu(r,e[n]);return s.concat(i)},[]))}function tc(t,e){let s=t;for(;s.parent&&s.parent!==e;)s=s.parent;return s.parent===e}function tl(t,e,s){let n=new Set;for(let r of t){let t=!1,i=new Set;for(let o of n)if(function(t,e){let s=new Set(t),n=new Set(e);for(let t of s)if(n.has(t))return!0;for(let t of n)if(s.has(t))return!0;return!1}(tp([r],e,s),tp([o],e,s)))if(tc(r.source,o.source))i.add(o);else{t=!0;break}if(!t){for(let t of i)n.delete(t);n.add(r)}}return Array.from(n)}function td(t,e){if(!t.target)return[];let s=new Set;for(let n of t.target)if(tn(n))if(e[n.id])for(let t of e[n.id])s.add(t);else for(let t of td(ts(n),e))s.add(t);else s.add(n);return[...s]}function th(t,e){let s=td(t,e);if(!s)return;if(!t.reenter&&s.every(e=>e===t.source||tc(e,t.source)))return t.source;let n=function(t){let[e,...s]=t;for(let t of z(e,void 0))if(s.every(e=>tc(e,t)))return t}(s.concat(t.source));return n||(t.reenter?void 0:t.source.machine.root)}function tp(t,e,s){let n=new Set;for(let r of t)if(r.target?.length){let t=th(r,s);for(let s of(r.reenter&&r.source===t&&n.add(t),e))tc(s,t)&&n.add(s)}return[...n]}function tf(t,e,s,n,r,i){if(!t.length)return e;let o=new Set(e._nodes),a=e.historyValue,u=tl(t,o,a),l=e;r||([l,a]=function(t,e,s,n,r,i,o,a){let u,c=t,l=tp(n,r,i);for(let t of(l.sort((t,e)=>e.order-t.order),l))for(let e of function(t){return Object.keys(t.states).map(e=>t.states[e]).filter(t=>"history"===t.type)}(t)){let s;s="deep"===e.history?e=>J(e)&&tc(e,t):e=>e.parent===t,(u??={...i})[e.id]=Array.from(r).filter(s)}for(let t of l)c=tv(c,e,s,[...t.exit,...t.invoke.map(t=>L(t.id))],o,void 0),r.delete(t);return[c,u||i]}(l,n,s,u,o,a,i,s.actionExecutor)),l=function(t,e,s,n,r,i,o,a){let u=t,l=new Set,d=new Set;(function(t,e,s,n){for(let r of t){let t=th(r,e);for(let i of r.target||[])!tn(i)&&(r.source!==i||r.source!==t||r.reenter)&&(n.add(i),s.add(i)),ty(i,e,s,n);for(let i of td(r,e)){let o=z(i,t);t?.type==="parallel"&&o.push(t),tg(n,e,s,o,!r.source.parent&&r.reenter?void 0:t)}}})(n,o,d,l),a&&d.add(t.machine.root);let h=new Set;for(let t of[...l].sort((t,e)=>t.order-e.order)){r.add(t);let n=[];for(let e of(n.push(...t.entry),t.invoke))n.push(C(e.src,{...e,syncSnapshot:!!e.onSnapshot}));if(d.has(t)){let e=t.initial.actions;n.push(...e)}if(u=tv(u,e,s,n,i,t.invoke.map(t=>t.id)),"final"===t.type){let n=t.parent,o=n?.type==="parallel"?n:n?.parent,a=o||t;for(n?.type==="compound"&&i.push(c(n.id,void 0!==t.output?m(t.output,u.context,e,s.self):void 0));o?.type==="parallel"&&!h.has(o)&&Z(r,o);)h.add(o),i.push(c(o.id)),a=o,o=o.parent;if(o)continue;u=t$(u,{status:"done",output:function(t,e,s,n,r){if(void 0===n.output)return;let i=c(r.id,void 0!==r.output&&r.parent?m(r.output,t.context,e,s.self):void 0);return m(n.output,t.context,i,s.self)}(u,e,s,u.machine.root,a)})}}return u}(l=tv(l,n,s,u.flatMap(t=>t.actions),i,void 0),n,s,u,o,i,a,r);let d=[...o];"done"===l.status&&(l=tv(l,n,s,d.sort((t,e)=>e.order-t.order).flatMap(t=>t.exit),i,void 0));try{if(a===e.historyValue&&function(t,e){if(t.length!==e.size)return!1;for(let s of t)if(!e.has(s))return!1;return!0}(e._nodes,o))return l;return t$(l,{_nodes:d,historyValue:a})}catch(t){throw t}}function ty(t,e,s,n){var r,i,o,a;if(tn(t))if(e[t.id]){let o=e[t.id];for(let t of o)n.add(t),ty(t,e,s,n);for(let a of o){r=a,i=t.parent,tg(n,e,s,z(r,i))}}else{let r=ts(t);for(let i of r.target)n.add(i),r===t.parent?.initial&&s.add(t.parent),ty(i,e,s,n);for(let i of r.target){o=i,a=t.parent,tg(n,e,s,z(o,a))}}else if("compound"===t.type){let[r]=t.initial.target;tn(r)||(n.add(r),s.add(r)),ty(r,e,s,n),tg(n,e,s,z(r,t))}else if("parallel"===t.type)for(let r of W(t).filter(t=>!tn(t)))[...n].some(t=>tc(t,r))||(tn(r)||(n.add(r),s.add(r)),ty(r,e,s,n))}function tg(t,e,s,n,r){for(let i of n)if((!r||tc(i,r))&&t.add(i),"parallel"===i.type)for(let n of W(i).filter(t=>!tn(t)))[...t].some(t=>tc(t,n))||(t.add(n),ty(n,e,s,t))}function tv(t,e,s,n,r,i){let o=i?[]:void 0,a=function t(e,s,n,r,i,o){let{machine:a}=e,u=e;for(let e of r){var c;let r="function"==typeof e,l=r?e:(c="string"==typeof e?e:e.type,a.implementations.actions[c]),d={context:u.context,event:s,self:n.self,system:n.system},h=r||"string"==typeof e?void 0:"params"in e?"function"==typeof e.params?e.params({context:u.context,event:s}):e.params:void 0;if(!l||!("resolve"in l)){n.actionExecutor({type:"string"==typeof e?e:"object"==typeof e?e.type:e.name||"(anonymous)",info:d,params:h,exec:l});continue}let[p,f,y]=l.resolve(n,u,d,h,l,i);u=p,"retryResolve"in l&&o?.push([l,f]),"execute"in l&&n.actionExecutor({type:l.type,info:d,params:f,exec:l.execute.bind(null,n,f)}),y&&(u=t(u,s,n,y,i,o))}return u}(t,e,s,n,{internalQueue:r,deferredActorIds:i},o);return o?.forEach(([t,e])=>{t.retryResolve(s,a,e)}),a}function tm(t,e,s,n){let r=t,i=[];function a(t,e,n){s.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:s.self,event:e,snapshot:t,_transitions:n}),i.push(t)}if(e.type===u)return a(r=t$(t_(r,e,s),{status:"stopped"}),e,[]),{snapshot:r,microstates:i};let c=e;if(c.type!==o){let e=c,o=e.type.startsWith("xstate.error.actor"),u=tb(e,r);if(o&&!u.length)return a(r=t$(t,{status:"error",error:e.error}),e,[]),{snapshot:r,microstates:i};a(r=tf(u,t,s,c,!1,n),e,u)}let l=!0;for(;"active"===r.status;){let t=l?function(t,e){let s=new Set;for(let n of t._nodes.filter(J))t:for(let r of[n].concat(z(n,void 0)))if(r.always){for(let n of r.always)if(void 0===n.guard||B(n.guard,t.context,e,t)){s.add(n);break t}}return tl(Array.from(s),new Set(t._nodes),t.historyValue)}(r,c):[],e=t.length?r:void 0;if(!t.length){if(!n.length)break;t=tb(c=n.shift(),r)}l=(r=tf(t,r,s,c,!1,n))!==e,a(r,c,t)}return"active"!==r.status&&t_(r,c,s),{snapshot:r,microstates:i}}function t_(t,e,s){return tv(t,e,s,Object.values(t.children).map(t=>L(t)),[],void 0)}function tb(t,e){return e.machine.getTransitionData(e,t)}function tx(t,e){let s=X(tu(t,e));return G(t,[...s])}let tS=function(t){return function t(e,s){let n=y(e),r=y(s);return"string"==typeof r?"string"==typeof n&&r===n:"string"==typeof n?n in r:Object.keys(n).every(e=>e in r&&t(n[e],r[e]))}(t,this.value)},tw=function(t){return this.tags.has(t)},tk=function(t){let e=this.machine.getTransitionData(this,t);return!!e?.length&&e.some(t=>void 0!==t.target||t.actions.length)},tT=function(){let{_nodes:t,tags:e,machine:s,getMeta:n,toJSON:r,can:i,hasTag:o,matches:a,...u}=this;return{...u,tags:Array.from(e)}},tI=function(){return this._nodes.reduce((t,e)=>(void 0!==e.meta&&(t[e.id]=e.meta),t),{})};function tE(t,e){return{status:t.status,output:t.output,error:t.error,machine:e,context:t.context,_nodes:t._nodes,value:G(e.root,t._nodes),tags:new Set(t._nodes.flatMap(t=>t.tags)),children:t.children,historyValue:t.historyValue||{},matches:tS,hasTag:tw,can:tk,getMeta:tI,toJSON:tT}}function t$(t,e={}){return tE({...t,...e},t.machine)}function tq(t,e){let{_nodes:s,tags:n,machine:r,children:i,context:o,can:a,hasTag:u,matches:c,getMeta:l,toJSON:d,...h}=t,p={};for(let t in i){let s=i[t];p[t]={snapshot:s.getPersistedSnapshot(e),src:s.src,systemId:s._systemId,syncSnapshot:s._syncSnapshot}}return{...h,context:function t(e){let s;for(let n in e){let r=e[n];if(r&&"object"==typeof r)if("sessionId"in r&&"send"in r&&"ref"in r)(s??=Array.isArray(e)?e.slice():{...e})[n]={xstate$$type:$,id:r.id};else{let i=t(r);i!==r&&((s??=Array.isArray(e)?e.slice():{...e})[n]=i)}}return s??e}(o),children:p,historyValue:function(t){if("object"!=typeof t||null===t)return{};let e={};for(let s in t){let n=t[s];Array.isArray(n)&&(e[s]=n.map(t=>({id:t.id})))}return e}(h.historyValue)}}function tR(t,e,s,n,{event:r,id:i,delay:o},{internalQueue:a}){let u,c=e.machine.implementations.delays;if("string"==typeof r)throw Error(`Only event objects may be used with raise; use raise({ type: "${r}" }) instead`);let l="function"==typeof r?r(s,n):r;if("string"==typeof o){let t=c&&c[o];u="function"==typeof t?t(s,n):t}else u="function"==typeof o?o(s,n):o;return"number"!=typeof u&&a.push(l),[e,{event:l,id:i,delay:u},void 0]}function tO(t,e){let{event:s,delay:n,id:r}=e;if("number"==typeof n)return void t.defer(()=>{let e=t.self;t.system.scheduler.schedule(e,e,s,n,r)})}function tj(t,e){function s(t,e){}return s.type="xstate.raise",s.event=t,s.id=e?.id,s.delay=e?.delay,s.resolve=tR,s.execute=tO,s}},40647:(t,e,s)=>{s.d(e,{a:()=>i});var n=s(26959);function r(t,e,s,r,{assignment:i}){if(!e.context)throw Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");let o={},a={context:e.context,event:s.event,spawn:function(t,{machine:e,context:s},r,i){return(o,a)=>{let u=((o,a)=>{if("string"!=typeof o)return(0,n.c)(o,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:a?.input,src:o,systemId:a?.systemId});{let u=(0,n.R)(e,o);if(!u)throw Error(`Actor logic '${o}' not implemented in machine '${e.id}'`);let c=(0,n.c)(u,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:"function"==typeof a?.input?a.input({context:s,event:r,self:t.self}):a?.input,src:o,systemId:a?.systemId});return i[c.id]=c,c}})(o,a);return i[u.id]=u,t.defer(()=>{u._processingStatus!==n.V.Stopped&&u.start()}),u}}(t,e,s.event,o),self:t.self,system:t.system},u={};if("function"==typeof i)u=i(a,r);else for(let t of Object.keys(i)){let e=i[t];u[t]="function"==typeof e?e(a,r):e}let c=Object.assign({},e.context,u);return[(0,n.W)(e,{context:c,children:Object.keys(o).length?{...e.children,...o}:e.children}),void 0,void 0]}function i(t){function e(t,e){}return e.type="xstate.assign",e.assignment=t,e.resolve=r,e}},42770:(t,e,s)=>{let n;s.d(e,{V2:()=>C,FS:()=>L,IM:()=>W,Tx:()=>B,Rr:()=>J,vd:()=>V,_K:()=>U,CC:()=>P,RM:()=>F,Uc:()=>X,tP:()=>z});let r={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},i=new Uint8Array(16),o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));let a=function(t,e,s){if(r.randomUUID&&!e&&!t)return r.randomUUID();let a=(t=t||{}).random||(t.rng||function(){if(!n){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");n=crypto.getRandomValues.bind(crypto)}return n(i)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,e){s=s||0;for(let t=0;t<16;++t)e[s+t]=a[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(a)};var u=s(80898),c=s(89632),l=s(69620),d=s(40647),h=s(26959),p=s(62789),f=s(39249),y=s(57173),g=s(94354),v=s(9953),m=s(56534),_=s(91208),b=s(781),x=["addListener","removeListener"],S=["addEventListener","removeEventListener"],w=["on","off"];function k(t,e,s,n){if((0,_.T)(s)&&(n=s,s=void 0),n)return k(t,e,s).pipe((0,b.I)(n));var r,i,o,a=(0,f.zs)((r=t,(0,_.T)(r.addEventListener)&&(0,_.T)(r.removeEventListener))?S.map(function(n){return function(r){return t[n](e,r,s)}}):(i=t,(0,_.T)(i.addListener)&&(0,_.T)(i.removeListener))?x.map(T(t,e)):(o=t,(0,_.T)(o.on)&&(0,_.T)(o.off))?w.map(T(t,e)):[],2),u=a[0],c=a[1];if(!u&&(0,m.X)(t))return(0,v.Z)(function(t){return k(t,e,s)})((0,y.Tg)(t));if(!u)throw TypeError("Invalid event target");return new g.c(function(t){var e=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];return t.next(1<e.length?e:e[0])};return u(e),function(){return c(e)}})}function T(t,e){return function(s){return function(n){return t[s](e,n)}}}var I=s(50793),E=s(88580),$=s(69647),q=s(1125),R=s(98175),O=s(575),j=s(79332);function D(t){return t<=0?function(){return j.w}:(0,q.N)(function(e,s){var n=0;e.subscribe((0,R._)(s,function(e){++n<=t&&(s.next(e),t<=n&&s.complete())}))})}var A=s(77020);let N=t=>({context:e})=>{let{count:s,include:n,exclude:r,responseType:i="message.received"}=t;return{count:s,domain:e.domain,from:e.connectTo,include:n?Array.isArray(n)?n:[n]:[],exclude:r?Array.isArray(r)?r:[r]:[],responseType:i,target:e.target,to:e.name}},M=(0,p.v)(()=>k(window,"message")),P=t=>(0,u.OX)(({input:e})=>{let s;return M.pipe(t?(0,I.T)(t):(0,E.F)(),(0,$.p)((t=>e=>{let{data:s}=e;return(!t.include.length||t.include.includes(s.type))&&(!t.exclude.length||!t.exclude.includes(s.type))&&s.domain===t.domain&&s.from===t.from&&s.to===t.to&&(!t.target||e.source===t.target)})(e)),(0,I.T)((s=e.responseType,t=>({type:s,message:t}))),e.count?(0,E.F)(function(t,e){return void 0===e&&(e=null),e=null!=e?e:t,(0,q.N)(function(s,n){var r=[],i=0;s.subscribe((0,R._)(n,function(s){var o,a,u,c,l=null;i++%e==0&&r.push([]);try{for(var d=(0,f.Ju)(r),h=d.next();!h.done;h=d.next()){var p=h.value;p.push(s),t<=p.length&&(l=null!=l?l:[]).push(p)}}catch(t){o={error:t}}finally{try{h&&!h.done&&(a=d.return)&&a.call(d)}finally{if(o)throw o.error}}if(l)try{for(var y=(0,f.Ju)(l),g=y.next();!g.done;g=y.next()){var p=g.value;(0,O.o)(r,p),n.next(p)}}catch(t){u={error:t}}finally{try{g&&!g.done&&(c=y.return)&&c.call(y)}finally{if(u)throw u.error}}},function(){var t,e;try{for(var s=(0,f.Ju)(r),i=s.next();!i.done;i=s.next()){var o=i.value;n.next(o)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}n.complete()},void 0,function(){r=null}))})}(e.count),function(t,e){return(0,_.T)(void 0)?(0,v.Z)(t,void 0,1):(0,v.Z)(t,1)}(t=>t),D(e.count)):(0,E.F)())}),C="sanity/comlink",U="comlink/response",V="comlink/heartbeat",L="comlink/disconnect",B="comlink/handshake/syn",J="comlink/handshake/syn-ack",W="comlink/handshake/ack",z=()=>(0,c.mj)({types:{},actors:{listen:(0,u.OX)(({input:t})=>{let e,s=t.signal?k(t.signal,"abort").pipe((e=`Request ${t.requestId} aborted`,t=>t.pipe(D(1),(0,I.T)(()=>{throw Error(e)})))):j.w;return k(window,"message").pipe((0,$.p)(e=>e.data?.type===U&&e.data?.responseTo===t.requestId&&!!e.source&&t.sources.has(e.source)),D(t.sources.size),(0,q.N)(function(t,e){(0,y.Tg)(s).subscribe((0,R._)(e,function(){return e.complete()},A.l)),e.closed||t.subscribe(e)}))})},actions:{"send message":({context:t},e)=>{let{sources:s,targetOrigin:n}=t,{message:r}=e;s.forEach(t=>{t.postMessage(r,{targetOrigin:n})})},"on success":(0,l.b)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.response&&t.resolvable?.resolve(t.response),{type:"request.success",requestId:e.id,response:t.response,responseTo:t.responseTo})),"on fail":(0,l.b)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.suppressWarnings||console.warn(`[@sanity/comlink] Received no response to message '${t.type}' on client '${t.from}' (ID: '${t.id}').`),t.resolvable?.reject(Error("No response received")),{type:"request.failed",requestId:e.id})),"on abort":(0,l.b)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.resolvable?.reject(Error("Request aborted")),{type:"request.aborted",requestId:e.id}))},guards:{expectsResponse:({context:t})=>t.expectResponse},delays:{initialTimeout:0,responseTimeout:({context:t})=>t.responseTimeout??3e3}}).createMachine({context:({input:t})=>({channelId:t.channelId,data:t.data,domain:t.domain,expectResponse:t.expectResponse??!1,from:t.from,id:`msg-${a()}`,parentRef:t.parentRef,resolvable:t.resolvable,response:null,responseTimeout:t.responseTimeout,responseTo:t.responseTo,signal:t.signal,sources:t.sources instanceof Set?t.sources:new Set([t.sources]),suppressWarnings:t.suppressWarnings,targetOrigin:t.targetOrigin,to:t.to,type:t.type}),initial:"idle",on:{abort:".aborted"},states:{idle:{after:{initialTimeout:[{target:"sending"}]}},sending:{entry:{type:"send message",params:({context:t})=>{let{channelId:e,data:s,domain:n,from:r,id:i,responseTo:o,to:a,type:u}=t;return{message:{channelId:e,data:s,domain:n,from:r,id:i,to:a,type:u,responseTo:o}}}},always:[{guard:"expectsResponse",target:"awaiting"},"success"]},awaiting:{invoke:{id:"listen for response",src:"listen",input:({context:t})=>({requestId:t.id,sources:t.sources,signal:t.signal}),onError:"aborted"},after:{responseTimeout:"failed"},on:{message:{actions:(0,d.a)({response:({event:t})=>t.data.data,responseTo:({event:t})=>t.data.responseTo}),target:"success"}}},failed:{type:"final",entry:"on fail"},success:{type:"final",entry:"on success"},aborted:{type:"final",entry:"on abort"}},output:({context:t,self:e})=>({requestId:e.id,response:t.response,responseTo:t.responseTo})}),X=((0,u.SP)(({sendBack:t,input:e})=>{let s=()=>{t(e.event)};e.immediate&&s();let n=setInterval(s,e.interval);return()=>{clearInterval(n)}}),()=>(0,c.mj)({types:{},actors:{requestMachine:z(),listen:P()},actions:{"buffer incoming message":(0,d.a)({handshakeBuffer:({event:t,context:e})=>((0,c.DT)(t,"message.received"),[...e.handshakeBuffer,t])}),"buffer message":(0,l.a)(({enqueue:t})=>{t.assign({buffer:({event:t,context:e})=>((0,c.DT)(t,"post"),[...e.buffer,{data:t.data,resolvable:t.resolvable,options:t.options}])}),t.emit(({event:t})=>((0,c.DT)(t,"post"),{type:"_buffer.added",message:t.data}))}),"create request":(0,d.a)({requests:({context:t,event:e,self:s,spawn:n})=>{(0,c.DT)(e,"request");let r=(Array.isArray(e.data)?e.data:[e.data]).map(e=>n("requestMachine",{id:`req-${a()}`,input:{channelId:t.channelId,data:e.data,domain:t.domain,expectResponse:e.expectResponse,from:t.name,parentRef:s,resolvable:e.resolvable,responseTimeout:e.options?.responseTimeout,responseTo:e.responseTo,signal:e.options?.signal,sources:t.target,suppressWarnings:e.options?.suppressWarnings,targetOrigin:t.targetOrigin,to:t.connectTo,type:e.type}}));return[...t.requests,...r]}}),"emit heartbeat":(0,l.e)(()=>({type:"_heartbeat"})),"emit received message":(0,l.a)(({enqueue:t})=>{t.emit(({event:t})=>((0,c.DT)(t,"message.received"),{type:"_message",message:t.message.data})),t.emit(({event:t})=>((0,c.DT)(t,"message.received"),{type:t.message.data.type,message:t.message.data}))}),"emit status":(0,l.e)((t,e)=>({type:"_status",status:e.status})),"flush buffer":(0,l.a)(({enqueue:t})=>{t.raise(({context:t})=>({type:"request",data:t.buffer.map(({data:t,resolvable:e,options:s})=>({data:t.data,type:t.type,expectResponse:!!e,resolvable:e,options:s}))})),t.emit(({context:t})=>({type:"_buffer.flushed",messages:t.buffer.map(({data:t})=>t)})),t.assign({buffer:[]})}),"flush handshake buffer":(0,l.a)(({context:t,enqueue:e})=>{t.handshakeBuffer.forEach(t=>e.raise(t)),e.assign({handshakeBuffer:[]})}),post:(0,h.r)(({event:t})=>((0,c.DT)(t,"post"),{type:"request",data:{data:t.data.data,expectResponse:!!t.resolvable,type:t.data.type,resolvable:t.resolvable,options:t.options}})),"remove request":(0,l.a)(({context:t,enqueue:e,event:s})=>{(0,c.DT)(s,["request.success","request.failed","request.aborted"]),(0,h.k)(s.requestId),e.assign({requests:t.requests.filter(({id:t})=>t!==s.requestId)})}),"send response":(0,h.r)(({event:t})=>((0,c.DT)(t,["message.received","heartbeat.received"]),{type:"request",data:{type:U,responseTo:t.message.data.id,data:void 0}})),"send handshake syn ack":(0,h.r)({type:"request",data:{type:J}}),"set connection config":(0,d.a)({channelId:({event:t})=>((0,c.DT)(t,"handshake.syn"),t.message.data.channelId),target:({event:t})=>((0,c.DT)(t,"handshake.syn"),t.message.source||void 0),targetOrigin:({event:t})=>((0,c.DT)(t,"handshake.syn"),t.message.origin)})},guards:{hasSource:({context:t})=>null!==t.target}}).createMachine({id:"node",context:({input:t})=>({buffer:[],channelId:null,connectTo:t.connectTo,domain:t.domain??C,handshakeBuffer:[],name:t.name,requests:[],target:void 0,targetOrigin:null}),invoke:{id:"listen for handshake syn",src:"listen",input:N({include:B,responseType:"handshake.syn"})},on:{"request.success":{actions:"remove request"},"request.failed":{actions:"remove request"},"request.aborted":{actions:"remove request"},"handshake.syn":{actions:"set connection config",target:".handshaking"}},initial:"idle",states:{idle:{entry:[{type:"emit status",params:{status:"idle"}}],on:{post:{actions:"buffer message"}}},handshaking:{guard:"hasSource",entry:["send handshake syn ack",{type:"emit status",params:{status:"handshaking"}}],invoke:[{id:"listen for handshake ack",src:"listen",input:N({include:W,count:1,responseType:"handshake.complete"}),onDone:"connected"},{id:"listen for disconnect",src:"listen",input:N({include:L,count:1,responseType:"disconnect"})},{id:"listen for messages",src:"listen",input:N({exclude:[L,B,W,V,U]})}],on:{request:{actions:"create request"},post:{actions:"buffer message"},"message.received":{actions:"buffer incoming message"},disconnect:{target:"idle"}}},connected:{entry:["flush handshake buffer","flush buffer",{type:"emit status",params:{status:"connected"}}],invoke:[{id:"listen for messages",src:"listen",input:N({exclude:[L,B,W,V,U]})},{id:"listen for heartbeat",src:"listen",input:N({include:V,responseType:"heartbeat.received"})},{id:"listen for disconnect",src:"listen",input:N({include:L,count:1,responseType:"disconnect"})}],on:{request:{actions:"create request"},post:{actions:"post"},disconnect:{target:"idle"},"message.received":{actions:["send response","emit received message"]},"heartbeat.received":{actions:["send response","emit heartbeat"]}}}}})),F=(t,e=X())=>{let s,n=(0,h.c)(e,{input:t}),r=()=>{n.stop()};return{actor:n,fetch:(t,e,s)=>{let{responseTimeout:r=1e4,signal:i,suppressWarnings:o}=s||{},a=Promise.withResolvers();return n.send({type:"post",data:{type:t,data:e},resolvable:a,options:{responseTimeout:r,signal:i,suppressWarnings:o}}),a.promise},machine:e,on:(t,e)=>{let{unsubscribe:s}=n.on(t,t=>{e(t.message.data)});return s},onStatus:(t,e)=>{let{unsubscribe:r}=n.on("_status",n=>{s=n.status,e&&n.status!==e||t(n.status)});return s&&t(s),r},post:(t,e)=>{n.send({type:"post",data:{type:t,data:e}})},start:()=>(n.start(),r),stop:r}}},62789:(t,e,s)=>{s.d(e,{v:()=>i});var n=s(94354),r=s(57173);function i(t){return new n.c(function(e){(0,r.Tg)(t()).subscribe(e)})}},69620:(t,e,s)=>{s.d(e,{a:()=>y,b:()=>h,e:()=>a,s:()=>p});var n=s(26959),r=s(40647);function i(t,e,s,n,{event:r}){return[e,{event:"function"==typeof r?r(s,n):r},void 0]}function o(t,{event:e}){t.defer(()=>t.emit(e))}function a(t){function e(t,e){}return e.type="xstate.emit",e.event=t,e.resolve=i,e.execute=o,e}let u=function(t){return t.Parent="#_parent",t.Internal="#_internal",t}({});function c(t,e,s,n,{to:r,event:i,id:o,delay:a},c){let l,d,h=e.machine.implementations.delays;if("string"==typeof i)throw Error(`Only event objects may be used with sendTo; use sendTo({ type: "${i}" }) instead`);let p="function"==typeof i?i(s,n):i;if("string"==typeof a){let t=h&&h[a];l="function"==typeof t?t(s,n):t}else l="function"==typeof a?a(s,n):a;let f="function"==typeof r?r(s,n):r;if("string"==typeof f){if(!(d=f===u.Parent?t.self._parent:f===u.Internal?t.self:f.startsWith("#_")?e.children[f.slice(2)]:c.deferredActorIds?.includes(f)?f:e.children[f]))throw Error(`Unable to send event to actor '${f}' from machine '${e.machine.id}'.`)}else d=f||t.self;return[e,{to:d,targetId:"string"==typeof f?f:void 0,event:p,id:o,delay:l},void 0]}function l(t,e,s){"string"==typeof s.to&&(s.to=e.children[s.to])}function d(t,e){t.defer(()=>{let{to:s,event:r,delay:i,id:o}=e;if("number"==typeof i)return void t.system.scheduler.schedule(t.self,s,r,i,o);t.system._relay(t.self,s,r.type===n.T?(0,n.U)(t.self.id,r.data):r)})}function h(t,e,s){function n(t,e){}return n.type="xstate.sendTo",n.to=t,n.event=e,n.id=s?.id,n.delay=s?.delay,n.resolve=c,n.retryResolve=l,n.execute=d,n}function p(t,e){return h(u.Parent,t,e)}function f(t,e,s,i,{collect:o}){let u=[],c=function(t){u.push(t)};return c.assign=(...t)=>{u.push((0,r.a)(...t))},c.cancel=(...t)=>{u.push((0,n.f)(...t))},c.raise=(...t)=>{u.push((0,n.r)(...t))},c.sendTo=(...t)=>{u.push(h(...t))},c.sendParent=(...t)=>{u.push(p(...t))},c.spawnChild=(...t)=>{u.push((0,n.h)(...t))},c.stopChild=(...t)=>{u.push((0,n.k)(...t))},c.emit=(...t)=>{u.push(a(...t))},o({context:s.context,event:s.event,enqueue:c,check:t=>(0,n.w)(t,e.context,s.event,e),self:t.self,system:t.system},i),[e,void 0,u]}function y(t){function e(t,e){}return e.type="xstate.enqueueActions",e.collect=t,e.resolve=f,e}},79332:(t,e,s)=>{s.d(e,{w:()=>n});var n=new(s(94354)).c(function(t){return t.complete()})},80898:(t,e,s)=>{s.d(e,{H6:()=>f,OX:()=>u,SP:()=>i,Sx:()=>h});var n=s(26959);let r=new WeakMap;function i(t){return{config:t,start:(e,s)=>{let{self:n,system:i,emit:o}=s,a={receivers:void 0,dispose:void 0};r.set(n,a),a.dispose=t({input:e.input,system:i,self:n,sendBack:t=>{"stopped"!==n.getSnapshot().status&&n._parent&&i._relay(n,n._parent,t)},receive:t=>{a.receivers??=new Set,a.receivers.add(t)},emit:o})},transition:(t,e,s)=>{let i=r.get(s.self);return e.type===n.X?(t={...t,status:"stopped",error:void 0},i.dispose?.()):i.receivers?.forEach(t=>t(e)),t},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let o="xstate.observable.error",a="xstate.observable.complete";function u(t){return{config:t,transition:(t,e)=>{if("active"!==t.status)return t;switch(e.type){case o:return{...t,status:"error",error:e.data,input:void 0,_subscription:void 0};case a:return{...t,status:"done",input:void 0,_subscription:void 0};case n.X:return t._subscription.unsubscribe(),{...t,status:"stopped",input:void 0,_subscription:void 0};default:return t}},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,context:void 0,input:e,_subscription:void 0}),start:(e,{self:s,system:n,emit:r})=>{"done"!==e.status&&(e._subscription=t({input:e.input,system:n,self:s,emit:r}).subscribe({next:t=>{s._parent&&n._relay(s,s._parent,t)},error:t=>{n._relay(s,s,{type:o,data:t})},complete:()=>{n._relay(s,s,{type:a})}}))},getPersistedSnapshot:({_subscription:t,...e})=>e,restoreSnapshot:t=>({...t,_subscription:void 0})}}let c="xstate.promise.resolve",l="xstate.promise.reject",d=new WeakMap;function h(t){return{config:t,transition:(t,e,s)=>{if("active"!==t.status)return t;switch(e.type){case c:{let s=e.data;return{...t,status:"done",output:s,input:void 0}}case l:return{...t,status:"error",error:e.data,input:void 0};case n.X:return d.get(s.self)?.abort(),{...t,status:"stopped",input:void 0};default:return t}},start:(e,{self:s,system:n,emit:r})=>{if("active"!==e.status)return;let i=new AbortController;d.set(s,i),Promise.resolve(t({input:e.input,system:n,self:s,signal:i.signal,emit:r})).then(t=>{"active"===s.getSnapshot().status&&(d.delete(s),n._relay(s,s,{type:c,data:t}))},t=>{"active"===s.getSnapshot().status&&(d.delete(s),n._relay(s,s,{type:l,data:t}))})},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let p=function(t,e){return{config:t,transition:(e,s,n)=>({...e,context:t(e.context,s,n)}),getInitialSnapshot:(t,s)=>({status:"active",output:void 0,error:void 0,context:"function"==typeof e?e({input:s}):e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}(t=>void 0,void 0);function f(){return(0,n.c)(p)}},89632:(t,e,s)=>{s.d(e,{DT:()=>d,mj:()=>h}),s(80898);var n=s(26959),r=s(40647);let i=new WeakMap;function o(t,e,s){let n=i.get(t);return n?e in n||(n[e]=s()):(n={[e]:s()},i.set(t,n)),n[e]}let a={},u=t=>"string"==typeof t?{type:t}:"function"==typeof t?"resolve"in t?{type:t.type}:{type:t.name}:t;class c{constructor(t,e){if(this.config=t,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=e._parent,this.key=e._key,this.machine=e._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(n.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,n.l)(this.config.states,(t,e)=>new c(t,{_parent:this,_key:e,_machine:this.machine})):a,"compound"===this.type&&!this.config.initial)throw Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,n.t)(this.config.entry).slice(),this.exit=(0,n.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,n.t)(t.tags).slice()}_initialize(){this.transitions=(0,n.q)(this),this.config.always&&(this.always=(0,n.u)(this.config.always).map(t=>(0,n.v)(this,n.N,t))),Object.keys(this.states).forEach(t=>{this.states[t]._initialize()})}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(u),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map(t=>`#${t.id}`),source:`#${this.id}`,actions:this.initial.actions.map(u),eventType:null})}:void 0,history:this.history,states:(0,n.l)(this.states,t=>t.definition),on:this.on,transitions:[...this.transitions.values()].flat().map(t=>({...t,actions:t.actions.map(u)})),entry:this.entry.map(u),exit:this.exit.map(u),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return o(this,"invoke",()=>(0,n.t)(this.config.invoke).map((t,e)=>{let{src:s,systemId:r}=t,i=t.id??(0,n.x)(this.id,e),o="string"==typeof s?s:`xstate.invoke.${(0,n.x)(this.id,e)}`;return{...t,src:o,id:i,systemId:r,toJSON(){let{onDone:e,onError:s,...n}=t;return{...n,type:"xstate.invoke",src:o,id:i}}}}))}get on(){return o(this,"on",()=>[...this.transitions].flatMap(([t,e])=>e.map(e=>[t,e])).reduce((t,[e,s])=>(t[e]=t[e]||[],t[e].push(s),t),{}))}get after(){return o(this,"delayedTransitions",()=>(0,n.y)(this))}get initial(){return o(this,"initial",()=>(0,n.z)(this,this.config.initial))}next(t,e){let s,r=e.type,i=[];for(let a of o(this,`candidates-${r}`,()=>(0,n.B)(this,r))){let{guard:o}=a,u=t.context,c=!1;try{c=!o||(0,n.w)(o,u,e,t)}catch(e){let t="string"==typeof o?o:"object"==typeof o?o.type:void 0;throw Error(`Unable to evaluate guard ${t?`'${t}' `:""}in transition for event '${r}' in state node '${this.id}':
${e.message}`)}if(c){i.push(...a.actions),s=a;break}}return s?[s]:void 0}get events(){return o(this,"events",()=>{let{states:t}=this,e=new Set(this.ownEvents);if(t)for(let s of Object.keys(t)){let n=t[s];if(n.states)for(let t of n.events)e.add(`${t}`)}return Array.from(e)})}get ownEvents(){return Array.from(new Set([...this.transitions.keys()].filter(t=>this.transitions.get(t).some(t=>!(!t.target&&!t.actions.length&&!t.reenter)))))}}class l{constructor(t,e){this.config=t,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.id=t.id||"(machine)",this.implementations={actors:e?.actors??{},actions:e?.actions??{},delays:e?.delays??{},guards:e?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new c(t,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(t){let{actions:e,guards:s,actors:n,delays:r}=this.implementations;return new l(this.config,{actions:{...e,...t.actions},guards:{...s,...t.guards},actors:{...n,...t.actors},delays:{...r,...t.delays}})}resolveState(t){let e=(0,n.C)(this.root,t.value),s=(0,n.D)((0,n.g)(this.root,e));return(0,n.E)({_nodes:[...s],context:t.context||{},children:{},status:(0,n.F)(s,this.root)?"done":t.status||"active",output:t.output,error:t.error,historyValue:t.historyValue},this)}transition(t,e,s){return(0,n.G)(t,e,s,[]).snapshot}microstep(t,e,s){return(0,n.G)(t,e,s,[]).microstates}getTransitionData(t,e){return(0,n.H)(this.root,t.value,t,e)||[]}getPreInitialState(t,e,s){let{context:i}=this.config,o=(0,n.E)({context:"function"!=typeof i&&i?i:{},_nodes:[this.root],children:{},status:"active"},this);return"function"==typeof i?(0,n.I)(o,e,t,[(0,r.a)(({spawn:t,event:e,self:s})=>i({spawn:t,input:e.input,self:s}))],s,void 0):o}getInitialSnapshot(t,e){let s=(0,n.J)(e),r=[],i=this.getPreInitialState(t,s,r),o=(0,n.K)([{target:[...(0,n.L)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],i,t,s,!0,r),{snapshot:a}=(0,n.G)(o,s,t,r);return a}start(t){Object.values(t.children).forEach(t=>{"active"===t.getSnapshot().status&&t.start()})}getStateNodeById(t){let e=(0,n.M)(t),s=e.slice(1),r=(0,n.O)(e[0])?e[0].slice(1):e[0],i=this.idMap.get(r);if(!i)throw Error(`Child state node '#${r}' does not exist on machine '${this.id}'`);return(0,n.P)(i,s)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(t,e){return(0,n.Q)(t,e)}restoreSnapshot(t,e){let s={},r=t.children;Object.keys(r).forEach(t=>{let i=r[t],o=i.snapshot,a=i.src,u="string"==typeof a?(0,n.R)(this,a):a;if(!u)return;let c=(0,n.c)(u,{id:t,parent:e.self,syncSnapshot:i.syncSnapshot,snapshot:o,src:a,systemId:i.systemId});s[t]=c});let i=function(t,e){if(!e||"object"!=typeof e)return{};let s={};for(let n in e)for(let r of e[n]){let e=function(t,e){if(e instanceof c)return e;try{return t.machine.getStateNodeById(e.id)}catch{}}(t,r);e&&(s[n]??=[],s[n].push(e))}return s}(this.root,t.historyValue),o=(0,n.E)({...t,children:s,_nodes:Array.from((0,n.D)((0,n.g)(this.root,t.value))),historyValue:i},this),a=new Set;return!function t(e,s){if(!a.has(e))for(let r in a.add(e),e){let i=e[r];if(i&&"object"==typeof i){if("xstate$$type"in i&&i.xstate$$type===n.$){e[r]=s[i.id];continue}t(i,s)}}}(o.context,s),o}}function d(t,e){let s=(0,n.t)(e);if(!s.includes(t.type)){let e=1===s.length?`type "${s[0]}"`:`one of types "${s.join('", "')}"`;throw Error(`Expected event ${JSON.stringify(t)} to have ${e}`)}}function h({schemas:t,actors:e,actions:s,guards:n,delays:r}){return{createMachine:i=>new l({...i,schemas:t},{actors:e,actions:s,guards:n,delays:r})}}}}]);