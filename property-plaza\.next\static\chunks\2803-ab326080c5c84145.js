"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2803],{5845:(t,e,n)=>{n.d(e,{i:()=>o});var r=n(12115),i=n(39033);function o({prop:t,defaultProp:e,onChange:n=()=>{}}){let[o,l]=function({defaultProp:t,onChange:e}){let n=r.useState(t),[o]=n,l=r.useRef(o),f=(0,i.c)(e);return r.useEffect(()=>{l.current!==o&&(f(o),l.current=o)},[o,l,f]),n}({defaultProp:e,onChange:n}),f=void 0!==t,u=f?t:o,a=(0,i.c)(n);return[u,r.useCallback(e=>{if(f){let n="function"==typeof e?e(t):e;n!==t&&a(n)}else l(e)},[f,t,l,a])]}},11275:(t,e,n)=>{n.d(e,{X:()=>o});var r=n(12115),i=n(52712);function o(t){let[e,n]=r.useState(void 0);return(0,i.N)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}},22475:(t,e,n)=>{n.d(e,{UE:()=>tL,ll:()=>tv,rD:()=>tD,UU:()=>tE,jD:()=>tS,ER:()=>tT,cY:()=>tb,BN:()=>tR,Ej:()=>tA});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,f=Math.floor,u=t=>({x:t,y:t}),a={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}let g=new Set(["top","bottom"]);function y(t){return g.has(d(t))?"y":"x"}function w(t){return t.replace(/start|end/g,t=>c[t])}let x=["left","right"],v=["right","left"],b=["top","bottom"],R=["bottom","top"];function E(t){return t.replace(/left|right|bottom|top/g,t=>a[t])}function A(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function S(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function L(t,e,n){let r,{reference:i,floating:o}=t,l=y(e),f=p(y(e)),u=m(f),a=d(e),c="y"===l,s=i.x+i.width/2-o.width/2,g=i.y+i.height/2-o.height/2,w=i[u]/2-o[u]/2;switch(a){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:g};break;case"left":r={x:i.x-o.width,y:g};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[f]-=w*(n&&c?-1:1);break;case"end":r[f]+=w*(n&&c?-1:1)}return r}let T=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(e)),a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:s}=L(a,r,u),d=r,h={},p=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:y,data:w,reset:x}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:h,rects:a,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,s=null!=y?y:s,h={...h,[o]:{...h[o],...w}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(a=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:c,y:s}=L(a,d,u)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:h}};async function D(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:f,strategy:u}=t,{boundary:a="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(e,t),m=A(p),g=f[h?"floating"===d?"reference":"floating":d],y=S(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:a,rootBoundary:c,strategy:u})),w="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),v=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},b=S(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:w,offsetParent:x,strategy:u}):w);return{top:(y.top-b.top+m.top)/v.y,bottom:(b.bottom-y.bottom+m.bottom)/v.y,left:(y.left-b.left+m.left)/v.x,right:(b.right-y.right+m.right)/v.x}}function O(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function C(t){return r.some(e=>t[e]>=0)}let k=new Set(["left","top"]);async function P(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),f=h(n),u="y"===y(n),a=k.has(l)?-1:1,c=o&&u?-1:1,p=s(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return f&&"number"==typeof w&&(g="end"===f?-1*w:w),u?{x:g*c,y:m*a}:{x:m*a,y:g*c}}function H(){return"undefined"!=typeof window}function B(t){return N(t)?(t.nodeName||"").toLowerCase():"#document"}function F(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function M(t){var e;return null==(e=(N(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function N(t){return!!H()&&(t instanceof Node||t instanceof F(t).Node)}function W(t){return!!H()&&(t instanceof Element||t instanceof F(t).Element)}function j(t){return!!H()&&(t instanceof HTMLElement||t instanceof F(t).HTMLElement)}function z(t){return!!H()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof F(t).ShadowRoot)}let U=new Set(["inline","contents"]);function V(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=Z(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!U.has(i)}let _=new Set(["table","td","th"]),I=[":popover-open",":modal"];function Y(t){return I.some(e=>{try{return t.matches(e)}catch(t){return!1}})}let $=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function G(t){let e=J(),n=W(t)?Z(t):t;return $.some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||q.some(t=>(n.willChange||"").includes(t))||X.some(t=>(n.contain||"").includes(t))}function J(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let K=new Set(["html","body","#document"]);function Q(t){return K.has(B(t))}function Z(t){return F(t).getComputedStyle(t)}function tt(t){return W(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function te(t){if("html"===B(t))return t;let e=t.assignedSlot||t.parentNode||z(t)&&t.host||M(t);return z(e)?e.host:e}function tn(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=te(e);return Q(n)?e.ownerDocument?e.ownerDocument.body:e.body:j(n)&&V(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=F(i);if(o){let t=tr(l);return e.concat(l,l.visualViewport||[],V(i)?i:[],t&&n?tn(t):[])}return e.concat(i,tn(i,[],n))}function tr(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function ti(t){let e=Z(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=j(t),o=i?t.offsetWidth:n,f=i?t.offsetHeight:r,u=l(n)!==o||l(r)!==f;return u&&(n=o,r=f),{width:n,height:r,$:u}}function to(t){return W(t)?t:t.contextElement}function tl(t){let e=to(t);if(!j(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=ti(e),f=(o?l(n.width):n.width)/r,a=(o?l(n.height):n.height)/i;return f&&Number.isFinite(f)||(f=1),a&&Number.isFinite(a)||(a=1),{x:f,y:a}}let tf=u(0);function tu(t){let e=F(t);return J()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:tf}function ta(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=to(t),f=u(1);e&&(r?W(r)&&(f=tl(r)):f=tl(t));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===F(l))&&i)?tu(l):u(0),c=(o.left+a.x)/f.x,s=(o.top+a.y)/f.y,d=o.width/f.x,h=o.height/f.y;if(l){let t=F(l),e=r&&W(r)?F(r):r,n=t,i=tr(n);for(;i&&r&&e!==n;){let t=tl(i),e=i.getBoundingClientRect(),r=Z(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,s*=t.y,d*=t.x,h*=t.y,c+=o,s+=l,i=tr(n=F(i))}}return S({width:d,height:h,x:c,y:s})}function tc(t,e){let n=tt(t).scrollLeft;return e?e.left+n:ta(M(t)).left+n}function ts(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:tc(t,r)),y:r.top+e.scrollTop}}let td=new Set(["absolute","fixed"]);function th(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=F(t),r=M(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,f=0,u=0;if(i){o=i.width,l=i.height;let t=J();(!t||t&&"fixed"===e)&&(f=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:f,y:u}}(t,n);else if("document"===e)r=function(t){let e=M(t),n=tt(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),l=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),f=-n.scrollLeft+tc(t),u=-n.scrollTop;return"rtl"===Z(r).direction&&(f+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:l,x:f,y:u}}(M(t));else if(W(e))r=function(t,e){let n=ta(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=j(t)?tl(t):u(1),l=t.clientWidth*o.x,f=t.clientHeight*o.y;return{width:l,height:f,x:i*o.x,y:r*o.y}}(e,n);else{let n=tu(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return S(r)}function tp(t){return"static"===Z(t).position}function tm(t,e){if(!j(t)||"fixed"===Z(t).position)return null;if(e)return e(t);let n=t.offsetParent;return M(t)===n&&(n=n.ownerDocument.body),n}function tg(t,e){var n;let r=F(t);if(Y(t))return r;if(!j(t)){let e=te(t);for(;e&&!Q(e);){if(W(e)&&!tp(e))return e;e=te(e)}return r}let i=tm(t,e);for(;i&&(n=i,_.has(B(n)))&&tp(i);)i=tm(i,e);return i&&Q(i)&&tp(i)&&!G(i)?r:i||function(t){let e=te(t);for(;j(e)&&!Q(e);){if(G(e))return e;if(Y(e))break;e=te(e)}return null}(t)||r}let ty=async function(t){let e=this.getOffsetParent||tg,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=j(e),i=M(e),o="fixed"===n,l=ta(t,!0,o,e),f={scrollLeft:0,scrollTop:0},a=u(0);if(r||!r&&!o)if(("body"!==B(e)||V(i))&&(f=tt(e)),r){let t=ta(e,!0,o,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&(a.x=tc(i));o&&!r&&i&&(a.x=tc(i));let c=!i||r||o?u(0):ts(i,f);return{x:l.left+f.scrollLeft-a.x-c.x,y:l.top+f.scrollTop-a.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tw={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=M(r),f=!!e&&Y(e.floating);if(r===l||f&&o)return n;let a={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=j(r);if((d||!d&&!o)&&(("body"!==B(r)||V(l))&&(a=tt(r)),j(r))){let t=ta(r);c=tl(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}let h=!l||d||o?u(0):ts(l,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+s.x+h.x,y:n.y*c.y-a.scrollTop*c.y+s.y+h.y}},getDocumentElement:M,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,f=[..."clippingAncestors"===n?Y(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=tn(t,[],!1).filter(t=>W(t)&&"body"!==B(t)),i=null,o="fixed"===Z(t).position,l=o?te(t):t;for(;W(l)&&!Q(l);){let e=Z(l),n=G(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&td.has(i.position)||V(l)&&!n&&function t(e,n){let r=te(e);return!(r===n||!W(r)||Q(r))&&("fixed"===Z(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=te(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],u=f[0],a=f.reduce((t,n)=>{let r=th(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},th(e,u,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:tg,getElementRects:ty,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=ti(t);return{width:e,height:n}},getScale:tl,isElement:W,isRTL:function(t){return"rtl"===Z(t).direction}};function tx(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tv(t,e,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:a=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=to(t),p=u||a?[...h?tn(h):[],...tn(e)]:[];p.forEach(t=>{u&&t.addEventListener("scroll",n,{passive:!0}),a&&t.addEventListener("resize",n)});let m=h&&s?function(t,e){let n,r=null,l=M(t);function u(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function a(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),u();let d=t.getBoundingClientRect(),{left:h,top:p,width:m,height:g}=d;if(c||e(),!m||!g)return;let y=f(p),w=f(l.clientWidth-(h+m)),x={rootMargin:-y+"px "+-w+"px "+-f(l.clientHeight-(p+g))+"px "+-f(h)+"px",threshold:o(0,i(1,s))||1},v=!0;function b(e){let r=e[0].intersectionRatio;if(r!==s){if(!v)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||tx(d,t.getBoundingClientRect())||a(),v=!1}try{r=new IntersectionObserver(b,{...x,root:l.ownerDocument})}catch(t){r=new IntersectionObserver(b,x)}r.observe(t)}(!0),u}(h,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),h&&!d&&y.observe(h),y.observe(e));let w=d?ta(t):null;return d&&function e(){let r=ta(t);w&&!tx(w,r)&&n(),w=r,l=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{u&&t.removeEventListener("scroll",n),a&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let tb=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:f}=e,u=await P(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(r=f.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},tR=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:l}=e,{mainAxis:f=!0,crossAxis:u=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=s(t,e),h={x:n,y:r},m=await D(e,c),g=y(d(l)),w=p(g),x=h[w],v=h[g];if(f){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=x+m[t],r=x-m[e];x=o(n,i(x,r))}if(u){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=v+m[t],r=v-m[e];v=o(n,i(v,r))}let b=a.fn({...e,[w]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:f,[g]:u}}}}}},tE=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:f,middlewareData:u,rects:a,initialPlacement:c,platform:g,elements:A}=e,{mainAxis:S=!0,crossAxis:L=!0,fallbackPlacements:T,fallbackStrategy:O="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:k=!0,...P}=s(t,e);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let H=d(f),B=y(c),F=d(c)===c,M=await (null==g.isRTL?void 0:g.isRTL(A.floating)),N=T||(F||!k?[E(c)]:function(t){let e=E(t);return[w(t),e,w(e)]}(c)),W="none"!==C;!T&&W&&N.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){switch(t){case"top":case"bottom":if(n)return e?v:x;return e?x:v;case"left":case"right":return e?b:R;default:return[]}}(d(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(w)))),o}(c,k,C,M));let j=[c,...N],z=await D(e,P),U=[],V=(null==(r=u.flip)?void 0:r.overflows)||[];if(S&&U.push(z[H]),L){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=p(y(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=E(l)),[l,E(l)]}(f,a,M);U.push(z[t[0]],z[t[1]])}if(V=[...V,{placement:f,overflows:U}],!U.every(t=>t<=0)){let t=((null==(i=u.flip)?void 0:i.index)||0)+1,e=j[t];if(e&&("alignment"!==L||B===y(e)||V.every(t=>t.overflows[0]>0&&y(t.placement)===B)))return{data:{index:t,overflows:V},reset:{placement:e}};let n=null==(o=V.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(O){case"bestFit":{let t=null==(l=V.filter(t=>{if(W){let e=y(t.placement);return e===B||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(f!==n)return{reset:{placement:n}}}return{}}}},tA=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let l,f,{placement:u,rects:a,platform:c,elements:p}=e,{apply:m=()=>{},...g}=s(t,e),w=await D(e,g),x=d(u),v=h(u),b="y"===y(u),{width:R,height:E}=a.floating;"top"===x||"bottom"===x?(l=x,f=v===(await (null==c.isRTL?void 0:c.isRTL(p.floating))?"start":"end")?"left":"right"):(f=x,l="end"===v?"top":"bottom");let A=E-w.top-w.bottom,S=R-w.left-w.right,L=i(E-w[l],A),T=i(R-w[f],S),O=!e.middlewareData.shift,C=L,k=T;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(k=S),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(C=A),O&&!v){let t=o(w.left,0),e=o(w.right,0),n=o(w.top,0),r=o(w.bottom,0);b?k=R-2*(0!==t||0!==e?t+e:o(w.left,w.right)):C=E-2*(0!==n||0!==r?n+r:o(w.top,w.bottom))}await m({...e,availableWidth:k,availableHeight:C});let P=await c.getDimensions(p.floating);return R!==P.width||E!==P.height?{reset:{rects:!0}}:{}}}},tS=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=s(t,e);switch(r){case"referenceHidden":{let t=O(await D(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:C(t)}}}case"escaped":{let t=O(await D(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:C(t)}}}default:return{}}}}},tL=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:f,platform:u,elements:a,middlewareData:c}=e,{element:d,padding:g=0}=s(t,e)||{};if(null==d)return{};let w=A(g),x={x:n,y:r},v=p(y(l)),b=m(v),R=await u.getDimensions(d),E="y"===v,S=E?"clientHeight":"clientWidth",L=f.reference[b]+f.reference[v]-x[v]-f.floating[b],T=x[v]-f.reference[v],D=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),O=D?D[S]:0;O&&await (null==u.isElement?void 0:u.isElement(D))||(O=a.floating[S]||f.floating[b]);let C=O/2-R[b]/2-1,k=i(w[E?"top":"left"],C),P=i(w[E?"bottom":"right"],C),H=O-R[b]-P,B=O/2-R[b]/2+(L/2-T/2),F=o(k,i(B,H)),M=!c.arrow&&null!=h(l)&&B!==F&&f.reference[b]/2-(B<k?k:P)-R[b]/2<0,N=M?B<k?B-k:B-H:0;return{[v]:x[v]+N,data:{[v]:F,centerOffset:B-F-N,...M&&{alignmentOffset:N}},reset:M}}}),tT=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:u=!0,crossAxis:a=!0}=s(t,e),c={x:n,y:r},h=y(i),m=p(h),g=c[m],w=c[h],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(u){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+v.mainAxis,n=o.reference[m]+o.reference[t]-v.mainAxis;g<e?g=e:g>n&&(g=n)}if(a){var b,R;let t="y"===m?"width":"height",e=k.has(d(i)),n=o.reference[h]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[h])||0)+(e?0:v.crossAxis),r=o.reference[h]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[h])||0)-(e?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[m]:g,[h]:w}}}},tD=(t,e,n)=>{let r=new Map,i={platform:tw,...n},o={...i.platform,_c:r};return T(t,e,{...i,platform:o})}},61285:(t,e,n)=>{n.d(e,{B:()=>u});var r,i=n(12115),o=n(52712),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),f=0;function u(t){let[e,n]=i.useState(l());return(0,o.N)(()=>{t||n(t=>t??String(f++))},[t]),t||(e?`radix-${e}`:"")}},84945:(t,e,n)=>{n.d(e,{BN:()=>h,ER:()=>p,Ej:()=>g,UE:()=>w,UU:()=>m,cY:()=>d,jD:()=>y,we:()=>s});var r=n(22475),i=n(12115),o=n(47650),l="undefined"!=typeof document?i.useLayoutEffect:function(){};function f(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!f(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function u(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function a(t,e){let n=u(t);return Math.round(e*n)/n}function c(t){let e=i.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:y}=t,[w,x]=i.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=i.useState(s);f(v,s)||b(s);let[R,E]=i.useState(null),[A,S]=i.useState(null),L=i.useCallback(t=>{t!==C.current&&(C.current=t,E(t))},[]),T=i.useCallback(t=>{t!==k.current&&(k.current=t,S(t))},[]),D=h||R,O=p||A,C=i.useRef(null),k=i.useRef(null),P=i.useRef(w),H=null!=g,B=c(g),F=c(d),M=c(y),N=i.useCallback(()=>{if(!C.current||!k.current)return;let t={placement:e,strategy:n,middleware:v};F.current&&(t.platform=F.current),(0,r.rD)(C.current,k.current,t).then(t=>{let e={...t,isPositioned:!1!==M.current};W.current&&!f(P.current,e)&&(P.current=e,o.flushSync(()=>{x(e)}))})},[v,e,n,F,M]);l(()=>{!1===y&&P.current.isPositioned&&(P.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[y]);let W=i.useRef(!1);l(()=>(W.current=!0,()=>{W.current=!1}),[]),l(()=>{if(D&&(C.current=D),O&&(k.current=O),D&&O){if(B.current)return B.current(D,O,N);N()}},[D,O,N,B,H]);let j=i.useMemo(()=>({reference:C,floating:k,setReference:L,setFloating:T}),[L,T]),z=i.useMemo(()=>({reference:D,floating:O}),[D,O]),U=i.useMemo(()=>{let t={position:n,left:0,top:0};if(!z.floating)return t;let e=a(z.floating,w.x),r=a(z.floating,w.y);return m?{...t,transform:"translate("+e+"px, "+r+"px)",...u(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,m,z.floating,w.x,w.y]);return i.useMemo(()=>({...w,update:N,refs:j,elements:z,floatingStyles:U}),[w,N,j,z,U])}let d=(t,e)=>({...(0,r.cY)(t),options:[t,e]}),h=(t,e)=>({...(0,r.BN)(t),options:[t,e]}),p=(t,e)=>({...(0,r.ER)(t),options:[t,e]}),m=(t,e)=>({...(0,r.UU)(t),options:[t,e]}),g=(t,e)=>({...(0,r.Ej)(t),options:[t,e]}),y=(t,e)=>({...(0,r.jD)(t),options:[t,e]}),w=(t,e)=>({...(t=>({name:"arrow",options:t,fn(e){let{element:n,padding:i}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:i}).fn(e):{}:n?(0,r.UE)({element:n,padding:i}).fn(e):{}}}))(t),options:[t,e]})}}]);