"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2841],{7642:(e,t,a)=>{a.d(t,{JX:()=>i,rJ:()=>s});let i=e=>e.toLowerCase().includes("day")?"DAY":e.toLowerCase().includes("week")?"WEEK":e.toLowerCase().includes("month")?"MONTH":e.toLowerCase().includes("year")?"YEAR":"MONTH",s=e=>"ONLINE"==e},10908:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Terrace.7d093efa.svg",height:48,width:48,blurWidth:0,blurHeight:0}},11382:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Gazebo.fe6e9c2d.svg",height:48,width:48,blurWidth:0,blurHeight:0}},25781:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Pet allowed.7a5262d8.svg",height:48,width:48,blurWidth:0,blurHeight:0}},27737:(e,t,a)=>{a.d(t,{E:()=>l});var i=a(95155),s=a(53999);function l(e){let{className:t,...a}=e;return(0,i.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-primary/10",t),...a})}},30962:(e,t,a)=>{a.d(t,{I$:()=>l,KA:()=>c,QS:()=>s,Uw:()=>r,_y:()=>o,b:()=>A,ed:()=>n});var i=a(99493);a(3157);let s=e=>i.apiClient.post("properties/favorite",e),l=e=>(0,i.apiClient)("/properties/filter-location?search=".concat(e.search)),r=e=>i.apiClient.post("properties/filter",e),n=()=>i.apiClient.get("filter-parameter"),A=e=>{let{page:t,per_page:a,search:s,sort_by:l}=e;return i.apiClient.get("users/favorite?page=".concat(t,"&per_page=").concat(a,"&search=").concat(s,"&sort_by=").concat(l))},o=e=>i.apiClient.put("users/filter-setting",e),c=e=>i.apiClient.post("properties/batch-property",e)},31917:(e,t,a)=>{a.d(t,{yM:()=>$,kV:()=>ei,Ex:()=>et,CQ:()=>ea,f1:()=>es,LG:()=>ee,Iq:()=>q,Ay:()=>K});var i=a(95155),s=a(97168),l=a(86894),r=a(27737),n=a(51688),A=a(48251),o=a(38755),c=a(53999),d=a(1702),u=a(33096),m=a(51976),g=a(4516),h=a(62267),x=a(22717),p=a(27043),f=a(66766),b=a(12115),N=a(57383),v=a(14666),j=a(45082),w=a(35216),C=a(11382),y=a(55385),E=a(25781),B=a(45288),R=a(34608),F=a(77563),T=a(95818),k=a(10908),O=a(35741),Q=a(71838),I=a(38333),D=a(38564);function U(e){let{amenities:t,className:a,showText:s=!0}=e,l=(0,p.useTranslations)("seeker");switch(t){case"PLUMBING":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:w.default||"",alt:l("listing.propertyCondition.optionFour.title"),"aria-label":l("listing.feature.additionalFeature.plumbing"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.propertyCondition.optionFour.title")]});case"GAZEBO":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:C.default||"",alt:l("listing.feature.additionalFeature.gazebo"),"aria-label":l("listing.feature.additionalFeature.gazebo"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.gazebo")]});case"CONSTRUCTION_NEARBY":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:y.default||"",alt:l("listing.feature.additionalFeature.constructionNearby"),"aria-label":l("listing.feature.additionalFeature.constructionNearby"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.constructionNearby")]});case"PET_ALLOWED":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:E.default||"",alt:l("listing.feature.additionalFeature.petAllowed"),"aria-label":l("listing.feature.additionalFeature.petAllowed"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.petAllowed")]});case"SUBLEASE_ALLOWED":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:B.default||"","aria-label":l("listing.feature.additionalFeature.subleaseAllowed"),alt:l("listing.feature.additionalFeature.subleaseAllowed"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.subleaseAllowed")]});case"RECENTLY_RENOVATED":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:R.default||"",alt:l("listing.feature.additionalFeature.recentlyRenovated"),"aria-label":l("listing.feature.additionalFeature.recentlyRenovated"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.recentlyRenovated")]});case"ROOFTOP_TERRACE":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:F.default||"",alt:l("listing.feature.additionalFeature.rooftopTerrace"),"aria-label":l("listing.feature.additionalFeature.rooftopTerrace"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.rooftopTerrace")]});case"GARDEN_BACKYARD":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:F.default||"","aria-label":l("listing.feature.additionalFeature.garden"),alt:l("listing.feature.additionalFeature.garden"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.garden")]});case"BATHUB":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:T.default||"",alt:l("listing.feature.additionalFeature.bathub"),"aria-label":l("listing.feature.additionalFeature.bathub"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.bathub")]});case"TERRACE":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:k.default||"",alt:l("listing.feature.additionalFeature.terrace"),"aria-label":l("listing.feature.additionalFeature.terrace"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.terrace")]});case"AIR_CONDITION":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:O.default||"","aria-label":l("listing.feature.additionalFeature.airCondition"),alt:l("listing.feature.additionalFeature.airCondition"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.airCondition")]});case"BALCONY":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:Q.default||"",alt:l("listing.feature.additionalFeature.balcony"),"aria-label":l("listing.feature.additionalFeature.balcony"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.balcony")]});case"MUNICIPAL_WATERWORK":return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(f.default,{src:I.default||"",alt:l("listing.feature.additionalFeature.municipalWaterwork"),"aria-label":l("listing.feature.additionalFeature.municipalWaterwork"),className:(0,c.cn)("w-6 h-6",a),width:24,height:24}),s&&l("listing.feature.additionalFeature.municipalWaterwork")]});default:return(0,i.jsxs)("div",{className:"flex gap-2 text-sm",children:[(0,i.jsx)(D.A,{}),t]})}}let L={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"};function z(e){let{value:t}=e,a=(0,p.useTranslations)("seeker");switch(t){case L.plumbing:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.plumbing,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.plumbing")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.airCondition:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.airCondition,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.airCondition")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.balcony:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.balcony,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.balcony")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.bathub:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.bathub,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.bathub")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.constructionNearby:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.constructionNearby,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.constructionNearby")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.garden:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.garden,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.garden")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.gazebo:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.gazebo,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.gazebo")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.petAllowed:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.petAllowed,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.petAllowed")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.recentlyRenovated:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.recentlyRenovated,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.recentlyRenovated")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.rooftopTerrace:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.rooftopTerrace,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.rooftopTerrace")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.subleaseAllowed:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.subleaseAllowed,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.subleaseAllowed")}),contentClassName:"text-seekers-primary p-2 text-sm"});case L.terrace:return(0,i.jsx)(j.A,{trigger:(0,i.jsx)("div",{className:"cursor-pointer",children:(0,i.jsx)(U,{amenities:L.terrace,className:"!w-4 !h-4",showText:!1})}),content:(0,i.jsx)("p",{children:a("listing.feature.additionalFeature.terrace")}),contentClassName:"text-seekers-primary p-2 text-sm"});default:return(0,i.jsx)(i.Fragment,{})}}var _=a(46797),W=a(21780),H=a(7642),S=a(58721),Y=a(74463),M=a(6874),G=a.n(M),J=a(74810),Z=a(53580);let P=(0,b.createContext)(void 0),V=()=>{let e=(0,b.useContext)(P);if(!e)throw Error("useListingContext must be used within a Listings");return e};function K(e){let{data:t,maxImage:a,conversion:s,forceLazyloading:l,disabledSubscriptionAction:r}=e;return(0,i.jsxs)(q,{data:{...t,thumbnail:a?t.thumbnail.slice(0,a):t.thumbnail},conversion:s,children:[(0,i.jsx)($,{forceLazyloading:l,disableSubscriptionAction:r}),(0,i.jsxs)("div",{className:"space-y-2 px-0.5",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(ee,{className:"line-clamp-1"}),(0,i.jsx)(et,{})]}),(0,i.jsx)(ea,{})]})]})}let q=(0,b.forwardRef)((e,t)=>{let{children:a,data:s,className:l,conversion:r,...n}=e,[A,o]=(0,b.useState)(s);return(0,b.useEffect)(()=>{o(s)},[s]),(0,i.jsx)(P.Provider,{value:{listing:A,setClientFavoriteListing:e=>{var t;o(t=>({...t,isFavorite:e})),null==(t=n.handleFavoriteListion)||t.call(n,e)},handleOpenListing:()=>{window.open("/".concat(s.title.replace(/\W+/g,"-"),"?code=").concat(s.code))},conversion:r},children:(0,i.jsx)("div",{...n,ref:t,className:(0,c.cn)("relative w-full space-y-2 isolate cursor-pointer",l),children:a})})});function X(e){let{isFavorite:t,code:a,size:l="small",extraAction:r,updateClientFavorite:A,activeListing:o=!1,allowFavoritedWhileInactive:d=!1}=e,u=(0,n._)(),g=(0,p.useTranslations)("seeker"),h=N.A.get(v.Xh),{role:x,seekers:f}=(0,_.k)(e=>e),b=(0,c.cn)("z-10  rounded-full h-[26px] w-[26px] hover:bg-transparent hover:scale-110 transition-transform duration-100 ease-linear","small"==l?"w-[24px] h-[24px]":"w-[26px] h-[26px]"),j=(0,c.cn)("text-white","small"==l?"!w-4 !h-4":"!w-5 !h-5"),{toast:w}=(0,Z.dj)(),C=async()=>{if((h||"SEEKER"===x)&&(o||d)){if("Free"===f.accounts.membership)return void w({title:g("misc.subscibePropgram.favorite.title"),description:(0,i.jsxs)(i.Fragment,{children:[g("misc.subscibePropgram.favorite.description"),(0,i.jsx)(s.$,{asChild:!0,variant:"link",size:"sm",className:"p-0 text-seekers-primary h-fit w-fit underline",children:(0,i.jsx)(G(),{href:f.email?Y.ch:Y.jd,children:g("cta.subscribe")})})]})});try{A(!t),await u.mutateAsync({code:a,is_favorite:!t})}catch(e){}}};return(0,i.jsxs)("div",{className:"w-full py-3 px-2.5 pr-3 flex justify-end items-center gap-2",children:[h&&"SEEKER"==x?(0,i.jsx)(s.$,{size:"icon",onClick:e=>{e.stopPropagation(),C()},className:b,variant:"ghost",children:(0,i.jsx)(m.A,{className:j,fill:t?"red":"#707070",fillOpacity:t?1:.5})}):(0,i.jsx)(W.default,{customTrigger:(0,i.jsx)(s.$,{size:"icon",className:b,variant:"ghost",children:(0,i.jsx)(m.A,{className:j,fill:"#707070",fillOpacity:.5})})}),r]})}function $(e){let{heartSize:t="small",containerClassName:a,extraHeaderAction:r,allowFavoriteWhileInactive:n=!1,forceLazyloading:A=!1,disableSubscriptionAction:d}=e,{listing:u,setClientFavoriteListing:m,handleOpenListing:g}=V(),h=(0,p.useTranslations)("seeker"),{seekers:x}=(0,_.k)(e=>e);return(0,i.jsxs)(l.FN,{opts:{loop:x.accounts.membership!=J.U$.free,active:(0,H.rJ)(u.status)&&u.thumbnail.length>1&&!d},className:(0,c.cn)("group isolate w-full aspect-[4/3] relative rounded-xl overflow-hidden",a),children:[(0,i.jsx)(X,{updateClientFavorite:m,isFavorite:u.isFavorite,code:u.code,size:t,extraAction:r,activeListing:(0,H.rJ)(u.status),allowFavoritedWhileInactive:n}),!(0,H.rJ)(u.status)&&(0,i.jsx)("div",{onClick:()=>g(),className:" absolute top-0 left-0 rounded-xl w-full h-full -z-10 bg-slate-800/30 flex flex-col items-center justify-center",children:(0,i.jsx)("p",{className:"text-white font-semibold",children:h("misc.notAvailable")})}),(0,i.jsxs)(l.Wk,{className:"absolute top-0 left-0 w-full h-full ml-0 -z-20",children:[u.thumbnail.map((e,t)=>(0,i.jsxs)(l.A7,{className:"relative",onClick:e=>{e.stopPropagation(),g()},children:[(0,i.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none watermark-overlay"}),(0,i.jsx)(f.default,{src:e.image,alt:"".concat(u.title),fill:!0,sizes:"300px",priority:0==t&&!A,loading:0!=t&&A?"lazy":"eager",style:{objectFit:"cover"},blurDataURL:o.b,placeholder:"blur",quality:10})]},e.id)),x.accounts.membership==J.U$.free&&!d&&(0,i.jsxs)(l.A7,{className:"relative isolate",onClick:e=>{e.stopPropagation()},children:[(0,i.jsx)(f.default,{className:"-z-10 brightness-50 blur-md",src:o.b,alt:"",fill:!0,sizes:"300px",loading:"lazy",blurDataURL:o.b,placeholder:"blur"}),(0,i.jsxs)("div",{className:"z-10 text-white absolute top-1/2 left-1/2 text-center flex flex-col items-center -translate-x-1/2 -translate-y-1/2 min-w-[200px]",children:[(0,i.jsxs)("p",{className:"text-center",children:[h("misc.subscibePropgram.detailPage.description")," "," "]}),(0,i.jsx)(s.$,{asChild:!0,variant:"link",size:"sm",className:"p-0 h-fit w-fit text-white underline",children:(0,i.jsx)(G(),{href:Y.jd,children:h("cta.subscribe")})})]})]})]}),u.thumbnail.length<=1||!(0,H.rJ)(u.status)?(0,i.jsx)(i.Fragment,{}):(0,i.jsxs)("div",{className:"flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3",children:[(0,i.jsx)(l.Q8,{className:"left-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in"}),(0,i.jsx)(l.Oj,{className:"right-3 !opacity-0 group-hover:!opacity-100 transition duration-75 ease-in"})]}),(0,i.jsx)("div",{className:"flex absolute bottom-4 left-0 w-full items-center justify-center",children:(0,i.jsx)(l.ZZ,{carouselDotClassName:"hover:bg-seekers-primary",className:""})}),(0,i.jsx)("div",{className:"absolute w-full pointer-events-none h-full top-0 left-0 bg-gradient-to-b from-neutral-900/40 via-neutral-900/5 to-neutral-100/0 -z-10 group-hover:opacity-0  transition-all duration-100 ease-in-out"})]})}function ee(e){let{className:t}=e,{listing:a,handleOpenListing:s}=V();return(0,i.jsx)("h3",{className:(0,c.cn)("font-semibold text-seekers-text text-base line-clamp-1",t),onClick:e=>{e.stopPropagation(),s()},children:a.title})}function et(e){let{className:t}=e,{listing:a,handleOpenListing:s}=V();return(0,i.jsxs)("div",{className:(0,c.cn)("flex items-center text-xs gap-1 text-seekers-text-light font-medium",t),onClick:e=>{e.stopPropagation(),s()},children:[(0,i.jsx)(g.A,{className:"w-4 h-4"})," ",a.location," "]})}function ea(){let{currency:e}=(0,d.M)(),{listing:t,handleOpenListing:a,conversion:s}=V(),{startWord:l,formattedPrice:r,suffix:n}=((e,t,a,i)=>{let s=(0,p.useTranslations)("seeker"),[l,r]=(0,b.useState)(""),[n,A]=(0,b.useState)(""),[o,c]=(0,b.useState)(0);return(0,b.useEffect)(()=>{let l=(0,H.JX)((null==a?void 0:a.suffix)||""),n=(0,H.JX)((null==i?void 0:i.suffix)||"");(()=>{switch(t){case"LEASEHOLD":let a="MONTH"==n?s("misc.month",{count:(null==i?void 0:i.value)||1}):"YEAR"==n?s("misc.yearWithCount",{count:(null==i?void 0:i.value)||1}):n;return A(s("listing.pricing.suffix.leasehold",{count:(null==i?void 0:i.value)||1,durationType:a})),c(e),r("");case"FREEHOLD":return c(e),r(s("conjuntion.for"));case"RENT":c(e);let o="MONTH"==l?s("misc.month",{count:1}):"YEAR"==l?s("misc.yearWithCount",{count:1}):n;return A("/ ".concat(o)),r(s("misc.startFrom"));default:return}})()},[t,null==i?void 0:i.suffix,null==i?void 0:i.value,null==a?void 0:a.suffix,null==a?void 0:a.value,e]),{startWord:l,suffix:n,formattedPrice:o}})(t.price,t.availability.type,t.availability.minDuration||void 0,t.availability.maxDuration||void 0);return(0,i.jsxs)("p",{className:" text-base text-seekers-text font-medium ",onClick:e=>{e.stopPropagation(),a()},children:[(0,i.jsx)("span",{className:"text-sm font-medium text-seekers-text-lighter",children:(0,c.tT)(l)})," ",(0,c.vv)(r*(s[e]||1),e,"en-US")," ",(0,i.jsx)("span",{className:"text-xs text-seekers-text-lighter",children:n})]})}function ei(){return(0,i.jsxs)("div",{className:"w-full space-y-2",children:[(0,i.jsx)(r.E,{className:"w-full aspect-[4/3]"}),(0,i.jsxs)("div",{className:"space-y-1 px-0.5",children:[(0,i.jsx)(r.E,{className:"w-full h-8"}),(0,i.jsx)(r.E,{className:"w-full h-4"}),(0,i.jsx)(r.E,{className:"w-full h-4"})]})]})}function es(e){var t;let{className:a}=e,s=(0,p.useTranslations)("seeker"),{listing:l,handleOpenListing:r}=V(),n=[A.BT.rooms,A.BT.commercialSpace,A.BT.cafeOrRestaurants,A.BT.offices,A.BT.shops,A.BT.shellAndCore],o=[A.BT.villa,A.BT.apartment,A.BT.homestay,A.BT.guestHouse];return(0,i.jsxs)("div",{className:(0,c.cn)("flex gap-2 text-xs font-normal h-fit !mt-0 text-seekers-text",a),onClick:e=>{e.stopPropagation(),r()},children:[n.includes(l.category||"")&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(j.A,{trigger:(0,i.jsxs)("div",{className:"flex gap-1 items-end",children:[(0,i.jsx)(f.default,{loading:"lazy",src:S.default||"",alt:"",width:16,height:16,className:"w-4 h-4","aria-label":s("listing.feature.additionalFeature.buildingSize")}),(0,i.jsx)("span",{children:l.listingDetail.buildingSize}),(0,i.jsxs)("span",{children:["m",(0,i.jsx)("span",{className:"align-super text-[10px]",children:"2"})]})]}),content:(0,i.jsx)("p",{children:s("listing.feature.additionalFeature.buildingSize")}),contentClassName:"text-seekers-primary p-2 text-sm"})}),o.includes(l.category||"")&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(j.A,{trigger:(0,i.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,i.jsx)(h.A,{className:"w-4 h-4",strokeWidth:1}),(0,i.jsx)("span",{children:l.listingDetail.bedRoom.value})]}),content:(0,i.jsx)("p",{children:s("listing.feature.additionalFeature.bedroom")}),contentClassName:"text-seekers-primary p-2 text-sm"}),(0,i.jsx)(j.A,{trigger:(0,i.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,i.jsx)(x.A,{className:"w-4 h-4",strokeWidth:1}),(0,i.jsx)("span",{children:l.listingDetail.bathRoom.value})]}),content:(0,i.jsx)("p",{children:s("listing.feature.additionalFeature.bathroom")}),contentClassName:"text-seekers-primary p-2 text-sm"})]}),l.category!==A.BT.lands&&(null==(t=l.sellingPoint)?void 0:t.length)>0&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:"flex gap-1 items-end",children:(0,i.jsx)(z,{...l.sellingPoint[0]})})}),(0,i.jsx)(j.A,{trigger:(0,i.jsxs)("div",{className:"flex gap-1 items-end cursor-pointer",children:[(0,i.jsx)(u.hWu,{className:"w-4 h-4",strokeWidth:1.5}),(0,i.jsxs)("p",{children:[l.listingDetail.landSize||""," "," ",(0,i.jsxs)("span",{children:["m",(0,i.jsx)("span",{className:"align-super text-[10px]",children:"2"})]})]})]}),content:(0,i.jsx)("p",{children:s("listing.feature.additionalFeature.land")}),contentClassName:"text-seekers-primary p-2 text-sm"})]})}q.displayName="ListingWrapper"},34608:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Recently renovated.8d37cebc.svg",height:48,width:48,blurWidth:0,blurHeight:0}},35216:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Plumbing.0ad0a3f8.svg",height:48,width:48,blurWidth:0,blurHeight:0}},35741:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Air Conditioning.211f8188.svg",height:48,width:48,blurWidth:0,blurHeight:0}},37777:(e,t,a)=>{a.d(t,{Tooltip:()=>A,TooltipContent:()=>c,TooltipProvider:()=>n,TooltipTrigger:()=>o});var i=a(95155),s=a(12115),l=a(70859),r=a(53999);let n=l.Kq,A=l.bL,o=l.l9,c=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...n}=e;return(0,i.jsx)(l.UC,{ref:t,sideOffset:s,className:(0,r.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})});c.displayName=l.UC.displayName},38333:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Municipal Waterworks.2ab3dfd5.svg",height:48,width:48,blurWidth:0,blurHeight:0}},38755:(e,t,a)=>{a.d(t,{b:()=>i});let i="data:image/jpeg;base64,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"},45082:(e,t,a)=>{a.d(t,{A:()=>l});var i=a(95155);a(12115);var s=a(37777);function l(e){let{content:t,trigger:a,contentClassName:l}=e;return(0,i.jsx)(s.TooltipProvider,{delayDuration:100,children:(0,i.jsxs)(s.Tooltip,{children:[(0,i.jsx)(s.TooltipTrigger,{asChild:!0,children:a}),(0,i.jsx)(s.TooltipContent,{className:l,children:t})]})})}},45288:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Sublease allowed.0d58e5e4.svg",height:48,width:48,blurWidth:0,blurHeight:0}},48251:(e,t,a)=>{a.d(t,{BT:()=>i,FT:()=>l,MC:()=>s,RX:()=>r,aB:()=>n});let i={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"},s={all:"ANY",mountain:"MOUNTAIN",ocean:"OCEAN",ricefield:"RICEFIELD",jungle:"JUNGLE"},l={anything:"ANY",placeToLive:"PLACE_TO_LIVE",business:"BUSINESS",land:"LAND"},r={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"},n={small:{min:1,max:300,key:"small"},medium:{min:301,max:1e3,key:"medium"},large:{min:1001,max:1e5,key:"large"}}},51688:(e,t,a)=>{a.d(t,{_:()=>l});var i=a(30962),s=a(5041);function l(){return(0,s.n)({mutationFn:e=>(0,i.QS)(e)})}},55385:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Construction nearby-next to the location.c84c971d.svg",height:48,width:48,blurWidth:0,blurHeight:0}},55807:(e,t,a)=>{a.d(t,{w:()=>i});function i(e){return{nextPage:e.next_page,page:e.page,pageCount:e.page_count,perPage:e.per_page,prevPage:e.prev_page,total:e.total}}},58721:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Building Size.76edd524.svg",height:48,width:48,blurWidth:0,blurHeight:0}},64237:(e,t,a)=>{a.d(t,{yZ:()=>c,Cv:()=>g,Bb:()=>m,lx:()=>d,xn:()=>u});var i=a(55807),s=a(30962);a(7642);var l=a(40054),r=a.n(l),n=a(7972);function A(e){return e.map(e=>{var t,a,i,s,l,r;return{code:e.code,geolocation:o(e.location.latitude,e.location.longitude),location:e.location.district+", "+e.location.city+", "+e.location.province,price:e.availability.price,thumbnail:(r=e.code,e.images.map((e,t)=>({id:r+t,image:e.image,isHighlight:e.is_highlight})).sort((e,t)=>t.isHighlight-e.isHighlight)),title:e.title,listingDetail:{bathRoom:e.detail.bathroom_total,bedRoom:e.detail.bedroom_total,buildingSize:e.detail.building_size,landSize:e.detail.land_size,cascoStatus:e.detail.casco_status,gardenSize:e.detail.garden_size},availability:{availableAt:e.availability.available_at||"",maxDuration:(null==(t=e.availability.duration_max_unit)?void 0:t.value)&&e.availability.duration_max?{value:e.availability.duration_max||1,suffix:null==(a=e.availability.duration_max_unit)?void 0:a.value}:null,minDuration:(null==(i=e.availability.duration_min_unit)?void 0:i.value)&&e.availability.duration_min?{value:e.availability.duration_min||1,suffix:null==(s=e.availability.duration_min_unit)?void 0:s.value}:null,type:e.availability.type.value||""},sellingPoint:e.features.selling_points,category:e.detail.option.type,isFavorite:(null==e||null==(l=e._count)?void 0:l.favorites)>0,status:e.status}})}let o=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,i=1/111320*a;return[e+.4*i,t+.4*i]};async function c(e){try{let t=await (0,s.KA)({property_list:e});return{data:A(t.data.data)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function d(e){try{let t=await (0,s.Uw)(e);try{let t=Object.fromEntries(Object.entries(e).filter(e=>{let[t,a]=e;return void 0!==a}));2!==Object.keys(t).length&&await (0,s._y)(e)}catch(e){}return{data:A(t.data.data.items),meta:(0,i.w)(t.data.data.meta)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function u(e){if(e.search.length<3)return{data:[]};try{let t=await (0,s.I$)(e);return{data:function(e,t){let a=[];return t.forEach(t=>{Object.values(t).forEach(t=>{(function(e,t){let a=(0,n.A)(e.toLowerCase(),t.toLowerCase());return 1-a/Math.max(e.length,t.length)})(t,e)>0&&a.push(t)})}),r().uniq(a)}(e.search,t.data.data)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function m(){var e,t;try{return{data:{priceRange:{min:(e=(await (0,s.ed)()).data.data).price_range._min.price,max:e.price_range._max.price},buildingSizeRange:{max:e.size_range._max.building_size,min:e.size_range._min.building_size},gardenSizeRange:{max:e.size_range._max.garden_size,min:e.size_range._min.garden_size},landSizeRange:{max:e.size_range._max.land_size,min:e.size_range._min.land_size},furnishingOptions:e.furnishing_options[0].childrens.map(e=>({title:e.title,value:e.value})),livingOptions:e.living_options[0].childrens.map(e=>({title:e.title,value:e.value})),parkingOptions:e.parking_options[0].childrens.map(e=>({title:e.title,value:e.value})),poolOptions:e.pool_options[0].childrens.map(e=>({title:e.title,value:e.value}))},meta:void 0}}catch(e){return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function g(e){try{let t=await (0,s.b)({page:+e.page,per_page:+e.per_page,search:e.search||"",sort_by:e.sort_by});return{data:A(t.data.data.items),meta:(0,i.w)(t.data.data.meta)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}},71838:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Balcony.322dc8e4.svg",height:48,width:48,blurWidth:0,blurHeight:0}},77563:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Rooftop terrace.cb94448e.svg",height:48,width:48,blurWidth:0,blurHeight:0}},86894:(e,t,a)=>{a.d(t,{A7:()=>h,FN:()=>m,Oj:()=>p,Q8:()=>x,Wk:()=>g,ZZ:()=>f});var i=a(95155),s=a(12115),l=a(85005),r=a(53999),n=a(97168),A=a(42355),o=a(13052),c=a(27043);let d=s.createContext(null);function u(){let e=s.useContext(d);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let m=s.forwardRef((e,t)=>{let{orientation:a="horizontal",opts:n,setApi:A,plugins:o,className:c,children:u,...m}=e,[g,h]=(0,l.A)({...n,axis:"horizontal"===a?"x":"y"},o),[x,p]=s.useState(!1),[f,b]=s.useState(!1),[N,v]=s.useState(0),j=s.useCallback(e=>{e&&(p(e.canScrollPrev()),b(e.canScrollNext()),v(e.selectedScrollSnap()))},[]),w=s.useCallback(()=>{null==h||h.scrollPrev()},[h]),C=s.useCallback(()=>{null==h||h.scrollNext()},[h]),y=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),w()):"ArrowRight"===e.key&&(e.preventDefault(),C())},[w,C]),E=s.useCallback(e=>{null==h||h.scrollTo(e)},[h]);return s.useEffect(()=>{h&&A&&A(h)},[h,A]),s.useEffect(()=>{if(h)return j(h),h.on("reInit",j),h.on("select",j),()=>{null==h||h.off("select",j)}},[h,j]),(0,i.jsx)(d.Provider,{value:{carouselRef:g,api:h,opts:n,orientation:a||((null==n?void 0:n.axis)==="y"?"vertical":"horizontal"),scrollPrev:w,scrollNext:C,canScrollPrev:x,canScrollNext:f,selectedIndex:N,scrollTo:E},children:(0,i.jsx)("div",{...m,ref:t,onKeyDownCapture:y,className:(0,r.cn)("relative",c),role:"region","aria-roledescription":"carousel",children:u})})});m.displayName="Carousel";let g=s.forwardRef((e,t)=>{let{className:a,...s}=e,{carouselRef:l,orientation:n}=u();return(0,i.jsx)("div",{ref:l,className:"overflow-hidden",children:(0,i.jsx)("div",{...s,ref:t,className:(0,r.cn)("flex","horizontal"===n?"-ml-4":"-mt-4 flex-col",a)})})});g.displayName="CarouselContent";let h=s.forwardRef((e,t)=>{let{className:a,...s}=e,{orientation:l}=u();return(0,i.jsx)("div",{...s,ref:t,role:"group","aria-roledescription":"slide",className:(0,r.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===l?"pl-4":"pt-4",a)})});h.displayName="CarouselItem";let x=s.forwardRef((e,t)=>{let{iconClassName:a,className:s,variant:l="outline",size:o="icon",...d}=e,{orientation:m,scrollPrev:g,canScrollPrev:h}=u(),x=(0,c.useTranslations)("universal");return(0,i.jsxs)(n.$,{...d,ref:t,variant:l,size:o,className:(0,r.cn)("absolute  h-6 w-6 rounded-full","horizontal"===m?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",s),disabled:!h,onClick:g,children:[(0,i.jsx)(A.A,{className:(0,r.cn)("h-4 w-4",a)}),(0,i.jsx)("span",{className:"sr-only",children:x("cta.previous")})]})});x.displayName="CarouselPrevious";let p=s.forwardRef((e,t)=>{let{iconClassName:a,className:s,variant:l="outline",size:A="icon",...d}=e,{orientation:m,scrollNext:g,canScrollNext:h}=u(),x=(0,c.useTranslations)("seeker");return(0,i.jsxs)(n.$,{...d,ref:t,variant:l,size:A,className:(0,r.cn)("absolute h-6 w-6 rounded-full","horizontal"===m?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",s),disabled:!h,onClick:g,children:[(0,i.jsx)(o.A,{className:(0,r.cn)("h-4 w-4",a)}),(0,i.jsx)("span",{className:"sr-only",children:x("cta.next")})]})});p.displayName="CarouselNext";let f=s.forwardRef((e,t)=>{let{className:a,carouselDotClassName:s,...l}=e,{selectedIndex:A,scrollTo:o,api:c}=u();return(0,i.jsx)("div",{ref:t,className:(0,r.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",a),...l,children:null==c?void 0:c.scrollSnapList().map((e,t)=>(0,i.jsx)(n.$,{size:"icon",className:(0,r.cn)(s,"embla__dot h-2 w-2 rounded-full ",t===A?"bg-white/90 ":"bg-black/10"),onClick:()=>null==o?void 0:o(t)},t))})});f.displayName="CarouselDots"},95818:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});let i={src:"/_next/static/media/Bathtub.64872ead.svg",height:48,width:48,blurWidth:0,blurHeight:0}}}]);