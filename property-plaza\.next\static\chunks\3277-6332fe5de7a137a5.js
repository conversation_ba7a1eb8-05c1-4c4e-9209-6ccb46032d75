"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3277],{12673:(e,t,r)=>{r.d(t,{cQ:()=>a,lL:()=>c,vN:()=>o,wJ:()=>i,x_:()=>n});var s=r(74810);function n(e){return s.U$.free.includes(e)?s.U$.free:s.U$.finder.includes(e)?s.U$.finder:s.U$.archiver.includes(e)?s.U$.archiver:s.U$.free}function a(e){return e==s.U$.free?0:e==s.U$.finder?5:10*(e==s.U$.archiver)}let i=10,o={max:13,min:10};function c(e){return e==s.U$.free?o:e==s.U$.finder?{max:14,min:i}:e==s.U$.archiver?{max:15,min:i}:o}},14666:(e,t,r)=>{r.d(t,{Dg:()=>n,Dj:()=>m,EM:()=>o,FN:()=>f,Ix:()=>v,Nr:()=>l,Xh:()=>s,Zu:()=>c,bV:()=>u,gF:()=>i,kj:()=>d,s7:()=>p,wz:()=>a});let s="tkn",n="SEEKER",a=8,i=1,o=30,c=300,l=10,d="cookies-collection-status",u="necessary-cookies-collection-status",m="functional-cookies-collection-status",f="analytic-cookies-collection-status",p="marketing-cookies-collection-status",v={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},28679:(e,t,r)=>{r.d(t,{DY:()=>n,jp:()=>i,yN:()=>a});var s=r(99493);let n=async(e,t)=>s.apiClient.post("auth/register",e,{headers:{"g-token":t||""}}),a=async e=>s.apiClient.put("users/update",e),i=async e=>s.apiClient.get("auth/me",e)},30070:(e,t,r)=>{r.d(t,{C5:()=>b,MJ:()=>g,Rr:()=>h,eI:()=>p,lR:()=>v,lV:()=>l,zB:()=>u});var s=r(95155),n=r(12115),a=r(66634),i=r(62177),o=r(53999),c=r(82714);let l=i.Op,d=n.createContext({}),u=e=>{let{...t}=e;return(0,s.jsx)(d.Provider,{value:{name:t.name},children:(0,s.jsx)(i.xI,{...t})})},m=()=>{let e=n.useContext(d),t=n.useContext(f),{getFieldState:r,formState:s}=(0,i.xW)(),a=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...a}},f=n.createContext({}),p=n.forwardRef((e,t)=>{let{className:r,...a}=e,i=n.useId();return(0,s.jsx)(f.Provider,{value:{id:i},children:(0,s.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",r),...a})})});p.displayName="FormItem";let v=n.forwardRef((e,t)=>{let{className:r,...n}=e,{error:a,formItemId:i}=m();return(0,s.jsx)(c.J,{ref:t,className:(0,o.cn)(a&&"text-destructive",r),htmlFor:i,...n})});v.displayName="FormLabel";let g=n.forwardRef((e,t)=>{let{...r}=e,{error:n,formItemId:i,formDescriptionId:o,formMessageId:c}=m();return(0,s.jsx)(a.DX,{ref:t,id:i,"aria-describedby":n?"".concat(o," ").concat(c):"".concat(o),"aria-invalid":!!n,...r})});g.displayName="FormControl";let h=n.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:a}=m();return(0,s.jsx)("p",{ref:t,id:a,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",r),...n})});h.displayName="FormDescription";let b=n.forwardRef((e,t)=>{let{className:r,children:n,...a}=e,{error:i,formMessageId:c}=m(),l=i?String(null==i?void 0:i.message):n;return l?(0,s.jsx)("p",{ref:t,id:c,className:(0,o.cn)("text-[0.8rem] font-medium text-destructive",r),...a,children:l}):null});b.displayName="FormMessage"},30145:(e,t,r)=>{r.d(t,{Q:()=>n});var s=r(23464);function n(e){if(s.A.isAxiosError(e)){var t,r;if((null==(t=e.response)?void 0:t.status)===401)throw Error("Unauthorized: Invalid token or missing credentials");if((null==(r=e.response)?void 0:r.status)===404)throw Error("Not Found: The requested resource could not be found");if(e.response)throw Error("Request failed with status code ".concat(e.response.status,": ").concat(e.response.statusText));else if(e.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error("Error during request setup: ".concat(e.message))}throw Error(e)}},35201:(e,t,r)=>{r.d(t,{H:()=>l,g:()=>c});var s=r(93846),n=r(14666),a=r(46797),i=r(19373),o=r(57383);let c="my-detail";function l(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{setSeekers:t,clearUser:r,setRole:l}=(0,a.k)(e=>e),d=o.A.get(n.Xh);return(0,i.I)({queryKey:[c,d||"0"],queryFn:async()=>{if(!d)return a.h;try{let e=await (0,s.i8)();return t(e),l("SEEKER"),e}catch(e){return r(),a.h}},refetchOnWindowFocus:!1,retry:!1,enabled:e})}},46797:(e,t,r)=>{r.d(t,{h:()=>l,k:()=>d});var s=r(12673),n=r(88693),a=r(46786),i=r(57383),o=r(74810);let c={getItem:e=>{let t=i.A.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{i.A.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{i.A.remove(e)}},l={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:o.U$.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:s.vN},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},d=(0,n.vt)()((0,a.Zr)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:l,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:l}))}),{name:"user",storage:(0,a.KU)(()=>c)}))},53580:(e,t,r)=>{r.d(t,{dj:()=>u});var s=r(12115);let n=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},o=[],c={toasts:[]};function l(e){c=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(c,e),o.forEach(e=>{e(c)})}function d(e){let{...t}=e,r=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=s.useState(c);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{r.d(t,{ZV:()=>l,cn:()=>o,gT:()=>d,jW:()=>m,lF:()=>p,q7:()=>f,tT:()=>v,vv:()=>c,yv:()=>u});var s=r(52596),n=r(82940),a=r.n(n),i=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,s.$)(t))}r(87358);let c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat((e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}})(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2*("IDR"!=t)}).format(e)},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function u(e){let t=a()(e),r=a()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function p(e,t){return e.some(e=>t.includes(e))}let v=e=>e.charAt(0).toUpperCase()+e.slice(1)},54568:(e,t,r)=>{r.d(t,{n:()=>l});var s=r(28679),n=r(26715),a=r(5041),i=r(35201),o=r(53580),c=r(27043);function l(){let e=(0,n.jE)(),{toast:t}=(0,o.dj)(),r=(0,c.useTranslations)("universal");return(0,a.n)({mutationFn:e=>(0,s.yN)(e),onSuccess:()=>{e.invalidateQueries({queryKey:[i.g]}),t({title:r("success.updateUser")})},onError:e=>{let s=e.response.data;t({title:r("error.foundError"),description:s.message,variant:"destructive"})}})}},74810:(e,t,r)=>{r.d(t,{U$:()=>s,dF:()=>n});let s={archiver:"Achiever",finder:"Finder",free:"Free"},n={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},76037:(e,t,r)=>{r.d(t,{Separator:()=>o});var s=r(95155),n=r(12115),a=r(14050),i=r(53999);let o=n.forwardRef((e,t)=>{let{className:r,orientation:n="horizontal",decorative:o=!0,...c}=e;return(0,s.jsx)(a.b,{ref:t,decorative:o,orientation:n,className:(0,i.cn)("shrink-0 bg-border","horizontal"===n?"h-[1px] w-full":"h-full w-[1px]",r),...c})});o.displayName=a.b.displayName},82714:(e,t,r)=>{r.d(t,{J:()=>l});var s=r(95155),n=r(12115),a=r(40968),i=r(74466),o=r(53999);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.b,{ref:t,className:(0,o.cn)(c(),r),...n})});l.displayName=a.b.displayName},89852:(e,t,r)=>{r.d(t,{p:()=>i});var s=r(95155),n=r(12115),a=r(53999);let i=n.forwardRef((e,t)=>{let{className:r,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,a.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},93846:(e,t,r)=>{r.d(t,{i8:()=>i});var s=r(28679),n=r(12673);r(14666);var a=r(30145);async function i(e){try{let t=await (0,s.jp)(e);return function(e){var t,r,s,a,i,o,c,l,d,u;let m=(0,n.x_)((null==(t=e.accounts.subscription)?void 0:t.detail.name)||"");return{accounts:{about:e.accounts.about,citizenship:e.accounts.citizenship||"",credit:{amount:(null==(r=e.accounts.credit)?void 0:r.amount)||0,updatedAt:(null==(s=e.accounts.credit)?void 0:s.updated_at)||""},facebookSocial:e.accounts.facebook_social||"",firstName:e.accounts.first_name,image:e.accounts.image,isSubscriber:e.accounts.is_subscriber,language:e.accounts.language,lastName:e.accounts.last_name,membership:m,twitterSocial:e.accounts.twitter_social||"",address:e.accounts.address||"",chat:{current:0,max:(0,n.cQ)(m)},zoomFeature:(0,n.lL)(m)},has2FA:e.is_2fa,email:e.email,code:e.code,isActive:e.is_active,phoneNumber:e.phone_number,phoneCode:e.phone_code,type:e.type,setting:{messageNotif:null==(a=e.accounts.settings)?void 0:a.message_notif,newsletterNotif:null==(i=e.accounts.settings)?void 0:i.newsletter_notif,priceAlertNotif:null==(o=e.accounts.settings)?void 0:o.price_alert_notif,propertyNotif:null==(c=e.accounts.settings)?void 0:c.property_notif,soundNotif:null==(l=e.accounts.settings)?void 0:l.sound_notif,specialOfferNotif:null==(d=e.accounts.settings)?void 0:d.special_offer_notif,surveyNotif:null==(u=e.accounts.settings)?void 0:u.survey_notif}}}(t.data.data)}catch(e){throw Error((0,a.Q)(e))}}},97168:(e,t,r)=>{r.d(t,{$:()=>d});var s=r(95155),n=r(12115),a=r(66634),i=r(74466),o=r(53999),c=r(51154);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:d=!1,loading:u=!1,...m}=e,f=d?a.DX:"button";return(0,s.jsx)(f,{className:(0,o.cn)(l({variant:n,size:i,className:r})),ref:t,disabled:u||m.disabled,...m,children:u?(0,s.jsx)(c.A,{className:(0,o.cn)("h-4 w-4 animate-spin")}):m.children})});d.displayName="Button"},99493:(e,t,r)=>{r.d(t,{B:()=>l,apiClient:()=>c});var s=r(14666),n=r(23464),a=r(57383),i=r(79189);let o=new(r.n(i)()).Agent({rejectUnauthorized:!1}),c=n.A.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:a.A.get(s.Xh)?"Bearer "+a.A.get(s.Xh):""},httpsAgent:o}),l=n.A.create({baseURL:"/api/",httpsAgent:o})}}]);