(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3288],{43377:(e,t,n)=>{"use strict";var r,i,o,a=function(e){var t=Object.create(null);return e&&Object.keys(e).forEach(function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}}),t.default=e,Object.freeze(t)}(n(12115));let{useRef:s,useEffect:l,isValidElement:u}=a,c=null!=(r=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE)?r:a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,d=Symbol.for("react.memo_cache_sentinel"),f="function"==typeof(null==(i=a.__COMPILER_RUNTIME)?void 0:i.c)?a.__COMPILER_RUNTIME.c:function(e){return a.useMemo(()=>{let t=Array(e);for(let n=0;n<e;n++)t[n]=d;return t[d]=!0,t},[])},p={};["readContext","useCallback","useContext","useEffect","useImperativeHandle","useInsertionEffect","useLayoutEffect","useMemo","useReducer","useRef","useState","useDebugValue","useDeferredValue","useTransition","useMutableSource","useSyncExternalStore","useId","unstable_isNewReconciler","getCacheSignal","getCacheForType","useCacheRefresh"].forEach(e=>{p[e]=()=>{throw Error(`[React] Unexpected React hook call (${e}) from a React compiled function. Check that all hooks are called directly and named according to convention ('use[A-Z]') `)}});p.useMemoCache=e=>{!1;throw Error("React Compiler internal invariant violation: unexpected null dispatcher")},function(e){e[e.PushGuardContext=0]="PushGuardContext",e[e.PopGuardContext=1]="PopGuardContext",e[e.PushExpectHook=2]="PushExpectHook",e[e.PopExpectHook=3]="PopExpectHook"}(o||(o={})),t.c=f},73288:(e,t,n)=>{"use strict";let r;n.r(t),n.d(t,{default:()=>aG});var i,o,a,s,l,u,c,d,f,p,h=n(95155),y=n(43377),m=n(12115),g=n(47650),v=n(42770);let b={"handshake/syn":v.Tx,"handshake/syn-ack":v.Rr,"handshake/ack":v.IM,"channel/response":v._K,"channel/heartbeat":v.vd,"channel/disconnect":v.FS,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},w={[v.Tx]:"handshake/syn",[v.Rr]:"handshake/syn-ack",[v.IM]:"handshake/ack",[v._K]:"channel/response",[v.vd]:"channel/heartbeat",[v.FS]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},x=e=>{let{data:t}=e;return t&&"object"==typeof t&&"domain"in t&&"type"in t&&"from"in t&&"to"in t&&("sanity/channels"===t.domain&&(t.domain=v.V2),"overlays"===t.to&&(t.to="visual-editing"),"overlays"===t.from&&(t.from="visual-editing"),t.channelId=t.connectionId,delete t.connectionId,t.type=b[t.type]??t.type),e},E=({context:e},t)=>{let{sources:n,targetOrigin:r}=e,i=(e=>{let{channelId:t,...n}=e,r={...n,connectionId:t};return r.domain===v.V2&&(r.domain="sanity/channels"),"visual-editing"===r.to&&(r.to="overlays"),"visual-editing"===r.from&&(r.from="overlays"),r.type=w[r.type]??r.type,"channel/response"===r.type&&r.responseTo&&!r.data&&(r.data={responseTo:r.responseTo}),("handshake/syn"===r.type||"handshake/syn-ack"===r.type||"handshake/ack"===r.type)&&(r.data={id:r.connectionId}),r})(t.message);n.forEach(e=>{e.postMessage(i,{targetOrigin:r})})},_={alt:"altKey",ctrl:"ctrlKey",mod:"u">typeof window&&/Mac|iPod|iPhone|iPad/.test(window.navigator.platform)?"metaKey":"ctrlKey",shift:"shiftKey"};function k(e){return"Alt"===e.key}var j=n(3812);function S(e){return e.split(/[[.\]]/g).filter(Boolean).map(e=>{var t;return e.includes("==")?function(e){let[t,n]=e.split("==");if("_key"!==t)throw Error(`Currently only "_key" is supported as path segment. Found ${t}`);if(typeof n>"u")throw Error('Invalid path segment, expected `key=="value"`');return{_key:n.replace(/^['"]/,"").replace(/['"]$/,"")}}(e):I.test(t=e)?Number(t):t})}let I=/^-?\d+$/,O=/^[a-z_$]+/;function A(e){return e.map((e,t)=>{var n;return n=0===t,Array.isArray(e)?`[${e[0]}:${e[1]||""}]`:"number"==typeof e?`[${e}]`:"object"==typeof e&&"_key"in e&&"string"==typeof e._key?`[_key==${JSON.stringify(e._key)}]`:"string"==typeof e&&O.test(e)?n?e:`.${e}`:`['${e}']`}).join("")}function M(e){if("createIfNotExists"in e)return{type:"createIfNotExists",document:e.createIfNotExists};if("createOrReplace"in e)return{type:"createOrReplace",document:e.createOrReplace};if("create"in e)return{type:"create",document:e.create};if("delete"in e)return{id:e.delete.id,type:"delete"};if("patch"in e){var t,n,r,i,o,a;return{type:"patch",id:e.patch.id,patches:[..."set"in(n=t=e.patch)?Object.keys(n.set).map(e=>({path:S(e),op:{type:"set",value:n.set[e]}})):[],..."setIfMissing"in(r=t)?Object.keys(r.setIfMissing).map(e=>({path:S(e),op:{type:"setIfMissing",value:r.setIfMissing[e]}})):[],..."unset"in(i=t)?i.unset.map(e=>({path:S(e),op:{type:"unset"}})):[],..."inc"in(o=t)?Object.keys(o.inc).map(e=>({path:S(e),op:{type:"inc",amount:o.inc[e]}})):[],..."inc"in(a=t)?Object.keys(a.dec).map(e=>({path:S(e),op:{type:"dec",amount:a.dec[e]}})):[],...function(e){if(!("insert"in e))return[];let t=function(e){let t=P.filter(t=>t in e);if(t.length>1)throw Error(`Insert patch is ambiguous. Should only contain one of: ${P.join(", ")}, instead found ${t.join(", ")}`);return t[0]}(e.insert);if(!t)throw Error("Insert patch missing position");let n=S(e.insert[t]),r=n.pop();return[{path:n,op:{type:"insert",position:t,referenceItem:r,items:e.insert.items}}]}(t)]}}throw Error(`Unknown mutation: ${JSON.stringify(e)}`)}let P=["before","replace","after"];function $(e){return T(e)}function C(e){return e.flatMap($)}function T(e){if("create"===e.type||"createIfNotExists"===e.type||"createOrReplace"===e.type)return{[e.type]:e.document};if("delete"===e.type)return{delete:{id:e.id}};let t=e.options?.ifRevision;return e.patches.map(n=>({patch:{id:e.id,...t&&{ifRevisionID:t},...function(e){let{path:t,op:n}=e;if("unset"===n.type)return{unset:[A(t)]};if("insert"===n.type)return{insert:{[n.position]:A([...t,n.referenceItem]),items:n.items}};if("diffMatchPatch"===n.type)return{diffMatchPatch:{[A(t)]:n.value}};if("inc"===n.type)return{inc:{[A(t)]:n.amount}};if("dec"===n.type)return{dec:{[A(t)]:n.amount}};if("set"===n.type||"setIfMissing"===n.type)return{[n.type]:{[A(t)]:n.value}};if("truncate"===n.type){let e=[n.startIndex,"number"==typeof n.endIndex?n.endIndex:""].join(":");return{unset:[`${A(t)}[${e}]`]}}if("upsert"===n.type)return{unset:n.items.map(e=>A([...t,{_key:e._key}])),insert:{[n.position]:A([...t,n.referenceItem]),items:n.items}};if("assign"===n.type)return{set:Object.fromEntries(Object.keys(n.value).map(e=>[A(t.concat(e)),n.value[e]]))};if("unassign"===n.type)return{unset:n.keys.map(e=>A(t.concat(e)))};if("replace"===n.type)return{insert:{replace:A(t.concat(n.referenceItem)),items:n.items}};if("remove"===n.type)return{unset:[A(t.concat(n.referenceItem))]};throw Error(`Unknown operation type ${n.type}`)}(n)}}))}function R(e){return Array.isArray(e)?e:[e]}var L=Object.freeze({__proto__:null,decode:function(e){return M(e)},decodeAll:function(e){return e.map(M)},encode:$,encodeAll:C,encodeMutation:T,encodeTransaction:function(e){return{transactionId:e.id,mutations:C(e.mutations)}}});function D(e,t,n){return{type:"insert",referenceItem:n,position:t,items:R(e)}}function N(e,t){return{type:"truncate",startIndex:e,endIndex:t}}function F(e){return{type:"remove",referenceItem:e}}function z(e,t){return{path:"string"==typeof e?S(e):e,op:t}}var U=n(80898);let W=(0,U.H6)(),V=W,q=new Set,B=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,G=/_key\s*==\s*['"](.*)['"]/,H=/^\d*:\d*$/;function K(e){return"number"==typeof e||"string"==typeof e&&/^\[\d+\]$/.test(e)}function Y(e){return"string"==typeof e?G.test(e.trim()):"object"==typeof e&&"_key"in e}function X(e){if("string"==typeof e&&H.test(e))return!0;if(!Array.isArray(e)||2!==e.length)return!1;let[t,n]=e;return("number"==typeof t||""===t)&&("number"==typeof n||""===n)}function J(e){if(!Array.isArray(e))throw Error("Path is not an array");return e.reduce((e,t,n)=>{let r=typeof t;if("number"===r)return`${e}[${t}]`;if("string"===r)return`${e}${0===n?"":"."}${t}`;if(Y(t)&&t._key)return`${e}[_key=="${t._key}"]`;if(Array.isArray(t)){let[n,r]=t;return`${e}[${n}:${r}]`}throw Error(`Unsupported path segment \`${JSON.stringify(t)}\``)},"")}function Z(e){if("string"!=typeof e)throw Error("Path is not a string");let t=e.match(B);if(!t)throw Error("Invalid path string");return t.map(Q)}function Q(e){return K(e)?Number(e.replace(/[^\d]/g,"")):Y(e)?{_key:e.match(G)[1]}:X(e)?function(e){let[t,n]=e.split(":").map(e=>""===e?e:Number(e));return[t,n]}(e):e}var ee=Object.freeze({__proto__:null,fromString:Z,get:function(e,t,n){let r="string"==typeof t?Z(t):t;if(!Array.isArray(r))throw Error("Path must be an array or a string");let i=e;for(let e=0;e<r.length;e++){let t=r[e];if(K(t)){if(!Array.isArray(i))return n;i=i[t]}if(Y(t)){if(!Array.isArray(i))return n;i=i.find(e=>e._key===t._key)}if("string"==typeof t&&(i="object"==typeof i&&null!==i?i[t]:void 0),typeof i>"u")return n}return i},isIndexSegment:K,isIndexTuple:X,isKeySegment:Y,reKeySegment:G,toString:J});let et="drafts.";function en(e){return{lang:e?.lang??l?.lang,message:e?.message,abortEarly:e?.abortEarly??l?.abortEarly,abortPipeEarly:e?.abortPipeEarly??l?.abortPipeEarly,skipPipe:e?.skipPipe}}function er(e,t,n,r,i){var o,a,s,l;let f,p=i&&"input"in i?i.input:n.value,h=i?.expected??e.expects,y=i?.received??("object"==(f=typeof p)&&(f=(p&&Object.getPrototypeOf(p)?.constructor?.name)??"null"),"string"===f?`"${p}"`:"number"===f||"bigint"===f||"boolean"===f?`${p}`:f),m={kind:e.kind,type:e.type,input:p,expected:h,received:y,message:`Invalid ${t}: ${h?`Expected ${h} but r`:"R"}eceived ${y}`,requirement:e.requirement,path:i?.path,issues:i?.issues,lang:r.lang,abortEarly:r.abortEarly,abortPipeEarly:r.abortPipeEarly,skipPipe:r.skipPipe},g="schema"===e.kind,v=e.message??(o=e.reference,a=m.lang,d?.get(o)?.get(a))??(g?(s=m.lang,c?.get(s)):null)??r.message??(l=m.lang,u?.get(l));v&&(m.message="function"==typeof v?v(m):v),g&&(n.typed=!1),n.issues?n.issues.push(m):n.issues=[m]}var ei=class extends Error{issues;constructor(e){super(e[0].message),this.name="ValiError",this.issues=e}};function eo(e,t){return{kind:"schema",type:"object",reference:eo,expects:"Object",async:!1,entries:e,message:t,_run(e,t){let n=e.value;if(n&&"object"==typeof n)for(let r in e.typed=!0,e.value={},this.entries){let i=n[r],o=this.entries[r]._run({typed:!1,value:i},t);if(o.issues){let a={type:"object",origin:"value",input:n,key:r,value:i};for(let t of o.issues)t.path?t.path.unshift(a):t.path=[a],e.issues?.push(t);if(e.issues||(e.issues=o.issues),t.abortEarly){e.typed=!1;break}}o.typed||(e.typed=!1),(void 0!==o.value||r in n)&&(e.value[r]=o.value)}else er(this,"type",e,t);return e}}}function ea(e,...t){let n={kind:"schema",type:"optional",reference:ea,expects:`${e.expects} | undefined`,async:!1,wrapped:e,_run(e,t){if(void 0===e.value&&("default"in this&&(e.value="function"==typeof this.default?this.default(e,t):this.default),void 0===e.value))return e.typed=!0,e;return this.wrapped._run(e,t)}};return 0 in t&&(n.default=t[0]),n}function es(e){return{kind:"schema",type:"string",reference:es,expects:"string",async:!1,message:e,_run(e,t){return"string"==typeof e.value?e.typed=!0:er(this,"type",e,t),e}}}function el(e,t,n){let r=e._run({typed:!1,value:t},en(n));return{typed:r.typed,success:!r.issues,output:r.value,issues:r.issues}}let eu=/^([\w-]+):(0|[1-9][0-9]*)$/,ec=/^([\w-]+):([0-9]+),([0-9]+)$/,ed=/^([\w-]+):([\w-]+)$/,ef="drafts.",ep=function(...e){return{...e[0],pipe:e,_run(t,n){for(let r=0;r<e.length;r++){t=e[r]._run(t,n);let i=e[r+1];if(n.skipPipe||t.issues&&(n.abortEarly||n.abortPipeEarly||i?.kind==="schema"||i?.kind==="transformation")){t.typed=!1;break}}return t}}}(es(),function e(t,n){return{kind:"validation",type:"min_length",reference:e,async:!1,expects:`>=${t}`,requirement:t,message:n,_run(e,t){return e.typed&&e.value.length<this.requirement&&er(this,"length",e,t,{received:`${e.value.length}`}),e}}}(1)),eh=ea(ep),ey=eo({baseUrl:ep,dataset:eh,id:ep,path:ep,projectId:eh,tool:eh,type:eh,workspace:eh,isDraft:ea(es())}),em=eo({origin:ep,href:ep,data:ea(function e(t,n,r){return{kind:"schema",type:"record",reference:e,expects:"Object",async:!1,key:t,value:n,message:r,_run(e,t){let n=e.value;if(n&&"object"==typeof n){for(let r in e.typed=!0,e.value={},n)if("__proto__"!==r&&"prototype"!==r&&"constructor"!==r){let i=n[r],o=this.key._run({typed:!1,value:r},t);if(o.issues){let a={type:"record",origin:"key",input:n,key:r,value:i};for(let t of o.issues)t.path=[a],e.issues?.push(t);if(e.issues||(e.issues=o.issues),t.abortEarly){e.typed=!1;break}}let a=this.value._run({typed:!1,value:i},t);if(a.issues){let o={type:"record",origin:"value",input:n,key:r,value:i};for(let t of a.issues)t.path?t.path.unshift(o):t.path=[o],e.issues?.push(t);if(e.issues||(e.issues=a.issues),t.abortEarly){e.typed=!1;break}}o.typed&&a.typed||(e.typed=!1),o.typed&&(e.value[o.value]=a.value)}}else er(this,"type",e,t);return e}}}(es(),function e(){return{kind:"schema",type:"unknown",reference:e,expects:"unknown",async:!1,_run:e=>(e.typed=!0,e)}}()))});function eg(e){let t=el(ey,e);if(t.success)return t.output;let n=el(em,e);if(n.success)try{let e=new URL(n.output.href,typeof document>"u"?"https://example.com":location.origin);return e.searchParams.size>0?function(e,t,n){let r=e._run({typed:!1,value:t},en(void 0));if(r.issues)throw new ei(r.issues);return r.value}(ey,Object.fromEntries(e.searchParams.entries())):n.output}catch(e){return console.error("Failed to parse sanity node",e),n.output}}let ev=/_key\s*==\s*['"](.*)['"]/,eb=/^\d*:\d*$/;function ew(e){return"number"==typeof e||"string"==typeof e&&/^\[\d+\]$/.test(e)}function ex(e){return"string"==typeof e?ev.test(e.trim()):"object"==typeof e&&"_key"in e}let eE=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,e_=/_key\s*==\s*['"](.*)['"]/;function ek(e,t,n){let r="string"==typeof t?function(e){if("string"!=typeof e)throw Error("Path is not a string");let t=e.match(eE);if(!t)throw Error("Invalid path string");return t.map(ej)}(t):t;if(!Array.isArray(r))throw Error("Path must be an array or a string");let i=e;for(let e=0;e<r.length;e++){let t=r[e];if(ew(t)){if(!Array.isArray(i))return n;i=i[t]}if(ex(t)){if(!Array.isArray(i))return n;i=i.find(e=>e._key===t._key)}if("string"==typeof t&&(i="object"==typeof i&&null!==i?i[t]:void 0),typeof i>"u")return n}return i}function ej(e){return ew(e)?Number(e.replace(/[^\d]/g,"")):ex(e)?{_key:e.match(e_)[1]}:!function(e){if("string"==typeof e&&eb.test(e))return!0;if(!Array.isArray(e)||2!==e.length)return!1;let[t,n]=e;return("number"==typeof t||""===t)&&("number"==typeof n||""===n)}(e)?e:function(e){let[t,n]=e.split(":").map(e=>""===e?e:Number(e));return[t,n]}(e)}function eS(e){return e.startsWith(ef)}function eI(e){return eS(e)?e:ef+e}function eO(e){return eS(e)?e.slice(ef.length):e}function eA(){return(0,m.useSyncExternalStore)(e$,eP,eM)}function eM(){return W}function eP(){return V}function e$(e){return q.add(e),()=>q.delete(e)}function eC(e,t){let n;return(...r)=>{clearTimeout(n),n=setTimeout(()=>{e.apply(e,r)},t)}}function eT(e,t){let n=window.self!==window.top||window.opener;if(t===W||!n)throw Error("The `useDocuments` hook cannot be used in this context");let r=eI(e),i=eO(e),o=t.getSnapshot().context?.documents,a=o?.[r],s=o?.[i],l=a||s;if(!l)throw Error(`Document "${e}" not found`);let u=a.getSnapshot().context?.local||s.getSnapshot().context?.local,c=new Promise(e=>{if(u)e(u);else{let t=l.on("ready",n=>{let{snapshot:r}=n;e(r||null),t.unsubscribe()})}});return{draftDoc:a,draftId:r,getSnapshot:()=>c,publishedDoc:s,publishedId:i,get snapshot(){if(!u)throw Error(`Snapshot for document "${e}" not found`);return u}}}function eR(){let e,t,n,r=(0,y.c)(7),i=eA();r[0]!==i?(e=e=>({id:e,commit:()=>{let{draftDoc:t}=eT(e,i);t.send({type:"submit"})},get:t=>{let{snapshot:n}=eT(e,i);return t?ek(n,t):n},getSnapshot:function(e,t){let{getSnapshot:n}=eT(e,t);return n}(e,i),patch:async(t,n)=>{let r=eT(e,i),{draftDoc:o,draftId:a,getSnapshot:s,publishedId:l}=r,{commit:u=!0}=n||{},c=await ("function"==typeof t?t({draftId:a,publishedId:l,get snapshot(){return r.snapshot},getSnapshot:s}):t),d=await s();if(!d)throw Error(`Snapshot for document "${e}" not found`);o.send({type:"mutate",mutations:[{type:"createIfNotExists",document:{...d,_id:a}},{type:"patch",id:a,patches:R(c),...{}}]}),u&&("object"==typeof u&&"debounce"in u?eC(()=>o.send({type:"submit"}),u.debounce)():o.send({type:"submit"}))}}),r[0]=i,r[1]=e):e=r[1];let o=e;r[2]!==i?(t=(e,t,n)=>{let{draftDoc:r}=eT(e,i),{commit:o}=n||{},a=void 0===o||o;r.send({type:"mutate",mutations:t}),a&&("object"==typeof a&&"debounce"in a?eC(()=>r.send({type:"submit"}),a.debounce)():r.send({type:"submit"}))},r[2]=i,r[3]=t):t=r[3];let a=t;return r[4]!==o||r[5]!==a?(n={getDocument:o,mutateDocument:a},r[4]=o,r[5]=a,r[6]=n):n=r[6],n}let eL=(()=>{let e;return()=>{if(e)return e;e=[];for(let t=0;t<256;++t)e[t]=(t+256).toString(16).slice(1);return e}})();function eD(e){let t=eL();return(function(e=16){let t=new Uint8Array(e);return function(e){let t="undefined"!=typeof window&&"crypto"in window?window.crypto:globalThis.crypto;if(!t||!t.getRandomValues)throw Error("WebCrypto not available in this environment");t.getRandomValues(e)}(t),t})(e).reduce((e,n)=>e+t[n],"").slice(0,e)}function eN(e){let t,n,r="string"==typeof e?e:e.path,i=r.lastIndexOf("."),o=r.substring(i+1,r.length);if(!o.indexOf("["))throw Error("Invalid path: not an array");let a=r.lastIndexOf("["),s=r.substring(0,a);if(o.includes("_key")){let e=o.indexOf('"')+1,r=o.indexOf('"',e);t=o.substring(e,r),n=!0}else{let e=o.indexOf("[")+1,r=o.indexOf("]",e);t=o.substring(e,r),n=!1}if(!s||!t)throw Error("Invalid path");return{path:s,key:t,hasExplicitKey:n}}async function eF(e,t,n){if(!e.type)throw Error("Node type is missing");let{path:r,key:i}=eN(e),o=await t.getSnapshot(),a=ek(o,r),s=ek(o,e.path),l=a.findIndex(e=>e._key===i),u=-1,c="before";if("first"===n){if(0===l)return[];u=0,c="before"}else if("last"===n){if(l===a.length-1)return[];u=-1,c="after"}else if("next"===n){if(l===a.length-1)return[];u=l,c="after"}else if("previous"===n){if(0===l)return[];u=l-1,c="before"}return[z(r,N(l,l+1)),z(r,D(s,c,u))]}var ez={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},eU={0:8203,1:8204,2:8205,3:65279},eW=[,,,,].fill(String.fromCodePoint(eU[0])).join(""),eV=Object.fromEntries(Object.entries(eU).map(e=>e.reverse())),eq=Object.fromEntries(Object.entries(ez).map(e=>e.reverse())),eB=`${Object.values(ez).map(e=>`\\u{${e.toString(16)}}`).join("")}`,eG=RegExp(`[${eB}]{4,}`,"gu");let eH=[];for(let e=0;e<256;++e)eH.push((e+256).toString(16).slice(1));let eK=new Uint8Array(16);var eY={randomUUID:"u">typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function eX(e){let t=e.getBoundingClientRect();return{x:t.x+scrollX,y:t.y+scrollY,w:t.width,h:t.height}}function eJ(e,t,n){return"x"===n?{x:e.x+t,y:e.y,w:e.w-2*t,h:e.h}:{x:e.x,y:e.y+t,w:e.w,h:e.h-2*t}}function eZ(e,t){return e.x===t.x&&e.y===t.y&&e.w===t.w&&e.h===t.h}function eQ(e,t){let n=e.x-t.x,r=e.y-t.y;return Math.sqrt(n*n+r*r)}function e0(e,t,n){let r,i,o={x:e.x1,y:e.y1};if(t.some(e=>(function(e,t){let n=e.x>=t.x&&e.x<=t.x+t.w,r=e.y>=t.y&&e.y<=t.y+t.h;return n&&r})(o,eJ(e,Math.min(e.w,e.h)/10,"horizontal"===n?"x":"y"))))return null;for(let a of t){let t=function(e,t){let n=[{x1:t.x,y1:t.y,x2:t.x+t.w,y2:t.y},{x1:t.x+t.w,y1:t.y,x2:t.x+t.w,y2:t.y+t.h},{x1:t.x+t.w,y1:t.y+t.h,x2:t.x,y2:t.y+t.h},{x1:t.x,y1:t.y+t.h,x2:t.x,y2:t.y}],r=[];for(let t=0;t<n.length;t++){let i=function(e,t){let{x1:n,y1:r,x2:i,y2:o}=e,{x1:a,y1:s,x2:l,y2:u}=t;if(n===i&&r===o||a===l&&s===u)return!1;let c=(u-s)*(i-n)-(l-a)*(o-r);if(0===c)return!1;let d=((l-a)*(r-s)-(u-s)*(n-a))/c,f=((i-n)*(r-s)-(o-r)*(n-a))/c;return!(d<0)&&!(d>1)&&!(f<0)&&!(f>1)&&{x:n+d*(i-n),y:r+d*(o-r)}}(e,n[t]);if(i){let e=!1;for(let t=0;t<r.length;t++)r[t].x===i.x&&r[t].y===i.y&&(e=!0);e||r.push(i)}}return 0!==r.length&&r.sort((t,n)=>eQ(t,{x:e.x1,y:e.y1})-eQ(n,{x:e.x1,y:e.y1}))}(e,eJ(a,Math.min(a.w,a.h)/10,"horizontal"===n?"x":"y"));if(t){let e=t[0];r?eQ(o,e)<eQ(o,r)&&(r=e,i=a):(r=e,i=a)}}return i||null}function e1(e,t,n){let{x:r,y:i,w:o,h:a}=e,{x:s,y:l}=n;return{x:s+(r-s)*t,y:l+(i-l)*t,w:o*t,h:a*t}}function e2(e){let t=Math.max(0,Math.min(...e.map(e=>e.y))),n=Math.min(document.body.scrollHeight,Math.max(...e.map(e=>e.y+e.h)));return{min:t,max:n,height:n-t}}function e3(e,t){return t.find(t=>eZ(eX(t.elements.element),e))?.sanity}function e5(e,t,n){return Object.values(t).every(e=>null===e)?null:"horizontal"===n?{left:t.left?{rect:t.left,sanity:e3(t.left,e)}:null,right:t.right?{rect:t.right,sanity:e3(t.right,e)}:null}:{top:t.top?{rect:t.top,sanity:e3(t.top,e)}:null,bottom:t.bottom?{rect:t.bottom,sanity:e3(t.bottom,e)}:null}}function e4(e){let t=document.body.getBoundingClientRect();return{x:Math.max(t.x,Math.min(e.clientX,t.x+t.width)),y:e.clientY+window.scrollY}}function e8(e,t,n){let r=eX(t),i=[...t.querySelectorAll(":where(h1, h2, h3, h4, p, a, img, span, button):not(:has(*))")];e.x<=r.x&&(e.x=r.x),e.x>=r.x+r.w&&(e.x=r.x+r.w),e.y>=r.y+r.h&&(e.y=r.y+r.h),e.y<=r.y&&(e.y=r.y);let o=i.map(e=>{let t=e1(eX(e),n,{x:r.x,y:r.y});return{x:t.x-r.x,y:t.y-r.y,w:t.w,h:t.h,tagName:e.tagName}});return{offsetX:(r.x-e.x)*n,offsetY:(r.y-e.y)*n,w:r.w*n,h:r.h*n,maxWidth:r.w*n*.75,childRects:o}}function e6(e){let t=function(e){let t=Math.max(0,Math.min(...e.map(e=>e.x))),n=Math.min(document.body.offsetWidth,Math.max(...e.map(e=>e.x+e.w)));return{min:t,max:n,width:n-t}}(e),n=e2(e),r=t.min>8&&t.min+t.width<=window.innerWidth-8,i=n.min>8&&n.min+n.height<=document.body.scrollHeight-8,o=r&&i;return{x:o?t.min-8:t.min,y:o?n.min-8:n.min,w:o?t.width+16:t.width,h:o?n.height+16:n.height}}async function e9(e,t,n,r,i,o){return new Promise(a=>{if(1===new DOMMatrix(window.getComputedStyle(t).transform).a)return;let s=n-window.innerHeight,l=scrollY;(e-=window.innerHeight/2)<0&&(e=0),t.addEventListener("transitionend",()=>{t.style.transition="none",t.style.transform="none",scrollTo({top:e,behavior:"instant"}),setTimeout(()=>{r({type:"overlay/dragEndMinimapTransition"}),r({type:"overlay/dragToggleMinimap",display:!1})},2*i),a()},{once:!0}),r({type:"overlay/dragStartMinimapTransition"}),t.style.transform=`translateY(${Math.max(l-e,-s+l)}px) scale(1)`,o&&(document.body.style.overflow=o.body.overflow,document.body.style.height=o.body.height,document.documentElement.style.overflow=o.documentElement.overflow,document.documentElement.style.height=o.documentElement.height)})}let e7=!1,te={x:0,y:0},tt={x:0,y:0},tn=typeof document>"u"?0:document.documentElement.scrollHeight,tr=null,ti=e=>e instanceof HTMLElement||e instanceof SVGElement,to=e=>e&&ti(e)?e.dataset?.sanityOverlayElement?e:to(e.parentElement):null;function ta(e,t=!1){return eG.lastIndex=0,eG.test(e)?function(e,t=!1){try{let n=function(e){let t=e.match(eG);if(t)return function(e,t=!1){let n=Array.from(e);if(n.length%2==0){if(n.length%4||!e.startsWith(eW))return function(e,t){var n;let r=[];for(let t=.5*e.length;t--;){let n=`${eq[e[2*t].codePointAt(0)]}${eq[e[2*t+1].codePointAt(0)]}`;r.unshift(String.fromCharCode(parseInt(n,16)))}let i=[],o=[r.join("")],a=10;for(;o.length;){let e=o.shift();try{if(i.push(JSON.parse(e)),t)break}catch(r){if(!a--)throw r;let t=+(null==(n=r.message.match(/\sposition\s(\d+)$/))?void 0:n[1]);if(!t)throw r;o.unshift(e.substring(0,t),e.substring(t))}}return i}(n,t)}else throw Error("Encoded data has invalid length");let r=[];for(let e=.25*n.length;e--;){let t=n.slice(4*e,4*e+4).map(e=>eV[e.codePointAt(0)]).join("");r.unshift(String.fromCharCode(parseInt(t,4)))}if(t){r.shift();let e=r.indexOf("\0");return -1===e&&(e=r.length),[JSON.parse(r.slice(0,e).join(""))]}return r.join("").split("\0").filter(Boolean).map(e=>JSON.parse(e))}(t[0],!0)[0]}(e);return n&&"sanity.io"===n.origin?(t&&(n.href=n.href?.replace(".alt","")),n):null}catch(t){return console.error("Failed to decode stega for string: ",e,"with the original error: ",t),null}}(e,t):null}let ts=e=>e.nodeType===Node.ELEMENT_NODE,tl=e=>"IMG"===e.tagName,tu=e=>"TIME"===e.tagName,tc=e=>"SVG"===e.tagName.toUpperCase();function td(e){return"path"in e}function tf(e){let t=e.lastIndexOf(".");return e.substring(t,e.length).includes("[")}function tp(e){if(!tf(e))return null;let t=e.split(".");return t[t.length-1]=t[t.length-1].replace(/\[.*?\]/g,"[]"),t.join(".")}function th(e,t){return!(!tf(e.path)||!tf(t.path))&&tp(e.path)===tp(t.path)}function ty(e,t,n,r){if(!e.getAttribute("data-sanity")||e.getAttribute("data-sanity-drag-disable")||!t||!td(t)||!tf(t.path))return null;let i=e.getAttribute("data-sanity-drag-group"),o=[...n].reduce((e,n)=>{let o=r.get(n),a=n.getAttribute("data-sanity-drag-disable"),s=n.getAttribute("data-sanity-drag-group"),l=null!==n.getAttribute("data-sanity");return o&&!a&&td(o.sanity)&&th(t,o.sanity)&&(null===i||i===s)&&l&&e.push(o),e},[]);return o.length<=1?null:o}let tm=(0,m.createContext)(null),tg=e=>"object"==typeof e&&null!=e&&1===e.nodeType,tv=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,tb=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let n=getComputedStyle(e,null);return tv(n.overflowY,t)||tv(n.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},tw=(e,t,n,r,i,o,a,s)=>o<e&&a>t||o>e&&a<t?0:o<=e&&s<=n||a>=t&&s>=n?o-e-r:a>t&&s<n||o<e&&s>n?a-t+i:0,tx=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},tE=(e,t)=>{var n,r,i,o;if("undefined"==typeof document)return[];let{scrollMode:a,block:s,inline:l,boundary:u,skipOverflowHiddenElements:c}=t,d="function"==typeof u?u:e=>e!==u;if(!tg(e))throw TypeError("Invalid target");let f=document.scrollingElement||document.documentElement,p=[],h=e;for(;tg(h)&&d(h);){if((h=tx(h))===f){p.push(h);break}null!=h&&h===document.body&&tb(h)&&!tb(document.documentElement)||null!=h&&tb(h,c)&&p.push(h)}let y=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,m=null!=(o=null==(i=window.visualViewport)?void 0:i.height)?o:innerHeight,{scrollX:g,scrollY:v}=window,{height:b,width:w,top:x,right:E,bottom:_,left:k}=e.getBoundingClientRect(),{top:j,right:S,bottom:I,left:O}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),A="start"===s||"nearest"===s?x-j:"end"===s?_+I:x+b/2-j+I,M="center"===l?k+w/2-O+S:"end"===l?E+S:k-O,P=[];for(let e=0;e<p.length;e++){let t=p[e],{height:n,width:r,top:i,right:o,bottom:u,left:c}=t.getBoundingClientRect();if("if-needed"===a&&x>=0&&k>=0&&_<=m&&E<=y&&x>=i&&_<=u&&k>=c&&E<=o)break;let d=getComputedStyle(t),h=parseInt(d.borderLeftWidth,10),j=parseInt(d.borderTopWidth,10),S=parseInt(d.borderRightWidth,10),I=parseInt(d.borderBottomWidth,10),O=0,$=0,C="offsetWidth"in t?t.offsetWidth-t.clientWidth-h-S:0,T="offsetHeight"in t?t.offsetHeight-t.clientHeight-j-I:0,R="offsetWidth"in t?0===t.offsetWidth?0:r/t.offsetWidth:0,L="offsetHeight"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(f===t)O="start"===s?A:"end"===s?A-m:"nearest"===s?tw(v,v+m,m,j,I,v+A,v+A+b,b):A-m/2,$="start"===l?M:"center"===l?M-y/2:"end"===l?M-y:tw(g,g+y,y,h,S,g+M,g+M+w,w),O=Math.max(0,O+v),$=Math.max(0,$+g);else{O="start"===s?A-i-j:"end"===s?A-u+I+T:"nearest"===s?tw(i,u,n,j,I+T,A,A+b,b):A-(i+n/2)+T/2,$="start"===l?M-c-h:"center"===l?M-(c+r/2)+C/2:"end"===l?M-o+S+C:tw(c,o,r,h,S+C,M,M+w,w);let{scrollLeft:e,scrollTop:a}=t;O=0===L?0:Math.max(0,Math.min(a+O/L,t.scrollHeight-n/L+T)),$=0===R?0:Math.max(0,Math.min(e+$/R,t.scrollWidth-r/R+C)),A+=a-O,M+=e-$}P.push({el:t,top:O,left:$})}return P};var t_=n(26959),tk=n(39249),tj=n(94354),tS=n(73817),tI=(0,n(68066).L)(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),tO=n(575),tA=n(39833),tM=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return(0,tk.C6)(t,e),t.prototype.lift=function(e){var t=new tP(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new tI},t.prototype.next=function(e){var t=this;(0,tA.Y)(function(){var n,r;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var i=(0,tk.Ju)(t.currentObservers),o=i.next();!o.done;o=i.next())o.value.next(e)}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}})},t.prototype.error=function(e){var t=this;(0,tA.Y)(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var n=t.observers;n.length;)n.shift().error(e)}})},t.prototype.complete=function(){var e=this;(0,tA.Y)(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null==(e=this.observers)?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,n=this.hasError,r=this.isStopped,i=this.observers;return n||r?tS.Kn:(this.currentObservers=null,i.push(e),new tS.yU(function(){t.currentObservers=null,(0,tO.o)(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,n=this.thrownError,r=this.isStopped;t?e.error(n):r&&e.complete()},t.prototype.asObservable=function(){var e=new tj.c;return e.source=this,e},t.create=function(e,t){return new tP(e,t)},t}(tj.c),tP=function(e){function t(t,n){var r=e.call(this)||this;return r.destination=t,r.source=n,r}return(0,tk.C6)(t,e),t.prototype.next=function(e){var t,n;null==(n=null==(t=this.destination)?void 0:t.next)||n.call(t,e)},t.prototype.error=function(e){var t,n;null==(n=null==(t=this.destination)?void 0:t.error)||n.call(t,e)},t.prototype.complete=function(){var e,t;null==(t=null==(e=this.destination)?void 0:e.complete)||t.call(e)},t.prototype._subscribe=function(e){var t,n;return null!=(n=null==(t=this.source)?void 0:t.subscribe(e))?n:tS.Kn},t}(tM),t$={now:function(){return(t$.delegate||Date).now()},delegate:void 0},tC=function(e){function t(t,n,r){void 0===t&&(t=1/0),void 0===n&&(n=1/0),void 0===r&&(r=t$);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=n,i._timestampProvider=r,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=n===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,n),i}return(0,tk.C6)(t,e),t.prototype.next=function(t){var n=this.isStopped,r=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,a=this._windowTime;!n&&(r.push(t),i||r.push(o.now()+a)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),n=this._infiniteTimeWindow,r=this._buffer.slice(),i=0;i<r.length&&!e.closed;i+=n?1:2)e.next(r[i]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,n=this._buffer,r=this._infiniteTimeWindow,i=(r?1:2)*e;if(e<1/0&&i<n.length&&n.splice(0,n.length-i),!r){for(var o=t.now(),a=0,s=1;s<n.length&&n[s]<=o;s+=2)a=s;a&&n.splice(0,a+1)}},t}(tM),tT=n(9953),tR=n(78906),tL=n(57173),tD=n(79332),tN=n(88332),tF=n(26269);function tz(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=(0,tN.lI)(t),i=(0,tN.R0)(t,1/0);return t.length?1===t.length?(0,tL.Tg)(t[0]):(void 0===(e=i)&&(e=1/0),(0,tT.Z)(tR.D,e))((0,tF.H)(t,r)):tD.w}function tU(e){let[t,n]=e;return[t,n]}function tW(e,t){let n=e,r=t,i=n.length,o=r.length;if(0===i||0===o)return 0;i>o?n=n.substring(i-o):i<o&&(r=r.substring(0,i));let a=Math.min(i,o);if(n===r)return a;let s=0,l=1;for(let e=0;-1!==e;){let t=n.substring(a-l);if(-1===(e=r.indexOf(t)))break;l+=e,(0===e||n.substring(a-l)===r.substring(0,l))&&(s=l,l++)}return s}function tV(e,t){if(!e||!t||e[0]!==t[0])return 0;let n=0,r=Math.min(e.length,t.length),i=r,o=0;for(;n<i;)e.substring(o,i)===t.substring(o,i)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i}function tq(e,t){if(!e||!t||e[e.length-1]!==t[t.length-1])return 0;let n=0,r=Math.min(e.length,t.length),i=r,o=0;for(;n<i;)e.substring(e.length-i,e.length-o)===t.substring(t.length-i,t.length-o)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i}function tB(e){let t=e.charCodeAt(0);return t>=56320&&t<=57343}function tG(e,t,n,r,i){let o=e.substring(0,n),a=t.substring(0,r),s=e.substring(n),l=t.substring(r),u=t0(o,a,{checkLines:!1,deadline:i}),c=t0(s,l,{checkLines:!1,deadline:i});return u.concat(c)}function tH(e,t,n){let r=e.slice(n,n+Math.floor(e.length/4)),i=-1,o="",a,s,l,u;for(;-1!==(i=t.indexOf(r,i+1));){let r=tV(e.slice(n),t.slice(i)),c=tq(e.slice(0,n),t.slice(0,i));o.length<c+r&&(o=t.slice(i-c,i)+t.slice(i,i+r),a=e.slice(0,n-c),s=e.slice(n+r),l=t.slice(0,i-c),u=t.slice(i+r))}return 2*o.length>=e.length?[a||"",s||"",l||"",u||"",o||""]:null}var tK=Object.defineProperty,tY=Object.getOwnPropertySymbols,tX=Object.prototype.hasOwnProperty,tJ=Object.prototype.propertyIsEnumerable,tZ=(e,t,n)=>t in e?tK(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function tQ(e,t,n){if(null===e||null===t)throw Error("Null input. (diff)");let r=t0(e,t,function(e){let t;var n,r={checkLines:!0,deadline:(n=e.timeout||1,t=1,"u">typeof n&&(t=n<=0?Number.MAX_VALUE:n),Date.now()+1e3*t)},i=e;for(var o in i||(i={}))tX.call(i,o)&&tZ(r,o,i[o]);if(tY)for(var o of tY(i))tJ.call(i,o)&&tZ(r,o,i[o]);return r}(n||{}));return function(e){for(let t=0;t<e.length;t++){let[n,r]=e[t];if(0===r.length)continue;let i=r[0];(function(e){let t=e.charCodeAt(0);return t>=55296&&t<=56319})(r[r.length-1])&&0===n&&t3(e,t,1),tB(i)&&0===n&&t3(e,t,-1)}for(let t=0;t<e.length;t++)0===e[t][1].length&&e.splice(t,1)}(r),r}function t0(e,t,n){let r=e,i=t;if(r===i)return r?[[0,r]]:[];let o=tV(r,i),a=r.substring(0,o);o=tq(r=r.substring(o),i=i.substring(o));let s=r.substring(r.length-o),l=function(e,t,n){let r;if(!e)return[[1,t]];if(!t)return[[-1,e]];let i=e.length>t.length?e:t,o=e.length>t.length?t:e,a=i.indexOf(o);if(-1!==a)return r=[[1,i.substring(0,a)],[0,o],[1,i.substring(a+o.length)]],e.length>t.length&&(r[0][0]=-1,r[2][0]=-1),r;if(1===o.length)return[[-1,e],[1,t]];let s=function(e,t,n=1){let r,i,o,a,s;if(n<=0)return null;let l=e.length>t.length?e:t,u=e.length>t.length?t:e;if(l.length<4||2*u.length<l.length)return null;let c=tH(l,u,Math.ceil(l.length/4)),d=tH(l,u,Math.ceil(l.length/2));if(c&&d)r=c[4].length>d[4].length?c:d;else{if(!c&&!d)return null;d?c||(r=d):r=c}if(!r)throw Error("Unable to find a half match.");return e.length>t.length?(i=r[0],o=r[1],a=r[2],s=r[3]):(a=r[0],s=r[1],i=r[2],o=r[3]),[i,o,a,s,r[4]]}(e,t);if(s){let e=s[0],t=s[1],r=s[2],i=s[3],o=s[4],a=t0(e,r,n),l=t0(t,i,n);return a.concat([[0,o]],l)}return n.checkLines&&e.length>100&&t.length>100?function(e,t,n){let r=e,i=t,o=function(e,t){let n=[],r={};function i(e){let t="",i=0,a=-1,s=n.length;for(;a<e.length-1;){-1===(a=e.indexOf(`
`,i))&&(a=e.length-1);let l=e.slice(i,a+1);(r.hasOwnProperty?r.hasOwnProperty(l):void 0!==r[l])?t+=String.fromCharCode(r[l]):(s===o&&(l=e.slice(i),a=e.length),t+=String.fromCharCode(s),r[l]=s,n[s++]=l),i=a+1}return t}n[0]="";let o=4e4,a=i(e);return o=65535,{chars1:a,chars2:i(t),lineArray:n}}(r,i);r=o.chars1,i=o.chars2;let a=o.lineArray,s=t0(r,i,{checkLines:!1,deadline:n.deadline});(function(e,t){for(let n=0;n<e.length;n++){let r=e[n][1],i=[];for(let e=0;e<r.length;e++)i[e]=t[r.charCodeAt(e)];e[n][1]=i.join("")}})(s,a),(s=t5(s)).push([0,""]);let l=0,u=0,c=0,d="",f="";for(;l<s.length;){switch(s[l][0]){case 1:c++,f+=s[l][1];break;case -1:u++,d+=s[l][1];break;case 0:if(u>=1&&c>=1){s.splice(l-u-c,u+c),l=l-u-c;let e=t0(d,f,{checkLines:!1,deadline:n.deadline});for(let t=e.length-1;t>=0;t--)s.splice(l,0,e[t]);l+=e.length}c=0,u=0,d="",f="";break;default:throw Error("Unknown diff operation.")}l++}return s.pop(),s}(e,t,n):function(e,t,n){let r=e.length,i=t.length,o=Math.ceil((r+i)/2),a=2*o,s=Array(a),l=Array(a);for(let e=0;e<a;e++)s[e]=-1,l[e]=-1;s[o+1]=0,l[o+1]=0;let u=r-i,c=u%2!=0,d=0,f=0,p=0,h=0;for(let y=0;y<o&&!(Date.now()>n);y++){for(let p=-y+d;p<=y-f;p+=2){let h,m=o+p,g=(h=p===-y||p!==y&&s[m-1]<s[m+1]?s[m+1]:s[m-1]+1)-p;for(;h<r&&g<i&&e.charAt(h)===t.charAt(g);)h++,g++;if(s[m]=h,h>r)f+=2;else if(g>i)d+=2;else if(c){let i=o+u-p;if(i>=0&&i<a&&-1!==l[i]&&h>=r-l[i])return tG(e,t,h,g,n)}}for(let d=-y+p;d<=y-h;d+=2){let f,m=o+d,g=(f=d===-y||d!==y&&l[m-1]<l[m+1]?l[m+1]:l[m-1]+1)-d;for(;f<r&&g<i&&e.charAt(r-f-1)===t.charAt(i-g-1);)f++,g++;if(l[m]=f,f>r)h+=2;else if(g>i)p+=2;else if(!c){let i=o+u-d;if(i>=0&&i<a&&-1!==s[i]){let a=s[i],l=o+a-i;if(a>=(f=r-f))return tG(e,t,a,l,n)}}}}return[[-1,e],[1,t]]}(e,t,n.deadline)}(r=r.substring(0,r.length-o),i=i.substring(0,i.length-o),n);return a&&l.unshift([0,a]),s&&l.push([0,s]),l=nt(l)}function t1(e,t,n){return 1===n?e+t:t+e}function t2(e,t){return 1===t?[e.substring(0,e.length-1),e[e.length-1]]:[e.substring(1),e[0]]}function t3(e,t,n){var r,i;let o=1===n?-1:1,a=null,s=null,l=t+n;for(;l>=0&&l<e.length&&(null===a||null===s);l+=n){let[r,i]=e[l];if(0!==i.length){if(1===r){null===a&&(a=l);continue}else if(-1===r){null===s&&(s=l);continue}else if(0===r){if(null===a&&null===s){let[r,i]=t2(e[t][1],n);e[t][1]=r,e[l][1]=t1(e[l][1],i,o);return}break}}}if(null!==a&&null!==s&&(r=a,i=s,1===n?e[r][1][e[r][1].length-1]===e[i][1][e[i][1].length-1]:e[r][1][0]===e[i][1][0])){let[r,i]=t2(e[a][1],o),[l]=t2(e[s][1],o);e[a][1]=r,e[s][1]=l,e[t][1]=t1(e[t][1],i,n);return}let[u,c]=t2(e[t][1],n);e[t][1]=u,null===a?(e.splice(l,0,[1,c]),null!==s&&s>=l&&s++):e[a][1]=t1(e[a][1],c,o),null===s?e.splice(l,0,[-1,c]):e[s][1]=t1(e[s][1],c,o)}function t5(e){let t=e.map(e=>tU(e)),n=!1,r=[],i=0,o=null,a=0,s=0,l=0,u=0,c=0;for(;a<t.length;)0===t[a][0]?(r[i++]=a,s=u,l=c,u=0,c=0,o=t[a][1]):(1===t[a][0]?u+=t[a][1].length:c+=t[a][1].length,o&&o.length<=Math.max(s,l)&&o.length<=Math.max(u,c)&&(t.splice(r[i-1],0,[-1,o]),t[r[i-1]+1][0]=1,i--,a=--i>0?r[i-1]:-1,s=0,l=0,u=0,c=0,o=null,n=!0)),a++;for(n&&(t=nt(t)),t=ne(t),a=1;a<t.length;){if(-1===t[a-1][0]&&1===t[a][0]){let e=t[a-1][1],n=t[a][1],r=tW(e,n),i=tW(n,e);r>=i?(r>=e.length/2||r>=n.length/2)&&(t.splice(a,0,[0,n.substring(0,r)]),t[a-1][1]=e.substring(0,e.length-r),t[a+1][1]=n.substring(r),a++):(i>=e.length/2||i>=n.length/2)&&(t.splice(a,0,[0,e.substring(0,i)]),t[a-1][0]=1,t[a-1][1]=n.substring(0,n.length-i),t[a+1][0]=-1,t[a+1][1]=e.substring(i),a++),a++}a++}return t}let t4=/[^a-zA-Z0-9]/,t8=/\s/,t6=/[\r\n]/,t9=/\n\r?\n$/,t7=/^\r?\n\r?\n/;function ne(e){let t=e.map(e=>tU(e));function n(e,t){if(!e||!t)return 6;let n=e.charAt(e.length-1),r=t.charAt(0),i=n.match(t4),o=r.match(t4),a=i&&n.match(t8),s=o&&r.match(t8),l=a&&n.match(t6),u=s&&r.match(t6),c=l&&e.match(t9),d=u&&t.match(t7);return c||d?5:l||u?4:i&&!a&&s?3:a||s?2:i||o?1:0}let r=1;for(;r<t.length-1;){if(0===t[r-1][0]&&0===t[r+1][0]){let e=t[r-1][1],i=t[r][1],o=t[r+1][1],a=tq(e,i);if(a){let t=i.substring(i.length-a);e=e.substring(0,e.length-a),i=t+i.substring(0,i.length-a),o=t+o}let s=e,l=i,u=o,c=n(e,i)+n(i,o);for(;i.charAt(0)===o.charAt(0);){e+=i.charAt(0),i=i.substring(1)+o.charAt(0),o=o.substring(1);let t=n(e,i)+n(i,o);t>=c&&(c=t,s=e,l=i,u=o)}t[r-1][1]!==s&&(s?t[r-1][1]=s:(t.splice(r-1,1),r--),t[r][1]=l,u?t[r+1][1]=u:(t.splice(r+1,1),r--))}r++}return t}function nt(e){let t=e.map(e=>tU(e));t.push([0,""]);let n=0,r=0,i=0,o="",a="",s;for(;n<t.length;)switch(t[n][0]){case 1:i++,a+=t[n][1],n++;break;case -1:r++,o+=t[n][1],n++;break;case 0:r+i>1?(0!==r&&0!==i&&(0!==(s=tV(a,o))&&(n-r-i>0&&0===t[n-r-i-1][0]?t[n-r-i-1][1]+=a.substring(0,s):(t.splice(0,0,[0,a.substring(0,s)]),n++),a=a.substring(s),o=o.substring(s)),0!==(s=tq(a,o))&&(t[n][1]=a.substring(a.length-s)+t[n][1],a=a.substring(0,a.length-s),o=o.substring(0,o.length-s))),n-=r+i,t.splice(n,r+i),o.length&&(t.splice(n,0,[-1,o]),n++),a.length&&(t.splice(n,0,[1,a]),n++),n++):0!==n&&0===t[n-1][0]?(t[n-1][1]+=t[n][1],t.splice(n,1)):n++,i=0,r=0,o="",a="";break;default:throw Error("Unknown diff operation")}""===t[t.length-1][1]&&t.pop();let l=!1;for(n=1;n<t.length-1;)0===t[n-1][0]&&0===t[n+1][0]&&(t[n][1].substring(t[n][1].length-t[n-1][1].length)===t[n-1][1]?(t[n][1]=t[n-1][1]+t[n][1].substring(0,t[n][1].length-t[n-1][1].length),t[n+1][1]=t[n-1][1]+t[n+1][1],t.splice(n-1,1),l=!0):t[n][1].substring(0,t[n+1][1].length)===t[n+1][1]&&(t[n-1][1]+=t[n+1][1],t[n][1]=t[n][1].substring(t[n+1][1].length)+t[n+1][1],t.splice(n+1,1),l=!0)),n++;return l&&(t=nt(t)),t}var nn=Object.defineProperty,nr=Object.getOwnPropertySymbols,ni=Object.prototype.hasOwnProperty,no=Object.prototype.propertyIsEnumerable,na=(e,t,n)=>t in e?nn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ns=(e,t)=>{for(var n in t||(t={}))ni.call(t,n)&&na(e,n,t[n]);if(nr)for(var n of nr(t))no.call(t,n)&&na(e,n,t[n]);return e};let nl={threshold:.5,distance:1e3};function nu(e,t,n,r={}){if(null===e||null===t||null===n)throw Error("Null input. (match())");let i=Math.max(0,Math.min(n,e.length));return e===t?0:e.length?e.substring(i,i+t.length)===t?i:function(e,t,n,r={}){if(t.length>32)throw Error("Pattern too long for this browser.");let i=ns(ns({},nl),r),o=function(e){let t={};for(let n=0;n<e.length;n++)t[e.charAt(n)]=0;for(let n=0;n<e.length;n++)t[e.charAt(n)]|=1<<e.length-n-1;return t}(t);function a(e,r){let o=e/t.length,a=Math.abs(n-r);return i.distance?o+a/i.distance:a?1:o}let s=i.threshold,l=e.indexOf(t,n);-1!==l&&(s=Math.min(a(0,l),s),-1!==(l=e.lastIndexOf(t,n+t.length))&&(s=Math.min(a(0,l),s)));let u=1<<t.length-1;l=-1;let c,d,f=t.length+e.length,p=[];for(let r=0;r<t.length;r++){for(c=0,d=f;c<d;)a(r,n+d)<=s?c=d:f=d,d=Math.floor((f-c)/2+c);f=d;let i=Math.max(1,n-d+1),h=Math.min(n+d,e.length)+t.length,y=Array(h+2);y[h+1]=(1<<r)-1;for(let t=h;t>=i;t--){let c=o[e.charAt(t-1)];if(0===r?y[t]=(y[t+1]<<1|1)&c:y[t]=(y[t+1]<<1|1)&c|((p[t+1]|p[t])<<1|1)|p[t+1],y[t]&u){let e=a(r,t-1);if(e<=s)if(s=e,(l=t-1)>n)i=Math.max(1,2*n-l);else break}}if(a(r+1,n)>s)break;p=y}return l}(e,t,i,r):-1}function nc(e){let t=[];for(let n=0;n<e.length;n++)1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")}function nd(e){let t=[];for(let n=0;n<e.length;n++)-1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")}function nf(e,t){let n=0,r=0,i=0,o=0,a;for(a=0;a<e.length&&(1!==e[a][0]&&(n+=e[a][1].length),-1!==e[a][0]&&(r+=e[a][1].length),!(n>t));a++)i=n,o=r;return e.length!==a&&-1===e[a][0]?o:o+(t-i)}function np(e){let t=0;for(let n=0;n<e.length;n++){let r=e.codePointAt(n);if(typeof r>"u")throw Error("Failed to get codepoint");t+=nh(r)}return t}function nh(e){return e<=127?1:e<=2047?2:e<=65535?3:4}function ny(e,t){return{diffs:[],start1:e,start2:t,utf8Start1:e,utf8Start2:t,length1:0,length2:0,utf8Length1:0,utf8Length2:0}}var nm=Object.defineProperty,ng=Object.getOwnPropertySymbols,nv=Object.prototype.hasOwnProperty,nb=Object.prototype.propertyIsEnumerable,nw=(e,t,n)=>t in e?nm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,nx=(e,t)=>{for(var n in t||(t={}))nv.call(t,n)&&nw(e,n,t[n]);if(ng)for(var n of ng(t))nb.call(t,n)&&nw(e,n,t[n]);return e};let nE={margin:4};function n_(e={}){return nx(nx({},nE),e)}function nk(e,t,n){if(0===t.length)return[];let r=[],i=ny(0,0),o=0,a=0,s=0,l=0,u=0,c=e,d=e;for(let e=0;e<t.length;e++){let f=t[e],[p,h]=f,y=h.length,m=np(h);switch(!o&&0!==p&&(i.start1=a,i.start2=s,i.utf8Start1=l,i.utf8Start2=u),p){case 1:i.diffs[o++]=f,i.length2+=y,i.utf8Length2+=m,d=d.substring(0,s)+h+d.substring(s);break;case -1:i.length1+=y,i.utf8Length1+=m,i.diffs[o++]=f,d=d.substring(0,s)+d.substring(s+y);break;case 0:y<=2*n.margin&&o&&t.length!==e+1?(i.diffs[o++]=f,i.length1+=y,i.length2+=y,i.utf8Length1+=m,i.utf8Length2+=m):y>=2*n.margin&&o&&(nj(i,c,n),r.push(i),i=ny(-1,-1),o=0,c=d,a=s,l=u);break;default:throw Error("Unknown diff type")}1!==p&&(a+=y,l+=m),-1!==p&&(s+=y,u+=m)}return o&&(nj(i,c,n),r.push(i)),r}function nj(e,t,n){if(0===t.length)return;let r=t.substring(e.start2,e.start2+e.length1),i=0;for(;t.indexOf(r)!==t.lastIndexOf(r)&&r.length<32-n.margin-n.margin;)i+=n.margin,r=t.substring(e.start2-i,e.start2+e.length1+i);i+=n.margin;let o=e.start2-i;o>=1&&tB(t[o])&&o--;let a=t.substring(o,e.start2);a&&e.diffs.unshift([0,a]);let s=a.length,l=np(a),u=e.start2+e.length1+i;u<t.length&&tB(t[u])&&u++;let c=t.substring(e.start2+e.length1,u);c&&e.diffs.push([0,c]);let d=c.length,f=np(c);e.start1-=s,e.start2-=s,e.utf8Start1-=l,e.utf8Start2-=l,e.length1+=s+d,e.length2+=s+d,e.utf8Length1+=l+f,e.utf8Length2+=l+f}let nS=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;function nI(e){return parseInt(e,10)}function nO(e){let t,n,r,{utf8Length1:i,utf8Length2:o,utf8Start1:a,utf8Start2:s,diffs:l}=e;t=0===i?`${a},0`:1===i?`${a+1}`:`${a+1},${i}`,n=0===o?`${s},0`:1===o?`${s+1}`:`${s+1},${o}`;let u=[`@@ -${t} +${n} @@
`];for(let e=0;e<l.length;e++){switch(l[e][0]){case 1:r="+";break;case -1:r="-";break;case 0:r=" ";break;default:throw Error("Unknown patch operation.")}u[e+1]=`${r+encodeURI(l[e][1])}
`}return u.join("").replace(/%20/g," ")}var nA="object"==typeof global&&global&&global.Object===Object&&global,nM="object"==typeof self&&self&&self.Object===Object&&self,nP=nA||nM||Function("return this")(),n$=nP.Symbol,nC=Object.prototype,nT=nC.hasOwnProperty,nR=nC.toString,nL=n$?n$.toStringTag:void 0;let nD=function(e){var t=nT.call(e,nL),n=e[nL];try{e[nL]=void 0;var r=!0}catch(e){}var i=nR.call(e);return r&&(t?e[nL]=n:delete e[nL]),i};var nN=Object.prototype.toString,nF=n$?n$.toStringTag:void 0;let nz=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":nF&&nF in Object(e)?nD(e):nN.call(e)},nU=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},nW=function(e){if(!nU(e))return!1;var t=nz(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};var nV=nP["__core-js_shared__"],nq=function(){var e=/[^.]+$/.exec(nV&&nV.keys&&nV.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),nB=Function.prototype.toString;let nG=function(e){if(null!=e){try{return nB.call(e)}catch(e){}try{return e+""}catch(e){}}return""};var nH=/^\[object .+?Constructor\]$/,nK=Object.prototype,nY=Function.prototype.toString,nX=nK.hasOwnProperty,nJ=RegExp("^"+nY.call(nX).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");let nZ=function(e){return!!nU(e)&&(!nq||!(nq in e))&&(nW(e)?nJ:nH).test(nG(e))},nQ=function(e,t){var n=null==e?void 0:e[t];return nZ(n)?n:void 0};var n0=function(){try{var e=nQ(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();let n1=function(e,t,n){"__proto__"==t&&n0?n0(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},n2=function(e,t,n,r){for(var i=-1,o=null==e?0:e.length;++i<o;){var a=e[i];t(r,a,n(a),e)}return r},n3=function(e,t,n){for(var r=-1,i=Object(e),o=n(e),a=o.length;a--;){var s=o[++r];if(!1===t(i[s],s,i))break}return e},n5=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},n4=function(e){return null!=e&&"object"==typeof e},n8=function(e){return n4(e)&&"[object Arguments]"==nz(e)};var n6=Object.prototype,n9=n6.hasOwnProperty,n7=n6.propertyIsEnumerable,re=n8(function(){return arguments}())?n8:function(e){return n4(e)&&n9.call(e,"callee")&&!n7.call(e,"callee")},rt=Array.isArray,rn="object"==typeof exports&&exports&&!exports.nodeType&&exports,rr=rn&&"object"==typeof module&&module&&!module.nodeType&&module,ri=rr&&rr.exports===rn?nP.Buffer:void 0;let ro=(ri?ri.isBuffer:void 0)||function(){return!1};var ra=/^(?:0|[1-9]\d*)$/;let rs=function(e,t){var n=typeof e;return!!(t=null==t?0x1fffffffffffff:t)&&("number"==n||"symbol"!=n&&ra.test(e))&&e>-1&&e%1==0&&e<t},rl=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff};var ru={};ru["[object Float32Array]"]=ru["[object Float64Array]"]=ru["[object Int8Array]"]=ru["[object Int16Array]"]=ru["[object Int32Array]"]=ru["[object Uint8Array]"]=ru["[object Uint8ClampedArray]"]=ru["[object Uint16Array]"]=ru["[object Uint32Array]"]=!0,ru["[object Arguments]"]=ru["[object Array]"]=ru["[object ArrayBuffer]"]=ru["[object Boolean]"]=ru["[object DataView]"]=ru["[object Date]"]=ru["[object Error]"]=ru["[object Function]"]=ru["[object Map]"]=ru["[object Number]"]=ru["[object Object]"]=ru["[object RegExp]"]=ru["[object Set]"]=ru["[object String]"]=ru["[object WeakMap]"]=!1;var rc="object"==typeof exports&&exports&&!exports.nodeType&&exports,rd=rc&&"object"==typeof module&&module&&!module.nodeType&&module,rf=rd&&rd.exports===rc&&nA.process,rp=function(){try{var e=rd&&rd.require&&rd.require("util").types;if(e)return e;return rf&&rf.binding&&rf.binding("util")}catch(e){}}(),rh=rp&&rp.isTypedArray,ry=rh?function(e){return rh(e)}:function(e){return n4(e)&&rl(e.length)&&!!ru[nz(e)]},rm=Object.prototype.hasOwnProperty;let rg=function(e,t){var n=rt(e),r=!n&&re(e),i=!n&&!r&&ro(e),o=!n&&!r&&!i&&ry(e),a=n||r||i||o,s=a?n5(e.length,String):[],l=s.length;for(var u in e)(t||rm.call(e,u))&&!(a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||rs(u,l)))&&s.push(u);return s};var rv=Object.prototype;let rb=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||rv)};var rw=(a=Object.keys,s=Object,function(e){return a(s(e))}),rx=Object.prototype.hasOwnProperty;let rE=function(e){if(!rb(e))return rw(e);var t=[];for(var n in Object(e))rx.call(e,n)&&"constructor"!=n&&t.push(n);return t},r_=function(e){return null!=e&&rl(e.length)&&!nW(e)},rk=function(e){return r_(e)?rg(e):rE(e)};var rj=(i=function(e,t){return e&&n3(e,t,rk)},function(e,t){if(null==e)return e;if(!r_(e))return i(e,t);for(var n=e.length,r=-1,o=Object(e);++r<n&&!1!==t(o[r],r,o););return e});let rS=function(e,t,n,r){return rj(e,function(e,i,o){t(r,e,n(e),o)}),r},rI=function(e,t){return e===t||e!=e&&t!=t},rO=function(e,t){for(var n=e.length;n--;)if(rI(e[n][0],t))return n;return -1};var rA=Array.prototype.splice;function rM(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}rM.prototype.clear=function(){this.__data__=[],this.size=0},rM.prototype.delete=function(e){var t=this.__data__,n=rO(t,e);return!(n<0)&&(n==t.length-1?t.pop():rA.call(t,n,1),--this.size,!0)},rM.prototype.get=function(e){var t=this.__data__,n=rO(t,e);return n<0?void 0:t[n][1]},rM.prototype.has=function(e){return rO(this.__data__,e)>-1},rM.prototype.set=function(e,t){var n=this.__data__,r=rO(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};var rP=nQ(nP,"Map"),r$=nQ(Object,"create"),rC=Object.prototype.hasOwnProperty,rT=Object.prototype.hasOwnProperty;function rR(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}rR.prototype.clear=function(){this.__data__=r$?r$(null):{},this.size=0},rR.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=!!t,t},rR.prototype.get=function(e){var t=this.__data__;if(r$){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return rC.call(t,e)?t[e]:void 0},rR.prototype.has=function(e){var t=this.__data__;return r$?void 0!==t[e]:rT.call(t,e)},rR.prototype.set=function(e,t){var n=this.__data__;return this.size+=+!this.has(e),n[e]=r$&&void 0===t?"__lodash_hash_undefined__":t,this};let rL=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e},rD=function(e,t){var n=e.__data__;return rL(t)?n["string"==typeof t?"string":"hash"]:n.map};function rN(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function rF(e){var t=this.__data__=new rM(e);this.size=t.size}function rz(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new rN;++t<n;)this.add(e[t])}rN.prototype.clear=function(){this.size=0,this.__data__={hash:new rR,map:new(rP||rM),string:new rR}},rN.prototype.delete=function(e){var t=rD(this,e).delete(e);return this.size-=!!t,t},rN.prototype.get=function(e){return rD(this,e).get(e)},rN.prototype.has=function(e){return rD(this,e).has(e)},rN.prototype.set=function(e,t){var n=rD(this,e),r=n.size;return n.set(e,t),this.size+=+(n.size!=r),this},rF.prototype.clear=function(){this.__data__=new rM,this.size=0},rF.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},rF.prototype.get=function(e){return this.__data__.get(e)},rF.prototype.has=function(e){return this.__data__.has(e)},rF.prototype.set=function(e,t){var n=this.__data__;if(n instanceof rM){var r=n.__data__;if(!rP||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new rN(r)}return n.set(e,t),this.size=n.size,this},rz.prototype.add=rz.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},rz.prototype.has=function(e){return this.__data__.has(e)};let rU=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1},rW=function(e,t,n,r,i,o){var a=1&n,s=e.length,l=t.length;if(s!=l&&!(a&&l>s))return!1;var u=o.get(e),c=o.get(t);if(u&&c)return u==t&&c==e;var d=-1,f=!0,p=2&n?new rz:void 0;for(o.set(e,t),o.set(t,e);++d<s;){var h=e[d],y=t[d];if(r)var m=a?r(y,h,d,t,e,o):r(h,y,d,e,t,o);if(void 0!==m){if(m)continue;f=!1;break}if(p){if(!rU(t,function(e,t){if(!p.has(t)&&(h===e||i(h,e,n,r,o)))return p.push(t)})){f=!1;break}}else if(!(h===y||i(h,y,n,r,o))){f=!1;break}}return o.delete(e),o.delete(t),f};var rV=nP.Uint8Array;let rq=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n},rB=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n};var rG=n$?n$.prototype:void 0,rH=rG?rG.valueOf:void 0;let rK=function(e,t,n,r,i,o,a){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!o(new rV(e),new rV(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return rI(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=rq;case"[object Set]":var l=1&r;if(s||(s=rB),e.size!=t.size&&!l)break;var u=a.get(e);if(u)return u==t;r|=2,a.set(e,t);var c=rW(s(e),s(t),r,i,o,a);return a.delete(e),c;case"[object Symbol]":if(rH)return rH.call(e)==rH.call(t)}return!1},rY=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e},rX=function(e,t,n){var r=t(e);return rt(e)?r:rY(r,n(e))},rJ=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var a=e[n];t(a,n,e)&&(o[i++]=a)}return o};var rZ=Object.prototype.propertyIsEnumerable,rQ=Object.getOwnPropertySymbols,r0=rQ?function(e){return null==e?[]:rJ(rQ(e=Object(e)),function(t){return rZ.call(e,t)})}:function(){return[]};let r1=function(e){return rX(e,rk,r0)};var r2=Object.prototype.hasOwnProperty;let r3=function(e,t,n,r,i,o){var a=1&n,s=r1(e),l=s.length;if(l!=r1(t).length&&!a)return!1;for(var u=l;u--;){var c=s[u];if(!(a?c in t:r2.call(t,c)))return!1}var d=o.get(e),f=o.get(t);if(d&&f)return d==t&&f==e;var p=!0;o.set(e,t),o.set(t,e);for(var h=a;++u<l;){var y=e[c=s[u]],m=t[c];if(r)var g=a?r(m,y,c,t,e,o):r(y,m,c,e,t,o);if(!(void 0===g?y===m||i(y,m,n,r,o):g)){p=!1;break}h||(h="constructor"==c)}if(p&&!h){var v=e.constructor,b=t.constructor;v!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b)&&(p=!1)}return o.delete(e),o.delete(t),p};var r5=nQ(nP,"DataView"),r4=nQ(nP,"Promise"),r8=nQ(nP,"Set"),r6=nQ(nP,"WeakMap"),r9="[object Map]",r7="[object Promise]",ie="[object Set]",it="[object WeakMap]",ir="[object DataView]",ii=nG(r5),io=nG(rP),ia=nG(r4),is=nG(r8),il=nG(r6),iu=nz;(r5&&iu(new r5(new ArrayBuffer(1)))!=ir||rP&&iu(new rP)!=r9||r4&&iu(r4.resolve())!=r7||r8&&iu(new r8)!=ie||r6&&iu(new r6)!=it)&&(iu=function(e){var t=nz(e),n="[object Object]"==t?e.constructor:void 0,r=n?nG(n):"";if(r)switch(r){case ii:return ir;case io:return r9;case ia:return r7;case is:return ie;case il:return it}return t});let ic=iu;var id="[object Arguments]",ip="[object Array]",ih="[object Object]",iy=Object.prototype.hasOwnProperty;let im=function(e,t,n,r,i,o){var a=rt(e),s=rt(t),l=a?ip:ic(e),u=s?ip:ic(t);l=l==id?ih:l,u=u==id?ih:u;var c=l==ih,d=u==ih,f=l==u;if(f&&ro(e)){if(!ro(t))return!1;a=!0,c=!1}if(f&&!c)return o||(o=new rF),a||ry(e)?rW(e,t,n,r,i,o):rK(e,t,l,n,r,i,o);if(!(1&n)){var p=c&&iy.call(e,"__wrapped__"),h=d&&iy.call(t,"__wrapped__");if(p||h){var y=p?e.value():e,m=h?t.value():t;return o||(o=new rF),i(y,m,n,r,o)}}return!!f&&(o||(o=new rF),r3(e,t,n,r,i,o))},ig=function e(t,n,r,i,o){return t===n||(null!=t&&null!=n&&(n4(t)||n4(n))?im(t,n,r,i,e,o):t!=t&&n!=n)},iv=function(e,t,n,r){var i=n.length,o=i,a=!r;if(null==e)return!o;for(e=Object(e);i--;){var s=n[i];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var l=(s=n[i])[0],u=e[l],c=s[1];if(a&&s[2]){if(void 0===u&&!(l in e))return!1}else{var d=new rF;if(r)var f=r(u,c,l,e,t,d);if(!(void 0===f?ig(c,u,3,r,d):f))return!1}}return!0},ib=function(e){return e==e&&!nU(e)},iw=function(e){for(var t=rk(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,ib(i)]}return t},ix=function(e,t){return function(n){return null!=n&&n[e]===t&&(void 0!==t||e in Object(n))}},iE=function(e){var t=iw(e);return 1==t.length&&t[0][2]?ix(t[0][0],t[0][1]):function(n){return n===e||iv(n,e,t)}},i_=function(e){return"symbol"==typeof e||n4(e)&&"[object Symbol]"==nz(e)};var ik=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,ij=/^\w*$/;let iS=function(e,t){if(rt(e))return!1;var n=typeof e;return!!("number"==n||"symbol"==n||"boolean"==n||null==e||i_(e))||ij.test(e)||!ik.test(e)||null!=t&&e in Object(t)};function iI(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(iI.Cache||rN),n}iI.Cache=rN;var iO=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,iA=/\\(\\)?/g,iM=function(e){var t=iI(e,function(e){return 500===n.size&&n.clear(),e}),n=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(iO,function(e,n,r,i){t.push(r?i.replace(iA,"$1"):n||e)}),t});let iP=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i};var i$=1/0,iC=n$?n$.prototype:void 0,iT=iC?iC.toString:void 0;let iR=function e(t){if("string"==typeof t)return t;if(rt(t))return iP(t,e)+"";if(i_(t))return iT?iT.call(t):"";var n=t+"";return"0"==n&&1/t==-i$?"-0":n},iL=function(e,t){return rt(e)?e:iS(e,t)?[e]:iM(null==e?"":iR(e))};var iD=1/0;let iN=function(e){if("string"==typeof e||i_(e))return e;var t=e+"";return"0"==t&&1/e==-iD?"-0":t},iF=function(e,t){t=iL(t,e);for(var n=0,r=t.length;null!=e&&n<r;)e=e[iN(t[n++])];return n&&n==r?e:void 0},iz=function(e,t,n){var r=null==e?void 0:iF(e,t);return void 0===r?n:r},iU=function(e,t){return null!=e&&t in Object(e)},iW=function(e,t,n){t=iL(t,e);for(var r=-1,i=t.length,o=!1;++r<i;){var a=iN(t[r]);if(!(o=null!=e&&n(e,a)))break;e=e[a]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&rl(i)&&rs(a,i)&&(rt(e)||re(e))},iV=function(e){return e},iq=function(e){var t;return iS(e)?(t=iN(e),function(e){return null==e?void 0:e[t]}):function(t){return iF(t,e)}},iB=function(e){if("function"==typeof e)return e;if(null==e)return iV;if("object"==typeof e){var t,n;return rt(e)?(t=e[0],n=e[1],iS(t)&&ib(n)?ix(iN(t),n):function(e){var r=iz(e,t);return void 0===r&&r===n?null!=e&&iW(e,t,iU):ig(n,r,3)}):iE(e)}return iq(e)};var iG=Object.prototype.hasOwnProperty,iH=(o=function(e,t,n){iG.call(e,n)?e[n].push(t):n1(e,n,[t])},function(e,t){return(rt(e)?n2:rS)(e,o,iB(t,2),{})});let iK=["Value","Copy","Blank","ReturnIntoArray","ReturnIntoObject","ReturnIntoObjectSameKey","PushField","PushElement","PushParent","Pop","PushFieldCopy","PushFieldBlank","PushElementCopy","PushElementBlank","ReturnIntoObjectPop","ReturnIntoObjectSameKeyPop","ReturnIntoArrayPop","ObjectSetFieldValue","ObjectCopyField","ObjectDeleteField","ArrayAppendValue","ArrayAppendSlice","StringAppendString","StringAppendSlice"];class iY{model;root;patch;i;inputStack;outputStack;constructor(e,t,n){this.model=e,this.root=t,this.patch=n,this.i=0,this.inputStack=[],this.outputStack=[]}read(){return this.patch[this.i++]}process(){for(this.inputStack.push({value:this.root}),this.outputStack.push({value:this.root});this.i<this.patch.length;){let e=this.read(),t=iK[e];if(!t)throw Error(`Unknown opcode: ${e}`);this[`process${t}`].apply(this)}let e=this.outputStack.pop();return this.finalizeOutput(e)}inputEntry(){return this.inputStack[this.inputStack.length-1]}inputKey(e,t){return e.keys||(e.keys=this.model.objectGetKeys(e.value).sort()),e.keys[t]}outputEntry(){return this.outputStack[this.outputStack.length-1]}outputArray(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyArray(e.value)),e.writeValue}outputObject(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyObject(e.value)),e.writeValue}outputString(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyString(e.value)),e.writeValue}finalizeOutput(e){return e.writeValue?this.model.finalize(e.writeValue):e.value}processValue(){let e=this.model.wrap(this.read());this.outputStack.push({value:e})}processCopy(){let e=this.inputEntry();this.outputStack.push({value:e.value})}processBlank(){this.outputStack.push({value:null})}processReturnIntoArray(){let e=this.outputStack.pop(),t=this.finalizeOutput(e),n=this.outputArray();this.model.arrayAppendValue(n,t)}processReturnIntoObject(){let e=this.read(),t=this.outputStack.pop(),n=this.finalizeOutput(t);n=this.model.markChanged(n);let r=this.outputObject();this.model.objectSetField(r,e,n)}processReturnIntoObjectSameKey(){let e=this.inputEntry(),t=this.outputStack.pop(),n=this.finalizeOutput(t),r=this.outputObject();this.model.objectSetField(r,e.key,n)}processPushField(){let e=this.read(),t=this.inputEntry(),n=this.inputKey(t,e),r=this.model.objectGetField(t.value,n);this.inputStack.push({value:r,key:n})}processPushElement(){let e=this.read(),t=this.inputEntry(),n=this.model.arrayGetElement(t.value,e);this.inputStack.push({value:n})}processPop(){this.inputStack.pop()}processPushFieldCopy(){this.processPushField(),this.processCopy()}processPushFieldBlank(){this.processPushField(),this.processBlank()}processPushElementCopy(){this.processPushElement(),this.processCopy()}processPushElementBlank(){this.processPushElement(),this.processBlank()}processReturnIntoObjectPop(){this.processReturnIntoObject(),this.processPop()}processReturnIntoObjectSameKeyPop(){this.processReturnIntoObjectSameKey(),this.processPop()}processReturnIntoArrayPop(){this.processReturnIntoArray(),this.processPop()}processObjectSetFieldValue(){this.processValue(),this.processReturnIntoObject()}processObjectCopyField(){this.processPushField(),this.processCopy(),this.processReturnIntoObjectSameKey(),this.processPop()}processObjectDeleteField(){let e=this.read(),t=this.inputEntry(),n=this.inputKey(t,e),r=this.outputObject();this.model.objectDeleteField(r,n)}processArrayAppendValue(){let e=this.model.wrap(this.read()),t=this.outputArray();this.model.arrayAppendValue(t,e)}processArrayAppendSlice(){let e=this.read(),t=this.read(),n=this.outputArray(),r=this.inputEntry().value;this.model.arrayAppendSlice(n,r,e,t)}processStringAppendString(){let e=this.model.wrap(this.read()),t=this.outputString();this.model.stringAppendValue(t,e)}processStringAppendSlice(){let e=this.read(),t=this.read(),n=this.outputString(),r=this.inputEntry().value;this.model.stringAppendSlice(n,r,e,t)}}function iX(e){return e>>16?4:e>>11?3:e>>7?2:1}function iJ(e,t,n=0){let r=n,i=0;for(i=n;r<t;i++){let t=iX(e.codePointAt(i));4===t&&i++,r+=t}return i}class iZ{wrap(e){return e}finalize(e){return Array.isArray(e)?e:e.data}markChanged(e){return e}objectGetKeys(e){return Object.keys(e)}objectGetField(e,t){return e[t]}arrayGetElement(e,t){return e[t]}copyObject(e){let t={type:"object",data:{}};if(null!==e)for(let[n,r]of Object.entries(e))t.data[n]=r;return t}copyArray(e){return null===e?[]:e.slice()}copyString(e){return{type:"string",data:null===e?"":e}}objectSetField(e,t,n){e.data[t]=n}objectDeleteField(e,t){delete e.data[t]}arrayAppendValue(e,t){e.push(t)}arrayAppendSlice(e,t,n,r){e.push(...t.slice(n,r))}stringAppendSlice(e,t,n,r){let i=iJ(t,n),o=iJ(t,r,i);e.data+=t.slice(i,o)}stringAppendValue(e,t){e.data+=t}}var iQ=n(27110),i0=n(1125);function i1(e){void 0===e&&(e={});var t=e.connector,n=void 0===t?function(){return new tM}:t,r=e.resetOnError,i=void 0===r||r,o=e.resetOnComplete,a=void 0===o||o,s=e.resetOnRefCountZero,l=void 0===s||s;return function(e){var t,r,o,s=0,u=!1,c=!1,d=function(){null==r||r.unsubscribe(),r=void 0},f=function(){d(),t=o=void 0,u=c=!1},p=function(){var e=t;f(),null==e||e.unsubscribe()};return(0,i0.N)(function(e,h){s++,c||u||d();var y=o=null!=o?o:n();h.add(function(){0!=--s||c||u||(r=i2(p,l))}),y.subscribe(h),!t&&s>0&&(t=new iQ.Ms({next:function(e){return y.next(e)},error:function(e){c=!0,d(),r=i2(f,i,e),y.error(e)},complete:function(){u=!0,d(),r=i2(f,a),y.complete()}}),(0,tL.Tg)(e).subscribe(t))})(e)}}function i2(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];if(!0===t)return void e();if(!1!==t){var i=new iQ.Ms({next:function(){i.unsubscribe(),e()}});return(0,tL.Tg)(t.apply(void 0,(0,tk.fX)([],(0,tk.zs)(n)))).subscribe(i)}}var i3=n(69647),i5=n(62789),i4=n(36270),i8=function(e){function t(t,n){return e.call(this)||this}return(0,tk.C6)(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(tS.yU),i6={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=i6.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,(0,tk.fX)([e,t],(0,tk.zs)(n))):setInterval.apply(void 0,(0,tk.fX)([e,t],(0,tk.zs)(n)))},clearInterval:function(e){var t=i6.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},i9=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r.pending=!1,r}return(0,tk.C6)(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var n,r=this.id,i=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(i,r,t)),this.pending=!0,this.delay=t,this.id=null!=(n=this.id)?n:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,n){return void 0===n&&(n=0),i6.setInterval(e.flush.bind(e,this),n)},t.prototype.recycleAsyncId=function(e,t,n){if(void 0===n&&(n=0),null!=n&&this.delay===n&&!1===this.pending)return t;null!=t&&i6.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var n=this._execute(e,t);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var n,r=!1;try{this.work(e)}catch(e){r=!0,n=e||Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),n},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,n=this.scheduler,r=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,(0,tO.o)(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(i8),i7=1,oe={};function ot(e){return e in oe&&(delete oe[e],!0)}var on={setImmediate:function(e){var t=i7++;return oe[t]=!0,f||(f=Promise.resolve()),f.then(function(){return ot(t)&&e()}),t},clearImmediate:function(e){ot(e)}},or=on.setImmediate,oi=on.clearImmediate,oo={setImmediate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=oo.delegate;return((null==n?void 0:n.setImmediate)||or).apply(void 0,(0,tk.fX)([],(0,tk.zs)(e)))},clearImmediate:function(e){var t=oo.delegate;return((null==t?void 0:t.clearImmediate)||oi)(e)},delegate:void 0},oa=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r}return(0,tk.C6)(t,e),t.prototype.requestAsyncId=function(t,n,r){return(void 0===r&&(r=0),null!==r&&r>0)?e.prototype.requestAsyncId.call(this,t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=oo.setImmediate(t.flush.bind(t,void 0))))},t.prototype.recycleAsyncId=function(t,n,r){if(void 0===r&&(r=0),null!=r?r>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,n,r);var i,o=t.actions;null!=n&&(null==(i=o[o.length-1])?void 0:i.id)!==n&&(oo.clearImmediate(n),t._scheduled===n&&(t._scheduled=void 0))},t}(i9),os=function(){function e(t,n){void 0===n&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(e,t,n){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(n,t)},e.now=t$.now,e}(),ol=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,tk.C6)(t,e),t.prototype.flush=function(e){this._active=!0;var t,n=this._scheduled;this._scheduled=void 0;var r=this.actions;e=e||r.shift();do if(t=e.execute(e.state,e.delay))break;while((e=r[0])&&e.id===n&&r.shift());if(this._active=!1,t){for(;(e=r[0])&&e.id===n&&r.shift();)e.unsubscribe();throw t}},t}(function(e){function t(t,n){void 0===n&&(n=os.now);var r=e.call(this,t,n)||this;return r.actions=[],r._active=!1,r}return(0,tk.C6)(t,e),t.prototype.flush=function(e){var t,n=this.actions;if(this._active)return void n.push(e);this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,t){for(;e=n.shift();)e.unsubscribe();throw t}},t}(os)))(oa),ou=n(89632),oc=n(40647),od=n(69620);function of(e){if("patch"===e.type)return e.id;if("create"===e.type)return e.document._id;if("delete"===e.type)return e.id;if("createIfNotExists"===e.type||"createOrReplace"===e.type)return e.document._id;throw Error("Invalid mutation type")}let op=(e=21)=>{let t="",n=crypto.getRandomValues(new Uint8Array(e));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&n[e]];return t};function oh(e){return"string"==typeof e?._key}function oy(e){return"object"==typeof e&&"_key"in e&&"string"==typeof e._key}function om(e){return"number"==typeof e||oy(e)}function og(e,t){if(0===e.length)return t;let n=t;for(let t of e){if(om(t)){if(!Array.isArray(n))return;if(oy(t)){n=n.find(e=>e._key===t._key);continue}n=n[t];continue}n=n[t]}return n}let ov=/^[a-z_$]+/;function ob(e){return e.map((e,t)=>{var n;return n=0===t,Array.isArray(e)?`[${e[0]}:${e[1]||""}]`:"number"==typeof e?`[${e}]`:oy(e)?`[_key==${JSON.stringify(e._key)}]`:"string"==typeof e&&ov.test(e)?n?e:`.${e}`:`['${e}']`}).join("")}function ow(e){return null!==e&&"object"==typeof e&&!Array.isArray(e)}function ox(e,t){if("number"==typeof t){var n=e.length,r=t;if(0===n&&(-1===r||0===r))return 0;let i=r<0?n+r:r;return i>=n||i<0?null:i}if(oy(t)){let n=e.findIndex(e=>(null!==e&&"object"==typeof e&&"string"==typeof e._key&&e._key||null)===t._key);return -1===n?null:n}throw Error(`Expected path segment to be addressing a single array item either by numeric index or by '_key'. Instead saw ${JSON.stringify(t)}`)}function oE(e,t,n,r){let i=e.slice();return i.splice(t,n,...r||[]),i}function o_(e,t){var n;if(!Array.isArray(t))throw TypeError('Cannot apply "insert()" on non-array value');let r=ox(t,e.referenceItem);if(null===r)throw Error(`Found no matching array element to insert ${e.position}`);return 0===t.length?e.items:oE(t,(n=e.position,"before"===n?r:r+1),0,e.items)}let ok=Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);var oj=Object.freeze({__proto__:null,assign:function(e,t){if(!ow(t))throw TypeError('Cannot apply "assign()" on non-object value');return!function(e){for(let t in e)if(ok(e,t))return!1;return!0}(e.value)?{...t,...e.value}:t},dec:function(e,t){if("number"!=typeof t)throw TypeError('Cannot apply "dec()" on non-numeric value');return t-e.amount},diffMatchPatch:function(e,t){if("string"!=typeof t)throw TypeError('Cannot apply "diffMatchPatch()" on non-string value');return function(e,t,n={}){if("string"==typeof e)throw Error("Patches must be an array - pass the patch to `parsePatch()` first");let r=t;if(0===e.length)return[r,[]];let i=function(e,t,n={}){let r=0,i=0;function o(e){for(;r<e;){let e=t.codePointAt(i);if(typeof e>"u")return i;r+=nh(e),e>65535?i+=2:i+=1}if(!n.allowExceedingIndices&&r!==e)throw Error("Failed to determine byte offset");return i}let a=[];for(let t of e)a.push({diffs:t.diffs.map(e=>tU(e)),start1:o(t.start1),start2:o(t.start2),utf8Start1:t.utf8Start1,utf8Start2:t.utf8Start2,length1:t.length1,length2:t.length2,utf8Length1:t.utf8Length1,utf8Length2:t.utf8Length2});return a}(e,r,{allowExceedingIndices:n.allowExceedingIndices}),o=n.margin||4,a=n.deleteThreshold||.4,s=function(e,t=4){let n="";for(let e=1;e<=t;e++)n+=String.fromCharCode(e);for(let n of e)n.start1+=t,n.start2+=t,n.utf8Start1+=t,n.utf8Start2+=t;let r=e[0],i=r.diffs;if(0===i.length||0!==i[0][0])i.unshift([0,n]),r.start1-=t,r.start2-=t,r.utf8Start1-=t,r.utf8Start2-=t,r.length1+=t,r.length2+=t,r.utf8Length1+=t,r.utf8Length2+=t;else if(t>i[0][1].length){let e=i[0][1].length,o=t-e;i[0][1]=n.substring(e)+i[0][1],r.start1-=o,r.start2-=o,r.utf8Start1-=o,r.utf8Start2-=o,r.length1+=o,r.length2+=o,r.utf8Length1+=o,r.utf8Length2+=o}if(0===(i=(r=e[e.length-1]).diffs).length||0!==i[i.length-1][0])i.push([0,n]),r.length1+=t,r.length2+=t,r.utf8Length1+=t,r.utf8Length2+=t;else if(t>i[i.length-1][1].length){let e=t-i[i.length-1][1].length;i[i.length-1][1]+=n.substring(0,e),r.length1+=e,r.length2+=e,r.utf8Length1+=e,r.utf8Length2+=e}return n}(i,o);r=s+r+s,function(e,t=4){for(let n=0;n<e.length;n++){if(e[n].length1<=32)continue;let r=e[n];e.splice(n--,1);let i=r.start1,o=r.start2,a="";for(;0!==r.diffs.length;){let s=ny(i-a.length,o-a.length),l=!0;if(""!==a){let e=np(a);s.length1=a.length,s.utf8Length1=e,s.length2=a.length,s.utf8Length2=e,s.diffs.push([0,a])}for(;0!==r.diffs.length&&s.length1<32-t;){let e=r.diffs[0][0],n=r.diffs[0][1],a=np(n);if(1===e){s.length2+=n.length,s.utf8Length2+=a,o+=n.length;let e=r.diffs.shift();e&&s.diffs.push(e),l=!1}else -1===e&&1===s.diffs.length&&0===s.diffs[0][0]&&n.length>64?(s.length1+=n.length,s.utf8Length1+=a,i+=n.length,l=!1,s.diffs.push([e,n]),r.diffs.shift()):(a=np(n=n.substring(0,32-s.length1-t)),s.length1+=n.length,s.utf8Length1+=a,i+=n.length,0===e?(s.length2+=n.length,s.utf8Length2+=a,o+=n.length):l=!1,s.diffs.push([e,n]),n===r.diffs[0][1]?r.diffs.shift():r.diffs[0][1]=r.diffs[0][1].substring(n.length))}a=(a=nd(s.diffs)).substring(a.length-t);let u=nc(r.diffs).substring(0,t),c=np(u);""!==u&&(s.length1+=u.length,s.length2+=u.length,s.utf8Length1+=c,s.utf8Length2+=c,0!==s.diffs.length&&0===s.diffs[s.diffs.length-1][0]?s.diffs[s.diffs.length-1][1]+=u:s.diffs.push([0,u])),l||e.splice(++n,0,s)}}}(i,o);let l=0,u=[];for(let e=0;e<i.length;e++){let t=i[e].start2+l,n=nc(i[e].diffs),o,s=-1;if(n.length>32?-1!==(o=nu(r,n.substring(0,32),t))&&(-1===(s=nu(r,n.substring(n.length-32),t+n.length-32))||o>=s)&&(o=-1):o=nu(r,n,t),-1===o)u[e]=!1,l-=i[e].length2-i[e].length1;else{let c;if(u[e]=!0,l=o-t,c=-1===s?r.substring(o,o+n.length):r.substring(o,s+32),n===c)r=r.substring(0,o)+nd(i[e].diffs)+r.substring(o+n.length);else{let t=tQ(n,c,{checkLines:!1});if(n.length>32&&function(e){let t=0,n=0,r=0;for(let i=0;i<e.length;i++){let o=e[i][0],a=e[i][1];switch(o){case 1:n+=a.length;break;case -1:r+=a.length;break;case 0:t+=Math.max(n,r),n=0,r=0;break;default:throw Error("Unknown diff operation.")}}return t+Math.max(n,r)}(t)/n.length>a)u[e]=!1;else{t=ne(t);let n=0,a=0;for(let s=0;s<i[e].diffs.length;s++){let l=i[e].diffs[s];0!==l[0]&&(a=nf(t,n)),1===l[0]?r=r.substring(0,o+a)+l[1]+r.substring(o+a):-1===l[0]&&(r=r.substring(0,o+a)+r.substring(o+nf(t,n+l[1].length))),-1!==l[0]&&(n+=l[1].length)}}}}}return[r=r.substring(s.length,r.length-s.length),u]}(function(e){if(!e)return[];let t=[],n=e.split(`
`),r=0;for(;r<n.length;){let e=n[r].match(nS);if(!e)throw Error(`Invalid patch string: ${n[r]}`);let i=ny(nI(e[1]),nI(e[3]));for(t.push(i),""===e[2]?(i.start1--,i.utf8Start1--,i.length1=1,i.utf8Length1=1):"0"===e[2]?(i.length1=0,i.utf8Length1=0):(i.start1--,i.utf8Start1--,i.utf8Length1=nI(e[2]),i.length1=i.utf8Length1),""===e[4]?(i.start2--,i.utf8Start2--,i.length2=1,i.utf8Length2=1):"0"===e[4]?(i.length2=0,i.utf8Length2=0):(i.start2--,i.utf8Start2--,i.utf8Length2=nI(e[4]),i.length2=i.utf8Length2),r++;r<n.length;){let e,t=n[r],o=t.charAt(0);if("@"===o)break;if(""===o){r++;continue}try{e=decodeURI(t.slice(1))}catch(e){throw Error(`Illegal escape in parse: ${t}`)}let a=np(e)-e.length;if("-"===o)i.diffs.push([-1,e]),i.length1-=a;else if("+"===o)i.diffs.push([1,e]),i.length2-=a;else if(" "===o)i.diffs.push([0,e]),i.length1-=a,i.length2-=a;else throw Error(`Invalid patch mode "${o}" in: ${e}`);r++}}return t}(e.value),t)[0]},inc:function(e,t){if("number"!=typeof t)throw TypeError('Cannot apply "inc()" on non-numeric value');return t+e.amount},insert:o_,remove:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "remove()" on non-array value');let n=ox(t,e.referenceItem);if(null===n)throw Error("Found no matching array element to replace");return oE(t,n,1,[])},replace:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "replace()" on non-array value');let n=ox(t,e.referenceItem);if(null===n)throw Error("Found no matching array element to replace");return oE(t,n,e.items.length,e.items)},set:function(e,t){return e.value},setIfMissing:function(e,t){return t??e.value},truncate:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "truncate()" on non-array value');return"number"==typeof e.endIndex?t.slice(0,e.startIndex).concat(t.slice(e.endIndex)):t.slice(0,e.startIndex)},unassign:function(e,t){if(!ow(t))throw TypeError('Cannot apply "unassign()" on non-object value');return 0===e.keys.length?t:function(e,t){let n={...e};for(let e of t)delete n[e];return n}(t,e.keys)},unset:function(e){},upsert:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "upsert()" on non-array value');if(0===e.items.length)return t;let n=[],r=[];if(e.items.forEach((e,i)=>{let o=t.findIndex(t=>t?._key===e._key);o>=0?n[o]=i:r.push(e)}),0===n.length&&0==r.length)return t;let i=[...t];for(let t of n)i[t]=e.items[n[t]];return o_({type:"insert",items:r,referenceItem:e.referenceItem,position:e.position},i)}});function oS(e,t){return e.reduce((e,t)=>oI(t,e),t)}function oI(e,t){return function e(t,n,r){if(!(t.length>0)){if(!(n.type in oj))throw Error(`Invalid operation type: "${n.type}"`);return oj[n.type](n,r)}let[i,...o]=t;if(om(i)&&Array.isArray(r)){var a=i,s=o,l=n,u=r;let t=ox(u,a);if(null===t||-1===t)return u;let c=u[t],d=e(s,l,c);return d===c?u:oE(u,t,1,[d])}if("string"==typeof i&&ow(r)){var c=i,d=o,f=n,p=r;let t=p[c];if(void 0===t&&d.length>0)return p;let a=e(d,f,t);return a===t?p:{...p,[c]:a}}throw Error(`Cannot apply operation of type "${n.type}" to path ${ob(t)} on ${typeof r} value`)}(e.path,e.op,t)}function oO(e){return"_id"in e}function oA(e,t){return t.reduce((e,t)=>{let n=oM(e,t);if("error"===n.status)throw Error(n.message);return"noop"===n.status?e:n.after},e)}function oM(e,t){if("create"===t.type)return function(e,t){var n;if(e)return{status:"error",message:"Document already exist"};let r=(n=t.document,oO(n)?n:{...n,_id:op()});return{status:"created",id:r._id,after:r}}(e,t);if("createIfNotExists"===t.type){return n=e,oO((r=t).document)?n?{status:"noop"}:{status:"created",id:r.document._id,after:r.document}:{status:"error",message:"Cannot createIfNotExists on document without _id"}}if("delete"===t.type){return i=e,o=t,i?o.id!==i._id?{status:"error",message:"Delete mutation targeted wrong document"}:{status:"deleted",id:o.id,before:i,after:void 0}:{status:"noop"}}if("createOrReplace"===t.type){return a=e,oO((s=t).document)?a?{status:"updated",id:s.document._id,before:a,after:s.document}:{status:"created",id:s.document._id,after:s.document}:{status:"error",message:"Cannot createIfNotExists on document without _id"}}if("patch"===t.type){var n,r,i,o,a,s,l=e,u=t;if(!l)return{status:"error",message:"Cannot apply patch on nonexistent document"};let c=function(e,t){if(e.options?.ifRevision&&t._rev!==e.options.ifRevision)throw Error("Revision mismatch");if(e.id!==t._id)throw Error(`Document id mismatch. Refusing to apply mutation for document with id="${e.id}" on the given document with id="${t._id}"`);return oS(e.patches,t)}(u,l);return l===c?{status:"noop"}:{status:"updated",id:u.id,before:l,after:c}}throw Error(`Invalid mutation type: ${t.type}`)}function oP(e,t,n){let r=[];for(let n of e.slice().reverse()){if(t(n))return r;r.push(n)}return r.reverse()}function o$(e,t){return ob(e)===ob(t)}function oC(e,t){let n=e;return t.reduce((e,t)=>{let r=n;if(n=oI(t,n),"set"===t.op.type&&"string"==typeof t.op.value){let n=og(t.path,r);if("string"==typeof n){let r={...t,op:{type:"diffMatchPatch",value:(function(e,t,n){if("string"==typeof e&&"string"==typeof t){let n=tQ(e,t,{checkLines:!0});return n.length>2&&(n=function(e,t=4){let n=e.map(e=>tU(e)),r=!1,i=[],o=0,a=null,s=0,l=!1,u=!1,c=!1,d=!1;for(;s<n.length;)0===n[s][0]?(n[s][1].length<t&&(c||d)?(i[o++]=s,l=c,u=d,a=n[s][1]):(o=0,a=null),c=!1,d=!1):(-1===n[s][0]?d=!0:c=!0,a&&(l&&u&&c&&d||a.length<t/2&&3===function(...e){return e.reduce((e,t)=>e+ +!!t,0)}(l,u,c,d))&&(n.splice(i[o-1],0,[-1,a]),n[i[o-1]+1][0]=1,o--,a=null,l&&u?(c=!0,d=!0,o=0):(s=--o>0?i[o-1]:-1,c=!1,d=!1),r=!0)),s++;return r&&(n=nt(n)),n}(n=t5(n))),nk(e,n,n_(void 0))}if(e&&Array.isArray(e)&&typeof t>"u")return nk(nc(e),e,n_(void 0));if("string"==typeof e&&t&&Array.isArray(t))return nk(e,t,n_(n));throw Error("Unknown call format to make()")})(n,t.op.value).map(nO).join("")}};return e.flatMap(e=>o$(e.path,t.path)&&"diffMatchPatch"===e.op.type?[]:e).concat(r)}}return e.push(t),e},[])}function oT(e,t,n,r){let i=t,o=r.map(t=>{let n=t.mutations.flatMap(t=>{if(of(t)!==e)return[];let n=i;return i=oA(i,[t]),n&&"patch"===t.type?{type:"dmpified",mutation:{...t,dmpPatches:oC(n,t.patches),original:t.patches}}:t});return{...t,mutations:n}}),a=n;return o.map(t=>{let n=[];return t.mutations.forEach(t=>{if("dmpified"===t.type)try{a=oS(t.mutation.dmpPatches,a),n.push(t)}catch{console.warn("Failed to apply dmp patch, falling back to original");try{a=oS(t.mutation.original,a),n.push(t)}catch(t){throw Error(`Failed to apply patch for document "${e}": ${t.message}`)}}else a=oA(a,[t])})}),[r.map(t=>({...t,mutations:t.mutations.map(t=>"patch"!==t.type||of(t)!==e?t:{...t,patches:t.patches.map(e=>"set"!==e.op.type?e:{...e,op:{...e.op,value:og(e.path,a)}})})})),a]}function oR(e){var t=e;if("create"===t.type||"createIfNotExists"===t.type||"createOrReplace"===t.type)return{[t.type]:t.document};if("delete"===t.type)return{delete:{id:t.id}};let n=t.options?.ifRevision;return t.patches.map(e=>({patch:{id:t.id,...n&&{ifRevisionID:n},...function(e){let{path:t,op:n}=e;if("unset"===n.type)return{unset:[ob(t)]};if("insert"===n.type)return{insert:{[n.position]:ob([...t,n.referenceItem]),items:n.items}};if("diffMatchPatch"===n.type)return{diffMatchPatch:{[ob(t)]:n.value}};if("inc"===n.type)return{inc:{[ob(t)]:n.amount}};if("dec"===n.type)return{dec:{[ob(t)]:n.amount}};if("set"===n.type||"setIfMissing"===n.type)return{[n.type]:{[ob(t)]:n.value}};if("truncate"===n.type){let e=[n.startIndex,"number"==typeof n.endIndex?n.endIndex:""].join(":");return{unset:[`${ob(t)}[${e}]`]}}if("upsert"===n.type)return{unset:n.items.map(e=>ob([...t,{_key:e._key}])),insert:{[n.position]:ob([...t,n.referenceItem]),items:n.items}};if("assign"===n.type)return{set:Object.fromEntries(Object.keys(n.value).map(e=>[ob(t.concat(e)),n.value[e]]))};if("unassign"===n.type)return{unset:n.keys.map(e=>ob(t.concat(e)))};if("replace"===n.type)return{insert:{replace:ob(t.concat(n.referenceItem)),items:n.items}};if("remove"===n.type)return{unset:[ob(t.concat(n.referenceItem))]};throw Error(`Unknown operation type ${n.type}`)}(e)}}))}function oL(e){var t,n,r,i,o,a,s,l;let u=e.listen('*[!(_id in path("_.**"))]',{},{events:["welcome","mutation","reconnect"],includeResult:!1,includePreviousRevision:!1,visibility:"transaction",effectFormat:"mendoza",includeMutations:!1}).pipe(i1({resetOnRefCountZero:!0})),c=u.pipe((0,i3.p)(e=>"reconnect"===e.type)),d=u.pipe((0,i3.p)(e=>"welcome"===e.type)),f=u.pipe((0,i3.p)(e=>"mutation"===e.type)),p=tz(d,c).pipe((l=!1,t={bufferSize:1,refCount:!0},s=void 0===(i=t.bufferSize)?1/0:i,n=void 0===(o=t.windowTime)?1/0:o,l=void 0!==(a=t.refCount)&&a,r=t.scheduler,i1({connector:function(){return new tC(s,n,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:l}))).pipe((0,i3.p)(e=>"welcome"===e.type));return tz(p,f,c)}let oD=(0,ou.mj)({types:{},actions:{"assign error to context":(0,oc.a)({error:({event:e})=>e}),"clear error from context":(0,oc.a)({error:void 0}),"connect to server-sent events":(0,t_.r)({type:"connect"}),"listen to server-sent events":(0,t_.h)("server-sent events",{id:"listener",input:({context:e})=>({listener:e.sharedListener||oL(e.client),id:e.id})}),"stop listening to server-sent events":(0,t_.k)("listener"),"buffer remote mutation events":(0,oc.a)({mutationEvents:({event:e,context:t})=>((0,ou.DT)(e,"mutation"),[...t.mutationEvents,e])}),"restore stashed changes":(0,oc.a)({stagedChanges:({event:e,context:t})=>((0,ou.DT)(e,"xstate.done.actor.submitTransactions"),t.stashedChanges),stashedChanges:[]}),"rebase fetched remote snapshot":(0,od.a)(({enqueue:e})=>{e.assign(({event:e,context:t})=>{(0,ou.DT)(e,"xstate.done.actor.getDocument");let n=t.remote,r=e.output,i=!1;for(let e of t.mutationEvents)e.effects?.apply&&(e.previousRev||"appear"===e.transition)&&(i||e.previousRev!==r?._rev||(i=!0),i&&(r=oN(r,e.effects.apply,e.resultRev)));t.cache&&(!t.cache.has(t.id)||t.cache.get(t.id)._rev!==r?._rev)&&t.cache.set(t.id,r);let[o,a]=oT(t.id,null===n?void 0:n,null===r?void 0:r,t.stagedChanges);return{remote:r,local:a,stagedChanges:o,mutationEvents:[]}}),e.sendParent(({context:e})=>({type:"rebased.remote",id:e.id,document:e.remote}))}),"apply mendoza patch":(0,oc.a)(({event:e,context:t})=>{(0,ou.DT)(e,"mutation");let n=t.remote;if(e.transactionId===n?._rev)return{};let r=oN(n,e.effects.apply,e.resultRev);t.cache&&(!t.cache.has(t.id)||t.cache.get(t.id)._rev!==r?._rev)&&t.cache.set(t.id,r);let[i,o]=oT(t.id,null===n?void 0:n,null===r?void 0:r,t.stagedChanges);return{remote:r,local:o,stagedChanges:i}}),"increment fetch attempts":(0,oc.a)({fetchRemoteSnapshotAttempts:({context:e})=>e.fetchRemoteSnapshotAttempts+1}),"reset fetch attempts":(0,oc.a)({fetchRemoteSnapshotAttempts:0}),"increment submit attempts":(0,oc.a)({submitTransactionsAttempts:({context:e})=>e.submitTransactionsAttempts+1}),"reset submit attempts":(0,oc.a)({submitTransactionsAttempts:0}),"stage mutation":(0,oc.a)({stagedChanges:({event:e,context:t})=>((0,ou.DT)(e,"mutate"),[...t.stagedChanges,{transaction:!1,mutations:e.mutations}])}),"stash mutation":(0,oc.a)({stashedChanges:({event:e,context:t})=>((0,ou.DT)(e,"mutate"),[...t.stashedChanges,{transaction:!1,mutations:e.mutations}])}),"rebase local snapshot":(0,od.a)(({enqueue:e})=>{e.assign({local:({event:e,context:t})=>{(0,ou.DT)(e,"mutate");let n=new Map;return t.local&&n.set(t.id,t.local),function(e,t){e.forEach(e=>{("created"===e.status||"updated"===e.status)&&t.set(e.id,e.after),"deleted"===e.status&&t.delete(e.id)})}(function(e,t){let n=Object.create(null);for(let r of e){let e=of(r);if(!e)throw Error("Unable to get document id from mutation");let i=n[e]?.after||t.get(e),o=oM(i,r);if("error"===o.status)throw Error(o.message);"noop"!==o.status&&("updated"===o.status||"created"===o.status||"deleted"===o.status)&&(e in n||(n[e]={before:i,after:void 0,muts:[]}),n[e].after=o.after)}return Object.entries(n).map(([e,{before:t,after:n,muts:r}])=>({id:e,status:n?t?"updated":"created":"deleted",mutations:r,before:t,after:n}))}(e.mutations,n),n),n.get(t.id)}}),e.sendParent(({context:e})=>({type:"rebased.local",id:e.id,document:e.local}))}),"send pristine event to parent":(0,od.s)(({context:e})=>({type:"pristine",id:e.id})),"send sync event to parent":(0,od.s)(({context:e})=>({type:"sync",id:e.id,document:e.remote})),"send mutation event to parent":(0,od.s)(({context:e,event:t})=>((0,ou.DT)(t,"mutation"),{type:"mutation",id:e.id,previousRev:t.previousRev,resultRev:t.resultRev,effects:t.effects}))},actors:{"server-sent events":(0,U.OX)(({input:e})=>{let{listener:t,id:n}=e;return(0,i5.v)(()=>t).pipe((0,i3.p)(e=>"welcome"===e.type||"reconnect"===e.type||"mutation"===e.type&&e.documentId===n),(0,i4.Q)(ol))}),"fetch remote snapshot":(0,U.Sx)(async({input:e,signal:t})=>{let{client:n,id:r}=e;return await n.getDocument(r,{signal:t}).catch(e=>{if(!(e instanceof Error&&"AbortError"===e.name))throw e})}),"submit mutations as transactions":(0,U.Sx)(async({input:e,signal:t})=>{let{client:n,transactions:r}=e;for(let e of r){if(t.aborted)return;await n.dataRequest("mutate",function(e){return{transactionId:e.id,mutations:e.mutations.flatMap(oR)}}(e),{visibility:"async",returnDocuments:!1,signal:t}).catch(e=>{if(!(e instanceof Error&&"AbortError"===e.name))throw e})}})},delays:{fetchRemoteSnapshotTimeout:({context:e})=>1e3*Math.pow(2,e.fetchRemoteSnapshotAttempts),submitTransactionsTimeout:({context:e})=>1e3*Math.pow(2,e.submitTransactionsAttempts)}}).createMachine({id:"document-mutator",context:({input:e})=>({client:e.client.withConfig({allowReconfigure:!1}),sharedListener:e.sharedListener,id:e.id,remote:void 0,local:void 0,mutationEvents:[],stagedChanges:[],stashedChanges:[],error:void 0,fetchRemoteSnapshotAttempts:0,submitTransactionsAttempts:0,cache:e.cache}),entry:["connect to server-sent events"],on:{mutate:{actions:["rebase local snapshot","stage mutation"]}},initial:"disconnected",states:{disconnected:{on:{connect:{target:"connecting",actions:["listen to server-sent events"]}}},connecting:{on:{welcome:"connected",reconnect:"reconnecting",error:"connectFailure"},tags:["busy"]},connectFailure:{on:{connect:{target:"connecting",actions:["listen to server-sent events"]}},entry:["stop listening to server-sent events","assign error to context"],exit:["clear error from context"],tags:["error"]},reconnecting:{on:{welcome:{target:"connected"},error:{target:"connectFailure"}},tags:["busy","error"]},connected:{on:{mutation:{actions:["buffer remote mutation events"]},reconnect:"reconnecting"},entry:["clear error from context"],initial:"loading",states:{loading:{invoke:{src:"fetch remote snapshot",id:"getDocument",input:({context:e})=>({client:e.client,id:e.id}),onError:{target:"loadFailure"},onDone:{target:"loaded",actions:["rebase fetched remote snapshot","reset fetch attempts"]}},tags:["busy"]},loaded:{entry:["send sync event to parent"],on:{mutation:{actions:["apply mendoza patch","send mutation event to parent"]}},initial:"pristine",states:{pristine:{on:{mutate:{actions:["rebase local snapshot","stage mutation"],target:"dirty"}},tags:["ready"]},dirty:{on:{submit:"submitting"},tags:["ready"]},submitting:{on:{mutate:{actions:["rebase local snapshot","stash mutation"]}},invoke:{src:"submit mutations as transactions",id:"submitTransactions",input:({context:e})=>{let t=new Map;return t.set(e.id,e.remote),{client:e.client,transactions:(function(e,t){let n=[],r=[];return e.forEach(e=>{t(e)?r.push(e):(r.length>0&&n.push(r),r=[],n.push([e]))}),r.length>0&&n.push(r),n})(e.stagedChanges,e=>!e.transaction).flatMap(e=>({...e[0],mutations:e.flatMap(e=>e.mutations)})).map(e=>({...e,mutations:Object.values(iH(e.mutations,of)).flatMap(e=>{var t,n;return(0===(n=0===(t=e).length?t:t.reduce((e,t)=>"delete"===t.type?[t]:(e.push(t),e),[])).length?n:n.reduce((e,t)=>("createIfNotExists"!==t.type?e.push(t):oP(e,e=>"delete"===e.type).find(e=>"createIfNotExists"===e.type)||e.push(t),e),[])).flat().reduce((e,t)=>{let n=e[e.length-1];return n&&"patch"!==n.type||"patch"!==t.type?e.concat(t):e.slice(0,-1).concat({...t,patches:(n?.patches||[]).concat(t.patches)})},[])})})).map(e=>({...e,mutations:e.mutations.map(e=>"patch"!==e.type?e:{...e,patches:e.patches.reduce((e,t)=>{if("unset"!==t.op.type)return e.push(t),e;let n=e.filter(e=>{var n,r;return n=t.path,r=e.path,!(n.length<=r.length&&n.every((e,t)=>{var n,i;return n=e,i=function(e,t){if(t<0||t>=e.length)throw Error("Index out of bounds");return e[t]}(r,t),oh(n)&&oh(i)?n._key===i._key:"number"==typeof n?Number(n)===Number(i):n===i}))});return n.push(t),n},[]).reduceRight((e,t)=>(e.find(e=>{var n,r;return n=e.op,("set"===(r=t.op).type||"unset"===r.type)&&("set"===n.type||"unset"===n.type)&&o$(e.path,t.path)})||e.unshift(t),e),[]).reduce((e,t)=>("setIfMissing"!==t.op.type?e.push(t):oP(e,e=>"unset"===e.op.type).find(e=>"setIfMissing"===e.op.type&&o$(e.path,t.path))||e.push(t),e),[])})})).map(e=>{var n;return{...e,mutations:(n=t,e.mutations.map((e,t)=>{var r,i;return"patch"===e.type?(r=n.get(e.id),i=e,r?{...i,patches:oC(r,i.patches)}:i):e}))}}).map(e=>e.transaction&&void 0!==e.id?{id:e.id,mutations:e.mutations}:{mutations:e.mutations})}},onError:{target:"submitFailure"},onDone:{target:"pristine",actions:["restore stashed changes","reset submit attempts","send pristine event to parent"]}},tags:["busy","ready"]},submitFailure:{exit:["clear error from context"],after:{submitTransactionsTimeout:{actions:["increment submit attempts"],target:"submitting"}},on:{retry:"submitting"},tags:["error","ready"]}}},loadFailure:{exit:["clear error from context"],after:{fetchRemoteSnapshotTimeout:{actions:["increment fetch attempts"],target:"loading"}},on:{retry:"loading"},tags:["error"]}}}}});function oN(e,t,n){var r;let i=(r=function(e){if(!e)return null;let{_rev:t,...n}=e;return n}(e),new iY(new iZ,r,t).process());return i?Object.assign(i,{_rev:n}):null}let oF=(0,ou.mj)({types:{},actions:{"emit sync event":(0,od.e)(({event:e})=>((0,ou.DT)(e,"sync"),e)),"emit mutation event":(0,od.e)(({event:e})=>((0,ou.DT)(e,"mutation"),e)),"emit rebased event":(0,od.e)(({event:e})=>((0,ou.DT)(e,["rebased.local","rebased.remote"]),e)),"emit pristine event":(0,od.e)(({event:e})=>((0,ou.DT)(e,["pristine"]),e)),"add document actor":(0,oc.a)({documents:({context:e,event:t,spawn:n})=>{(0,ou.DT)(t,"observe");let r=t.documentId;return e.documents[r]?e.documents:{...e.documents,[r]:n("documentMutatorMachine",{input:{id:r,client:e.client,sharedListener:e.sharedListener||oL(e.client)},id:r})}}}),"stop remote snapshot":(0,t_.k)(({context:e,event:t})=>((0,ou.DT)(t,"unobserve"),e.documents[t.documentId])),"remove remote snapshot from context":(0,oc.a)({documents:({context:e,event:t})=>{if((0,ou.DT)(t,"unobserve"),!e.documents[t.documentId])return e.documents;let{[t.documentId]:n,...r}=e.documents;return r}})},actors:{documentMutatorMachine:oD}}).createMachine({id:"dataset-mutator",context:({input:e})=>({documents:{},client:e.client,sharedListener:e.sharedListener}),on:{sync:{actions:["emit sync event"]},mutation:{actions:["emit mutation event"]},"rebased.*":{actions:["emit rebased event"]},pristine:{actions:["emit pristine event"]},observe:{actions:["add document actor"]},unobserve:{actions:["stop remote snapshot","remove remote snapshot from context"]}},initial:"pristine",states:{pristine:{}}}),oz=e=>{let t,n,r,i,o=(0,y.c)(8),{comlink:a,history:s}=e;return o[0]!==a||o[1]!==s?(t=()=>a?.on("presentation/navigate",e=>{s?.update(e)}),n=[a,s],o[0]=a,o[1]=s,o[2]=t,o[3]=n):(t=o[2],n=o[3]),(0,m.useEffect)(t,n),o[4]!==a||o[5]!==s?(r=()=>{if(s)return s.subscribe(e=>{e.title=e.title||document.title,a?.post("visual-editing/navigate",e)})},i=[a,s],o[4]=a,o[5]=s,o[6]=r,o[7]=i):(r=o[6],i=o[7]),(0,m.useEffect)(r,i),null},oU=e=>{let t,n,r=(0,y.c)(3),{comlink:i}=e;return r[0]!==i?(t=()=>{let e=()=>{i.post("visual-editing/meta",{title:document.title})},t=new MutationObserver(t=>{let[n]=t;"TITLE"===n.target.nodeName&&e()});return t.observe(document.head,{subtree:!0,characterData:!0,childList:!0}),e(),()=>t.disconnect()},n=[i],r[0]=i,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,m.useEffect)(t,n),null};function oW(e){let t=new CustomEvent("sanity/dragEnd",{detail:e,cancelable:!0});window.dispatchEvent(t)}let oV=(0,m.createContext)(null);function oq(){let e=(0,m.useContext)(oV);if(!e)throw Error("Schema context is missing");return e}function oB(e,t,n,r){if(!e.type)throw Error("Node type is missing");return()=>t.patch(()=>(function(e,t,n){let{path:r,key:i}=eN(e);return[z(r,D([{_type:t,_key:eD()}],n,{_key:i}))]})(e,n,r))}function oG(e){let{node:t,doc:n}=e;return n?[{type:"action",label:"Duplicate",icon:j.C,action:function(e,t){if(!e.type)throw Error("Node type is missing");return()=>t.patch(async({getSnapshot:t})=>(function(e,t,n="after"){let{path:r,key:i}=eN(e);return[z(r,D({...ek(t,e.path),_key:eD()},n,{_key:i}))]})(e,await t()))}(t,n)}]:[]}function oH(e){let{node:t,doc:n}=e;return n?[{type:"action",label:"Remove",icon:j.R,action:function(e,t){if(!e.type)throw Error("Node type is missing");return()=>t.patch(async({getSnapshot:t})=>(function(e,t){let{path:n,key:r}=eN(e),i=ek(t,n).findIndex(e=>e._key===r);return[z(n,N(i,i+1))]})(e,await t()))}(t,n)}]:[]}async function oK(e,t=!0){let{node:n,doc:r}=e;if(!r)return[];let i=[],o=[],[a,s,l,u]=await Promise.all([eF(n,r,"previous"),eF(n,r,"next"),eF(n,r,"first"),eF(n,r,"last")]);return l.length&&o.push({type:"action",label:"To top",icon:j.c,action:()=>r.patch(l)}),a.length&&o.push({type:"action",label:"Up",icon:j.e,action:()=>r.patch(a)}),s.length&&o.push({type:"action",label:"Down",icon:j.f,action:()=>r.patch(s)}),u.length&&o.push({type:"action",label:"To bottom",icon:j.U,action:()=>r.patch(u)}),o.length&&(i.push({type:"group",label:"Move",icon:j.S,items:o}),t&&i.push({type:"divider"})),i}let oY=e=>{let t,n,r,i,o,a=(0,y.c)(12),{label:s,parent:l,width:u,onSelect:c,boundaryElement:d}=e;return a[0]===Symbol.for("react.memo_cache_sentinel")?(t=["left-start","right","left","right-end","left-end","bottom","top"],a[0]=t):t=a[0],a[1]===Symbol.for("react.memo_cache_sentinel")?(n=[4,4,4,4],a[1]=n):n=a[1],a[2]!==d||a[3]!==u?(r={arrow:!1,constrainSize:!0,floatingBoundary:d,padding:0,placement:"right-start",fallbackPlacements:t,preventOverflow:!0,width:u,__unstable_margins:n},a[2]=d,a[3]=u,a[4]=r):r=a[4],a[5]!==c||a[6]!==l?(i=(0,h.jsx)(j.h,{node:l,onSelect:c}),a[5]=c,a[6]=l,a[7]=i):i=a[7],a[8]!==s||a[9]!==r||a[10]!==i?(o=(0,h.jsx)(j.M,{fontSize:1,icon:j.b,padding:2,popover:r,space:2,text:s,children:i}),a[8]=s,a[9]=r,a[10]=i,a[11]=o):o=a[11],o},oX=[-4,4,-4,4];function oJ(e){let t,n=(0,y.c)(25),{node:r,onDismiss:i,boundaryElement:o}=e;n[0]!==r||n[1]!==i?(t=()=>{"action"===r.type&&(r.action?.(),i?.())},n[0]=r,n[1]=i,n[2]=t):t=n[2];let a=t;if("divider"===r.type){let e;return n[3]===Symbol.for("react.memo_cache_sentinel")?(e=(0,h.jsx)(j.i,{}),n[3]=e):e=n[3],e}if("action"===r.type){let e,t=!r.action;return n[4]!==r.hotkeys||n[5]!==r.icon||n[6]!==r.label||n[7]!==a||n[8]!==t?(e=(0,h.jsx)(j.p,{fontSize:1,hotkeys:r.hotkeys,icon:r.icon,padding:2,space:2,text:r.label,disabled:t,onClick:a}),n[4]=r.hotkeys,n[5]=r.icon,n[6]=r.label,n[7]=a,n[8]=t,n[9]=e):e=n[9],e}if("group"===r.type){let e,t,a,s=r.icon;n[10]===Symbol.for("react.memo_cache_sentinel")?(e={arrow:!1,constrainSize:!0,placement:"right-start",fallbackPlacements:["left-start","right","left","right-end","left-end","bottom","top"],preventOverflow:!0,__unstable_margins:oX},n[10]=e):e=n[10];let l=r.label;if(n[11]!==o||n[12]!==r.items||n[13]!==i){let e;n[15]!==o||n[16]!==i?(e=(e,t)=>(0,h.jsx)(oJ,{node:e,onDismiss:i,boundaryElement:o},t),n[15]=o,n[16]=i,n[17]=e):e=n[17],t=r.items.map(e),n[11]=o,n[12]=r.items,n[13]=i,n[14]=t}else t=n[14];return n[18]!==r.icon||n[19]!==r.label||n[20]!==t?(a=(0,h.jsx)(j.M,{fontSize:1,icon:s,padding:2,popover:e,space:2,text:l,children:t}),n[18]=r.icon,n[19]=r.label,n[20]=t,n[21]=a):a=n[21],a}if("custom"===r.type){let e,{component:t}=r;return n[22]!==t||n[23]!==o?(e=(0,h.jsx)(t,{boundaryElement:o}),n[22]=t,n[23]=o,n[24]=e):e=n[24],e}return null}let oZ=e=>{let t,n,r,i,o,a,s,l,u,c,d,f,p,g,v,b,w,x,E=(0,y.c)(43),{node:_,onDismiss:k,position:S}=e,{x:I,y:O}=S,[A,M]=(0,m.useState)(null),{getField:P}=oq(),{getDocument:$}=eR();E[0]!==P||E[1]!==_?(t=P(_),E[0]=P,E[1]=_,E[2]=t):t=E[2];let{field:C,parent:T}=t;n=C?.title||C?.name||"Unknown type";let[R,L]=(0,m.useState)(void 0);E[3]!==C||E[4]!==$||E[5]!==_||E[6]!==T?(r=()=>{(async()=>{let e=$(_.id);e&&L(await function(e){let{node:t,field:n,parent:r,doc:i}=e;return"arrayItem"===n?.type?async function(e){let{node:t,field:n,doc:r}=e,i=[];return i.push(...oG(e)),i.push(...oH(e)),i.push(...await oK(e)),i.push({type:"action",label:"Insert before",icon:j.a,action:oB(t,r,n.name,"before")}),i.push({type:"action",label:"Insert after",icon:j.b,action:oB(t,r,n.name,"after")}),i}({node:t,field:n,doc:i}):"union"===r?.type?async function(e){let{doc:t,node:n,parent:r}=e,i=[];if(i.push(...oG(e)),i.push(...oH(e)),i.push(...await oK(e)),r.options?.insertMenu){let e=(r.options.insertMenu||{}).views?.some(e=>"grid"===e.name)?0:void 0;i.push({type:"custom",component:({boundaryElement:i})=>(0,h.jsx)(oY,{label:"Insert before",onSelect:e=>{oB(n,t,e.name,"before")()},parent:r,width:e,boundaryElement:i})}),i.push({type:"custom",component:({boundaryElement:i})=>(0,h.jsx)(oY,{label:"Insert after",onSelect:e=>{oB(n,t,e.name,"after")()},parent:r,width:e,boundaryElement:i})})}else i.push({type:"group",label:"Insert before",icon:j.a,items:r.of.filter(e=>"unionOption"===e.type).map(e=>({type:"action",icon:(0,j.g)(e),label:"block"===e.name?"Paragraph":e.title||e.name,action:oB(n,t,e.name,"before")}))}),i.push({type:"group",label:"Insert after",icon:j.b,items:r.of.filter(e=>"unionOption"===e.type).map(e=>({type:"action",label:"block"===e.name?"Paragraph":e.title||e.name,icon:(0,j.g)(e),action:oB(n,t,e.name,"after")}))});return i}({node:t,parent:r,doc:i}):Promise.resolve([])}({node:_,field:C,parent:T,doc:e}))})()},i=[C,_,T,$],E[3]=C,E[4]=$,E[5]=_,E[6]=T,E[7]=r,E[8]=i):(r=E[7],i=E[8]),(0,m.useEffect)(r,i),E[9]!==I||E[10]!==O?(a={getBoundingClientRect:()=>({bottom:O,left:I,right:I,top:O,width:0,height:0})},E[9]=I,E[10]=O,E[11]=a):a=E[11],o=a,E[12]!==C?(l=(0,j.g)(C),E[12]=C,E[13]=l):l=E[13],s=l,E[14]===Symbol.for("react.memo_cache_sentinel")?(u={minWidth:120,maxWidth:160},E[14]=u):u=E[14],E[15]!==s||E[16]!==R?(c=(0,h.jsx)(j.j,{flex:"none",children:R?(0,h.jsx)(j.T,{size:1,children:s}):(0,h.jsx)(j.k,{size:1})}),E[15]=s,E[16]=R,E[17]=c):c=E[17];let D=R?n:"Loading...";E[18]!==D?(d=(0,h.jsx)(j.l,{flex:1,space:2,children:(0,h.jsx)(j.T,{size:1,weight:"semibold",children:D})}),E[18]=D,E[19]=d):d=E[19],E[20]!==c||E[21]!==d?(f=(0,h.jsxs)(j.F,{gap:2,padding:2,children:[c,d]}),E[20]=c,E[21]=d,E[22]=f):f=E[22],E[23]!==A||E[24]!==R||E[25]!==k?(p=R&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(j.i,{}),R.map((e,t)=>(0,h.jsx)(oJ,{node:e,onDismiss:k,boundaryElement:A},t))]}),E[23]=A,E[24]=R,E[25]=k,E[26]=p):p=E[26],E[27]!==f||E[28]!==p?(g=(0,h.jsxs)(j.m,{style:u,children:[f,p]}),E[27]=f,E[28]=p,E[29]=g):g=E[29];let N=`${I}-${O}`;return E[30]!==I||E[31]!==O?(v={position:"absolute",left:I,top:O},E[30]=I,E[31]=O,E[32]=v):v=E[32],E[33]!==N||E[34]!==v?(b=(0,h.jsx)("div",{style:v},N),E[33]=N,E[34]=v,E[35]=b):b=E[35],E[36]!==o||E[37]!==g||E[38]!==b?(w=(0,h.jsx)(j.n,{__unstable_margins:oX,arrow:!1,open:!0,placement:"right-start",referenceElement:o,content:g,children:b}),E[36]=o,E[37]=g,E[38]=b,E[39]=w):w=E[39],E[40]!==k||E[41]!==w?(x=(0,h.jsx)(j.o,{setBoundaryElement:M,onDismiss:k,children:w}),E[40]=k,E[41]=w,E[42]=x):x=E[42],x},oQ=(0,m.createContext)(null),o0=(0,j.d)(j.q)`
  background-color: var(--overlay-bg);
  border-radius: 3px;
  pointer-events: none;
  position: absolute;
  will-change: transform;
  box-shadow: var(--overlay-box-shadow);
  transition: none;

  --overlay-bg: transparent;
  --overlay-box-shadow: inset 0 0 0 1px transparent;

  [data-overlays] & {
    --overlay-bg: color-mix(in srgb, transparent 95%, var(--card-focus-ring-color));
    --overlay-box-shadow: inset 0 0 0 2px
      color-mix(in srgb, transparent 50%, var(--card-focus-ring-color));
  }

  [data-fading-out] & {
    transition:
      box-shadow 1550ms,
      background-color 1550ms;

    --overlay-bg: rgba(0, 0, 255, 0);
    --overlay-box-shadow: inset 0 0 0 1px transparent;
  }

  &[data-focused] {
    --overlay-box-shadow: inset 0 0 0 1px var(--card-focus-ring-color);
  }

  &[data-hovered]:not([data-focused]) {
    transition: none;
    --overlay-box-shadow: inset 0 0 0 2px var(--card-focus-ring-color);
  }

  /* [data-unmounted] & {
    --overlay-box-shadow: inset 0 0 0 1px var(--card-focus-ring-color);
  } */

  :link {
    text-decoration: none;
  }
`,o1=(0,j.d)(j.F)`
  bottom: 100%;
  cursor: pointer;
  pointer-events: none;
  position: absolute;
  right: 0;

  [data-hovered] & {
    pointer-events: all;
  }
`,o2=(0,j.d)(j.q)`
  background-color: var(--card-focus-ring-color);
  right: 0;
  border-radius: 3px;

  & [data-ui='Text'] {
    color: #fff;
    white-space: nowrap;
  }
`,o3=(0,j.d)(j.F)`
  bottom: 100%;
  cursor: pointer;
  pointer-events: none;
  position: absolute;
  left: 0;
`,o5=(0,j.d)(j.F)`
  display: flex;
  align-items: center;
  background-color: var(--card-focus-ring-color);
  right: 0;
  border-radius: 3px;
  & [data-ui='Text'],
  & [data-sanity-icon] {
    color: #fff;
    white-space: nowrap;
  }
`,o4=e=>{let t,n,r,i,o,a,s,l,u,c,d=(0,y.c)(35),{element:f,focused:p,componentResolver:g,node:v,showActions:b,draggable:w}=e,{getField:x,getType:E}=oq();d[0]!==E||d[1]!==v?(t=E(v),d[0]=E,d[1]=v,d[2]=t):t=d[2];let _=t;d[3]!==x||d[4]!==v?(n=x(v),d[3]=x,d[4]=v,d[5]=n):n=d[5];let{field:k,parent:S}=n;d[6]!==v?(r="path"in v?function(e){let{id:t,type:n,path:r,baseUrl:i,tool:o,workspace:a}=e;return function(e){let{baseUrl:t,workspace:n="default",tool:r="default",id:i,type:o,path:a,projectId:s,dataset:l}=e;if(!t)throw Error("baseUrl is required");if(!a)throw Error("path is required");if(!i)throw Error("id is required");if("/"!==t&&t.endsWith("/"))throw Error("baseUrl must not end with a slash");let u="default"===n?void 0:n,c="default"===r?void 0:r,d=i.startsWith(et)?i.slice(et.length):i,f=Array.isArray(a)?J(a.map(e=>{if("string"==typeof e||"number"==typeof e)return e;if(""!==e._key)return{_key:e._key};if(-1!==e._index)return e._index;throw Error(`invalid segment:${JSON.stringify(e)}`)})):a,p=new URLSearchParams({baseUrl:t,id:d,type:o,path:f});u&&p.set("workspace",u),c&&p.set("tool",c),s&&p.set("projectId",s),l&&p.set("dataset",l),i.startsWith(et)&&p.set("isDraft","");let h=["/"===t?"":t];u&&h.push(u);let y=["mode=presentation",`id=${d}`,`type=${o}`,`path=${encodeURIComponent(f)}`];return c&&y.push(`tool=${c}`),h.push("intent","edit",`${y.join(";")}?${p}`),h.join("/")}({baseUrl:i,workspace:a,tool:o,type:n,id:t,path:function(e){let t="";for(let n of e)"string"!=typeof n?"number"!=typeof n?null!==n&&Array.isArray(n)?(t&&(t+=":"),t+=`${n.join(",")}}`):n._key&&(t&&(t+=":"),t+=`${n._key}`):(t&&(t+=":"),t+=`${n}`):(t&&(t+="."),t+=n);return t}(ee.fromString(r))})}(v):v.href,d[6]=v,d[7]=r):r=d[7];let I=r,O=function(){let e=(0,m.useContext)(oQ);if(!e)throw Error("Preview Snapshots context is missing");return e}();e:{let e;if(!("path"in v)){i=void 0;break e}d[8]!==v.id||d[9]!==O?(e=O.find(e=>e._id===v.id)?.title,d[8]=v.id,d[9]=O,d[10]=e):e=d[10],i=e}let A=i;e:{let e;if(!("path"in v)||!k||!_){o=void 0;break e}let t=k.value.type,n=!!p;d[11]!==f||d[12]!==k||d[13]!==v||d[14]!==S||d[15]!==_||d[16]!==n||d[17]!==t?(e={document:_,element:f,field:k,focused:n,node:v,parent:S,type:t},d[11]=f,d[12]=k,d[13]=v,d[14]=S,d[15]=_,d[16]=n,d[17]=t,d[18]=e):e=d[18],o=e}let M=o,P=function(e,t){let n,r=(0,y.c)(4);e:{let i;if(!e){n=void 0;break e}if(r[0]!==e||r[1]!==t){let o=t?.(e);if(!o){n=void 0;break e}if((0,m.isValidElement)(o)){n=o;break e}i=(Array.isArray(o)?o:[o]).map(o6),r[0]=e,r[1]=t,r[2]=i,r[3]=n}else i=r[2],n=r[3];n=i}return n}(M,g);d[19]!==_?(a=_?.icon?(0,h.jsx)("div",{dangerouslySetInnerHTML:{__html:_.icon}}):(0,h.jsx)(j.D,{}),d[19]=_,d[20]=a):a=d[20];let $=a;return d[21]!==I||d[22]!==b?(s=b?(0,h.jsx)(o1,{gap:1,paddingBottom:1,"data-sanity-overlay-element":!0,children:(0,h.jsx)(o9,{href:I})}):null,d[21]=I,d[22]=b,d[23]=s):s=d[23],d[24]!==w||d[25]!==$||d[26]!==A?(l=A&&(0,h.jsx)(o3,{gap:1,paddingBottom:1,children:(0,h.jsxs)(o5,{gap:2,padding:2,children:[w&&(0,h.jsx)(j.j,{marginRight:1,children:(0,h.jsx)(j.T,{className:"drag-handle",size:0,children:(0,h.jsx)(j.r,{})})}),(0,h.jsx)(j.T,{size:0,children:$}),(0,h.jsx)(j.T,{size:1,weight:"medium",children:A})]})}),d[24]=w,d[25]=$,d[26]=A,d[27]=l):l=d[27],d[28]!==M||d[29]!==P?(u=Array.isArray(P)?P.map((e,t)=>{let{component:n,props:r}=e;return(0,h.jsx)(n,{PointerEvents:j.P,...M,...r},t)}):P,d[28]=M,d[29]=P,d[30]=u):u=d[30],d[31]!==s||d[32]!==l||d[33]!==u?(c=(0,h.jsxs)(h.Fragment,{children:[s,l,u]}),d[31]=s,d[32]=l,d[33]=u,d[34]=c):c=d[34],c},o8=(0,m.memo)(function(e){let t,n,r,i,o,a,s=(0,y.c)(17),{focused:l,hovered:u,rect:c,wasMaybeCollapsed:d,enableScrollIntoView:f}=e,p=(0,m.useRef)(null),g=(0,m.useRef)(!1),v=`${c.w}px`,b=`${c.h}px`,w=`translate(${c.x}px, ${c.y}px)`;s[0]!==v||s[1]!==b||s[2]!==w?(n={width:v,height:b,transform:w},s[0]=v,s[1]=b,s[2]=w,s[3]=n):n=s[3],t=n,s[4]!==f||s[5]!==l||s[6]!==d?(r=()=>{if(!g.current&&!d&&!0===l&&p.current&&f){let e=p.current;!function(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let n=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(tE(e,t));let r="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:i,top:o,left:a}of tE(e,!1===t?{block:"end",inline:"nearest"}:t===Object(t)&&0!==Object.keys(t).length?t:{block:"start",inline:"nearest"})){let e=o-n.top+n.bottom,t=a-n.left+n.right;i.scroll({top:e,left:t,behavior:r})}}(p.current,{behavior:t=>{0!==t.length&&e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},scrollMode:"if-needed",block:"center",inline:"nearest"})}g.current=!0===l},i=[l,d,f],s[4]=f,s[5]=l,s[6]=d,s[7]=r,s[8]=i):(r=s[7],i=s[8]),(0,m.useEffect)(r,i);let x=l?"":void 0,E=u?"":void 0;return s[9]!==u||s[10]!==e?(o=u&&(0,h.jsx)(o4,{...e}),s[9]=u,s[10]=e,s[11]=o):o=s[11],s[12]!==t||s[13]!==x||s[14]!==E||s[15]!==o?(a=(0,h.jsx)(o0,{"data-focused":x,"data-hovered":E,ref:p,style:t,children:o}),s[12]=t,s[13]=x,s[14]=E,s[15]=o,s[16]=a):a=s[16],a});function o6(e){return"object"==typeof e&&"component"in e?e:{component:e,props:{}}}let o9=(0,m.memo)(function(e){let t,n,r,i,o=(0,y.c)(6),a=(0,m.useSyncExternalStore)(o7,ae);return o[0]!==e.href||o[1]!==a?(n=function e(t,n){try{let r=new URL(t,typeof location>"u"?void 0:location.origin);if(r.hash){let t=new URL(e(r.hash.slice(1),n));return`${r.origin}${r.pathname}${r.search}#${t.pathname}${t.search}`}return r.searchParams.set("preview",n),r.toString()}catch{return t}}(e.href,a),o[0]=e.href,o[1]=a,o[2]=n):n=o[2],t=n,o[3]===Symbol.for("react.memo_cache_sentinel")?(r=(0,h.jsx)(o2,{padding:2,children:(0,h.jsx)(j.T,{size:1,weight:"medium",children:"Open in Studio"})}),o[3]=r):r=o[3],o[4]!==t?(i=(0,h.jsx)(j.j,{as:"a",href:t,target:"_blank",rel:"noopener noreferrer",children:r}),o[4]=t,o[5]=i):i=o[5],i});function o7(e){let t=()=>e();return window.addEventListener("popstate",t),()=>window.removeEventListener("popstate",t)}function ae(){return window.location.href}let at=e=>{let t,n=(0,y.c)(5),{dragGroupRect:r}=e,i=`${r.y}px`,o=`${r.x}px`,a=r.w-1+"px",s=r.h-1+"px";return n[0]!==i||n[1]!==o||n[2]!==a||n[3]!==s?(t=(0,h.jsx)("div",{style:{position:"absolute",top:i,left:o,width:a,height:s,border:"1px dashed #f0709b",pointerEvents:"none"}}),n[0]=i,n[1]=o,n[2]=a,n[3]=s,n[4]=t):t=n[4],t},an=({dragInsertPosition:e})=>{if(null===e)return;let t=0,n=0,r=0,i=0;if("horizontal"==(e?.left||e?.right?"horizontal":"vertical")){let{left:o,right:a}=e;if(r=6,a&&o){let e=o.rect.x+o.rect.w,r=a.rect.x,s=.0125*Math.min(a.rect.h,o.rect.h);t=.5*e+.5*r-3,n=o.rect.y+s,i=Math.min(a.rect.h,o.rect.h)-2*s}else if(a&&!o){let e=.0125*a.rect.h;t=a.rect.x-3,n=a.rect.y+e,i=a.rect.h-2*e}else if(o&&!a){let e=.0125*o.rect.h;t=o.rect.x+o.rect.w-3,n=o.rect.y+e,i=o.rect.h-2*e}}else{let{bottom:o,top:a}=e;if(o&&a){let e=Math.min(a.rect.x,o.rect.x),s=a.rect.y+a.rect.h,l=o.rect.y,u=.0125*Math.min(o.rect.w,a.rect.w);i=6,t=e+u,n=.5*s+.5*l-3,r=Math.max(o.rect.w,a.rect.w)-2*u}else if(o&&!a){let e=.0125*o.rect.w;t=o.rect.x+e,n=o.rect.y-3,r=o.rect.w-2*e,i=6}else if(a&&!o){let e=.0125*a.rect.w;t=a.rect.x+e,n=a.rect.y+a.rect.h-3,r=a.rect.w-2*e,i=6}}return(0,h.jsx)("div",{style:{position:"absolute",width:`${r}px`,height:`${i}px`,transform:`translate(${t}px, ${n}px)`,background:"#556bfc",border:"2px solid white",borderRadius:"999px",zIndex:"999999"}})},ar=j.d.div`
  --drag-preview-opacity: 0.98;
  --drag-preview-skeleton-stroke: #ffffff;

  @media (prefers-color-scheme: dark) {
    --drag-preview-skeleton-stroke: #383d51;
  }

  position: fixed;
  display: grid;
  transform: ${({$scaleFactor:e,$width:t,$height:n})=>`translate(calc(var(--drag-preview-x) - ${t/2}px), calc(var(--drag-preview-y) - ${n/2}px)) scale(${e})`};
  width: ${({$width:e})=>`${e}px`};
  height: ${({$height:e})=>`${e}px`};
  z-index: 9999999;
  opacity: var(--drag-preview-opacity);
  cursor: move;

  .drag-preview-content-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    container-type: inline-size;
  }

  [data-ui='card'] {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .drag-preview-skeleton {
    position: absolute;
    inset: 0;

    rect {
      stroke: var(--drag-preview-skeleton-stroke);
    }
  }

  .drag-preview-handle {
    position: absolute;
    top: 4cqmin;
    left: 4cqmin;
    width: 6cqmin;
    fill: var(--drag-preview-handle-fill);
  }
`,ai=e=>{var t,n;let r,i,o,a,s,l,u,c=(0,y.c)(24),{skeleton:d}=e,f=Math.min(d.maxWidth,window.innerWidth/2),p=d.w>f?f/d.w:1,m=d.offsetX*p,g=d.offsetY*p,v=(0,j.u)(),b=(0,j.s)(),w=b.radius[~~(n=(d.w-0)*((t=b.radius.length-2)-1)/1920+1,n<1?1:n>t?t:n)];c[0]!==d.childRects?(r=d.childRects.filter(ao),c[0]=d.childRects,c[1]=r):r=c[1];let x=r;c[2]!==d.childRects?(i=d.childRects.filter(aa),c[2]=d.childRects,c[3]=i):i=c[3];let E=i,_=d.w,k=d.h,S=v?"dark":"light",I=`0 0 ${d.w} ${d.h}`;return c[4]!==x||c[5]!==E?(o=[...x,...E],c[4]=x,c[5]=E,c[6]=o):o=c[6],c[7]!==o||c[8]!==b.color.skeleton.from?(a=o.map((e,t)=>(0,h.jsx)("rect",{x:e.x,y:e.y,width:e.w,height:e.h,fill:b.color.skeleton.from},t)),c[7]=o,c[8]=b.color.skeleton.from,c[9]=a):a=c[9],c[10]!==I||c[11]!==a?(s=(0,h.jsx)("div",{className:"drag-preview-content-wrapper",children:(0,h.jsx)("svg",{className:"drag-preview-skeleton",viewBox:I,children:a})}),c[10]=I,c[11]=a,c[12]=s):s=c[12],c[13]!==w||c[14]!==S||c[15]!==s?(l=(0,h.jsx)(j.q,{radius:w,shadow:4,overflow:"hidden",tone:"transparent",scheme:S,children:s}),c[13]=w,c[14]=S,c[15]=s,c[16]=l):l=c[16],c[17]!==m||c[18]!==g||c[19]!==p||c[20]!==d.h||c[21]!==d.w||c[22]!==l?(u=(0,h.jsx)(ar,{$width:_,$height:k,$offsetX:m,$offsetY:g,$scaleFactor:p,children:l}),c[17]=m,c[18]=g,c[19]=p,c[20]=d.h,c[21]=d.w,c[22]=l,c[23]=u):u=c[23],u};function ao(e){return"IMG"===e.tagName}function aa(e){return"IMG"!==e.tagName}let as=(0,j.d)(j.q)`
  position: fixed;
  bottom: 2rem;
  left: 2rem;
`,al=()=>{let e,t,n=(0,y.c)(2);return n[0]===Symbol.for("react.memo_cache_sentinel")?(e={zIndex:"999999"},n[0]=e):e=n[0],n[1]===Symbol.for("react.memo_cache_sentinel")?(t=(0,h.jsx)(as,{padding:2,shadow:2,radius:2,style:e,children:(0,h.jsxs)(j.F,{align:"center",gap:2,children:[(0,h.jsx)(j.H,{keys:["Shift"]}),(0,h.jsx)(j.T,{size:1,children:"Zoom Out"}),(0,h.jsx)(j.E,{})]})}),n[1]=t):t=n[1],t};function au(e,t){let{type:n}=t,{contextMenu:r,focusPath:i,perspective:o,isDragging:a,dragInsertPosition:s,dragShowMinimap:l,dragShowMinimapPrompt:u,dragSkeleton:c,dragMinimapTransition:d,dragGroupRect:f}=e,p=!1;if("presentation/focus"===n){let n=e.focusPath;n!==(i=t.data.path)&&(p=n.slice(i.length).startsWith("["))}return"presentation/perspective"===n&&(o=t.data.perspective),"element/contextmenu"===n&&(r="sanity"in t?{node:t.sanity,position:t.position}:null),("element/click"===n||"element/mouseleave"===n||"overlay/blur"===n||"presentation/blur"===n||"presentation/focus"===n)&&(r=null),"overlay/dragUpdateInsertPosition"===n&&(s=t.insertPosition),"overlay/dragStart"===n&&(a=!0),"overlay/dragUpdateSkeleton"===t.type&&(c=t.skeleton),"overlay/dragEnd"===n&&(a=!1),"overlay/dragToggleMinimapPrompt"===t.type&&(u=t.display),"overlay/dragStartMinimapTransition"===n&&(d=!0),"overlay/dragEndMinimapTransition"===n&&(d=!1),"overlay/dragUpdateGroupRect"===n&&(f=t.groupRect),"overlay/dragToggleMinimap"===n&&(l=t.display),{...e,contextMenu:r,elements:((e,t)=>{let{type:n}=t;switch(n){case"element/register":return e.find(e=>e.id===t.id)?e:[...e,{id:t.id,activated:!1,element:t.element,focused:!1,hovered:!1,rect:t.rect,sanity:t.sanity,dragDisabled:t.dragDisabled}];case"element/activate":return e.map(e=>e.id===t.id?{...e,activated:!0}:e);case"element/update":return e.map(e=>e.id===t.id?{...e,sanity:t.sanity,rect:t.rect}:e);case"element/unregister":return e.filter(e=>e.id!==t.id);case"element/deactivate":return e.map(e=>e.id===t.id?{...e,activated:!1,hovered:!1}:e);case"element/mouseenter":return e.map(e=>e.id===t.id?{...e,rect:t.rect,hovered:!0}:{...e,hovered:!1});case"element/mouseleave":return e.map(e=>e.id===t.id?{...e,hovered:!1}:e);case"element/updateRect":return e.map(e=>e.id===t.id?{...e,rect:t.rect}:e);case"element/click":return e.map(e=>({...e,focused:e.id===t.id&&"clicked"}));case"overlay/blur":case"presentation/blur":return e.map(e=>({...e,focused:!1}));case"presentation/focus":{let n=e.find(e=>"clicked"===e.focused);return e.map(e=>{let r="path"in e.sanity&&e.sanity.id===t.data.id&&e.sanity.path===t.data.path;return n&&e===n&&r?e:{...e,focused:r&&n?"duplicate":r}})}default:return e}})(e.elements,t),dragInsertPosition:s,dragSkeleton:c,dragGroupRect:f,isDragging:a,focusPath:i,perspective:o,wasMaybeCollapsed:p,dragShowMinimap:l,dragShowMinimapPrompt:u,dragMinimapTransition:d}}let ac=function(e){let t,n,r,i,o=(0,y.c)(7),{comlink:a,children:s}=e;o[0]===Symbol.for("react.memo_cache_sentinel")?(t=[],o[0]=t):t=o[0];let[l,u]=(0,m.useState)(t);return o[1]!==a?(n=()=>a?.on("presentation/preview-snapshots",e=>{u(e.snapshots)}),r=[a],o[1]=a,o[2]=n,o[3]=r):(n=o[2],r=o[3]),(0,m.useEffect)(n,r),o[4]!==s||o[5]!==l?(i=(0,h.jsx)(oQ.Provider,{value:l,children:s}),o[4]=s,o[5]=l,o[6]=i):i=o[6],i};function ad(e){return"document"===e.type}function af(e){return"type"===e.type}let ap=function(e){let t,n,r,i,o,a,s,l,u,c,d,f,p,g=(0,y.c)(27),{comlink:v,children:b,elements:w}=e;g[0]===Symbol.for("react.memo_cache_sentinel")?(t=new Map,g[0]=t):t=g[0];let[x,E]=(0,m.useState)(t),[_,k]=(0,m.useState)(null);g[1]!==v?(n=async e=>{if(v)try{let t=await v.fetch("visual-editing/schema",void 0,{signal:e,suppressWarnings:!0});k(t.schema)}catch{}},g[1]=v,g[2]=n):n=g[2];let j=n;g[3]!==v||g[4]!==j?(r=()=>{if(!v)return;let e=new AbortController,t=v.onStatus(()=>{j(e.signal)},"connected");return()=>{e.abort(),t()}},i=[v,j],g[3]=v,g[4]=j,g[5]=r,g[6]=i):(r=g[5],i=g[6]),(0,m.useEffect)(r,i),g[7]===Symbol.for("react.memo_cache_sentinel")?(o=[],g[7]=o):o=g[7];let S=(0,m.useRef)(o);g[8]!==v||g[9]!==w?(a=()=>{let e=new AbortController,t=w.reduce((e,t)=>{let{sanity:n}=t;if(!("id"in n)||!n.path.includes("[_key=="))return e;let r=n.path.split(".").toReversed().reduce((e,t)=>e.length?[t,...e]:t.includes("[_key==")?[t]:[],[]).join(".");return e.find(e=>e.id===n.id&&e.path===r)||e.push({id:n.id,path:r}),e},[]);return t.some(e=>!S.current.find(t=>{let{id:n,path:r}=t;return n===e.id&&r===e.path}))&&(async(e,t)=>{if(e.length&&v)try{let n=await v.fetch("visual-editing/schema-union-types",{paths:e},{signal:t,suppressWarnings:!0});E(n.types),S.current=e}catch{}})(t,e.signal),()=>e.abort()},s=[v,w],g[8]=v,g[9]=w,g[10]=a,g[11]=s):(a=g[10],s=g[11]),(0,m.useEffect)(a,s),g[12]!==_?(l=(e,t)=>{if(!_||"string"!=typeof e&&(!("path"in e)||!Array.isArray(_)))return;let n="string"==typeof e?e:e.type;return _.filter("document"===(t||"document")?ad:af).find(e=>e.name===n)},g[12]=_,g[13]=l):l=g[13];let I=l;g[14]!==I||g[15]!==x?(u=e=>{if(!("path"in e))return{field:void 0,parent:void 0};let t=I(e);if(!t)return{field:void 0,parent:void 0};let n=e.path.split(".").flatMap(ah);try{return function t(n,r,i,o){let a=void 0===o?[]:o;if(!n)return{field:void 0,parent:void 0};let[s,...l]=r;if("fields"in n){let e=n.fields[s];if(!e&&"rest"in n)return t(n.rest,r,n,a);if(!l.length)return{field:e,parent:i};if(!e)throw Error(`[@sanity/visual-editing] No field could be resolved at path: "${[...a,...r].join(".")}"`);return t(e.value,l,n,[...a,s])}if("array"===n.type)return t(n.of,r,n,a);if("arrayItem"===n.type)return l.length?t(n.value,l,n,[...a,s]):{field:n,parent:i};if("union"===n.type){let r=s.startsWith("[_key==")?x?.get(e.id)?.get([a.join("."),s].filter(Boolean).join("")):s;return t(n.of.find(e=>"unionOption"===e.type?e.name===r:e),l,n,[...a,s])}if("unionOption"===n.type)return s?t(n.value,r,n,a):{field:n,parent:i};if("inline"===n.type)return t(I(n.name,"type").value,r,n,a);throw Error(`[@sanity/visual-editing] No field could be resolved at path: "${[...a,...r].join(".")}"`)}(t,n,void 0)}catch(e){return e instanceof Error&&console.warn(e.message),{field:void 0,parent:void 0}}},g[14]=I,g[15]=x,g[16]=u):u=g[16];let O=u;return g[17]!==_?(d=_||[],g[17]=_,g[18]=d):d=g[18],g[19]!==O||g[20]!==I||g[21]!==x||g[22]!==d?(f={getField:O,getType:I,resolvedTypes:x,schema:d},g[19]=O,g[20]=I,g[21]=x,g[22]=d,g[23]=f):f=g[23],c=f,g[24]!==b||g[25]!==c?(p=(0,h.jsx)(oV.Provider,{value:c,children:b}),g[24]=b,g[25]=c,g[26]=p):p=g[26],p};function ah(e){return e.includes("[")?e.split(/(\[.+\])/,2):[e]}let ay=(e=>{let t=e,n=new Set;return{getState:()=>t,setState:e=>{t=e(t),n.forEach(e=>e())},subscribe:e=>(n.add(e),()=>n.delete(e))}})({}),am=e=>{let t,n,r,i,o,a,s,l=(0,y.c)(11),{comlink:u,children:c}=e;return l[0]!==u?(t=()=>u?.on("presentation/shared-state",ag),n=[u],l[0]=u,l[1]=t,l[2]=n):(t=l[1],n=l[2]),(0,m.useEffect)(t,n),l[3]!==u?(r=()=>{(async function(){let e=await u?.fetch("visual-editing/shared-state",void 0,{suppressWarnings:!0});e&&ay.setState(()=>e.state)})().catch(av)},i=[u],l[3]=u,l[4]=r,l[5]=i):(r=l[4],i=l[5]),(0,m.useEffect)(r,i),l[6]!==u?(a={comlink:u,store:ay},l[6]=u,l[7]=a):a=l[7],o=a,l[8]!==c||l[9]!==o?(s=(0,h.jsx)(tm.Provider,{value:o,children:c}),l[8]=c,l[9]=o,l[10]=s):s=l[10],s};function ag(e){"value"in e?ay.setState(t=>({...t,[e.key]:e.value})):ay.setState(t=>Object.fromEntries(Object.entries(t).filter(t=>{let[n]=t;return n!==e.key})))}function av(e){console.debug(e),console.warn("[@sanity/visual-editing]: Failed to fetch shared state. Check your version of `@sanity/presentation` is up-to-date")}let ab={"Visual Editing Drag Sequence Completed":{type:"log",name:(p={name:"Visual Editing Drag Sequence Completed",description:"An array is successfully reordered using drag and drop.",version:1}).name,version:p.version,description:p.description,schema:void 0}};function aw(){}function ax(e){return e.id}function aE(e){return!!e}function a_(e){let{sanity:t}=e;return"id"in t?{...t,id:"isDraft"in t?`${ef}${t.id}`:t.id}:null}let ak=j.d.div`
  background-color: transparent;
  direction: ltr;
  inset: 0;
  pointer-events: none;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: ${({$zIndex:e})=>e??"9999999"};
`;function aj(e){let t,n;return t=requestAnimationFrame(()=>{n=requestAnimationFrame(e)}),()=>{void 0!==t&&cancelAnimationFrame(t),void 0!==n&&cancelAnimationFrame(n)}}let aS=e=>{let t,n,r,i,o,a=(0,y.c)(8),{documentIds:s}=e;a[0]===Symbol.for("react.memo_cache_sentinel")?(t=[],a[0]=t):t=a[0];let[l,u]=(0,m.useState)(t);a[1]!==s?(n=()=>{u(e=>{let t=Array.from(new Set(s));return e.length===t.length&&0===e.reduce(aA,t)?.length?e:t})},r=[s],a[1]=s,a[2]=n,a[3]=r):(n=a[2],r=a[3]),(0,m.useEffect)(n,r);let c=eA();return a[4]!==c||a[5]!==l?(i=()=>{for(let e of l)c.send({type:"observe",documentId:eI(e)}),c.send({type:"observe",documentId:eO(e)});return()=>{for(let e of l)c.send({type:"unobserve",documentId:eI(e)}),c.send({type:"unobserve",documentId:eO(e)})}},o=[c,l],a[4]=c,a[5]=l,a[6]=i,a[7]=o):(i=a[6],o=a[7]),(0,m.useEffect)(i,o),null},aI=e=>{let t,n,i,o=(0,y.c)(11),{comlink:a,dispatch:s,inFrame:l,onDrag:u,overlayEnabled:c,rootElement:d}=e,{dispatchDragEndEvent:f}=function(){let e,t,n,r=(0,y.c)(4),{getDocument:i}=eR();return r[0]!==i?(e=()=>{let e=e=>{let{insertPosition:t,target:n,preventInsertDefault:r}=e.detail;if(r)return;let o=function(e){if(e){let{top:t,right:n,bottom:r,left:i}=e;if(i||t)return{node:(i??t).sanity,position:"after"};if(n||r)return{node:(n??r).sanity,position:"before"}}}(t);if(o){let e=i(n.id),{node:t,position:r}=o,{key:a,hasExplicitKey:s}=eN(n),{path:l,key:u}=eN(t);l&&u&&u!==a&&e.patch(async e=>{let{getSnapshot:t}=e,i=ek(await t(),n.path);return s?[z(l,F({_key:a})),z(l,D(i,r,{_key:u}))]:[z(l,F(~~a)),z(l,D(i,r,u>a?~~u-1:~~u))]})}};return window.addEventListener("sanity/dragEnd",e),()=>{window.removeEventListener("sanity/dragEnd",e)}},t=[i],r[0]=i,r[1]=e,r[2]=t):(e=r[1],t=r[2]),(0,m.useEffect)(e,t),r[3]===Symbol.for("react.memo_cache_sentinel")?(n={dispatchDragEndEvent:oW},r[3]=n):n=r[3],n}();o[0]!==a||o[1]!==s||o[2]!==f||o[3]!==u?(t=e=>{if("element/click"===e.type){let{sanity:t}=e;a?.post("visual-editing/focus",t)}else if("overlay/activate"===e.type)a?.post("visual-editing/toggle",{enabled:!0});else if("overlay/deactivate"===e.type)a?.post("visual-editing/toggle",{enabled:!1});else if("overlay/dragEnd"===e.type){let{insertPosition:t,target:n,dragGroup:r,flow:i,preventInsertDefault:o}=e;f({insertPosition:t,target:n,dragGroup:r,flow:i,preventInsertDefault:o}),t&&function(e,t,n){if(!n)return;let r=ab[e];if(!r)throw Error(`Telemetry event: ${e} does not exist`);n.post("visual-editing/telemetry-log",{event:r,data:null})}("Visual Editing Drag Sequence Completed",0,a)}else{if("overlay/dragUpdateCursorPosition"===e.type)return void u(e.x,e.y);if("overlay/setCursor"===e.type){let{element:t,cursor:n}=e;n?t.style.cursor=n:t.style.removeProperty("cursor")}}s(e)},o[0]=a,o[1]=s,o[2]=f,o[3]=u,o[4]=t):t=o[4];let p=function(e,t,n){let i,o,a=(0,y.c)(6),s=(0,m.useRef)(),l=eA()!==W;return a[0]!==e||a[1]!==t||a[2]!==n||a[3]!==l?(i=()=>{if(e)return s.current=function({handler:e,overlayElement:t,inFrame:n,optimisticActorReady:i}){let o=!1,a=new Map,s=new WeakMap,l=new Set,u=new WeakMap,c=new WeakMap,d,f,p,h=!1,y=[],m=()=>y[y.length-1];function g(e,t){e.removeEventListener("click",t.click,{capture:!0}),e.removeEventListener("contextmenu",t.contextmenu,{capture:!0}),e.removeEventListener("mousemove",t.mousemove,{capture:!0}),e.removeEventListener("mousedown",t.mousedown,{capture:!0}),e.removeEventListener("mouseenter",t.mouseenter),e.removeEventListener("mouseleave",t.mouseleave)}function v({id:t,elements:n,handlers:r}){let{element:i,measureElement:o}=n;i.addEventListener("click",r.click,{capture:!0}),i.addEventListener("contextmenu",r.contextmenu,{capture:!0}),i.addEventListener("mousemove",r.mousemove,{once:!0,capture:!0}),i.addEventListener("mousedown",r.mousedown,{capture:!0}),d.observe(o),e({type:"element/activate",id:t})}function b({id:t,elements:n,handlers:r}){let{element:i,measureElement:o}=n;g(i,r),d.unobserve(o),y=y.filter(e=>e!==i),e({type:"element/deactivate",id:t})}function w(t){if(n&&i)for(let n of y){if(t===n){let n=s.get(t)?.sanity;if(!n||!td(n))return;if(ty(t,n,l,s)){let n=t.style.cursor;n&&c.set(t,n),e({type:"overlay/setCursor",element:t,cursor:"move"});continue}}x(n)}}function x(t){let n=c.get(t);e({type:"overlay/setCursor",element:t,cursor:n})}function E(c){for(let d of function e(t){let n=[];function r(e,t){let r=function(e){if("object"==typeof e&&null!==e)return eg(e);try{return eg(JSON.parse(e))}catch{let t=e.split(";").reduce((e,t)=>{let[n,r]=t.split("=");if(!n||t.includes("=")&&!r)return e;switch(n){case"id":e.id=r;break;case"type":e.type=r;break;case"path":e.path=ee.toString(function(e){let t=[];for(let n of e.split(".")){let e=eu.exec(n);if(e){t.push(e[1],Number(e[2]));continue}let r=ec.exec(n);if(r){t.push(r[1],[Number(r[2]),Number(r[3])]);continue}let i=ed.exec(n);i?t.push(i[1],{_key:i[2]}):t.push(n)}return t}(r));break;case"base":e.baseUrl=decodeURIComponent(r);break;case"tool":e.tool=r;break;case"workspace":e.workspace=r;break;case"projectId":e.projectId=r;break;case"dataset":e.dataset=r;break;case"isDraft":e.isDraft=""}return e},{});if(!ey._run({typed:!1,value:t},{abortEarly:!0}).issues)return t;return}}(t);if(!r)return;let i=function e(t){let{display:n}=window.getComputedStyle(t);if("inline"!==n)return t;let r=t.parentElement;return r?e(r):null}(e);i&&n.push({elements:{element:e,measureElement:i},sanity:r})}if(t)for(let i of t.childNodes){let{nodeType:t,parentElement:o,textContent:a}=i;if(ts(i)&&void 0!==i.dataset?.sanityEditTarget){let t=e(i).map(({sanity:e})=>e);if(!t.map(e=>td(e)).every((e,t,n)=>e===n[0]))continue;let r=function(e){if(!e.length||!e.map(e=>td(e)).every((e,t,n)=>e===n[0]))return;if(!td(e[0]))return e[0];let t=e.filter(td),n=e[0],r=["projectId","dataset","id","baseUrl","workspace","tool"];for(let e=1;e<t.length;e++){let i=t[e];if(r.some(e=>i[e]!==n?.[e])){n=void 0;break}n.path=function(e,t){let n=e.split("."),r=t.split("."),i=Math.min(n.length,r.length);return n=n.slice(0,i).reverse(),r=r.slice(0,i).reverse(),n.reduce((e,t,n)=>t===r[n]?[...e,t]:[],[]).reverse().join(".")}(n.path,i.path)}return n}(t);r&&n.push({elements:{element:i,measureElement:i},sanity:r})}else if(t===Node.TEXT_NODE&&o&&a){let e=ta(a);if(!e)continue;r(o,e)}else if(ts(i)){if("SCRIPT"===i.tagName||"SANITY-VISUAL-EDITING"===i.tagName)continue;if(i.dataset?.sanity)r(i,i.dataset.sanity);else if(i.dataset?.sanityEditInfo)r(i,i.dataset.sanityEditInfo);else{if(tl(i)){let e=ta(i.alt,!0);if(!e)continue;r(i,e);continue}if(tu(i)){let e=ta(i.dateTime,!0);if(!e)continue;r(i,e)}else if(tc(i)){if(!i.ariaLabel)continue;let e=ta(i.ariaLabel,!0);if(!e)continue;r(i,e)}}n.push(...e(i))}}return n}(c)){let{element:c}=d.elements;s.has(c)?function({elements:t,sanity:n}){let{element:r}=t,i=s.get(r);i&&(s.set(r,{...i,sanity:n}),e({type:"element/update",id:i.id,rect:eX(r),sanity:n}))}(d):function({elements:c,sanity:d}){let{element:p,measureElement:g}=c,b={click(t){let r=t.target;if(p===m()&&p.contains(r)){n&&(t.preventDefault(),t.stopPropagation());let r=s.get(p)?.sanity;r&&!h&&e({type:"element/click",id:E,sanity:r})}},contextmenu(t){if(!("path"in d&&n&&i&&d.path.split(".").pop()?.includes("[_key==")))return;let r=t.target;p===m()&&p.contains(r)&&(n&&(t.preventDefault(),t.stopPropagation()),e({type:"element/contextmenu",id:E,position:{x:t.clientX,y:t.clientY},sanity:d}))},mousedown(t){if(t.preventDefault(),t.currentTarget!==y.at(-1)||p.getAttribute("data-sanity-drag-disable")||!n||!i)return;let r=s.get(p)?.sanity;if(!r||!td(r)||!tf(r.path))return;let o=ty(p,d,l,s);o&&function(e){var t;let{mouseEvent:n,element:r,overlayGroup:i,handler:o,target:a,onSequenceStart:s,onSequenceEnd:l}=e;if(0!==n.button)return;window.focus();let u=i.map(e=>eX(e.elements.element)),c=r.getAttribute("data-sanity-drag-flow")||((t=u).some(e=>t.filter(t=>!eZ(e,t)).some(t=>e.y===t.y))?"horizontal":"vertical"),d=r.getAttribute("data-sanity-drag-group"),f=!!r.getAttribute("data-sanity-drag-minimap-disable"),p=!!r.getAttribute("data-sanity-drag-prevent-default"),h=r.getAttribute("data-unstable_sanity-drag-document-height"),y=r.getAttribute("data-unstable_sanity-drag-group-height"),m=null,g=e4(n),v=document.body,{minYScaled:b,scaleFactor:w}=function(e,t){let n=t||e2(e).height,r=(n+=200)>window.innerHeight?window.innerHeight/n:1,{min:i}=e2(e.map(e=>e1(e,r,{x:window.innerWidth/2,y:0})));return{scaleFactor:r,minYScaled:i-100*r}}(u,y?~~y:null),x=!1,E=!1,_=!0;e7||(tr={body:{overflow:window.getComputedStyle(document.body).overflow,height:window.getComputedStyle(document.body).height},documentElement:{overflow:window.getComputedStyle(document.documentElement).overflow,height:window.getComputedStyle(document.documentElement).height}},tn=h?~~h:document.documentElement.scrollHeight);let k=setInterval(()=>{u=i.map(e=>eX(e.elements.element))},150),j=()=>{w>=1||(o({type:"overlay/dragUpdateSkeleton",skeleton:e8(tt,r,w)}),o({type:"overlay/dragToggleMinimapPrompt",display:!1}),o({type:"overlay/dragToggleMinimap",display:!0}),e7=!0,(async function(e,t,n,r,i){return new Promise(i=>{e.addEventListener("transitionend",()=>{setTimeout(()=>{r({type:"overlay/dragEndMinimapTransition"})},300),i()},{once:!0}),r({type:"overlay/dragStartMinimapTransition"}),r({type:"overlay/dragToggleMinimap",display:!0}),document.body.style.overflow="hidden",document.body.style.height="100%",document.documentElement.style.overflow="initial",document.documentElement.style.height="100%",setTimeout(()=>{e.style.transformOrigin="50% 0px",e.style.transition="transform 150ms ease",e.style.transform=`translate3d(0px, ${-n+scrollY}px, 0px) scale(${t})`},25)})})(v,w,b,o,0).then(()=>{setTimeout(()=>{o({type:"overlay/dragUpdateGroupRect",groupRect:e6(u)})},300)}))},S=e=>{Math.abs(e.deltaY)>=10&&w<1&&!e7&&!E&&!f&&_&&(o({type:"overlay/dragToggleMinimapPrompt",display:!0}),E=!0),!e.shiftKey||e7||f||(window.dispatchEvent(new CustomEvent("unstable_sanity/dragApplyMinimap")),setTimeout(()=>{j()},50))},I=e=>{if(e.preventDefault(),te=function(e){let t=document.body,n=window.getComputedStyle(t).transform;if("none"===n)return{x:e.x,y:e.y};let r=new DOMMatrix(n).inverse(),i=new DOMPoint(e.x,e.y).matrixTransform(r);return{x:i.x,y:i.y}}(tt=e4(e)),4>Math.abs(eQ(tt,g)))return;if(!x){let e=e6(u),t=e8(tt,r,1);o({type:"overlay/dragStart",flow:c}),o({type:"overlay/dragUpdateSkeleton",skeleton:t}),o({type:"overlay/dragUpdateGroupRect",groupRect:e}),x=!0,s()}o({type:"overlay/dragUpdateCursorPosition",x:tt.x,y:tt.y}),!e.shiftKey||e7||f||(window.dispatchEvent(new CustomEvent("unstable_sanity/dragApplyMinimap")),setTimeout(()=>{j()},50));let t=function(e,t,n){if("horizontal"===n){let r={x1:e.x,y1:e.y,x2:e.x-1e8,y2:e.y},i={x1:e.x,y1:e.y,x2:e.x+1e8,y2:e.y};return{left:e0(r,t,n),right:e0(i,t,n)}}{let r={x1:e.x,y1:e.y,x2:e.x,y2:e.y-1e8},i={x1:e.x,y1:e.y,x2:e.x,y2:e.y+1e8};return{top:e0(r,t,n),bottom:e0(i,t,n)}}}(tt,u,c);JSON.stringify(m)!==JSON.stringify(t)&&o({type:"overlay/dragUpdateInsertPosition",insertPosition:e5(i,m=t,c)})},O=()=>{_=!1,o({type:"overlay/dragEnd",target:a,insertPosition:m?e5(i,m,c):null,dragGroup:d,flow:c,preventInsertDefault:p}),E&&o({type:"overlay/dragToggleMinimapPrompt",display:!1}),e7||(clearInterval(k),l(),C(),$()),P()},A=e=>{"Shift"===e.key&&e7&&(e7=!1,o({type:"overlay/dragUpdateSkeleton",skeleton:e8(tt,r,1/w)}),window.dispatchEvent(new CustomEvent("unstable_sanity/dragResetMinimap")),setTimeout(()=>{e9(te.y,v,tn,o,150,tr)},50),o({type:"overlay/dragUpdateGroupRect",groupRect:null}),_||(clearInterval(k),P(),C(),$(),l()))},M=()=>{o({type:"overlay/dragUpdateGroupRect",groupRect:null}),window.dispatchEvent(new CustomEvent("unstable_sanity/dragResetMinimap")),setTimeout(()=>{e9(te.y,v,tn,o,150,tr).then(()=>{e7=!1})},50),clearInterval(k),P(),C(),$(),l()},P=()=>{window.removeEventListener("mousemove",I),window.removeEventListener("wheel",S),window.removeEventListener("mouseup",O)},$=()=>{window.removeEventListener("keyup",A)},C=()=>{window.removeEventListener("blur",M)};window.addEventListener("blur",M),window.addEventListener("keyup",A),window.addEventListener("wheel",S),window.addEventListener("mousemove",I),window.addEventListener("mouseup",O)}({element:p,handler:e,mouseEvent:t,overlayGroup:o,target:r,onSequenceStart:()=>{h=!0},onSequenceEnd:()=>{setTimeout(()=>{h=!1},250)}})},mousemove(e){b.mouseenter(e);let t=e.currentTarget;t&&(t.addEventListener("mouseenter",b.mouseenter),t.addEventListener("mouseleave",b.mouseleave))},mouseenter(){document.querySelector("vercel-live-feedback")&&p.closest("[data-vercel-edit-info]")||p.closest("[data-vercel-edit-target]")||(y.push(p),e({type:"element/mouseenter",id:E,rect:eX(p)}),w(p))},mouseleave(n){function r(){y.pop();let t=m();if(e({type:"element/mouseleave",id:E}),t){w(t);let n=s.get(t);n&&e({type:"element/mouseenter",id:n.id,rect:eX(t)})}x(p)}let{relatedTarget:i}=n,o=to(i),a=t.contains(o);if(ti(o)&&a)return function e(t){let n=i=>{let{relatedTarget:o}=i;to(o)?o&&ti(o)&&(t.removeEventListener("mouseleave",n),e(o)):(t.removeEventListener("mouseleave",n),r())};t.addEventListener("mouseleave",n)}(o);r()}},E=function(e,t,n){if(eY.randomUUID&&!e)return eY.randomUUID();let i=(e=e||{}).random||(e.rng||function(){if(!r){if(typeof crypto>"u"||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(eK)})();return i[6]=15&i[6]|64,i[8]=63&i[8]|128,function(e,t=0){return(eH[e[t+0]]+eH[e[t+1]]+eH[e[t+2]]+eH[e[t+3]]+"-"+eH[e[t+4]]+eH[e[t+5]]+"-"+eH[e[t+6]]+eH[e[t+7]]+"-"+eH[e[t+8]]+eH[e[t+9]]+"-"+eH[e[t+10]]+eH[e[t+11]]+eH[e[t+12]]+eH[e[t+13]]+eH[e[t+14]]+eH[e[t+15]]).toLowerCase()}(i)}(),_={id:E,elements:c,sanity:d,handlers:b};l.add(p),u.set(g,p),a.set(E,p),s.set(p,_),f?.observe(p),e({type:"element/register",id:E,element:p,rect:eX(p),sanity:d,dragDisabled:!!p.getAttribute("data-sanity-drag-disable")}),o&&v(_)}(d)}}function _(t){let n=s.get(t);if(n){let{id:r,handlers:i}=n;g(t,i),d.unobserve(t),s.delete(t),l.delete(t),a.delete(r),e({type:"element/unregister",id:r})}}function k(t){let n=s.get(t);n&&e({type:"element/updateRect",id:n.id,rect:eX(t)})}function j(e){if(o)for(let t of e){let{target:e}=t,n=ti(e)&&s.get(e);n&&(t.isIntersecting?v(n):b(n))}}function S(t){let n=to(t.target);n?"capture"===n.dataset.sanityOverlayElement&&(t.preventDefault(),t.stopPropagation()):(y=[],e({type:"overlay/blur"}))}function I(){for(let e of l)k(e)}function O(t){"Escape"===t.key&&(y=[],e({type:"overlay/blur"}))}function A(e){let{target:t}=e;if(t!==window.document&&ti(t))for(let e of l)t.contains(e)&&k(e)}function M(){o||(f=new IntersectionObserver(j,{threshold:.3}),l.forEach(e=>f.observe(e)),e({type:"overlay/activate"}),o=!0)}function P(){o&&(f?.disconnect(),l.forEach(e=>{let t=s.get(e);t&&b(t)}),e({type:"overlay/deactivate"}),o=!1)}return window.document.fonts.ready.then(()=>{for(let e of l)k(e)}),window.addEventListener("click",S),window.addEventListener("contextmenu",S),window.addEventListener("keydown",O),window.addEventListener("resize",I),window.addEventListener("scroll",A,{capture:!0,passive:!0}),d=new ResizeObserver(function(e){for(let t of e){let e=t.target;if(ti(e)){let t=u.get(e);if(!t)return;k(t)}}}),(p=new MutationObserver(function(e){let n=!1;for(let r of e){let{target:e,type:i}=r,o="characterData"===i?e.parentElement:e;o===t||t.contains(o)||(n=!0,ti(o)&&E({childNodes:[o]}))}if(n)for(let e of l)e.isConnected||_(e)})).observe(document.body,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),E(document.body),M(),{activate:M,deactivate:P,destroy:function(){window.removeEventListener("click",S),window.removeEventListener("contextmenu",S),window.removeEventListener("keydown",O),window.removeEventListener("resize",I),window.removeEventListener("scroll",A),p.disconnect(),d.disconnect(),l.forEach(e=>{_(e)}),a.clear(),l.clear(),y=[],P()}}}({handler:t,overlayElement:e,inFrame:n,optimisticActorReady:l}),()=>{s.current?.destroy(),s.current=void 0}},o=[e,t,n,l],a[0]=e,a[1]=t,a[2]=n,a[3]=l,a[4]=i,a[5]=o):(i=a[4],o=a[5]),(0,m.useEffect)(i,o),s}(d,t,l);return o[5]!==p.current||o[6]!==c?(n=()=>{c?p.current?.activate():p.current?.deactivate()},o[5]=p.current,o[6]=c,o[7]=n):n=o[7],o[8]!==p||o[9]!==c?(i=[p,c],o[8]=p,o[9]=c,o[10]=i):i=o[10],(0,m.useEffect)(n,i),null},aO=e=>{let t,n,r,i,o,a,s,l,u,c,d,f,p,g,v,b,w,x,E,S,I,O,A,M,P=(0,y.c)(70),{comlink:$,componentResolver:C,inFrame:T,zIndex:R}=e,[L,D]=(0,m.useState)(),N=(0,j.u)();P[0]===Symbol.for("react.memo_cache_sentinel")?(t={contextMenu:null,dragInsertPosition:null,dragShowMinimap:!1,dragShowMinimapPrompt:!1,dragSkeleton:null,elements:[],focusPath:"",isDragging:!1,perspective:"published",wasMaybeCollapsed:!1,dragMinimapTransition:!1,dragGroupRect:null},P[0]=t):t=P[0];let[F,z]=(0,m.useReducer)(au,t),{contextMenu:U,dragInsertPosition:V,dragShowMinimap:q,dragShowMinimapPrompt:B,dragSkeleton:G,elements:H,isDragging:K,perspective:Y,wasMaybeCollapsed:X,dragMinimapTransition:J,dragGroupRect:Z}=F,[Q,ee]=(0,m.useState)(null),[et,en]=(0,m.useState)(!0);P[1]!==$?(n=()=>{let e=[$?.on("presentation/focus",e=>{z({type:"presentation/focus",data:e})}),$?.on("presentation/blur",e=>{z({type:"presentation/blur",data:e})}),$?.on("presentation/toggle-overlay",()=>{en(aM)}),$?.onStatus(e=>{D(e)})].filter(Boolean);return()=>e.forEach(aP)},r=[$],P[1]=$,P[2]=n,P[3]=r):(n=P[2],r=P[3]),(0,m.useEffect)(n,r),function(e,t){let n,r,i=(0,y.c)(4);i[0]!==e||i[1]!==t?(n=()=>{let n=new AbortController;e?.fetch("visual-editing/fetch-perspective",void 0,{signal:n.signal,suppressWarnings:!0}).then(e=>{t({type:"presentation/perspective",data:e})}).catch(aw);let r=e?.on("presentation/perspective",e=>{t({type:"presentation/perspective",data:e})});return()=>{r?.(),n.abort()}},r=[e,t],i[0]=e,i[1]=t,i[2]=n,i[3]=r):(n=i[2],r=i[3]),(0,m.useEffect)(n,r)}($,z),function(e,t,n){let r,i,o,a=(0,y.c)(7),s=(0,m.useRef)(void 0);a[0]!==e?(r=(t,n)=>{e?.post("visual-editing/documents",{documents:t,perspective:n})},a[0]=e,a[1]=r):r=a[1];let l=r;a[2]!==t||a[3]!==n||a[4]!==l?(i=()=>{let e=t.map(a_).filter(aE),r=new Set(e.map(ax));if(!s.current||!function(e,t){if(e===t)return!0;if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}(r,s.current.nodeIds)||n!==s.current.perspective){let t=Array.from(r).map(t=>{let{type:n,projectId:r,dataset:i}=e.find(e=>e.id===t);return r&&i?{_id:t,_type:n,_projectId:r,_dataset:i}:{_id:t,_type:n}});s.current={nodeIds:r,perspective:n},l(t,n)}},o=[t,n,l],a[2]=t,a[3]=n,a[4]=l,a[5]=i,a[6]=o):(i=a[5],o=a[6]),(0,m.useEffect)(i,o)}($,H,Y),P[4]!==Q?(i=(e,t)=>{Q&&(Q.style.setProperty("--drag-preview-x",`${e}px`),Q.style.setProperty("--drag-preview-y",t-window.scrollY+"px"))},P[4]=Q,P[5]=i):i=P[5];let er=i;P[6]===Symbol.for("react.memo_cache_sentinel")?(o=()=>{let e=e=>{k(e)&&en(aC)},t=e=>{k(e)&&en(aT),function(e,t){return e.every(e=>_[e]?t[_[e]]:t.key===e.toUpperCase())}(["mod","\\"],e)&&en(aR)};return window.addEventListener("click",a$),window.addEventListener("keydown",t),window.addEventListener("keyup",e),()=>{window.removeEventListener("click",a$),window.removeEventListener("keydown",t),window.removeEventListener("keyup",e)}},a=[en],P[6]=o,P[7]=a):(o=P[6],a=P[7]),(0,m.useEffect)(o,a);let[ei,eo]=(0,m.useState)(!1),[ea,es]=(0,m.useState)(!1),el=(0,m.useRef)(void 0);P[8]!==et?(s=()=>{if(et)return aj(()=>{eo(!0),aj(()=>{es(!0),el.current=setTimeout(()=>{es(!1),eo(!1)},1500)})});el.current&&(clearTimeout(el.current),eo(!1),es(!1))},l=[et],P[8]=et,P[9]=s,P[10]=l):(s=P[9],l=P[10]),(0,m.useEffect)(s,l),P[11]!==H?(c=H.flatMap(aL),P[11]=H,P[12]=c):c=P[12],u=c,P[13]===Symbol.for("react.memo_cache_sentinel")?(d=()=>{z({type:"overlay/blur"})},P[13]=d):d=P[13];let eu=d,ec=eA()!==W;f=ec?C:void 0;e:{let e;if(T&&"connected"!==L||K){let e;P[14]===Symbol.for("react.memo_cache_sentinel")?(e=[],P[14]=e):e=P[14],p=e;break e}P[15]!==f||P[16]!==J||P[17]!==q||P[18]!==H||P[19]!==T||P[20]!==K||P[21]!==ec||P[22]!==X?(e=H.filter(aD).map(e=>{let{id:t,element:n,focused:r,hovered:i,rect:o,sanity:a,dragDisabled:s}=e,l=!s&&!!n.getAttribute("data-sanity")&&ec&&H.some(e=>"id"in e.sanity&&"id"in a&&th(e.sanity,a)&&e.sanity.path!==a.path);return(0,h.jsx)(o8,{componentResolver:f,element:n,enableScrollIntoView:!K&&!J&&!q,focused:r,hovered:i,node:a,rect:o,showActions:!T,draggable:l,isDragging:K||J,wasMaybeCollapsed:r&&X},t)}),P[15]=f,P[16]=J,P[17]=q,P[18]=H,P[19]=T,P[20]=K,P[21]=ec,P[22]=X,P[23]=e):e=P[23],p=e}let ed=p,ef=N?"dark":"light",ep=ea?"":void 0,eh=ei?"":void 0;return P[24]!==u||P[25]!==Y?(g=(0,h.jsx)(aS,{documentIds:u,perspective:Y}),P[24]=u,P[25]=Y,P[26]=g):g=P[26],P[27]!==$||P[28]!==T||P[29]!==et||P[30]!==Q||P[31]!==er?(v=(0,h.jsx)(aI,{comlink:$,dispatch:z,inFrame:T,onDrag:er,overlayEnabled:et,rootElement:Q}),P[27]=$,P[28]=T,P[29]=et,P[30]=Q,P[31]=er,P[32]=v):v=P[32],P[33]!==U?(b=U&&(0,h.jsx)(oZ,{...U,onDismiss:eu}),P[33]=U,P[34]=b):b=P[34],P[35]!==Z||P[36]!==V||P[37]!==J||P[38]!==B||P[39]!==K?(w=K&&!J&&(0,h.jsxs)(h.Fragment,{children:[V&&(0,h.jsx)(an,{dragInsertPosition:V}),B&&(0,h.jsx)(al,{}),Z&&(0,h.jsx)(at,{dragGroupRect:Z})]}),P[35]=Z,P[36]=V,P[37]=J,P[38]=B,P[39]=K,P[40]=w):w=P[40],P[41]!==G||P[42]!==K?(x=K&&G&&(0,h.jsx)(ai,{skeleton:G}),P[41]=G,P[42]=K,P[43]=x):x=P[43],P[44]!==ed||P[45]!==ep||P[46]!==eh||P[47]!==g||P[48]!==v||P[49]!==b||P[50]!==w||P[51]!==x||P[52]!==R?(E=(0,h.jsxs)(ak,{"data-fading-out":ep,"data-overlays":eh,ref:ee,$zIndex:R,children:[g,v,b,ed,w,x]}),P[44]=ed,P[45]=ep,P[46]=eh,P[47]=g,P[48]=v,P[49]=b,P[50]=w,P[51]=x,P[52]=R,P[53]=E):E=P[53],P[54]!==$||P[55]!==E?(S=(0,h.jsx)(am,{comlink:$,children:E}),P[54]=$,P[55]=E,P[56]=S):S=P[56],P[57]!==$||P[58]!==S?(I=(0,h.jsx)(ac,{comlink:$,children:S}),P[57]=$,P[58]=S,P[59]=I):I=P[59],P[60]!==$||P[61]!==H||P[62]!==I?(O=(0,h.jsx)(ap,{comlink:$,elements:H,children:I}),P[60]=$,P[61]=H,P[62]=I,P[63]=O):O=P[63],P[64]!==Q||P[65]!==O?(A=(0,h.jsx)(j.L,{children:(0,h.jsx)(j.t,{element:Q,children:O})}),P[64]=Q,P[65]=O,P[66]=A):A=P[66],P[67]!==ef||P[68]!==A?(M=(0,h.jsx)(j.v,{scheme:ef,theme:j.w,tone:"transparent",children:A}),P[67]=ef,P[68]=A,P[69]=M):M=P[69],M};function aA(e,t){return e.filter(e=>e!==t)}function aM(e){return!e}function aP(e){return e()}function a$(e){let t=e.target;if(((0,j.x)(t)||(0,j.y)(t)&&t.closest("a"))&&e.altKey){e.preventDefault(),e.stopPropagation();let t=new MouseEvent(e.type,{...e,altKey:!1,bubbles:!0,cancelable:!0});e.target?.dispatchEvent(t)}}function aC(e){return!e}function aT(e){return!e}function aR(e){return!e}function aL(e){return"id"in e.sanity?[e.sanity.id]:[]}function aD(e){return e.activated||e.focused}let aN=e=>{let t,n,r=(0,y.c)(4),{comlink:i,refresh:o}=e,a=(0,m.useRef)(0),s=(0,m.useRef)(0);return r[0]!==i||r[1]!==o?(t=()=>i.on("presentation/refresh",e=>{if("manual"===e.source){let t;clearTimeout(a.current);let n=o(e);!1!==n&&(i.post("visual-editing/refreshing",e),t=!1,a.current=window.setTimeout(()=>{i.post("visual-editing/refreshed",e),t=!0},3e3),n?.finally?.(()=>{t||(clearTimeout(a.current),i.post("visual-editing/refreshed",e))}))}else if("mutation"===e.source){clearTimeout(s.current);let t=o(e);if(!1===t)return;i.post("visual-editing/refreshing",e),s.current=window.setTimeout(()=>{let t=o(e);!1!==t&&(i.post("visual-editing/refreshing",e),t?.finally?.(()=>{i.post("visual-editing/refreshed",e)})||i.post("visual-editing/refreshed",e))},1e3),t?.finally?.(()=>{i.post("visual-editing/refreshed",e)})||i.post("visual-editing/refreshed",e)}}),n=[i,o],r[0]=i,r[1]=o,r[2]=t,r[3]=n):(t=r[2],n=r[3]),(0,m.useEffect)(t,n),null};function aF(){console.warn("[@sanity/visual-editing] Package version mismatch detected: Please update your Sanity studio to prevent potential compatibility issues.")}function az(){}let aU=e=>{let t,n,r,i,o,a,s,l,u=(0,y.c)(20),{components:c,history:d,portal:f,refresh:p,zIndex:b}=e,w=void 0===f||f,[_,k]=(0,m.useState)(null);u[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>k(window.self!==window.top||!!window.opener),n=[],u[0]=t,u[1]=n):(t=u[0],n=u[1]),(0,m.useEffect)(t,n);let[j,S]=(0,m.useState)(null);u[2]!==w?(r=()=>{if(!1===w)return;let e=document.createElement("sanity-visual-editing");return document.documentElement.appendChild(e),S(e),()=>{S(null),document.documentElement.contains(e)&&document.documentElement.removeChild(e)}},i=[w],u[2]=w,u[3]=r,u[4]=i):(r=u[3],i=u[4]),(0,m.useEffect)(r,i);let I=function(e){let t,n,r=(0,y.c)(3),i=void 0===e||e,[o,a]=(0,m.useState)();return r[0]!==i?(t=()=>{let e;if(!i)return;let t=(0,v.RM)({name:"visual-editing",connectTo:"presentation"},(0,v.Uc)().provide({actors:{listen:(0,v.CC)(x),requestMachine:(0,v.tP)().provide({actions:{"send message":E}})}}));e=0;let n=t.start(),r=t.onStatus(()=>{e=window.setTimeout(()=>{a(t)},3e3)},"connected");return()=>{clearTimeout(e),r(),n(),a(void 0)}},n=[i],r[0]=i,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,m.useEffect)(t,n),o}(!0===_);(function(e){let t,n,r=(0,y.c)(3);r[0]!==e?(t=()=>{if(!e)return;let t=function(e){let t=new tC(1),n=new tM;return e.fetch("visual-editing/snapshot-welcome",void 0,{suppressWarnings:!0}).then(e=>{t.next(e.event)}).catch(()=>{}),e.on("presentation/snapshot-event",e=>{"reconnect"===e.event.type&&t.next(e.event),"mutation"===e.event.type&&n.next(e.event)}),tz(t,n)}(e),n=oF.provide({actors:{documentMutatorMachine:(e=>{let t=(0,U.Sx)(async({input:t,signal:n})=>{let{id:r}=t,{snapshot:i}=await e.fetch("visual-editing/fetch-snapshot",{documentId:r},{signal:n});return i}),n=(0,U.Sx)(async({input:t})=>{let{transactions:n}=t;for(let t of n){let n=L.encodeTransaction(t);return e.post("visual-editing/mutate",n)}});return oD.provide({actions:{"send sync event to parent":(0,od.a)(({enqueue:e})=>{e.sendParent(({context:e})=>({type:"sync",id:e.id,document:e.remote})),e.emit(({context:e})=>({type:"ready",snapshot:e.local}))})},actors:{"fetch remote snapshot":t,"submit mutations as transactions":n}})})(e)}}),r=(0,t_.c)(n,{input:{client:{withConfig:az},sharedListener:t}});r.start();let i=new AbortController,o=e.onStatus(()=>{e.fetch("visual-editing/features",void 0,{signal:i.signal,suppressWarnings:!0}).then(e=>{e.features.optimistic&&function(e){for(let t of(V=e,q))t()}(r)}).catch(aF)},"connected");return()=>{r.stop(),i.abort(),o()}},n=[e],r[0]=e,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,m.useEffect)(t,n)})(I),u[5]!==I||u[6]!==c||u[7]!==_||u[8]!==b?(o=null!==_&&(0,h.jsx)(aO,{comlink:I,componentResolver:c,inFrame:_,zIndex:b}),u[5]=I,u[6]=c,u[7]=_,u[8]=b,u[9]=o):o=u[9],u[10]!==I||u[11]!==d||u[12]!==p?(a=I&&(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(oz,{comlink:I,history:d}),(0,h.jsx)(oU,{comlink:I}),p&&(0,h.jsx)(aN,{comlink:I,refresh:p})]}),u[10]=I,u[11]=d,u[12]=p,u[13]=a):a=u[13],u[14]!==o||u[15]!==a?(s=(0,h.jsxs)(h.Fragment,{children:[o,a]}),u[14]=o,u[15]=a,u[16]=s):s=u[16];let O=s;return!1!==w&&j?(u[17]!==O||u[18]!==j?(l=(0,g.createPortal)(O,j),u[17]=O,u[18]=j,u[19]=l):l=u[19],l):O};aU.displayName="VisualEditing";var aW=n(35695),aV=n(71433);function aq(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function aB(e,t){if(!function(e,t){if("string"!=typeof e)return!1;let{pathname:n}=aq(e);return n===t||n.startsWith(`${t}/`)}(e,t))return e;let n=e.slice(t.length);return n.startsWith("/")?n:`/${n}`}function aG(e){let{basePath:t="",components:n,refresh:r,trailingSlash:i=!1,zIndex:o}=e,a=(0,aW.useRouter)(),s=(0,m.useRef)(a),[l,u]=(0,m.useState)();(0,m.useEffect)(()=>{s.current=a},[a]);let c=(0,m.useMemo)(()=>({subscribe:e=>(u(()=>e),()=>u(void 0)),update:e=>{switch(e.type){case"push":return s.current.push(aB(e.url,t));case"pop":return s.current.back();case"replace":return s.current.replace(aB(e.url,t));default:throw Error(`Unknown update type: ${e.type}`)}}}),[t]),d=(0,aW.usePathname)(),f=(0,aW.useSearchParams)();(0,m.useEffect)(()=>{l&&l({type:"push",url:((e,t)=>{let{pathname:n,query:r,hash:i}=aq(e);return t?n.endsWith("/")?`${n}${r}${i}`:`${n}/${r}${i}`:`${n.replace(/\/$/,"")||"/"}${r}${i}`})(function(e,t){if(!e.startsWith("/")||!t)return e;if("/"===e&&t)return t;let{pathname:n,query:r,hash:i}=aq(e);return`${t}${n}${r}${i}`}(`${d}${f?.size?`?${f}`:""}`,t),i)})},[t,l,d,f,i]);let p=(0,m.useCallback)(e=>{if(r)return r(e);switch(e.source){case"manual":return e.livePreviewEnabled?(console.debug("Live preview is setup, calling router.refresh() to refresh the server components without refetching cached data"),s.current.refresh(),Promise.resolve()):(console.debug("No loaders in live mode detected, or preview kit setup, revalidating root layout"),(0,aV.q)());case"mutation":return e.livePreviewEnabled?(console.debug("Live preview is setup, mutation is skipped assuming its handled by the live preview"),!1):(console.debug("No loaders in live mode detected, or preview kit setup, revalidating root layout"),(0,aV.q)());default:throw Error("Unknown refresh source",{cause:e})}},[r]);return(0,h.jsx)(aU,{components:n,history:c,portal:!0,refresh:p,zIndex:o})}}}]);