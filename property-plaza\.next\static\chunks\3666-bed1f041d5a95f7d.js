(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3666],{1806:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(4518),s=a(12115),n=a(74463),l=a(53999),i=a(1221),o=a(27043),c=a(48251),d=a(14666);function u(){let e=(0,o.useTranslations)(),t=(0,r.o)(e=>e),[a,u]=(0,s.useState)(!1),[m,h]=(0,s.useState)(null),p=(0,i.useRouter)(),A=[{content:e("seeker.listing.category.villa"),id:"1",value:c.BT.villas},{content:e("seeker.listing.category.apartment"),id:"2",value:c.BT.apartment},{content:e("seeker.listing.category.guestHouse"),id:"3",value:c.BT.rooms},{content:e("seeker.listing.category.commercial"),id:"4",value:c.BT.commercialSpace},{content:e("seeker.listing.category.cafeAndRestaurent"),id:"5",value:c.BT.cafeOrRestaurants},{content:e("seeker.listing.category.office"),id:"6",value:c.BT.offices},{content:e("seeker.listing.category.shops"),id:"7",value:c.BT.shops},{content:e("seeker.listing.category.shellAndCore"),id:"8",value:c.BT.shellAndCore},{content:e("seeker.listing.category.land"),id:"9",value:c.BT.lands}],x=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],g={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},f=(0,s.useMemo)(()=>!t.query||a?x:x.filter(e=>{let a=e.name.replace(", Bali","").toLowerCase(),r=t.query.toLowerCase();return!!a.includes(r)||(g[e.value]||[]).some(e=>e.toLowerCase().includes(r))}),[t.query,a]);return{seekersSearch:t,handleSetQuery:e=>{let a=e.split(","),r=e.length;a.length>3&&","==e.charAt(r-1)||t.setQuery(e)},handleSetType:e=>{(!(t.propertyType.length>=3)||t.propertyType.includes(e))&&t.setPropertyType(e)},propertyType:A,handleSearch:(e,a)=>{e&&t.setQuery(e),a&&t.setPropertyTypeFromArray(a);let r=e||t.query,s=a||t.propertyType;""!==t.activeSearch.query&&t.setSearchHistory({propertyType:t.activeSearch.propertyType,query:t.activeSearch.query}),t.setActiveSearch({query:r,propertyType:s});let i=(0,l.jW)(r);p.push(n.Eq+"/"+(i||"all")+"?"+d.Ix.type+"="+(s.toString()||"all"))},propertyTypeFormatHelper:e=>e.map(e=>{let t=A.find(t=>t.value==e);return null==t?void 0:t.content}),locations:x,banjars:g,getMatchingBanjars:e=>{let a=t.query;return a?(g[e]||[]).filter(e=>e.toLowerCase().includes(a.toLowerCase())):[]},showBanjars:a,setShowBanjars:u,selectedLocation:m,setSelectedLocation:h,handleSelectLocation:e=>{u(!0),h(e),t.setQuery(e)},handleBackToLocations:()=>{u(!1);let e=t.query.replace(m||"","");t.setQuery(e)},handleSetBanjar:function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.query.split(",").filter(e=>""!==e.trim()&&e!==m);if(r.includes(e)){let a=r.filter(t=>t!==e);t.setQuery(a.toString());return}if(!(r.length>=3)||""===r[r.length-1]){if(a){let t=r.length;r[t-1]=e}else r.push(e);t.setQuery(r.toString())}},filteredLocations:f}}},4518:(e,t,a)=>{"use strict";a.d(t,{o:()=>o});var r=a(53999),s=a(82940),n=a.n(s),l=a(88693),i=a(46786);let o=(0,l.vt)()((0,i.Zr)(e=>({activeSearch:{propertyType:[],query:""},propertyType:[],query:"",searchHistory:[],isOpen:!0,locationInputFocused:!1,categoryInputFocused:!1,setActiveSearch:t=>e({activeSearch:t}),setPropertyType:t=>e(e=>({propertyType:(0,r.q7)(e.propertyType,t)})),setQuery:t=>e({query:t}),setSearchHistory:t=>e(e=>{let a={...t,validUntil:n()().add(7,"days").format("DD-MMM-YYYY")};if(e.searchHistory.findIndex(e=>e.query==a.query)>=0)return e;let r=[...e.searchHistory,a];return e.searchHistory.length<5?e.searchHistory=r:e.searchHistory=[...r.slice(1,4),a],e}),setIsOpen:t=>e({isOpen:t}),setCategoryInputFocused:t=>e({categoryInputFocused:t}),setLocationInputFocused:t=>e({locationInputFocused:t}),clearSearch:()=>e({query:"",propertyType:[]}),setPropertyTypeFromArray:t=>e({propertyType:t}),clearCategory:()=>e({propertyType:[]})}),{name:"seeker-search",storage:(0,i.KU)(()=>localStorage),onRehydrateStorage(e){if(!e)return;let t=e.searchHistory.filter(e=>{let t=n()(e.validUntil);return n()().isSameOrBefore(t)});e.searchHistory=t}}))},7642:(e,t,a)=>{"use strict";a.d(t,{JX:()=>r,rJ:()=>s});let r=e=>e.toLowerCase().includes("day")?"DAY":e.toLowerCase().includes("week")?"WEEK":e.toLowerCase().includes("month")?"MONTH":e.toLowerCase().includes("year")?"YEAR":"MONTH",s=e=>"ONLINE"==e},26291:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(95155),s=a(37130),n=a(99840),l=a(13423),i=a(53999);function o(e){let{children:t,className:a}=e;return(0,s.U)("(min-width:1024px)")?(0,r.jsx)(n.Es,{className:(0,i.cn)("px-0",a),children:t}):(0,r.jsx)(l.tb,{className:(0,i.cn)("px-0",a),children:t})}},27247:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});var r=a(35695),s=a(12115),n=a(1221);function l(){let e=(0,n.useRouter)(),t=(0,r.usePathname)(),a=(0,r.useSearchParams)(),l=(0,s.useCallback)(r=>{let s=new URLSearchParams(a.toString());r.forEach(e=>s.set(e.name,e.value)),e.push(t+"?"+s.toString())},[a,e,t]),i=(0,s.useCallback)((e,t)=>{let r=new URLSearchParams(a.toString());return r.set(e,t),r.toString()},[a]);return{searchParams:a,createQueryString:(r,s)=>{let n=new URLSearchParams(a.toString());n.set(r,s),e.push(t+"?"+n.toString())},generateQueryString:i,removeQueryParam:(t,r)=>{let s=new URLSearchParams(a.toString());t.forEach(e=>{s.delete(e)});let n="".concat(window.location.pathname,"?").concat(s.toString());if(r)return window.location.href=n;e.push(n)},createMultipleQueryString:l,pathname:t,updateQuery:(r,s)=>{let n=new URLSearchParams(a.toString());n.set(r,s),e.push(t+"?"+n.toString())}}}},30962:(e,t,a)=>{"use strict";a.d(t,{I$:()=>n,KA:()=>d,QS:()=>s,Uw:()=>l,_y:()=>c,b:()=>o,ed:()=>i});var r=a(99493);a(3157);let s=e=>r.apiClient.post("properties/favorite",e),n=e=>(0,r.apiClient)("/properties/filter-location?search=".concat(e.search)),l=e=>r.apiClient.post("properties/filter",e),i=()=>r.apiClient.get("filter-parameter"),o=e=>{let{page:t,per_page:a,search:s,sort_by:n}=e;return r.apiClient.get("users/favorite?page=".concat(t,"&per_page=").concat(a,"&search=").concat(s,"&sort_by=").concat(n))},c=e=>r.apiClient.put("users/filter-setting",e),d=e=>r.apiClient.post("properties/batch-property",e)},31787:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var r=a(95155),s=a(37130),n=a(13423),l=a(99840);function i(e){let{children:t,className:a}=e;return(0,s.U)("(min-width:1024px)")?(0,r.jsx)(l.L3,{className:a,children:t}):(0,r.jsx)(n.gk,{className:a,children:t})}},32059:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155),s=a(53999);function n(e){return(0,r.jsx)("div",{...e,ref:e.ref,className:(0,s.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},37777:(e,t,a)=>{"use strict";a.d(t,{Tooltip:()=>o,TooltipContent:()=>d,TooltipProvider:()=>i,TooltipTrigger:()=>c});var r=a(95155),s=a(12115),n=a(70859),l=a(53999);let i=n.Kq,o=n.bL,c=n.l9,d=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...i}=e;return(0,r.jsx)(n.UC,{ref:t,sideOffset:s,className:(0,l.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})});d.displayName=n.UC.displayName},43666:(e,t,a)=>{"use strict";a.d(t,{default:()=>eS});var r=a(95155),s=a(1806),n=a(32059),l=a(66766),i=a(88327),o=a(97168),c=a(47924),d=a(74783),u=a(12115),m=a(60760),h=a(1978),p=a(4518),A=a(53999),x=a(95784),g=a(1702);let f=[{id:"1",content:"IDR",value:"IDR"},{id:"2",content:"EUR",value:"EUR"},{id:"3",content:"GBP",value:"GBP"},{id:"4",content:"AUD",value:"AUD"},{id:"5",content:"USD",value:"USD"}],y=(0,u.forwardRef)((e,t)=>{let{triggerClassName:a,showCaret:s=!1,defaultCurrency:n="IDR",onClick:l}=e,{currency:i,setCurrency:o,isLoading:c}=(0,g.M)(),[d,m]=(0,u.useState)(n),[h,p]=(0,u.useState)(!1);return(0,u.useEffect)(()=>{if(c)return m(n);m(i)},[i,c,n]),(0,r.jsx)("div",{className:"max-sm:w-fit w-full",children:(0,r.jsxs)(x.l6,{defaultValue:n,value:d,onValueChange:o,open:h,onOpenChange:e=>{p(e),e&&l&&l(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,r.jsx)(x.bq,{ref:t,showCaret:s,className:"rounded-full border flex items-center justify-center border-seekers-text-lighter shadow-md h-10 px-2 !w-full ".concat(a),onClick:e=>{e.stopPropagation(),null==l||l(e)},children:(0,r.jsx)(x.yv,{className:"text-xs text-center"})}),(0,r.jsx)(x.gC,{children:f.map(e=>(0,r.jsx)(x.eb,{value:e.value,children:e.content},e.id))})]})})});y.displayName="CurrencyForm";var w=a(52129);function v(e){let{code:t}=e,a=w[t];return(0,r.jsx)(a,{className:"border border-colortext-foreground rounded-full w-4 h-4 aspect-square my-auto"})}var j=a(27043),N=a(43089),b=a(10018),C=a(64229);(0,b.M6)(async e=>{let{requestLocale:t}=e,r=await t;return{locale:await t,messages:(await a(67311)("./".concat(r,".json"))).default,defaultLocale:C.DT.defaultLocale,locales:C.DT.locales}});let{Link:k,redirect:S,usePathname:E,useRouter:T}=(0,N.xp)({defaultLocale:"en",locales:["en","id"],pathnames:{"/":"/","/pathnames":{en:"/pathnames",de:"/pfadnamen",id:"/nama-jalur"}},localePrefix:"as-needed"});var R=a(27247);let B=(0,u.forwardRef)((e,t)=>{let{triggerClassName:a,showCaret:s=!1,defaultValue:n="en",onClick:l}=e,{changeLanguage:i,locale:o}=function(){let e=(0,j.useLocale)(),t=T(),a=E(),{generateQueryString:r}=(0,R.A)();return{changeLanguage:e=>{let s=r("","");(0,u.startTransition)(()=>{t.replace(a+"?"+s,{locale:e}),t.refresh()})},locale:e}}(),c=[{id:"2",content:(0,r.jsx)(v,{code:"US"}),value:"EN"},{id:"1",content:(0,r.jsx)(v,{code:"ID"}),value:"ID"}],[d,m]=(0,u.useState)(!1);return(0,r.jsx)("div",{className:"max-sm:w-fit w-full",onClick:e=>e.stopPropagation(),children:(0,r.jsxs)(x.l6,{defaultValue:n,onValueChange:i,value:o.toUpperCase(),open:d,onOpenChange:e=>{m(e),e&&l&&l(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,r.jsx)(x.bq,{ref:t,showCaret:s,className:"rounded-full border border-seekers-text-lighter shadow-md h-10 w-14 px-2 flex items-center justify-center ".concat(a),onClick:e=>{e.stopPropagation(),null==l||l(e)},children:(0,r.jsx)(x.yv,{className:"text-xs"})}),(0,r.jsx)(x.gC,{children:c.map(e=>(0,r.jsx)(x.eb,{className:"",value:e.value,children:e.content},e.id))})]})})});B.displayName="SeekersLocaleForm";var U=a(57383),_=a(14666),P=a(46797),D=a(18618),L=a(26715),O=a(5041),z=a(35201),I=a(22190),M=a(37996),F=a(99840),q=a(67133),H=a(72236),V=a(21780),K=a(74463),Q=a(6874),J=a.n(Q);function Y(e){let{localeId:t="EN",currency_:a="EUR"}=e,s=(0,u.useRef)(null),n=(0,u.useRef)(null),l=(0,u.useRef)(null),i=(0,P.k)(e=>e.role),[o,c]=(0,u.useState)(!1),[d,m]=(0,u.useState)(0),[p,x]=(0,u.useState)(null);return(0,u.useEffect)(()=>{let e=e=>{var t,a,r;let i=e.target,o=null==(t=l.current)?void 0:t.contains(i),d=null==(a=n.current)?void 0:a.contains(i);if(o){x("currency"),c(!0);return}if(d){x("language"),c(!0);return}(null==(r=s.current)?void 0:r.contains(i))||(c(!1),x(null))};return window.addEventListener("mousedown",e),()=>{window.removeEventListener("mousedown",e)}},[]),(0,u.useEffect)(()=>{let e=()=>{d!==window.scrollY-4&&(m(window.scrollY),c(!1))};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[d,c]),(0,r.jsxs)("div",{ref:s,className:"flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]",children:[(0,r.jsx)("div",{className:"w-fit",children:(0,r.jsx)(h.P.div,{className:"overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex",initial:{width:"110px"},animate:{width:o?"166px":"112px"},transition:{duration:.1},children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 py-2 w-full",children:[(0,r.jsx)(y,{triggerClassName:(0,A.cn)("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0",o?"w-full":"pl-3 max-w-[48px]"),defaultCurrency:a,ref:l,onClick:e=>{e.stopPropagation(),x("currency"),c(!0)},showCaret:o&&"currency"===p}),(0,r.jsx)("div",{className:"w-[2px] h-[24px] bg-seekers-text-lighter"}),(0,r.jsx)(B,{triggerClassName:(0,A.cn)("rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",o?"w-full":"pl-2 max-w-[32px]"),defaultValue:t,ref:n,onClick:e=>{e.stopPropagation(),x("language"),c(!0)},showCaret:o&&"language"===p})]})})}),(0,r.jsx)(r.Fragment,{children:U.A.get(_.Xh)&&"SEEKER"==i?(0,r.jsx)(G,{trigger:(0,r.jsx)("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10",children:(0,r.jsx)(H.A,{url:""})})}):(0,r.jsx)("div",{children:(0,r.jsx)(V.default,{triggerClassName:(0,A.cn)("!w-10 rounded-full overflow-hidden")})})})]})}function G(e){let{trigger:t}=e,a=(0,j.useTranslations)("seeker");return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(q.rI,{modal:!1,children:[(0,r.jsx)(q.ty,{asChild:!0,children:t}),(0,r.jsxs)(q.SQ,{align:"end",className:"!w-[256px]",children:[(0,r.jsx)(q._2,{asChild:!0,children:(0,r.jsx)(J(),{href:K.DW,children:a("accountAndProfile.profile")})}),(0,r.jsx)(q._2,{asChild:!0,children:(0,r.jsx)(J(),{href:K.gA,children:a("accountAndProfile.favorite")})}),(0,r.jsx)(q._2,{className:"w-full",asChild:!0,children:(0,r.jsx)(J(),{href:K.Nx,children:(0,r.jsx)("div",{className:"flex justify-between items-center w-full ",children:a("accountAndProfile.message")})})}),(0,r.jsx)(q._2,{onClick:e=>{e.preventDefault();let t=document.getElementById("open-logout-dialog");null==t||t.click()},children:a("accountAndProfile.logout.title")})]})]}),(0,r.jsx)(W,{trigger:(0,r.jsx)("button",{id:"open-logout-dialog"})})]})}function W(e){let{trigger:t}=e,[a,s]=(0,u.useState)(!1),n=function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];let e=(0,L.jE)();return(0,O.n)({mutationFn:()=>(0,D.ri)(),onSuccess:()=>{U.A.remove(_.Xh),U.A.remove("user"),e.invalidateQueries({queryKey:[z.g],refetchType:"none"}),window.location.assign("/")},onError:e=>{U.A.remove(_.Xh),U.A.remove("user"),window.location.assign("/")}})}("seekers"),l=(0,j.useTranslations)("seeker");return(0,r.jsxs)(I.A,{open:a,setOpen:s,openTrigger:t,dialogClassName:"max-w-md",children:[(0,r.jsxs)(M.A,{className:"text-start px-0",children:[(0,r.jsx)("h2",{className:"max-sm:text-center font-semibold",children:l("accountAndProfile.logout.title")}),(0,r.jsx)("p",{className:"max-sm:text-center max-sm:mb-4",children:l("owner.accountAndProfile.logout.description")})]}),(0,r.jsxs)(F.Es,{children:[(0,r.jsx)(o.$,{variant:"default-seekers",loading:n.isPending,className:"min-w-20 max-sm:order-last",onClick:()=>s(!1),children:l("cta.cancel")}),(0,r.jsx)(o.$,{variant:"ghost",onClick:()=>{if(!U.A.get(_.Xh))return void window.location.assign("");n.mutate()},loading:n.isPending,className:"min-w-20",children:l("cta.logout")})]})]})}var X=a(82568);let Z=X.bL;X.l9;let $=X.Mz,ee=u.forwardRef((e,t)=>{let{className:a,align:s="center",sideOffset:n=4,...l}=e;return(0,r.jsx)(X.ZL,{children:(0,r.jsx)(X.UC,{ref:t,align:s,sideOffset:n,className:(0,A.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});ee.displayName=X.UC.displayName;var et=a(82714),ea=a(89852),er=a(54416),es=a(64237),en=a(19373),el=a(43782),ei=a(13052),eo=a(35169),ec=a(5196),ed=a(4516);let eu={src:"/_next/static/media/canggu.84e6fbe6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUUeIgAAAACXRSTlMBCyAwTkBmWH4H4C9lAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAMklEQVR4nCXLuQ0AMAzDQFryt//EgRHWR4CI4FJWGvBkTwHl7rJO7KZ0Zkels47wff99FjIAmG4RX6UAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},em={src:"/_next/static/media/ubud.81668090.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlNrAVtEKhuADk09jhuwZCAAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAySURBVHicBcEHAQAwDMMwJ/3jD3gSKo8kMU4rYyiqw3EU2diBDjck2uQ1Yu04PwjYBH0dsQDRzN90KwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},eh={src:"/_next/static/media/nusa-dua.9acfd1fe.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAABMaXEAAAAAAAAAAAAAAAAAAABReBoRAAAACXRSTlNsU34AN11hPRckhWnFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAMklEQVR4nB3JQRLAMAwCsSWAnf+/uNPoKmwntvFNm2vMY3QKRWwlicUZafLXwiLmPPMBGmcAtF10IQcAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},ep={src:"/_next/static/media/uluwatu.71df2404.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAABMaXEAAAAAAAAKNf92AAAACnRSTlM5b3xVi2MwACERUm+ZFgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAADNJREFUeJwFwYcBADAMwjBDBun/D1ciESgh17t9IaVdVXie3fEDNbSgzB0u1Aa3mPKMaz4l7wDv+DrveAAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8},eA={src:"/_next/static/media/seminyak.639fb2f5.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlMCQTd4Wx9LESZpobATef0AAAAJcEhZcwAADsQAAA7EAZUrDhsAAAA1SURBVHicFcm3EcBAEAOxJc+r/4I1jxSApxtAEfEB3s2LApciLoGxUqqXbpVhpO59V5mn+QEcVADSvRMKNQAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:8};function ex(e){let{locationName:t}=e;switch(t){case"canggu":return(0,r.jsx)(l.default,{src:eu,alt:"canggu",width:36,className:"aspect-square "});case"ubud":return(0,r.jsx)(l.default,{src:em,alt:"ubud",width:32,className:"aspect-square "});case"seminyak":return(0,r.jsx)(l.default,{src:eA,alt:"Seminyak",width:32,className:"aspect-square"});case"uluwatu":return(0,r.jsx)(l.default,{src:ep,alt:"uluwatu",width:32,className:"aspect-square "});case"Nusa Dua":return(0,r.jsx)(l.default,{src:eh,alt:"nusa dua",width:32,className:"aspect-square "});default:return(0,r.jsx)(r.Fragment,{})}}function eg(e){var t,a,n,l,i,o;let{showContent:c}=e,d=(0,j.useTranslations)("seeker"),{query:m}=(0,p.o)(e=>e),h=function(e){let{search:t}=e;return(0,en.I)({queryKey:["location-suggestion",t],queryFn:async()=>await (0,es.xn)(e),retry:!1})}({search:(0,el.d)(m,500)}),{handleSetQuery:A,seekersSearch:x,banjars:g,showBanjars:f,selectedLocation:y,handleSelectLocation:w,handleBackToLocations:v,handleSetBanjar:N,filteredLocations:b,getMatchingBanjars:C}=(0,s.A)();return(0,u.useEffect)(()=>{var e,t;if(h.isPending)return null==c?void 0:c(!0);b.length<=0&&(null==(t=h.data)||null==(e=t.data)?void 0:e.length)<=0?null==c||c(!1):null==c||c(!0)},[m.length,b.length,null==(a=h.data)||null==(t=a.data)?void 0:t.length,h.isPending,c]),(0,r.jsxs)(r.Fragment,{children:[f?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4  px-3 max-sm:px-0",children:[(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),v()},className:"text-seekers-text-light hover:text-seekers-text",children:(0,r.jsx)(eo.A,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"font-medium capitalize",children:y})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 px-3 max-sm:px-0",children:g[y].map(e=>(0,r.jsx)("div",{className:"relative ",children:(0,r.jsxs)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),N(e)},className:"w-full border border-gray-200 rounded-full py-3 px-4 flex items-center gap-3\n                                      ".concat(x.query.includes(e)?"bg-gray-50":"bg-white"),children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border flex items-center justify-center\n                                      ".concat(x.query.includes(e)?"border-seekers-primary bg-seekers-primary":"border-gray-300"),children:x.query.includes(e)&&(0,r.jsx)(ec.A,{className:"h-3 w-3 text-white"})}),(0,r.jsx)("span",{className:"text-sm text-seekers-text-light",children:e})]})},e))})]})}):(0,r.jsx)(r.Fragment,{children:b.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{children:d("misc.region")}),b.map(e=>{var t;let a=e.name.replace(", Bali",""),s=(null==(t=g[e.value])?void 0:t.length)>0;return(0,r.jsx)("div",{children:(0,r.jsxs)("button",{className:"w-full flex items-center justify-between p-3 hover:bg-gray-100 transition-colors rounded-lg",onClick:t=>{t.preventDefault(),t.stopPropagation(),s?w(e.value):A(e.value)},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(ex,{locationName:e.value}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:a}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]}),s&&(0,r.jsx)(ei.A,{className:"h-4 w-4 text-gray-400"})]})},e.value)})]})}),m.length>=3&&!f&&(b.some(e=>C(e.value).length>0)||((null==(l=h.data)||null==(n=l.data)?void 0:n.length)||0)>0)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium mt-4 mb-2 px-3",children:d("misc.areas")}),(0,r.jsxs)("div",{children:[b.map(e=>{let t=C(e.value);return 0===t.length?null:t.map(t=>(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{N(t,!0)},children:[(0,r.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,r.jsx)(ed.A,{className:"w-4 h-4 text-seekers-primary"})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:t}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.name})]})]},"".concat(e.name,"-").concat(t)))}),null==(o=h.data)||null==(i=o.data)?void 0:i.map((e,t)=>(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{N(e)},children:[(0,r.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,r.jsx)(ed.A,{className:"w-4 h-4 text-seekers-primary"})}),(0,r.jsx)("div",{className:"text-left",children:(0,r.jsx)("div",{className:"font-medium",children:e})})]},t))]})]})]})}function ef(e){let{customTrigger:t,isUseAnimation:a=!0}=e,n=(0,j.useTranslations)("seeker"),[l,i]=(0,u.useState)(!1),{isOpen:c,setLocationInputFocused:d,query:m}=(0,p.o)(e=>e),[x,g]=(0,u.useState)(!0),{handleSetQuery:f,seekersSearch:y,handleSearch:w}=(0,s.A)(),v=(0,u.useRef)(null),N=e=>{i(e),d(e)};return(0,r.jsx)("div",{className:(0,A.cn)(a?c?"w-full":"w-fit":"w-full"),onClick:()=>{var e;N(!0),null==(e=v.current)||e.focus()},children:(0,r.jsxs)(Z,{open:l&&x,onOpenChange:N,children:[t?(0,r.jsx)($,{asChild:!0,children:t}):(0,r.jsx)($,{className:"w-full px-4",children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(et.J,{className:"text-xs font-medium text-seekers-text",children:n("navbar.search.locationTitle")}),(0,r.jsxs)(h.P.div,{animate:{height:20*!!c,opacity:100*!!c,width:c?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,r.jsx)(ea.p,{ref:v,onFocus:e=>N(!0),onChange:e=>{f(e.target.value),g(!0)},value:m,placeholder:n("form.placeholder.seekersFindPropertyLocation"),onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(w(),i(!1))},className:"border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b w-full rounded-none pb-2 !p-0 h-fit"}),(0,r.jsx)(o.$,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),y.setQuery("")},size:"icon",className:(0,A.cn)("-mt-2",y.query.length>0?"":"hidden"),children:(0,r.jsx)(er.A,{})})]})]})}),(0,r.jsx)(ee,{className:"w-full border-seekers-text-lighter/20",align:"start",onOpenAutoFocus:e=>e.preventDefault(),children:(0,r.jsx)(eg,{showContent:g})})]})})}var ey=a(66474),ew=a(26291),ev=a(31787),ej=a(76485);function eN(){let e=(0,j.useTranslations)("seeker"),{handleSearch:t}=(0,s.A)(),[a,n]=(0,u.useState)(!1),[l,i]=(0,u.useState)("location"),{query:d}=(0,p.o)(e=>e),{handleSetType:m,seekersSearch:h,propertyType:x,handleSetQuery:g}=(0,s.A)();return(0,el.d)(d,500),(0,r.jsxs)(I.A,{open:a,setOpen:n,drawerClassName:"relative",openTrigger:(0,r.jsxs)("div",{className:"w-full border h-10 pl-4 pr-1 flex items-center justify-between text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md",children:[(0,r.jsx)("span",{className:"line-clamp-1",children:e("listing.search.placeholder")}),(0,r.jsx)(o.$,{variant:"default-seekers",className:"rounded-full !h-8 !w-[2.25rem]",size:"icon",children:(0,r.jsx)(c.A,{className:"!w-4 !h-4",strokeWidth:3})})]}),children:[(0,r.jsxs)("div",{className:"flex flex-col h-[calc(80vh-24px)] pb-16",children:[(0,r.jsxs)("div",{className:"flex-shrink-0 bg-white z-10 border-b",children:[(0,r.jsxs)(M.A,{className:"px-0 !text-center",children:[(0,r.jsx)(ev.A,{className:"font-semibold p-0",children:e("listing.search.title")}),(0,r.jsx)(F.rr,{children:e("misc.findYourPerfectProperty")})]}),(0,r.jsxs)("div",{className:"px-4 mb-4 relative",children:[(0,r.jsx)(ea.p,{type:"text",placeholder:"Search destinations",className:"w-full px-3 py-2 !text-sm !h-10",value:d,onChange:e=>{g(e.target.value)},onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(t(),n(!1))}}),(0,r.jsx)(er.A,{className:"w-4 h-4 absolute right-7 top-1/2 -translate-y-1/2 text-seekers-text-light",onClick:()=>g("")})]})]}),(0,r.jsx)("div",{className:"flex-grow overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>i("location"),children:[(0,r.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.locationTitle")}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(ey.A,{className:(0,A.cn)("h-4 w-4 transition-transform","location"==l?"transform rotate-180":"")})})]}),"location"==l&&(0,r.jsx)(eg,{})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>i("category"),children:[(0,r.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.category")}),(0,r.jsx)(o.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(ey.A,{className:(0,A.cn)("h-4 w-4 transition-transform","category"==l?"transform rotate-180":"")})})]}),"category"==l&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:x.map(e=>(0,r.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),m(e.value)},children:[(0,r.jsx)("div",{className:(0,A.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",h.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,r.jsx)(ec.A,{className:(0,A.cn)(h.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),(0,r.jsx)(ej.A,{category:e.value,className:"!w-6 !h-6"}),(0,r.jsx)("span",{className:"text-center",children:e.content})]},e.id))})]})]})})]}),(0,r.jsx)(ew.A,{className:"absolute bottom-0 w-[calc(100%-32px)]",children:(0,r.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,r.jsx)(o.$,{variant:"link",className:"px-0 text-seekers-primary",onClick:()=>n(!1),children:e("cta.clearAll")}),(0,r.jsxs)(o.$,{className:"flex gap-2",variant:"default-seekers",onClick:()=>{t(),n(!1)},children:[(0,r.jsx)(c.A,{}),e("cta.search")]})]})})]})}var eb=a(45082);function eC(e){let{value:t,textOnly:a=!1}=e,n=(0,j.useTranslations)("seeker"),{propertyTypeFormatHelper:l}=(0,s.A)(),i=l(t.split(","));if(t.includes("all"))if(a)return n("listing.filter.category.all.title");else return(0,r.jsx)("p",{children:n("listing.filter.category.all.title")});return a?i.toString().replace(","," ".concat(n("conjuntion.and")," ")):(0,r.jsx)(r.Fragment,{children:i.length>2?(0,r.jsx)(eb.A,{trigger:(0,r.jsx)("div",{children:(0,r.jsxs)("p",{children:[i[0]," ",n("conjuntion.and")," ",(0,r.jsxs)("span",{children:["+ ",i.length-1," ",n("misc.more")]})]})}),content:i.toString().replaceAll(",",", "),contentClassName:"text-seekers-text"}):(0,r.jsx)("div",{children:(0,r.jsx)("p",{children:i.toString().replace(","," ".concat(n("conjuntion.and")," "))})})})}function ek(e){let{customTrigger:t}=e,a=(0,j.useTranslations)(),[n,l]=(0,u.useState)(!1),{isOpen:i,setCategoryInputFocused:c}=(0,p.o)(e=>e),{handleSetType:d,seekersSearch:m,propertyType:x}=(0,s.A)();return(0,r.jsxs)(q.rI,{modal:!1,open:n,onOpenChange:e=>{l(e),c(e)},children:[(0,r.jsx)(q.ty,{asChild:!0,children:t||(0,r.jsxs)("div",{className:(0,A.cn)("px-2",i?"w-full":"w-0"),children:[(0,r.jsx)(et.J,{className:"text-xs font-medium text-seekers-text",children:a("seeker.navbar.search.category")}),(0,r.jsxs)(h.P.div,{animate:{height:20*!!i,opacity:100*!!i,width:i?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,r.jsx)(o.$,{variant:"ghost",className:"w-full h-fit font-normal p-0 overflow-hidden justify-start hover:bg-transparent",children:m.propertyType.length<1?(0,r.jsx)("p",{className:"text-seekers-text-lighter",children:a("seeker.navbar.search.propertyType")}):(0,r.jsx)(eC,{value:m.propertyType.toString()})}),(0,r.jsx)(o.$,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),m.clearCategory()},size:"icon",className:(0,A.cn)("-mt-2",m.propertyType.length>0?"":"hidden"),children:(0,r.jsx)(er.A,{})})]})]})}),(0,r.jsx)(q.SQ,{className:(0,A.cn)("border-seekers-text-lighter/20 grid grid-cols-2 sm:grid-cols-3 gap-3 p-4",i?"w-fit":"w-0"),align:"start",children:x.map(e=>(0,r.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   md:w-28 p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),d(e.value)},"data-inside-dropdown":!0,children:[(0,r.jsx)("div",{className:(0,A.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",m.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,r.jsx)(ec.A,{className:(0,A.cn)(m.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),(0,r.jsx)(ej.A,{category:e.value,className:"!w-6 !h-6"}),(0,r.jsx)("span",{className:"text-center",children:e.content})]},e.id))})]})}function eS(e){let{localeId:t="EN",currency_:a="EUR"}=e,{handleSearch:x}=(0,s.A)(),[g,f]=(0,u.useState)(!1),y=(0,u.useRef)(null),w=(0,u.useRef)(null),{isOpen:v,setIsOpen:j,categoryInputFocused:N,locationInputFocused:b}=(0,p.o)(e=>e);return(0,u.useEffect)(()=>{let e=()=>{if(f(!1),window.scrollY<200)return void j(!0);window.scrollY>200&&(N||b)?j(!0):j(!1)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[N,v,b,j]),(0,u.useEffect)(()=>{if(N||b)return void j(!0)},[N,b,j]),(0,r.jsx)(m.N,{children:(0,r.jsxs)("nav",{ref:w,className:"w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]",children:[(0,r.jsx)(n.A,{className:"!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ",children:(0,r.jsxs)("div",{className:"w-full flex justify-between items-center flex-wrap gap-y-6",children:[(0,r.jsx)(k,{href:"/",children:(0,r.jsx)(l.default,{src:i.default,alt:"Property-Plaza",width:164,height:24})}),(0,r.jsxs)(h.P.div,{className:"flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4",initial:{opacity:1,width:"60%"},animate:{width:v?"60%":"30%"},transition:{duration:.3},children:[(0,r.jsxs)("div",{className:"flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter",children:[(0,r.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pr-8",children:(0,r.jsx)(ef,{})}),(0,r.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pl-8",children:(0,r.jsx)(ek,{})})]}),(0,r.jsx)(h.P.div,{initial:{height:48,width:48},animate:{height:v?48:36,width:v?48:36},transition:{duration:.3},children:(0,r.jsx)(o.$,{variant:"default-seekers",onClick:()=>x(),className:"rounded-full w-full h-full !aspect-square",size:"icon",children:(0,r.jsx)(c.A,{className:"!w-5 !h-5",strokeWidth:3})})})]}),(0,r.jsx)("div",{className:"lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2",children:(0,r.jsx)(eN,{})}),(0,r.jsx)("div",{className:"md:hidden flex gap-1 w-[164px] justify-end",children:(0,r.jsx)(o.$,{variant:"ghost",className:"px-0 pl-4",onClick:()=>f(e=>!e),children:(0,r.jsx)(d.A,{className:"!h-6 !w-6"})})}),(0,r.jsx)("div",{className:"max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]",children:(0,r.jsx)(Y,{currency_:a,localeId:t})})]})}),(0,r.jsx)("div",{className:(0,A.cn)(g?"fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0":"hidden")}),(0,r.jsx)("div",{ref:y,className:"absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ".concat(g?"h-fit  py-4 px-4":"h-0"," overflow-hidden transition-all ease-linear duration-75 transform"),children:(0,r.jsx)(Y,{currency_:a,localeId:t})})]})})}},43782:(e,t,a)=>{"use strict";a.d(t,{d:()=>s});var r=a(12115);let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,[a,s]=(0,r.useState)(e);return(0,r.useEffect)(()=>{let a=setTimeout(()=>{s(e)},t);return()=>{clearTimeout(a)}},[e,t]),a}},45082:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(95155);a(12115);var s=a(37777);function n(e){let{content:t,trigger:a,contentClassName:n}=e;return(0,r.jsx)(s.TooltipProvider,{delayDuration:100,children:(0,r.jsxs)(s.Tooltip,{children:[(0,r.jsx)(s.TooltipTrigger,{asChild:!0,children:a}),(0,r.jsx)(s.TooltipContent,{className:n,children:t})]})})}},48251:(e,t,a)=>{"use strict";a.d(t,{BT:()=>r,FT:()=>n,MC:()=>s,RX:()=>l,aB:()=>i});let r={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"},s={all:"ANY",mountain:"MOUNTAIN",ocean:"OCEAN",ricefield:"RICEFIELD",jungle:"JUNGLE"},n={anything:"ANY",placeToLive:"PLACE_TO_LIVE",business:"BUSINESS",land:"LAND"},l={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"},i={small:{min:1,max:300,key:"small"},medium:{min:301,max:1e3,key:"medium"},large:{min:1001,max:1e5,key:"large"}}},55807:(e,t,a)=>{"use strict";function r(e){return{nextPage:e.next_page,page:e.page,pageCount:e.page_count,perPage:e.per_page,prevPage:e.prev_page,total:e.total}}a.d(t,{w:()=>r})},64229:(e,t,a)=>{"use strict";a.d(t,{DT:()=>s,N_:()=>n,a8:()=>i});var r=a(43089);let s={locales:["en","id"],defaultLocale:"en"},{Link:n,redirect:l,usePathname:i,useRouter:o}=(0,r.xp)(s)},64237:(e,t,a)=>{"use strict";a.d(t,{yZ:()=>d,Cv:()=>p,Bb:()=>h,lx:()=>u,xn:()=>m});var r=a(55807),s=a(30962);a(7642);var n=a(40054),l=a.n(n),i=a(7972);function o(e){return e.map(e=>{var t,a,r,s,n,l;return{code:e.code,geolocation:c(e.location.latitude,e.location.longitude),location:e.location.district+", "+e.location.city+", "+e.location.province,price:e.availability.price,thumbnail:(l=e.code,e.images.map((e,t)=>({id:l+t,image:e.image,isHighlight:e.is_highlight})).sort((e,t)=>t.isHighlight-e.isHighlight)),title:e.title,listingDetail:{bathRoom:e.detail.bathroom_total,bedRoom:e.detail.bedroom_total,buildingSize:e.detail.building_size,landSize:e.detail.land_size,cascoStatus:e.detail.casco_status,gardenSize:e.detail.garden_size},availability:{availableAt:e.availability.available_at||"",maxDuration:(null==(t=e.availability.duration_max_unit)?void 0:t.value)&&e.availability.duration_max?{value:e.availability.duration_max||1,suffix:null==(a=e.availability.duration_max_unit)?void 0:a.value}:null,minDuration:(null==(r=e.availability.duration_min_unit)?void 0:r.value)&&e.availability.duration_min?{value:e.availability.duration_min||1,suffix:null==(s=e.availability.duration_min_unit)?void 0:s.value}:null,type:e.availability.type.value||""},sellingPoint:e.features.selling_points,category:e.detail.option.type,isFavorite:(null==e||null==(n=e._count)?void 0:n.favorites)>0,status:e.status}})}let c=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=1/111320*a;return[e+.4*r,t+.4*r]};async function d(e){try{let t=await (0,s.KA)({property_list:e});return{data:o(t.data.data)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function u(e){try{let t=await (0,s.Uw)(e);try{let t=Object.fromEntries(Object.entries(e).filter(e=>{let[t,a]=e;return void 0!==a}));2!==Object.keys(t).length&&await (0,s._y)(e)}catch(e){}return{data:o(t.data.data.items),meta:(0,r.w)(t.data.data.meta)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function m(e){if(e.search.length<3)return{data:[]};try{let t=await (0,s.I$)(e);return{data:function(e,t){let a=[];return t.forEach(t=>{Object.values(t).forEach(t=>{(function(e,t){let a=(0,i.A)(e.toLowerCase(),t.toLowerCase());return 1-a/Math.max(e.length,t.length)})(t,e)>0&&a.push(t)})}),l().uniq(a)}(e.search,t.data.data)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function h(){var e,t;try{return{data:{priceRange:{min:(e=(await (0,s.ed)()).data.data).price_range._min.price,max:e.price_range._max.price},buildingSizeRange:{max:e.size_range._max.building_size,min:e.size_range._min.building_size},gardenSizeRange:{max:e.size_range._max.garden_size,min:e.size_range._min.garden_size},landSizeRange:{max:e.size_range._max.land_size,min:e.size_range._min.land_size},furnishingOptions:e.furnishing_options[0].childrens.map(e=>({title:e.title,value:e.value})),livingOptions:e.living_options[0].childrens.map(e=>({title:e.title,value:e.value})),parkingOptions:e.parking_options[0].childrens.map(e=>({title:e.title,value:e.value})),poolOptions:e.pool_options[0].childrens.map(e=>({title:e.title,value:e.value}))},meta:void 0}}catch(e){return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}async function p(e){try{let t=await (0,s.b)({page:+e.page,per_page:+e.per_page,search:e.search||"",sort_by:e.sort_by});return{data:o(t.data.data.items),meta:(0,r.w)(t.data.data.meta)}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}},67133:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>h,mB:()=>p,rI:()=>o,ty:()=>c});var r=a(95155),s=a(12115),n=a(17499),l=a(33096),i=a(53999);let o=n.bL,c=n.l9;n.YJ,n.ZL,n.Pb,n.z6,s.forwardRef((e,t)=>{let{className:a,inset:s,children:o,...c}=e;return(0,r.jsxs)(n.ZP,{ref:t,className:(0,i.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",s&&"pl-8",a),...c,children:[o,(0,r.jsx)(l.vKP,{className:"ml-auto h-4 w-4"})]})}).displayName=n.ZP.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.G5,{ref:t,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...s})}).displayName=n.G5.displayName;let d=s.forwardRef((e,t)=>{let{className:a,sideOffset:s=4,...l}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{ref:t,sideOffset:s,className:(0,i.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})})});d.displayName=n.UC.displayName;let u=s.forwardRef((e,t)=>{let{className:a,inset:s,...l}=e;return(0,r.jsx)(n.q7,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",a),...l})});u.displayName=n.q7.displayName;let m=s.forwardRef((e,t)=>{let{className:a,children:s,checked:o,checkboxPosition:c="start",...d}=e;return(0,r.jsxs)(n.H_,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5  text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50","start"==c?"pl-8 pr-2":"pl-2 pr-8",a),checked:o,...d,children:[(0,r.jsx)("span",{className:(0,i.cn)("absolute flex h-3.5 w-3.5 items-center justify-center","start"==c?"left-2":"right-2"),children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(l.Srz,{className:"h-4 w-4"})})}),s]})});m.displayName=n.H_.displayName,s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,r.jsxs)(n.hN,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(l.RiX,{className:"h-4 w-4 fill-current"})})}),s]})}).displayName=n.hN.displayName;let h=s.forwardRef((e,t)=>{let{className:a,inset:s,...l}=e;return(0,r.jsx)(n.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",a),...l})});h.displayName=n.JU.displayName;let p=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(n.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...s})});p.displayName=n.wv.displayName},67311:(e,t,a)=>{var r={"./en.json":[4200,4200],"./id.json":[35394,5394]};function s(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],s=t[0];return a.e(t[1]).then(()=>a.t(s,19))}s.keys=()=>Object.keys(r),s.id=67311,e.exports=s},76485:(e,t,a)=>{"use strict";a.d(t,{A:()=>x});var r=a(95155),s=a(48251),n=a(53999),l=a(40483),i=a(29186),o=a(57340),c=a(55868),d=a(67312),u=a(48264),m=a(27809),h=a(53896),p=a(23343),A=a(38564);function x(e){let{category:t,className:a}=e;switch(t){case s.BT.villa:case s.BT.villas:return(0,r.jsx)(l.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.apartment:return(0,r.jsx)(i.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.homestay:case s.BT.guestHouse:case s.BT.rooms:return(0,r.jsx)(o.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.ruko:case s.BT.commercialSpace:return(0,r.jsx)(c.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.cafeOrRestaurants:return(0,r.jsx)(d.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.offices:return(0,r.jsx)(u.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.shops:return(0,r.jsx)(m.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.shellAndCore:return(0,r.jsx)(h.A,{className:(0,n.cn)("!w-6 !h-6",a)});case s.BT.lands:return(0,r.jsx)(p.A,{className:(0,n.cn)("!w-6 !h-6",a)});default:return(0,r.jsx)(A.A,{className:(0,n.cn)("!w-6 !h-6",a)})}}},88327:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SuiVK1jFWxjFVjqnREAAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}},95784:(e,t,a)=>{"use strict";a.d(t,{bq:()=>d,eb:()=>p,gC:()=>h,l6:()=>o,yv:()=>c});var r=a(95155),s=a(12115),n=a(33096),l=a(36307),i=a(53999);let o=l.bL;l.YJ;let c=l.WT,d=s.forwardRef((e,t)=>{let{className:a,children:s,showCaret:o=!0,...c}=e;return(0,r.jsxs)(l.l9,{ref:t,className:(0,i.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[s,o&&(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(n.TBE,{className:"h-4 w-4 opacity-50"})})]})});d.displayName=l.l9.displayName;let u=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.PP,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(n.Mtm,{})})});u.displayName=l.PP.displayName;let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wn,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...s,children:(0,r.jsx)(n.D3D,{})})});m.displayName=l.wn.displayName;let h=s.forwardRef((e,t)=>{let{className:a,children:s,position:n="popper",...o}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:t,className:(0,i.cn)("relative z-50 max-h-96 w-fit overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...o,children:[(0,r.jsx)(u,{}),(0,r.jsx)(l.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(m,{})]})})});h.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.JU,{ref:t,className:(0,i.cn)("px-2 py-1.5 text-sm font-semibold",a),...s})}).displayName=l.JU.displayName;let p=s.forwardRef((e,t)=>{let{className:a,children:s,...o}=e;return(0,r.jsxs)(l.q7,{ref:t,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(n.Srz,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})});p.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)(l.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",a),...s})}).displayName=l.wv.displayName}}]);