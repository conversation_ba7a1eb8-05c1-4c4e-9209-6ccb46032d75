"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3864],{1221:(e,t,r)=>{var n=Object.create,o=Object.defineProperty,l=Object.defineProperties,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,f=(e,t,r)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,h=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of s(t))d.call(e,l)||l===r||o(e,l,{get:()=>t[l],enumerable:!(n=i(t,l))||n.enumerable});return e},v={};((e,t)=>{for(var r in t)o(e,r,{get:t[r],enumerable:!0})})(v,{useRouter:()=>g}),e.exports=h(o({},"__esModule",{value:!0}),v);var w=r(35695),m=r(12115),b=((e,t,r)=>(r=null!=e?n(u(e)):{},h(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)))(r(76770)),g=o(()=>{let e=(0,w.useRouter)(),t=(0,w.usePathname)();(0,m.useEffect)(()=>{b.done()},[t]);let r=(0,m.useCallback)((r,n)=>{r!==t&&b.start(),e.replace(r,n)},[e,t]),n=(0,m.useCallback)((r,n)=>{r!==t&&b.start(),e.push(r,n)},[e,t]);return l(((e,t)=>{for(var r in t||(t={}))d.call(t,r)&&f(e,r,t[r]);if(c)for(var r of c(t))p.call(t,r)&&f(e,r,t[r]);return e})({},e),a({replace:r,push:n}))},"name",{value:"useRouter",configurable:!0})},4516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},7972:(e,t,r)=>{r.d(t,{A:()=>l});let n=[],o=[];function l(e,t){let r,l,i,a;if(e===t)return 0;let s=e;e.length>t.length&&(e=t,t=s);let c=e.length,u=t.length;for(;c>0&&e.charCodeAt(~-c)===t.charCodeAt(~-u);)c--,u--;let d=0;for(;d<c&&e.charCodeAt(d)===t.charCodeAt(d);)d++;if(c-=d,u-=d,0===c)return u;let p=0,f=0;for(;p<c;)o[p]=e.charCodeAt(d+p),n[p]=++p;for(;f<u;)for(p=0,r=t.charCodeAt(d+f),i=f++,l=f;p<c;p++)a=r===o[p]?i:i+1,i=n[p],l=n[p]=i>l?a>l?l+1:a:a>i?i+1:a;return l}},22717:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Bath",[["path",{d:"M9 6 6.5 3.5a1.5 1.5 0 0 0-1-.5C4.683 3 4 3.683 4 4.5V17a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5",key:"1r8yf5"}],["line",{x1:"10",x2:"8",y1:"5",y2:"7",key:"h5g8z4"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"7",x2:"7",y1:"19",y2:"21",key:"16jp00"}],["line",{x1:"17",x2:"17",y1:"19",y2:"21",key:"1pxrnk"}]])},51976:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},62267:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("BedDouble",[["path",{d:"M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8",key:"1k78r4"}],["path",{d:"M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4",key:"fb3tl2"}],["path",{d:"M12 4v6",key:"1dcgq2"}],["path",{d:"M2 18h20",key:"ajqnye"}]])},68121:(e,t,r)=>{r.d(t,{OK:()=>$,bL:()=>B,VM:()=>E,lr:()=>D,LM:()=>G});var n=r(12115),o=r(63540),l=r(6101),i=r(52712),a=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),a=n.useRef({}),c=n.useRef(e),u=n.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=s(a.current);u.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=a.current,r=c.current;if(r!==e){let n=u.current,o=s(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):r&&n!==o?p("ANIMATION_OUT"):p("UNMOUNT"),c.current=e}},[e,p]),(0,i.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=s(a.current).includes(e.animationName);if(e.target===o&&n&&(p("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(u.current=s(a.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),l(e)},[])}}(t),a="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),c=(0,l.s)(o.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||o.isPresent?n.cloneElement(a,{ref:c}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence";var c=r(95155),u=r(39033),d=r(94315),p=r(89367),f=r(85185),h="ScrollArea",[v,w]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,s=r?.[e]?.[i]||l,u=n.useMemo(()=>a,Object.values(a));return(0,c.jsx)(s.Provider,{value:u,children:o})};return a.displayName=t+"Provider",[a,function(r,a){let s=a?.[e]?.[i]||l,c=n.useContext(s);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(h),[m,b]=v(h),g=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:a,scrollHideDelay:s=600,...u}=e,[p,f]=n.useState(null),[h,v]=n.useState(null),[w,b]=n.useState(null),[g,y]=n.useState(null),[S,x]=n.useState(null),[E,C]=n.useState(0),[T,N]=n.useState(0),[R,P]=n.useState(!1),[A,O]=n.useState(!1),_=(0,l.s)(t,e=>f(e)),j=(0,d.jH)(a);return(0,c.jsx)(m,{scope:r,type:i,dir:j,scrollHideDelay:s,scrollArea:p,viewport:h,onViewportChange:v,content:w,onContentChange:b,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:R,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:A,onScrollbarYEnabledChange:O,onCornerWidthChange:C,onCornerHeightChange:N,children:(0,c.jsx)(o.sG.div,{dir:j,...u,ref:_,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});g.displayName=h;var y="ScrollAreaViewport",S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,asChild:a,nonce:s,...u}=e,d=b(y,r),p=n.useRef(null),f=(0,l.s)(t,p,d.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n[data-radix-scroll-area-viewport] {\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n}\n[data-radix-scroll-area-viewport]::-webkit-scrollbar {\n  display: none;\n}\n:where([data-radix-scroll-area-viewport]) {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n:where([data-radix-scroll-area-content]) {\n  flex-grow: 1;\n}\n"},nonce:s}),(0,c.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...u,asChild:a,ref:f,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:function(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let l=n.Children.only(o);return n.cloneElement(l,{children:"function"==typeof t?t(l.props.children):t})}({asChild:a,children:i},e=>(0,c.jsx)("div",{"data-radix-scroll-area-content":"",ref:d.onContentChange,style:{minWidth:d.scrollbarXEnabled?"fit-content":void 0},children:e}))})]})});S.displayName=y;var x="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=b(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,c.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,c.jsx)(T,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,c.jsx)(N,{...o,ref:t,forceMount:r}):"always"===l.type?(0,c.jsx)(R,{...o,ref:t}):null});E.displayName=x;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=b(x,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,c.jsx)(a,{present:r||i,children:(0,c.jsx)(N,{"data-state":i?"visible":"hidden",...o,ref:t})})}),T=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,i=b(x,e.__scopeScrollArea),s="horizontal"===e.orientation,u=V(()=>p("SCROLL_END"),100),[d,p]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>p("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,i.scrollHideDelay,p]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(p("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,p,u]),(0,c.jsx)(a,{present:o||"hidden"!==d,children:(0,c.jsx)(R,{"data-state":"hidden"===d?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),N=n.forwardRef((e,t)=>{let r=b(x,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,d=V(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return q(r.viewport,d),q(r.content,d),(0,c.jsx)(a,{present:o||i,children:(0,c.jsx)(R,{"data-state":i?"visible":"hidden",...l,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=b(x,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(s.viewport,s.content),p={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=H(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return Y([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,c.jsx)(P,{...p,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,c.jsx)(A,{...p,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=b(x,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),p=(0,l.s)(t,d,a.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(j,{"data-orientation":"horizontal",...i,ref:p,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:W(s.paddingLeft),paddingEnd:W(s.paddingRight)}})}})}),A=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=b(x,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),p=(0,l.s)(t,d,a.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(j,{"data-orientation":"vertical",...i,ref:p,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:W(s.paddingTop),paddingEnd:W(s.paddingBottom)}})}})}),[O,_]=v(x),j=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:a,onThumbChange:s,onThumbPointerUp:d,onThumbPointerDown:p,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:m,...g}=e,y=b(x,r),[S,E]=n.useState(null),C=(0,l.s)(t,e=>E(e)),T=n.useRef(null),N=n.useRef(""),R=y.viewport,P=i.content-i.viewport,A=(0,u.c)(w),_=(0,u.c)(h),j=V(m,10);function L(e){T.current&&v({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&A(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,S,P,A]),n.useEffect(_,[i,_]),q(S,j),q(y.content,j),(0,c.jsx)(O,{scope:r,scrollbar:S,hasThumb:a,onThumbChange:(0,u.c)(s),onThumbPointerUp:(0,u.c)(d),onThumbPositionChange:_,onThumbPointerDown:(0,u.c)(p),children:(0,c.jsx)(o.sG.div,{...g,ref:C,style:{position:"absolute",...g.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=S.getBoundingClientRect(),N.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),L(e))}),onPointerMove:(0,f.m)(e.onPointerMove,L),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=N.current,y.viewport&&(y.viewport.style.scrollBehavior=""),T.current=null})})})}),L="ScrollAreaThumb",D=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=_(L,e.__scopeScrollArea);return(0,c.jsx)(a,{present:r||o.hasThumb,children:(0,c.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...a}=e,s=b(L,r),u=_(L,r),{onThumbPositionChange:d}=u,p=(0,l.s)(t,e=>u.onThumbChange(e)),h=n.useRef(),v=V(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{v(),h.current||(h.current=F(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,c.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,u.onThumbPointerUp)})});D.displayName=L;var k="ScrollAreaCorner",I=n.forwardRef((e,t)=>{let r=b(k,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,c.jsx)(U,{...e,ref:t}):null});I.displayName=k;var U=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=b(k,r),[a,s]=n.useState(0),[u,d]=n.useState(0),p=!!(a&&u);return q(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),q(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),p?(0,c.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function H(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=H(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,p.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return Y([0,i],[0,l-n])(a)}function Y(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function V(e,t){let r=(0,u.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=(0,u.c)(t);(0,i.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var B=g,G=S,$=I}}]);