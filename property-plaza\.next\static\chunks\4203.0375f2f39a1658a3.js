"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4203],{84203:(e,n,a)=>{a.r(n),a.d(n,{default:()=>y});var t=a(42770),o=a(34477);let s=(0,o.createServerReference)("7f72133c10a72731b2d7b169807226a88ba6e85f39",o.callServer,void 0,o.findSourceMapURL,"setPerspectiveCookie");var i=a(35695),r=a(12115),l=a(46295),c=a(2981);let d={"handshake/syn":t.Tx,"handshake/syn-ack":t.Rr,"handshake/ack":t.IM,"channel/response":t._K,"channel/heartbeat":t.vd,"channel/disconnect":t.FS,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},v={[t.Tx]:"handshake/syn",[t.Rr]:"handshake/syn-ack",[t.IM]:"handshake/ack",[t._K]:"channel/response",[t.vd]:"channel/heartbeat",[t.FS]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},h=e=>{let{data:n}=e;return n&&"object"==typeof n&&"domain"in n&&"type"in n&&"from"in n&&"to"in n&&("sanity/channels"===n.domain&&(n.domain=t.V2),"overlays"===n.to&&(n.to="visual-editing"),"overlays"===n.from&&(n.from="visual-editing"),n.channelId=n.connectionId,delete n.connectionId,n.type=d[n.type]??n.type),e},p=({context:e},n)=>{let{sources:a,targetOrigin:o}=e,s=(e=>{let{channelId:n,...a}=e,o={...a,connectionId:n};return o.domain===t.V2&&(o.domain="sanity/channels"),"visual-editing"===o.to&&(o.to="overlays"),"visual-editing"===o.from&&(o.from="overlays"),o.type=v[o.type]??o.type,"channel/response"===o.type&&o.responseTo&&!o.data&&(o.data={responseTo:o.responseTo}),("handshake/syn"===o.type||"handshake/syn-ack"===o.type||"handshake/ack"===o.type)&&(o.data={id:o.connectionId}),o})(n.message);a.forEach(e=>{e.postMessage(s,{targetOrigin:o})})};function y(e){let{draftModeEnabled:n,draftModePerspective:a}=e,o=(0,i.useRouter)(),d=(0,l.J)((e,t)=>{n&&e!==a&&s(e).then(()=>{t.aborted||o.refresh()}).catch(e=>console.error("Failed to set the preview perspective cookie",e))});return(0,r.useEffect)(()=>{let e,n=(0,t.RM)({name:"loaders",connectTo:"presentation"},(0,t.Uc)().provide({actors:{listen:(0,t.CC)(h),requestMachine:(0,t.tP)().provide({actions:{"send message":p}})}}));n.on("loader/perspective",n=>{e?.abort(),e=new AbortController,d(n.perspective,e.signal)});let a=n.start();return(0,c.El)(n),()=>{a()}},[d]),null}y.displayName="PresentationComlink"}}]);