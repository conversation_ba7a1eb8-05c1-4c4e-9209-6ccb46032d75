"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[516],{15968:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},23343:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("LandPlot",[["path",{d:"m12 8 6-3-6-3v10",key:"mvpnpy"}],["path",{d:"m8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12",key:"ek95tt"}],["path",{d:"m6.49 12.85 11.02 6.3",key:"1kt42w"}],["path",{d:"M17.51 12.85 6.5 19.15",key:"v55bdg"}]])},27809:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29186:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("Hotel",[["path",{d:"M10 22v-6.57",key:"1wmca3"}],["path",{d:"M12 11h.01",key:"z322tv"}],["path",{d:"M12 7h.01",key:"1ivr5q"}],["path",{d:"M14 15.43V22",key:"1q2vjd"}],["path",{d:"M15 16a5 5 0 0 0-6 0",key:"o9wqvi"}],["path",{d:"M16 11h.01",key:"xkw8gn"}],["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 7h.01",key:"1vti4s"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]])},40483:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("TreePalm",[["path",{d:"M13 8c0-2.76-2.46-5-5.5-5S2 5.24 2 8h2l1-1 1 1h4",key:"foxbe7"}],["path",{d:"M13 7.14A5.82 5.82 0 0 1 16.5 6c3.04 0 5.5 2.24 5.5 5h-3l-1-1-1 1h-3",key:"18arnh"}],["path",{d:"M5.89 9.71c-2.15 2.15-2.3 5.47-.35 7.43l4.24-4.25.7-.7.71-.71 2.12-2.12c-1.95-1.96-5.27-1.8-7.42.35",key:"ywahnh"}],["path",{d:"M11 15.5c.5 2.5-.17 4.5-1 6.5h4c2-5.5-.5-12-1-14",key:"ft0feo"}]])},48264:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("Presentation",[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]])},53896:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},55868:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},57340:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},63578:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("Map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]])},67312:(t,e,r)=>{r.d(e,{A:()=>s});let s=(0,r(19946).A)("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},83232:(t,e,r)=>{r.d(e,{w1:()=>C});var s,i=r(51616),a=r.n(i);let o=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];class n{static from(t){if(!(t instanceof ArrayBuffer))throw Error("Data must be an instance of ArrayBuffer.");let[e,r]=new Uint8Array(t,0,2);if(219!==e)throw Error("Data does not appear to be in a KDBush format.");let s=r>>4;if(1!==s)throw Error(`Got v${s} data when expected v1.`);let i=o[15&r];if(!i)throw Error("Unrecognized array type.");let[a]=new Uint16Array(t,2,1),[h]=new Uint32Array(t,4,1);return new n(h,a,i,t)}constructor(t,e=64,r=Float64Array,s){if(isNaN(t)||t<0)throw Error(`Unpexpected numItems value: ${t}.`);this.numItems=+t,this.nodeSize=Math.min(Math.max(+e,2),65535),this.ArrayType=r,this.IndexArrayType=t<65536?Uint16Array:Uint32Array;let i=o.indexOf(this.ArrayType),a=2*t*this.ArrayType.BYTES_PER_ELEMENT,n=t*this.IndexArrayType.BYTES_PER_ELEMENT,h=(8-n%8)%8;if(i<0)throw Error(`Unexpected typed array class: ${r}.`);s&&s instanceof ArrayBuffer?(this.data=s,this.ids=new this.IndexArrayType(this.data,8,t),this.coords=new this.ArrayType(this.data,8+n+h,2*t),this._pos=2*t,this._finished=!0):(this.data=new ArrayBuffer(8+a+n+h),this.ids=new this.IndexArrayType(this.data,8,t),this.coords=new this.ArrayType(this.data,8+n+h,2*t),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+i]),new Uint16Array(this.data,2,1)[0]=e,new Uint32Array(this.data,4,1)[0]=t)}add(t,e){let r=this._pos>>1;return this.ids[r]=r,this.coords[this._pos++]=t,this.coords[this._pos++]=e,r}finish(){let t=this._pos>>1;if(t!==this.numItems)throw Error(`Added ${t} items when expected ${this.numItems}.`);return function t(e,r,s,i,a,o){if(a-i<=s)return;let n=i+a>>1;(function t(e,r,s,i,a,o){for(;a>i;){if(a-i>600){let n=a-i+1,h=s-i+1,l=Math.log(n),p=.5*Math.exp(2*l/3),d=.5*Math.sqrt(l*p*(n-p)/n)*(h-n/2<0?-1:1),u=Math.max(i,Math.floor(s-h*p/n+d)),c=Math.min(a,Math.floor(s+(n-h)*p/n+d));t(e,r,s,u,c,o)}let n=r[2*s+o],l=i,p=a;for(h(e,r,i,s),r[2*a+o]>n&&h(e,r,i,a);l<p;){for(h(e,r,l,p),l++,p--;r[2*l+o]<n;)l++;for(;r[2*p+o]>n;)p--}r[2*i+o]===n?h(e,r,i,p):h(e,r,++p,a),p<=s&&(i=p+1),s<=p&&(a=p-1)}})(e,r,n,i,a,o),t(e,r,s,i,n-1,1-o),t(e,r,s,n+1,a,1-o)}(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(t,e,r,s){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");let{ids:i,coords:a,nodeSize:o}=this,n=[0,i.length-1,0],h=[];for(;n.length;){let l=n.pop()||0,p=n.pop()||0,d=n.pop()||0;if(p-d<=o){for(let o=d;o<=p;o++){let n=a[2*o],l=a[2*o+1];n>=t&&n<=r&&l>=e&&l<=s&&h.push(i[o])}continue}let u=d+p>>1,c=a[2*u],m=a[2*u+1];c>=t&&c<=r&&m>=e&&m<=s&&h.push(i[u]),(0===l?t<=c:e<=m)&&(n.push(d),n.push(u-1),n.push(1-l)),(0===l?r>=c:s>=m)&&(n.push(u+1),n.push(p),n.push(1-l))}return h}within(t,e,r){if(!this._finished)throw Error("Data not yet indexed - call index.finish().");let{ids:s,coords:i,nodeSize:a}=this,o=[0,s.length-1,0],n=[],h=r*r;for(;o.length;){let l=o.pop()||0,d=o.pop()||0,u=o.pop()||0;if(d-u<=a){for(let r=u;r<=d;r++)p(i[2*r],i[2*r+1],t,e)<=h&&n.push(s[r]);continue}let c=u+d>>1,m=i[2*c],g=i[2*c+1];p(m,g,t,e)<=h&&n.push(s[c]),(0===l?t-r<=m:e-r<=g)&&(o.push(u),o.push(c-1),o.push(1-l)),(0===l?t+r>=m:e+r>=g)&&(o.push(c+1),o.push(d),o.push(1-l))}return n}}function h(t,e,r,s){l(t,r,s),l(e,2*r,2*s),l(e,2*r+1,2*s+1)}function l(t,e,r){let s=t[e];t[e]=t[r],t[r]=s}function p(t,e,r,s){let i=t-r,a=e-s;return i*i+a*a}let d={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:t=>t},u=Math.fround||(t=>e=>(t[0]=+e,t[0]))(new Float32Array(1));class c{constructor(t){this.options=Object.assign(Object.create(d),t),this.trees=Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(t){let{log:e,minZoom:r,maxZoom:s}=this.options;e&&console.time("total time");let i=`prepare ${t.length} points`;e&&console.time(i),this.points=t;let a=[];for(let e=0;e<t.length;e++){let r=t[e];if(!r.geometry)continue;let[s,i]=r.geometry.coordinates,o=u(f(s)),n=u(y(i));a.push(o,n,1/0,e,-1,1),this.options.reduce&&a.push(0)}let o=this.trees[s+1]=this._createTree(a);e&&console.timeEnd(i);for(let t=s;t>=r;t--){let r=+Date.now();o=this.trees[t]=this._createTree(this._cluster(o,t)),e&&console.log("z%d: %d clusters in %dms",t,o.numItems,Date.now()-r)}return e&&console.timeEnd("total time"),this}getClusters(t,e){let r=((t[0]+180)%360+360)%360-180,s=Math.max(-90,Math.min(90,t[1])),i=180===t[2]?180:((t[2]+180)%360+360)%360-180,a=Math.max(-90,Math.min(90,t[3]));if(t[2]-t[0]>=360)r=-180,i=180;else if(r>i){let t=this.getClusters([r,s,180,a],e),o=this.getClusters([-180,s,i,a],e);return t.concat(o)}let o=this.trees[this._limitZoom(e)],n=o.range(f(r),y(a),f(i),y(s)),h=o.data,l=[];for(let t of n){let e=this.stride*t;l.push(h[e+5]>1?m(h,e,this.clusterProps):this.points[h[e+3]])}return l}getChildren(t){let e=this._getOriginId(t),r=this._getOriginZoom(t),s="No cluster with the specified id.",i=this.trees[r];if(!i)throw Error(s);let a=i.data;if(e*this.stride>=a.length)throw Error(s);let o=this.options.radius/(this.options.extent*Math.pow(2,r-1)),n=a[e*this.stride],h=a[e*this.stride+1],l=i.within(n,h,o),p=[];for(let e of l){let r=e*this.stride;a[r+4]===t&&p.push(a[r+5]>1?m(a,r,this.clusterProps):this.points[a[r+3]])}if(0===p.length)throw Error(s);return p}getLeaves(t,e,r){e=e||10,r=r||0;let s=[];return this._appendLeaves(s,t,e,r,0),s}getTile(t,e,r){let s=this.trees[this._limitZoom(t)],i=Math.pow(2,t),{extent:a,radius:o}=this.options,n=o/a,h=(r-n)/i,l=(r+1+n)/i,p={features:[]};return this._addTileFeatures(s.range((e-n)/i,h,(e+1+n)/i,l),s.data,e,r,i,p),0===e&&this._addTileFeatures(s.range(1-n/i,h,1,l),s.data,i,r,i,p),e===i-1&&this._addTileFeatures(s.range(0,h,n/i,l),s.data,-1,r,i,p),p.features.length?p:null}getClusterExpansionZoom(t){let e=this._getOriginZoom(t)-1;for(;e<=this.options.maxZoom;){let r=this.getChildren(t);if(e++,1!==r.length)break;t=r[0].properties.cluster_id}return e}_appendLeaves(t,e,r,s,i){for(let a of this.getChildren(e)){let e=a.properties;if(e&&e.cluster?i+e.point_count<=s?i+=e.point_count:i=this._appendLeaves(t,e.cluster_id,r,s,i):i<s?i++:t.push(a),t.length===r)break}return i}_createTree(t){let e=new n(t.length/this.stride|0,this.options.nodeSize,Float32Array);for(let r=0;r<t.length;r+=this.stride)e.add(t[r],t[r+1]);return e.finish(),e.data=t,e}_addTileFeatures(t,e,r,s,i,a){for(let o of t){let t,n,h,l,p=o*this.stride,d=e[p+5]>1;if(d)t=g(e,p,this.clusterProps),n=e[p],h=e[p+1];else{let r=this.points[e[p+3]];t=r.properties;let[s,i]=r.geometry.coordinates;n=f(s),h=y(i)}let u={type:1,geometry:[[Math.round(this.options.extent*(n*i-r)),Math.round(this.options.extent*(h*i-s))]],tags:t};void 0!==(l=d||this.options.generateId?e[p+3]:this.points[e[p+3]].id)&&(u.id=l),a.features.push(u)}}_limitZoom(t){return Math.max(this.options.minZoom,Math.min(Math.floor(+t),this.options.maxZoom+1))}_cluster(t,e){let{radius:r,extent:s,reduce:i,minPoints:a}=this.options,o=r/(s*Math.pow(2,e)),n=t.data,h=[],l=this.stride;for(let r=0;r<n.length;r+=l){if(n[r+2]<=e)continue;n[r+2]=e;let s=n[r],p=n[r+1],d=t.within(n[r],n[r+1],o),u=n[r+5],c=u;for(let t of d){let r=t*l;n[r+2]>e&&(c+=n[r+5])}if(c>u&&c>=a){let t,a=s*u,o=p*u,m=-1,g=((r/l|0)<<5)+(e+1)+this.points.length;for(let s of d){let h=s*l;if(n[h+2]<=e)continue;n[h+2]=e;let p=n[h+5];a+=n[h]*p,o+=n[h+1]*p,n[h+4]=g,i&&(t||(t=this._map(n,r,!0),m=this.clusterProps.length,this.clusterProps.push(t)),i(t,this._map(n,h)))}n[r+4]=g,h.push(a/c,o/c,1/0,g,-1,c),i&&h.push(m)}else{for(let t=0;t<l;t++)h.push(n[r+t]);if(c>1)for(let t of d){let r=t*l;if(!(n[r+2]<=e)){n[r+2]=e;for(let t=0;t<l;t++)h.push(n[r+t])}}}}return h}_getOriginId(t){return t-this.points.length>>5}_getOriginZoom(t){return(t-this.points.length)%32}_map(t,e,r){if(t[e+5]>1){let s=this.clusterProps[t[e+6]];return r?Object.assign({},s):s}let s=this.points[t[e+3]].properties,i=this.options.map(s);return r&&i===s?Object.assign({},i):i}}function m(t,e,r){return{type:"Feature",id:t[e+3],properties:g(t,e,r),geometry:{type:"Point",coordinates:[(t[e]-.5)*360,360*Math.atan(Math.exp((180-360*t[e+1])*Math.PI/180))/Math.PI-90]}}}function g(t,e,r){let s=t[e+5],i=s>=1e4?`${Math.round(s/1e3)}k`:s>=1e3?`${Math.round(s/100)/10}k`:s,a=t[e+6];return Object.assign(-1===a?{}:Object.assign({},r[a]),{cluster:!0,cluster_id:t[e+3],point_count:s,point_count_abbreviated:i})}function f(t){return t/360+.5}function y(t){let e=Math.sin(t*Math.PI/180),r=.5-.25*Math.log((1+e)/(1-e))/Math.PI;return r<0?0:r>1?1:r}class k{static isAdvancedMarkerAvailable(t){return google.maps.marker&&!0===t.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(t){return google.maps.marker&&t instanceof google.maps.marker.AdvancedMarkerElement}static setMap(t,e){this.isAdvancedMarker(t)?t.map=e:t.setMap(e)}static getPosition(t){if(this.isAdvancedMarker(t)){if(t.position){if(t.position instanceof google.maps.LatLng)return t.position;if(t.position.lat&&t.position.lng)return new google.maps.LatLng(t.position.lat,t.position.lng)}return new google.maps.LatLng(null)}return t.getPosition()}static getVisible(t){return!!this.isAdvancedMarker(t)||t.getVisible()}}class M{constructor({markers:t,position:e}){this.markers=t,e&&(e instanceof google.maps.LatLng?this._position=e:this._position=new google.maps.LatLng(e))}get bounds(){if(0===this.markers.length&&!this._position)return;let t=new google.maps.LatLngBounds(this._position,this._position);for(let e of this.markers)t.extend(k.getPosition(e));return t}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter(t=>k.getVisible(t)).length}push(t){this.markers.push(t)}delete(){this.marker&&(k.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}class w{constructor({maxZoom:t=16}){this.maxZoom=t}noop({markers:t}){return x(t)}}let x=t=>t.map(t=>new M({position:k.getPosition(t),markers:[t]}));class A extends w{constructor(t){var{maxZoom:e,radius:r=60}=t,s=function(t,e){var r={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&0>e.indexOf(s)&&(r[s]=t[s]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(t);i<s.length;i++)0>e.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(t,s[i])&&(r[s[i]]=t[s[i]]);return r}(t,["maxZoom","radius"]);super({maxZoom:e}),this.state={zoom:-1},this.superCluster=new c(Object.assign({maxZoom:this.maxZoom,radius:r},s))}calculate(t){let e=!1,r={zoom:t.map.getZoom()};if(!a()(t.markers,this.markers)){e=!0,this.markers=[...t.markers];let r=this.markers.map(t=>{let e=k.getPosition(t);return{type:"Feature",geometry:{type:"Point",coordinates:[e.lng(),e.lat()]},properties:{marker:t}}});this.superCluster.load(r)}return!e&&(this.state.zoom<=this.maxZoom||r.zoom<=this.maxZoom)&&(e=!a()(this.state,r)),this.state=r,e&&(this.clusters=this.cluster(t)),{clusters:this.clusters,changed:e}}cluster({map:t}){return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map(t=>this.transformCluster(t))}transformCluster({geometry:{coordinates:[t,e]},properties:r}){if(r.cluster)return new M({markers:this.superCluster.getLeaves(r.cluster_id,1/0).map(t=>t.properties.marker),position:{lat:e,lng:t}});let s=r.marker;return new M({markers:[s],position:k.getPosition(s)})}}class v{constructor(t,e){this.markers={sum:t.length};let r=e.map(t=>t.count),s=r.reduce((t,e)=>t+e,0);this.clusters={count:e.length,markers:{mean:s/e.length,sum:s,min:Math.min(...r),max:Math.max(...r)}}}}class _{render({count:t,position:e},r,s){let i=t>Math.max(10,r.clusters.markers.mean)?"#ff0000":"#0000ff",a=`<svg fill="${i}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">
<circle cx="120" cy="120" opacity=".6" r="70" />
<circle cx="120" cy="120" opacity=".3" r="90" />
<circle cx="120" cy="120" opacity=".2" r="110" />
<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">${t}</text>
</svg>`,o=`Cluster of ${t} markers`,n=Number(google.maps.Marker.MAX_ZINDEX)+t;if(k.isAdvancedMarkerAvailable(s)){let t=new DOMParser().parseFromString(a,"image/svg+xml").documentElement;return t.setAttribute("transform","translate(0 25)"),new google.maps.marker.AdvancedMarkerElement({map:s,position:e,zIndex:n,title:o,content:t})}let h={position:e,zIndex:n,title:o,icon:{url:`data:image/svg+xml;base64,${btoa(a)}`,anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(h)}}class E{constructor(){!function(t,e){for(let r in e.prototype)t.prototype[r]=e.prototype[r]}(E,google.maps.OverlayView)}}!function(t){t.CLUSTERING_BEGIN="clusteringbegin",t.CLUSTERING_END="clusteringend",t.CLUSTER_CLICK="click"}(s||(s={}));let b=(t,e,r)=>{r.fitBounds(e.bounds)};class C extends E{constructor({map:t,markers:e=[],algorithmOptions:r={},algorithm:s=new A(r),renderer:i=new _,onClusterClick:a=b}){super(),this.markers=[...e],this.clusters=[],this.algorithm=s,this.renderer=i,this.onClusterClick=a,t&&this.setMap(t)}addMarker(t,e){!this.markers.includes(t)&&(this.markers.push(t),e||this.render())}addMarkers(t,e){t.forEach(t=>{this.addMarker(t,!0)}),e||this.render()}removeMarker(t,e){let r=this.markers.indexOf(t);return -1!==r&&(k.setMap(t,null),this.markers.splice(r,1),e||this.render(),!0)}removeMarkers(t,e){let r=!1;return t.forEach(t=>{r=this.removeMarker(t,!0)||r}),r&&!e&&this.render(),r}clearMarkers(t){this.markers.length=0,t||this.render()}render(){let t=this.getMap();if(t instanceof google.maps.Map&&t.getProjection()){google.maps.event.trigger(this,s.CLUSTERING_BEGIN,this);let{clusters:e,changed:r}=this.algorithm.calculate({markers:this.markers,map:t,mapCanvasProjection:this.getProjection()});if(r||void 0==r){let t=new Set;for(let r of e)1==r.markers.length&&t.add(r.markers[0]);let r=[];for(let e of this.clusters)null!=e.marker&&(1==e.markers.length?t.has(e.marker)||k.setMap(e.marker,null):r.push(e.marker));this.clusters=e,this.renderClusters(),requestAnimationFrame(()=>r.forEach(t=>k.setMap(t,null)))}google.maps.event.trigger(this,s.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach(t=>k.setMap(t,null)),this.clusters.forEach(t=>t.delete()),this.clusters=[]}renderClusters(){let t=new v(this.markers,this.clusters),e=this.getMap();this.clusters.forEach(r=>{1===r.markers.length?r.marker=r.markers[0]:(r.marker=this.renderer.render(r,t,e),r.markers.forEach(t=>k.setMap(t,null)),this.onClusterClick&&r.marker.addListener("click",t=>{google.maps.event.trigger(this,s.CLUSTER_CLICK,r),this.onClusterClick(t,r,e)})),k.setMap(r.marker,e)})}}}}]);