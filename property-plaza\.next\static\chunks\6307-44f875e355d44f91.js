"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6307],{2564:(e,t,n)=>{n.d(t,{s:()=>a});var r=n(12115),l=n(63540),o=n(95155),a=r.forwardRef((e,t)=>(0,o.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden"},36307:(e,t,n)=>{n.d(t,{UC:()=>tu,YJ:()=>td,In:()=>ti,q7:()=>tv,VF:()=>th,p4:()=>tp,JU:()=>tf,ZL:()=>ts,bL:()=>tl,wn:()=>tw,PP:()=>tm,wv:()=>tg,l9:()=>to,WT:()=>ta,LM:()=>tc});var r,l=n(12115),o=n(47650),a=n(89367),i=n(85185),s=n(76589),u=n(6101),c=n(46081),d=n(94315),f=n(63540),v=n(39033),p=n(95155),h="dismissableLayer.update",m=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=l.forwardRef((e,t)=>{var n,o;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:d,onInteractOutside:w,onDismiss:b,...E}=e,x=l.useContext(m),[C,S]=l.useState(null),R=null!=(o=null==C?void 0:C.ownerDocument)?o:null==(n=globalThis)?void 0:n.document,[,L]=l.useState({}),N=(0,u.s)(t,e=>S(e)),T=Array.from(x.layers),[P]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),k=T.indexOf(P),j=C?T.indexOf(C):-1,D=x.layersWithOutsidePointerEventsDisabled.size>0,I=j>=k,M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){y("dismissableLayer.pointerDownOutside",r,l,{discrete:!0})},l={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...x.branches].some(e=>e.contains(t));I&&!n&&(null==c||c(e),null==w||w(e),e.defaultPrevented||null==b||b())},R),O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,v.c)(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&y("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...x.branches].some(e=>e.contains(t))&&(null==d||d(e),null==w||w(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,v.c)(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===x.layers.size-1&&(null==s||s(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),l.useEffect(()=>{if(C)return a&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(C)),x.layers.add(C),g(),()=>{a&&1===x.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[C,R,a,x]),l.useEffect(()=>()=>{C&&(x.layers.delete(C),x.layersWithOutsidePointerEventsDisabled.delete(C),g())},[C,x]),l.useEffect(()=>{let e=()=>L({});return document.addEventListener(h,e),()=>document.removeEventListener(h,e)},[]),(0,p.jsx)(f.sG.div,{...E,ref:N,style:{pointerEvents:D?I?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});function g(){let e=new CustomEvent(h);document.dispatchEvent(e)}function y(e,t,n,r){let{discrete:l}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),l?(0,f.hO)(o,a):o.dispatchEvent(a)}w.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(m),r=l.useRef(null),o=(0,u.s)(t,r);return l.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(f.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var b=n(92293),E="focusScope.autoFocusOnMount",x="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},S=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[s,c]=l.useState(null),d=(0,v.c)(o),h=(0,v.c)(a),m=l.useRef(null),w=(0,u.s)(t,e=>c(e)),g=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let e=function(e){if(g.paused||!s)return;let t=e.target;s.contains(t)?m.current=t:N(m.current,{select:!0})},t=function(e){if(g.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||N(m.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&N(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,g.paused]),l.useEffect(()=>{if(s){T.add(g);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(E,C);s.addEventListener(E,d),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(N(r,{select:t}),document.activeElement!==n)return}(R(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&N(s))}return()=>{s.removeEventListener(E,d),setTimeout(()=>{let t=new CustomEvent(x,C);s.addEventListener(x,h),s.dispatchEvent(t),t.defaultPrevented||N(null!=e?e:document.body,{select:!0}),s.removeEventListener(x,h),T.remove(g)},0)}}},[s,d,h,g]);let y=l.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,l=document.activeElement;if(t&&l){let t=e.currentTarget,[r,o]=function(e){let t=R(e);return[L(t,e),L(t.reverse(),e)]}(t);r&&o?e.shiftKey||l!==o?e.shiftKey&&l===r&&(e.preventDefault(),n&&N(o,{select:!0})):(e.preventDefault(),n&&N(r,{select:!0})):l===t&&e.preventDefault()}},[n,r,g.paused]);return(0,p.jsx)(f.sG.div,{tabIndex:-1,...i,ref:w,onKeyDown:y})});function R(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function L(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function N(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}S.displayName="FocusScope";var T=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=P(e,t)).unshift(t)},remove(t){var n;null==(n=(e=P(e,t))[0])||n.resume()}}}();function P(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var k=n(61285),j=n(63753),D=n(52712),I=l.forwardRef((e,t)=>{var n,r;let{container:a,...i}=e,[s,u]=l.useState(!1);(0,D.N)(()=>u(!0),[]);let c=a||s&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?o.createPortal((0,p.jsx)(f.sG.div,{...i,ref:t}),c):null});I.displayName="Portal";var M=l.forwardRef((e,t)=>{let{children:n,...r}=e,o=l.Children.toArray(n),a=o.find(F);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,p.jsx)(O,{...r,ref:t,children:l.isValidElement(e)?l.cloneElement(e,void 0,n):null})}return(0,p.jsx)(O,{...r,ref:t,children:n})});M.displayName="Slot";var O=l.forwardRef((e,t)=>{let{children:n,...r}=e;if(l.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return l.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{o(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?(0,u.t)(t,e):e})}return l.Children.count(n)>1?l.Children.only(null):null});O.displayName="SlotClone";var B=({children:e})=>(0,p.jsx)(p.Fragment,{children:e});function F(e){return l.isValidElement(e)&&e.type===B}var A=n(5845),W=n(45503),H=n(2564),_=n(38168),K=n(39249),V=n(56985),G=n(70464),U=(0,n(37548).f)(),z=function(){},X=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:z,onWheelCapture:z,onTouchMoveCapture:z}),o=r[0],a=r[1],i=e.forwardProps,s=e.children,u=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,p=e.noIsolation,h=e.inert,m=e.allowPinchZoom,w=e.as,g=e.gapMode,y=(0,K.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=(0,G.S)([n,t]),E=(0,K.Cl)((0,K.Cl)({},y),o);return l.createElement(l.Fragment,null,d&&l.createElement(v,{sideCar:U,removeScrollBar:c,shards:f,noIsolation:p,inert:h,setCallbacks:a,allowPinchZoom:!!m,lockRef:n,gapMode:g}),i?l.cloneElement(l.Children.only(s),(0,K.Cl)((0,K.Cl)({},E),{ref:b})):l.createElement(void 0===w?"div":w,(0,K.Cl)({},E,{className:u,ref:b}),s))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},X.classNames={fullWidth:V.pN,zeroRight:V.Mi};var Y=n(50514),Z=n(21515),q=n(29874),J=!1;if("undefined"!=typeof window)try{var Q=Object.defineProperty({},"passive",{get:function(){return J=!0,!0}});window.addEventListener("test",Q,Q),window.removeEventListener("test",Q,Q)}catch(e){J=!1}var $=!!J&&{passive:!1},ee=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},et=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),en(e,r)){var l=er(e,r);if(l[1]>l[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},en=function(e,t){return"v"===e?ee(t,"overflowY"):ee(t,"overflowX")},er=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},el=function(e,t,n,r,l){var o,a=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),i=a*r,s=n.target,u=t.contains(s),c=!1,d=i>0,f=0,v=0;do{var p=er(e,s),h=p[0],m=p[1]-p[2]-a*h;(h||m)&&en(e,s)&&(f+=m,v+=h),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(l&&1>Math.abs(f)||!l&&i>f)?c=!0:!d&&(l&&1>Math.abs(v)||!l&&-i>v)&&(c=!0),c},eo=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ea=function(e){return[e.deltaX,e.deltaY]},ei=function(e){return e&&"current"in e?e.current:e},es=0,eu=[];let ec=(0,Y.m)(U,function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(es++)[0],a=l.useState(q.T0)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,K.fX)([e.lockRef.current],(e.shards||[]).map(ei),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!i.current.allowPinchZoom;var l,o=eo(e),a=n.current,s="deltaX"in e?e.deltaX:a[0]-o[0],u="deltaY"in e?e.deltaY:a[1]-o[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=et(d,c);if(!f)return!0;if(f?l=d:(l="v"===d?"h":"v",f=et(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=l),!l)return!0;var v=r.current||l;return el(v,t,e,"h"===v?s:u,!0)},[]),u=l.useCallback(function(e){if(eu.length&&eu[eu.length-1]===a){var n="deltaY"in e?ea(e):eo(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var l=(i.current.shards||[]).map(ei).filter(Boolean).filter(function(t){return t.contains(e.target)});(l.length>0?s(e,l[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,l){var o={name:e,delta:n,target:r,should:l,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=l.useCallback(function(e){n.current=eo(e),r.current=void 0},[]),f=l.useCallback(function(t){c(t.type,ea(t),t.target,s(t,e.lockRef.current))},[]),v=l.useCallback(function(t){c(t.type,eo(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return eu.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",u,$),document.addEventListener("touchmove",u,$),document.addEventListener("touchstart",d,$),function(){eu=eu.filter(function(e){return e!==a}),document.removeEventListener("wheel",u,$),document.removeEventListener("touchmove",u,$),document.removeEventListener("touchstart",d,$)}},[]);var p=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?l.createElement(Z.jp,{gapMode:e.gapMode}):null)});var ed=l.forwardRef(function(e,t){return l.createElement(X,(0,K.Cl)({},e,{ref:t,sideCar:ec}))});ed.classNames=X.classNames;var ef=[" ","Enter","ArrowUp","ArrowDown"],ev=[" ","Enter"],ep="Select",[eh,em,ew]=(0,s.N)(ep),[eg,ey]=(0,c.A)(ep,[ew,j.Bk]),eb=(0,j.Bk)(),[eE,ex]=eg(ep),[eC,eS]=eg(ep),eR=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:f,autoComplete:v,disabled:h,required:m}=e,w=eb(t),[g,y]=l.useState(null),[b,E]=l.useState(null),[x,C]=l.useState(!1),S=(0,d.jH)(c),[R=!1,L]=(0,A.i)({prop:r,defaultProp:o,onChange:a}),[N,T]=(0,A.i)({prop:i,defaultProp:s,onChange:u}),P=l.useRef(null),D=!g||!!g.closest("form"),[I,M]=l.useState(new Set),O=Array.from(I).map(e=>e.props.value).join(";");return(0,p.jsx)(j.bL,{...w,children:(0,p.jsxs)(eE,{required:m,scope:t,trigger:g,onTriggerChange:y,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:x,onValueNodeHasChildrenChange:C,contentId:(0,k.B)(),value:N,onValueChange:T,open:R,onOpenChange:L,dir:S,triggerPointerDownPosRef:P,disabled:h,children:[(0,p.jsx)(eh.Provider,{scope:t,children:(0,p.jsx)(eC,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{M(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),D?(0,p.jsxs)(tt,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:v,value:N,onChange:e=>T(e.target.value),disabled:h,children:[void 0===N?(0,p.jsx)("option",{value:""}):null,Array.from(I)]},O):null]})})};eR.displayName=ep;var eL="SelectTrigger",eN=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...l}=e,o=eb(n),a=ex(eL,n),s=a.disabled||r,c=(0,u.s)(t,a.onTriggerChange),d=em(n),[v,h,m]=tn(e=>{let t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===a.value),r=tr(t,e,n);void 0!==r&&a.onValueChange(r.value)}),w=()=>{s||(a.onOpenChange(!0),m())};return(0,p.jsx)(j.Mz,{asChild:!0,...o,children:(0,p.jsx)(f.sG.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":te(a.value)?"":void 0,...l,ref:c,onClick:(0,i.m)(l.onClick,e=>{e.currentTarget.focus()}),onPointerDown:(0,i.m)(l.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(w(),a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:(0,i.m)(l.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&ef.includes(e.key)&&(w(),e.preventDefault())})})})});eN.displayName=eL;var eT="SelectValue",eP=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:a="",...i}=e,s=ex(eT,n),{onValueNodeHasChildrenChange:c}=s,d=void 0!==o,v=(0,u.s)(t,s.onValueNodeChange);return(0,D.N)(()=>{c(d)},[c,d]),(0,p.jsx)(f.sG.span,{...i,ref:v,style:{pointerEvents:"none"},children:te(s.value)?(0,p.jsx)(p.Fragment,{children:a}):o})});eP.displayName=eT;var ek=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,p.jsx)(f.sG.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});ek.displayName="SelectIcon";var ej=e=>(0,p.jsx)(I,{asChild:!0,...e});ej.displayName="SelectPortal";var eD="SelectContent",eI=l.forwardRef((e,t)=>{let n=ex(eD,e.__scopeSelect),[r,a]=l.useState();return((0,D.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,p.jsx)(eB,{...e,ref:t}):r?o.createPortal((0,p.jsx)(eM,{scope:e.__scopeSelect,children:(0,p.jsx)(eh.Slot,{scope:e.__scopeSelect,children:(0,p.jsx)("div",{children:e.children})})}),r):null});eI.displayName=eD;var[eM,eO]=eg(eD),eB=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:s,side:c,sideOffset:d,align:f,alignOffset:v,arrowPadding:h,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:E,avoidCollisions:x,...C}=e,R=ex(eD,n),[L,N]=l.useState(null),[T,P]=l.useState(null),k=(0,u.s)(t,e=>N(e)),[j,D]=l.useState(null),[I,O]=l.useState(null),B=em(n),[F,A]=l.useState(!1),W=l.useRef(!1);l.useEffect(()=>{if(L)return(0,_.Eq)(L)},[L]),(0,b.Oh)();let H=l.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),null==n||n.focus(),document.activeElement!==l))return},[B,T]),K=l.useCallback(()=>H([j,L]),[H,j,L]);l.useEffect(()=>{F&&K()},[F,K]);let{onOpenChange:V,triggerPointerDownPosRef:G}=R;l.useEffect(()=>{if(L){let e={x:0,y:0},t=t=>{var n,r,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(n=G.current)?void 0:n.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(r=G.current)?void 0:r.y)?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():L.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),G.current=null};return null!==G.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[L,V,G]),l.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[U,z]=tn(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=tr(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),X=l.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==R.value&&R.value===t||r)&&(D(e),r&&(W.current=!0))},[R.value]),Y=l.useCallback(()=>null==L?void 0:L.focus(),[L]),Z=l.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==R.value&&R.value===t||r)&&O(e)},[R.value]),q="popper"===r?eA:eF,J=q===eA?{side:c,sideOffset:d,align:f,alignOffset:v,arrowPadding:h,collisionBoundary:m,collisionPadding:g,sticky:y,hideWhenDetached:E,avoidCollisions:x}:{};return(0,p.jsx)(eM,{scope:n,content:L,viewport:T,onViewportChange:P,itemRefCallback:X,selectedItem:j,onItemLeave:Y,itemTextRefCallback:Z,focusSelectedItem:K,selectedItemText:I,position:r,isPositioned:F,searchRef:U,children:(0,p.jsx)(ed,{as:M,allowPinchZoom:!0,children:(0,p.jsx)(S,{asChild:!0,trapped:R.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.m)(o,e=>{var t;null==(t=R.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,p.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>R.onOpenChange(!1),children:(0,p.jsx)(q,{role:"listbox",id:R.contentId,"data-state":R.open?"open":"closed",dir:R.dir,onContextMenu:e=>e.preventDefault(),...C,...J,onPlaced:()=>A(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,i.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>H(t)),e.preventDefault()}})})})})})})});eB.displayName="SelectContentImpl";var eF=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...o}=e,i=ex(eD,n),s=eO(eD,n),[c,d]=l.useState(null),[v,h]=l.useState(null),m=(0,u.s)(t,e=>h(e)),w=em(n),g=l.useRef(!1),y=l.useRef(!0),{viewport:b,selectedItem:E,selectedItemText:x,focusSelectedItem:C}=s,S=l.useCallback(()=>{if(i.trigger&&i.valueNode&&c&&v&&b&&E&&x){let e=i.trigger.getBoundingClientRect(),t=v.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),l=x.getBoundingClientRect();if("rtl"!==i.dir){let r=l.left-t.left,o=n.left-r,i=e.left-o,s=e.width+i,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.q)(o,[10,d-u]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let r=t.right-l.right,o=window.innerWidth-n.right-r,i=window.innerWidth-e.right-o,s=e.width+i,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,a.q)(o,[10,d-u]);c.style.minWidth=s+"px",c.style.right=f+"px"}let o=w(),s=window.innerHeight-20,u=b.scrollHeight,d=window.getComputedStyle(v),f=parseInt(d.borderTopWidth,10),p=parseInt(d.paddingTop,10),h=parseInt(d.borderBottomWidth,10),m=f+p+u+parseInt(d.paddingBottom,10)+h,y=Math.min(5*E.offsetHeight,m),C=window.getComputedStyle(b),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),L=e.top+e.height/2-10,N=E.offsetHeight/2,T=f+p+(E.offsetTop+N);if(T<=L){let e=E===o[o.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-L,N+(e?R:0)+(v.clientHeight-b.offsetTop-b.offsetHeight)+h);c.style.height=T+t+"px"}else{let e=E===o[0].ref.current;c.style.top="0px";let t=Math.max(L,f+b.offsetTop+(e?S:0)+N);c.style.height=t+(m-T)+"px",b.scrollTop=T-L+b.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=y+"px",c.style.maxHeight=s+"px",null==r||r(),requestAnimationFrame(()=>g.current=!0)}},[w,i.trigger,i.valueNode,c,v,b,E,x,i.dir,r]);(0,D.N)(()=>S(),[S]);let[R,L]=l.useState();(0,D.N)(()=>{v&&L(window.getComputedStyle(v).zIndex)},[v]);let N=l.useCallback(e=>{e&&!0===y.current&&(S(),null==C||C(),y.current=!1)},[S,C]);return(0,p.jsx)(eW,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:N,children:(0,p.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,p.jsx)(f.sG.div,{...o,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});eF.displayName="SelectItemAlignedPosition";var eA=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,a=eb(n);return(0,p.jsx)(j.UC,{...a,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eA.displayName="SelectPopperPosition";var[eW,eH]=eg(eD,{}),e_="SelectViewport",eK=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...o}=e,a=eO(e_,n),s=eH(e_,n),c=(0,u.s)(t,a.onViewportChange),d=l.useRef(0);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,p.jsx)(eh.Slot,{scope:n,children:(0,p.jsx)(f.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:c,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:(0,i.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});eK.displayName=e_;var eV="SelectGroup",[eG,eU]=eg(eV),ez=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,k.B)();return(0,p.jsx)(eG,{scope:n,id:l,children:(0,p.jsx)(f.sG.div,{role:"group","aria-labelledby":l,...r,ref:t})})});ez.displayName=eV;var eX="SelectLabel",eY=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=eU(eX,n);return(0,p.jsx)(f.sG.div,{id:l.id,...r,ref:t})});eY.displayName=eX;var eZ="SelectItem",[eq,eJ]=eg(eZ),eQ=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:o=!1,textValue:a,...s}=e,c=ex(eZ,n),d=eO(eZ,n),v=c.value===r,[h,m]=l.useState(null!=a?a:""),[w,g]=l.useState(!1),y=(0,u.s)(t,e=>{var t;return null==(t=d.itemRefCallback)?void 0:t.call(d,e,r,o)}),b=(0,k.B)(),E=()=>{o||(c.onValueChange(r),c.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,p.jsx)(eq,{scope:n,value:r,disabled:o,textId:b,isSelected:v,onItemTextChange:l.useCallback(e=>{m(t=>{var n;return t||(null!=(n=null==e?void 0:e.textContent)?n:"").trim()})},[]),children:(0,p.jsx)(eh.ItemSlot,{scope:n,value:r,disabled:o,textValue:h,children:(0,p.jsx)(f.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":w?"":void 0,"aria-selected":v&&w,"data-state":v?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...s,ref:y,onFocus:(0,i.m)(s.onFocus,()=>g(!0)),onBlur:(0,i.m)(s.onBlur,()=>g(!1)),onPointerUp:(0,i.m)(s.onPointerUp,E),onPointerMove:(0,i.m)(s.onPointerMove,e=>{if(o){var t;null==(t=d.onItemLeave)||t.call(d)}else e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.m)(s.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=d.onItemLeave)||t.call(d)}}),onKeyDown:(0,i.m)(s.onKeyDown,e=>{var t;((null==(t=d.searchRef)?void 0:t.current)===""||" "!==e.key)&&(ev.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});eQ.displayName=eZ;var e$="SelectItemText",e0=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:a,...i}=e,s=ex(e$,n),c=eO(e$,n),d=eJ(e$,n),v=eS(e$,n),[h,m]=l.useState(null),w=(0,u.s)(t,e=>m(e),d.onItemTextChange,e=>{var t;return null==(t=c.itemTextRefCallback)?void 0:t.call(c,e,d.value,d.disabled)}),g=null==h?void 0:h.textContent,y=l.useMemo(()=>(0,p.jsx)("option",{value:d.value,disabled:d.disabled,children:g},d.value),[d.disabled,d.value,g]),{onNativeOptionAdd:b,onNativeOptionRemove:E}=v;return(0,D.N)(()=>(b(y),()=>E(y)),[b,E,y]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(f.sG.span,{id:d.textId,...i,ref:w}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(i.children,s.valueNode):null]})});e0.displayName=e$;var e1="SelectItemIndicator",e5=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return eJ(e1,n).isSelected?(0,p.jsx)(f.sG.span,{"aria-hidden":!0,...r,ref:t}):null});e5.displayName=e1;var e2="SelectScrollUpButton",e3=l.forwardRef((e,t)=>{let n=eO(e2,e.__scopeSelect),r=eH(e2,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,u.s)(t,r.onScrollButtonChange);return(0,D.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(e9,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});e3.displayName=e2;var e4="SelectScrollDownButton",e6=l.forwardRef((e,t)=>{let n=eO(e4,e.__scopeSelect),r=eH(e4,e.__scopeSelect),[o,a]=l.useState(!1),i=(0,u.s)(t,r.onScrollButtonChange);return(0,D.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,p.jsx)(e9,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});e6.displayName=e4;var e9=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...o}=e,a=eO("SelectScrollButton",n),s=l.useRef(null),u=em(n),c=l.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return l.useEffect(()=>()=>c(),[c]),(0,D.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,p.jsx)(f.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,i.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,i.m)(o.onPointerMove,()=>{var e;null==(e=a.onItemLeave)||e.call(a),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,i.m)(o.onPointerLeave,()=>{c()})})}),e8=l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,p.jsx)(f.sG.div,{"aria-hidden":!0,...r,ref:t})});e8.displayName="SelectSeparator";var e7="SelectArrow";function te(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=eb(n),o=ex(e7,n),a=eO(e7,n);return o.open&&"popper"===a.position?(0,p.jsx)(j.i3,{...l,...r,ref:t}):null}).displayName=e7;var tt=l.forwardRef((e,t)=>{let{value:n,...r}=e,o=l.useRef(null),a=(0,u.s)(t,o),i=(0,W.Z)(n);return l.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[i,n]),(0,p.jsx)(H.s,{asChild:!0,children:(0,p.jsx)("select",{...r,ref:a,defaultValue:n})})});function tn(e){let t=(0,v.c)(e),n=l.useRef(""),r=l.useRef(0),o=l.useCallback(e=>{let l=n.current+e;t(l),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(l)},[t]),a=l.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,a]}function tr(e,t,n){var r,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,i=(r=e,l=Math.max(a,0),r.map((e,t)=>r[(l+t)%r.length]));1===o.length&&(i=i.filter(e=>e!==n));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==n?s:void 0}tt.displayName="BubbleSelect";var tl=eR,to=eN,ta=eP,ti=ek,ts=ej,tu=eI,tc=eK,td=ez,tf=eY,tv=eQ,tp=e0,th=e5,tm=e3,tw=e6,tg=e8},45503:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(12115);function l(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}}]);