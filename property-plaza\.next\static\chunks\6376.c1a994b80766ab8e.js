"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6376],{26376:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});var s=n(35695),i=n(12115);function r(){let e=(0,s.useRouter)();return(0,i.useEffect)(()=>{let t=new AbortController,n=0,s=()=>{let t=Date.now();t>n&&"hidden"!==document.visibilityState&&(e.refresh(),n=t+5e3)},{signal:i}=t;return document.addEventListener("visibilitychange",s,{passive:!0,signal:i}),window.addEventListener("focus",s,{passive:!0,signal:i}),()=>t.abort()},[e]),null}r.displayName="RefreshOnFocus"}}]);