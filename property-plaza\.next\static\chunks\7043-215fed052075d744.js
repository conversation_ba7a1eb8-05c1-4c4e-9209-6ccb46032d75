"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7043],{2648:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(26521),i=r(7903),o=r(79035),s=r(61787),a=r(47735),h=r(87999);r(42550),r(12115),r(6690),r(91684),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=i.createTranslator,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.IntlProvider=s.IntlProvider,t.useFormatter=a.useFormatter,t.useMessages=a.useMessages,t.useNow=a.useNow,t.useTimeZone=a.useTimeZone,t.useTranslations=a.useTranslations,t.useLocale=h.useLocale},4946:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(7450),i=r(12115),o=r(60067),s=function(e){return e&&e.__esModule?e:{default:e}}(i);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return s.default.createElement(o.IntlProvider,n.extends({locale:t},r))}},7450:(e,t)=>{function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}Object.defineProperty(t,"__esModule",{value:!0}),t.extends=r},7903:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(26521),i=r(79035);r(42550),r(12115),r(6690),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.createTranslator=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),getMessageFallback:o=i.defaultGetMessageFallback,messages:s,namespace:a,onError:h=i.defaultOnError,...u}=e;return function(e,t){let{messages:r,namespace:i,...o}=e;return r=r["!"],i=n.resolveNamespace(i,"!"),n.createBaseTranslator({...o,messages:r,namespace:i})}({...u,onError:h,cache:t,formatters:r,getMessageFallback:o,messages:{"!":s},namespace:a?"!.".concat(a):"!"},"!")}},26521:(e,t,r)=>{var n=r(42550),i=r(12115),o=r(79035),s=function(e){return e&&e.__esModule?e:{default:e}}(n);function a(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let h=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class u extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),a(this,"code",void 0),a(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function l(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function c(e,t,r,n){let i=o.joinPath(n,r);if(!t)throw Error(i);let s=t;return r.split(".").forEach(t=>{let r=s[t];if(null==t||null==r)throw Error(i+" (".concat(e,")"));s=r}),s}let f=365/12*86400,m={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=u,t.IntlErrorCode=h,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:o.defaultOnError;try{if(!t)throw Error(void 0);let n=r?c(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new u(h.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:a,getMessageFallback:f=o.defaultGetMessageFallback,locale:m,messagesOrError:p,namespace:E,onError:g,timeZone:b}=e,d=p instanceof u;function T(e,t,r){let n=new u(t,r);return g(n),f({error:n,key:e,namespace:E})}function v(e,u,g){let v,y;if(d)return f({error:p,key:e,namespace:E});try{v=c(m,p,e,E)}catch(t){return T(e,h.MISSING_MESSAGE,t.message)}if("object"==typeof v){let t;return T(e,Array.isArray(v)?h.INVALID_MESSAGE:h.INSUFFICIENT_PATH,t)}let _=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(v,u);if(_)return _;a.getMessageFormat||(a.getMessageFormat=o.memoFn(function(){return new s.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:a,...arguments.length<=3?void 0:arguments[3]})},t.message));try{y=a.getMessageFormat(v,m,function(e,t){let r=t?{...e,dateTime:l(e.dateTime,t)}:e,n=s.default.formats.date,i=t?l(n,t):n,o=s.default.formats.time,a=t?l(o,t):o;return{...r,date:{...i,...r.dateTime},time:{...a,...r.dateTime}}}({...n,...g},b),{formatters:{...a,getDateTimeFormat:(e,t)=>a.getDateTimeFormat(e,{timeZone:b,...t})}})}catch(t){return T(e,h.INVALID_MESSAGE,t.message)}try{let e=y.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,o=0,s=e[r];n="function"==typeof s?e=>{let t=s(e);return i.isValidElement(t)?i.cloneElement(t,{key:r+o++}):t}:s,t[r]=n}),t}({...r,...u}));if(null==e)throw Error(void 0);return i.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return T(e,h.FORMATTING_ERROR,t.message)}}function y(e,t,r){let n=v(e,t,r);return"string"!=typeof n?T(e,h.INVALID_MESSAGE,void 0):n}return y.rich=v,y.markup=(e,t,r)=>{let n=v(e,t,r);if("string"!=typeof n){let t=new u(h.FORMATTING_ERROR,void 0);return g(t),f({error:t,key:e,namespace:E})}return n},y.raw=e=>{if(d)return f({error:p,key:e,namespace:E});try{return c(m,p,e,E)}catch(t){return T(e,h.MISSING_MESSAGE,t.message)}},y.has=e=>{if(d)return!1;try{return c(m,p,e,E),!0}catch(e){return!1}},y}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),formats:n,locale:i,now:s,onError:a=o.defaultOnError,timeZone:l}=e;function c(e){var t;return null!=(t=e)&&t.timeZone||(l?e={...e,timeZone:l}:a(new u(h.ENVIRONMENT_FALLBACK,void 0))),e}function p(e,t,r,n){let i;try{i=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new u(h.MISSING_FORMAT,void 0);throw a(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(i)}catch(e){return a(new u(h.FORMATTING_ERROR,e.message)),n()}}function E(e,t){return p(t,null==n?void 0:n.dateTime,t=>(t=c(t),r.getDateTimeFormat(i,t).format(e)),()=>String(e))}function g(){return s||(a(new u(h.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:E,number:function(e,t){return p(t,null==n?void 0:n.number,t=>r.getNumberFormat(i,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let o,s,a={};t instanceof Date||"number"==typeof t?o=new Date(t):t&&(o=null!=t.now?new Date(t.now):g(),s=t.unit,a.style=t.style,a.numberingSystem=t.numberingSystem),o||(o=g());let h=(new Date(e).getTime()-o.getTime())/1e3;s||(s=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(h)),a.numeric="second"===s?"auto":"always";let u=(n=s,Math.round(h/m[n]));return r.getRelativeTimeFormat(i,a).format(u,s)}catch(t){return a(new u(h.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let o=[],s=new Map,a=0;for(let t of e){let e;"object"==typeof t?(e=String(a),s.set(e,t)):e=String(t),o.push(e),a++}return p(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(i,e).formatToParts(o).map(e=>"literal"===e.type?e.value:s.get(e.value)||e.value);return s.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,o){return p(o,null==n?void 0:n.dateTime,n=>(n=c(n),r.getDateTimeFormat(i,n).formatRange(e,t)),()=>[E(e),E(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},27043:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(77766),i=r(97923),o=r(4946),s=r(97488);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=i.default,t.NextIntlClientProvider=o.default,Object.keys(s).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}})})},42550:(e,t,r)=>{r.r(t),r.d(t,{ErrorCode:()=>h,FormatError:()=>et,IntlMessageFormat:()=>eh,InvalidValueError:()=>er,InvalidValueTypeError:()=>en,MissingValueError:()=>ei,PART_TYPE:()=>u,default:()=>eu,formatToParts:()=>es,isFormatXMLElementFn:()=>eo});var n,i,o,s,a,h,u,l=r(39249),c=r(6690);function f(e){return e.type===i.literal}function m(e){return e.type===i.number}function p(e){return e.type===i.date}function E(e){return e.type===i.time}function g(e){return e.type===i.select}function b(e){return e.type===i.plural}function d(e){return e.type===i.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===o.number)}function v(e){return!!(e&&"object"==typeof e&&e.type===o.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(i||(i={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(o||(o={}));var y=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,_=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,H=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,A=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,B=/^(@+)?(\+|#+)?[rs]?$/g,I=/(\*)(0+)|(#+)(0+)|(0+)/g,N=/^(0+)$/;function S(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(B,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function L(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function C(e){var t=L(e);return t||{}}var M={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},P=new RegExp("^".concat(y.source,"*")),R=new RegExp("".concat(y.source,"*$"));function O(e,t){return{start:e,end:t}}var G=!!String.prototype.startsWith&&"_a".startsWith("a",1),F=!!String.fromCodePoint,U=!!Object.fromEntries,w=!!String.prototype.codePointAt,D=!!String.prototype.trimStart,k=!!String.prototype.trimEnd,V=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},X=!0;try{X=(null==(s=z("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:s[0])==="a"}catch(e){X=!1}var x=G?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},j=F?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",i=t.length,o=0;i>o;){if((e=t[o++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},K=U?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],i=n[0],o=n[1];t[i]=o}return t},Z=w?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var i=e.charCodeAt(t);return i<55296||i>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?i:(i-55296<<10)+(r-56320)+65536}},Y=D?function(e){return e.trimStart()}:function(e){return e.replace(P,"")},W=k?function(e){return e.trimEnd()}:function(e){return e.replace(R,"")};function z(e,t){return new RegExp(e,t)}if(X){var Q=z("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");a=function(e,t){var r;return Q.lastIndex=t,null!=(r=Q.exec(e)[1])?r:""}}else a=function(e,t){for(var r=[];;){var n,i=Z(e,t);if(void 0===i||$(i)||(n=i)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(i),t+=i>=65536?2:1}return j.apply(void 0,r)};var q=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var o=[];!this.isEOF();){var s=this.char();if(123===s){var a=this.parseArgument(e,r);if(a.err)return a;o.push(a.val)}else if(125===s&&e>0)break;else if(35===s&&("plural"===t||"selectordinal"===t)){var h=this.clonePosition();this.bump(),o.push({type:i.pound,location:O(h,this.clonePosition())})}else if(60!==s||this.ignoreTag||47!==this.peek())if(60===s&&!this.ignoreTag&&J(this.peek()||0)){var a=this.parseTag(e,t);if(a.err)return a;o.push(a.val)}else{var a=this.parseLiteral(e,t);if(a.err)return a;o.push(a.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,O(this.clonePosition(),this.clonePosition()));else break}return{val:o,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var o=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:i.literal,value:"<".concat(o,"/>"),location:O(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,O(r,this.clonePosition()));var s=this.parseMessage(e+1,t,!0);if(s.err)return s;var a=s.val,h=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,O(r,this.clonePosition()));if(this.isEOF()||!J(this.char()))return this.error(n.INVALID_TAG,O(h,this.clonePosition()));var u=this.clonePosition();return o!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,O(u,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:i.tag,value:o,children:a,location:O(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,O(h,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var o=this.tryParseQuote(t);if(o){n+=o;continue}var s=this.tryParseUnquoted(e,t);if(s){n+=s;continue}var a=this.tryParseLeftAngleBracket();if(a){n+=a;continue}break}var h=O(r,this.clonePosition());return{val:{type:i.literal,value:n,location:h},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(J(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return j.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),j(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,O(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,O(r,this.clonePosition()));var o=this.parseIdentifierIfPossible().value;if(!o)return this.error(n.MALFORMED_ARGUMENT,O(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,O(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:i.argument,value:o,location:O(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,O(r,this.clonePosition()));return this.parseArgumentOptions(e,t,o,r);default:return this.error(n.MALFORMED_ARGUMENT,O(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=a(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:O(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,s){var a,h=this.clonePosition(),u=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(u){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,O(h,c));case"number":case"date":case"time":this.bumpSpace();var f=null;if(this.bumpIf(",")){this.bumpSpace();var m=this.clonePosition(),p=this.parseSimpleArgStyleIfPossible();if(p.err)return p;var E=W(p.val);if(0===E.length)return this.error(n.EXPECT_ARGUMENT_STYLE,O(this.clonePosition(),this.clonePosition()));f={style:E,styleLocation:O(m,this.clonePosition())}}var g=this.tryParseArgumentClose(s);if(g.err)return g;var b=O(s,this.clonePosition());if(f&&x(null==f?void 0:f.style,"::",0)){var d=Y(f.style.slice(2));if("number"===u){var p=this.parseNumberSkeletonFromString(d,f.styleLocation);if(p.err)return p;return{val:{type:i.number,value:r,location:b,style:p.val},err:null}}if(0===d.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,b);var T,v=d;this.locale&&(v=function(e,t){for(var r="",n=0;n<e.length;n++){var i=e.charAt(n);if("j"===i){for(var o=0;n+1<e.length&&e.charAt(n+1)===i;)o++,n++;var s=1+(1&o),a=o<2?1:3+(o>>1),h=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(M[t||""]||M[n||""]||M["".concat(n,"-001")]||M["001"])[0]}(t);for(("H"==h||"k"==h)&&(a=0);a-- >0;)r+="a";for(;s-- >0;)r=h+r}else"J"===i?r+="H":r+=i}return r}(d,this.locale));var E={type:o.dateTime,pattern:v,location:f.styleLocation,parsedOptions:this.shouldParseSkeletons?(T={},v.replace(_,function(e){var t=e.length;switch(e[0]){case"G":T.era=4===t?"long":5===t?"narrow":"short";break;case"y":T.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":T.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":T.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":T.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");T.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");T.weekday=["short","long","narrow","short"][t-4];break;case"a":T.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":T.hourCycle="h12",T.hour=["numeric","2-digit"][t-1];break;case"H":T.hourCycle="h23",T.hour=["numeric","2-digit"][t-1];break;case"K":T.hourCycle="h11",T.hour=["numeric","2-digit"][t-1];break;case"k":T.hourCycle="h24",T.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":T.minute=["numeric","2-digit"][t-1];break;case"s":T.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":T.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),T):{}};return{val:{type:"date"===u?i.date:i.time,value:r,location:b,style:E},err:null}}return{val:{type:"number"===u?i.number:"date"===u?i.date:i.time,value:r,location:b,style:null!=(a=null==f?void 0:f.style)?a:null},err:null};case"plural":case"selectordinal":case"select":var y=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,O(y,(0,l.Cl)({},y)));this.bumpSpace();var H=this.parseIdentifierIfPossible(),A=0;if("select"!==u&&"offset"===H.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,O(this.clonePosition(),this.clonePosition()));this.bumpSpace();var p=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(p.err)return p;this.bumpSpace(),H=this.parseIdentifierIfPossible(),A=p.val}var B=this.tryParsePluralOrSelectOptions(e,u,t,H);if(B.err)return B;var g=this.tryParseArgumentClose(s);if(g.err)return g;var I=O(s,this.clonePosition());if("select"===u)return{val:{type:i.select,value:r,options:K(B.val),location:I},err:null};return{val:{type:i.plural,value:r,options:K(B.val),offset:A,pluralType:"plural"===u?"cardinal":"ordinal",location:I},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,O(h,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,O(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,O(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(H).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var i=t[n].split("/");if(0===i.length)throw Error("Invalid number skeleton");for(var o=i[0],s=i.slice(1),a=0;a<s.length;a++)if(0===s[a].length)throw Error("Invalid number skeleton");r.push({stem:o,options:s})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:o.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=(0,l.Cl)((0,l.Cl)((0,l.Cl)({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return(0,l.Cl)((0,l.Cl)({},e),C(t))},{}));continue;case"engineering":t=(0,l.Cl)((0,l.Cl)((0,l.Cl)({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return(0,l.Cl)((0,l.Cl)({},e),C(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(I,function(e,r,n,i,o,s){if(r)t.minimumIntegerDigits=n.length;else if(i&&o)throw Error("We currently do not support maximum integer digits");else if(s)throw Error("We currently do not support exact integer digits");return""});continue}if(N.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(A.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(A,function(e,r,n,i,o,s){return"*"===n?t.minimumFractionDigits=r.length:i&&"#"===i[0]?t.maximumFractionDigits=i.length:o&&s?(t.minimumFractionDigits=o.length,t.maximumFractionDigits=o.length+s.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var i=n.options[0];"w"===i?t=(0,l.Cl)((0,l.Cl)({},t),{trailingZeroDisplay:"stripIfInteger"}):i&&(t=(0,l.Cl)((0,l.Cl)({},t),S(i)));continue}if(B.test(n.stem)){t=(0,l.Cl)((0,l.Cl)({},t),S(n.stem));continue}var o=L(n.stem);o&&(t=(0,l.Cl)((0,l.Cl)({},t),o));var s=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!N.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);s&&(t=(0,l.Cl)((0,l.Cl)({},t),s))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,i){for(var o,s=!1,a=[],h=new Set,u=i.value,l=i.location;;){if(0===u.length){var c=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;l=O(c,this.clonePosition()),u=this.message.slice(c.offset,this.offset())}else break}if(h.has(u))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);"other"===u&&(s=!0),this.bumpSpace();var m=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,O(this.clonePosition(),this.clonePosition()));var p=this.parseMessage(e+1,t,r);if(p.err)return p;var E=this.tryParseArgumentClose(m);if(E.err)return E;a.push([u,{value:p.val,location:O(m,this.clonePosition())}]),h.add(u),this.bumpSpace(),u=(o=this.parseIdentifierIfPossible()).value,l=o.location}return 0===a.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,O(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!s?this.error(n.MISSING_OTHER_CLAUSE,O(this.clonePosition(),this.clonePosition())):{val:a,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var i=!1,o=0;!this.isEOF();){var s=this.char();if(s>=48&&s<=57)i=!0,o=10*o+(s-48),this.bump();else break}var a=O(n,this.clonePosition());return i?V(o*=r)?{val:o,err:null}:this.error(t,a):this.error(e,a)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Z(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(x(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&$(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function J(e){return e>=97&&e<=122||e>=65&&e<=90}function $(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ee(e,t){void 0===t&&(t={});var r=new q(e,t=(0,l.Cl)({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var i=SyntaxError(n[r.err.kind]);throw i.location=r.err.location,i.originalMessage=r.err.message,i}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,g(t)||b(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else m(t)&&T(t.style)||(p(t)||E(t))&&v(t.style)?delete t.style.location:d(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(h||(h={}));var et=function(e){function t(t,r,n){var i=e.call(this,t)||this;return i.code=r,i.originalMessage=n,i}return(0,l.C6)(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),er=function(e){function t(t,r,n,i){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),h.INVALID_VALUE,i)||this}return(0,l.C6)(t,e),t}(et),en=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),h.INVALID_VALUE,n)||this}return(0,l.C6)(t,e),t}(et),ei=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),h.MISSING_VALUE,r)||this}return(0,l.C6)(t,e),t}(et);function eo(e){return"function"==typeof e}function es(e,t,r,n,o,s,a){if(1===e.length&&f(e[0]))return[{type:u.literal,value:e[0].value}];for(var l=[],c=0;c<e.length;c++){var y=e[c];if(f(y)){l.push({type:u.literal,value:y.value});continue}if(y.type===i.pound){"number"==typeof s&&l.push({type:u.literal,value:r.getNumberFormat(t).format(s)});continue}var _=y.value;if(!(o&&_ in o))throw new ei(_,a);var H=o[_];if(y.type===i.argument){H&&"string"!=typeof H&&"number"!=typeof H||(H="string"==typeof H||"number"==typeof H?String(H):""),l.push({type:"string"==typeof H?u.literal:u.object,value:H});continue}if(p(y)){var A="string"==typeof y.style?n.date[y.style]:v(y.style)?y.style.parsedOptions:void 0;l.push({type:u.literal,value:r.getDateTimeFormat(t,A).format(H)});continue}if(E(y)){var A="string"==typeof y.style?n.time[y.style]:v(y.style)?y.style.parsedOptions:n.time.medium;l.push({type:u.literal,value:r.getDateTimeFormat(t,A).format(H)});continue}if(m(y)){var A="string"==typeof y.style?n.number[y.style]:T(y.style)?y.style.parsedOptions:void 0;A&&A.scale&&(H*=A.scale||1),l.push({type:u.literal,value:r.getNumberFormat(t,A).format(H)});continue}if(d(y)){var B=y.children,I=y.value,N=o[I];if(!eo(N))throw new en(I,"function",a);var S=N(es(B,t,r,n,o,s).map(function(e){return e.value}));Array.isArray(S)||(S=[S]),l.push.apply(l,S.map(function(e){return{type:"string"==typeof e?u.literal:u.object,value:e}}))}if(g(y)){var L=y.options[H]||y.options.other;if(!L)throw new er(y.value,H,Object.keys(y.options),a);l.push.apply(l,es(L.value,t,r,n,o));continue}if(b(y)){var L=y.options["=".concat(H)];if(!L){if(!Intl.PluralRules)throw new et('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',h.MISSING_INTL_API,a);var C=r.getPluralRules(t,{type:y.pluralType}).select(H-(y.offset||0));L=y.options[C]||y.options.other}if(!L)throw new er(y.value,H,Object.keys(y.options),a);l.push.apply(l,es(L.value,t,r,n,o,H-(y.offset||0)));continue}}return l.length<2?l:l.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===u.literal&&t.type===u.literal?r.value+=t.value:e.push(t),e},[])}function ea(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(u||(u={}));var eh=function(){function e(t,r,n,i){void 0===r&&(r=e.defaultLocale);var o,s,a=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=a.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===u.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return es(a.ast,a.locales,a.formatters,a.formats,e,void 0,a.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=a.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(a.locales)[0]}},this.getAst=function(){return a.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var h=i||{},f=(h.formatters,(0,l.Tt)(h,["formatters"]));this.ast=e.__parse(t,(0,l.Cl)((0,l.Cl)({},f),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(o=e.formats,n?Object.keys(o).reduce(function(e,t){var r,i;return e[t]=(r=o[t],(i=n[t])?(0,l.Cl)((0,l.Cl)((0,l.Cl)({},r||{}),i||{}),Object.keys(r).reduce(function(e,t){return e[t]=(0,l.Cl)((0,l.Cl)({},r[t]),i[t]||{}),e},{})):r),e},(0,l.Cl)({},o)):o),this.formatters=i&&i.formatters||(void 0===(s=this.formatterCache)&&(s={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,c.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:ea(s.number),strategy:c.strategies.variadic}),getDateTimeFormat:(0,c.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:ea(s.dateTime),strategy:c.strategies.variadic}),getPluralRules:(0,c.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:ea(s.pluralRules),strategy:c.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ee,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let eu=eh},47735:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(61787),i=r(87999),o=r(12115),s=r(26521);r(79035),r(6690),r(91684),r(42550);let a=!1,h="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=i.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:a,timeZone:h}=i.useIntlContext();return o.useMemo(()=>s.createFormatter({formats:e,locale:r,now:n,onError:a,timeZone:h,_formatters:t}),[e,t,n,r,a,h])},t.useMessages=function(){let e=i.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=i.useIntlContext(),[n,s]=o.useState(r||new Date);return o.useEffect(()=>{if(!t)return;let e=setInterval(()=>{s(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return i.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:u,formats:l,formatters:c,getMessageFallback:f,locale:m,onError:p,timeZone:E}=i.useIntlContext(),g=e["!"],b=s.resolveNamespace(t,"!");return E||a||!h||(a=!0,p(new s.IntlError(s.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),o.useMemo(()=>s.createBaseTranslator({cache:n,formatters:c,getMessageFallback:f,messages:g,defaultTranslationValues:u,namespace:b,onError:p,formats:l,locale:m,timeZone:E}),[n,c,f,g,u,b,p,l,m,E])}({"!":i.useIntlContext().messages},e?"!.".concat(e):"!","!")}},60067:(e,t,r)=>{e.exports=r(61787)},65403:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},77766:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(97488);function i(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let o=i(0,n.useTranslations);t.useFormatter=i(0,n.useFormatter),t.useTranslations=o,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},80942:(e,t,r)=>{e.exports=r(97526)},97488:(e,t,r)=>{e.exports=r(2648)},97923:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(35695),i=r(80942),o=r(65403);t.default=function(){let e,t=n.useParams();try{e=i.useLocale()}catch(r){if("string"!=typeof(null==t?void 0:t[o.LOCALE_SEGMENT_NAME]))throw r;e=t[o.LOCALE_SEGMENT_NAME]}return e}}}]);