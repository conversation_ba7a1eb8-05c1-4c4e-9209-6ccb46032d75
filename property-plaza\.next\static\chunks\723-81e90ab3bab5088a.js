"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[723],{6101:(t,e,n)=>{n.d(e,{s:()=>s,t:()=>r});var i=n(12115);function r(...t){return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}function s(...t){return i.useCallback(r(...t),t)}},6784:(t,e,n)=>{n.d(e,{II:()=>h,v_:()=>a,wm:()=>l});var i=n(50920),r=n(21239),s=n(73504),o=n(52020);function u(t){return Math.min(1e3*2**t,3e4)}function a(t){return(t??"online")!=="online"||r.t.isOnline()}var c=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function l(t){return t instanceof c}function h(t){let e,n=!1,l=0,h=!1,d=(0,s.T)(),f=()=>i.m.isFocused()&&("always"===t.networkMode||r.t.isOnline())&&t.canRun(),p=()=>a(t.networkMode)&&t.canRun(),y=n=>{h||(h=!0,t.onSuccess?.(n),e?.(),d.resolve(n))},v=n=>{h||(h=!0,t.onError?.(n),e?.(),d.reject(n))},m=()=>new Promise(n=>{e=t=>{(h||f())&&n(t)},t.onPause?.()}).then(()=>{e=void 0,h||t.onContinue?.()}),b=()=>{let e;if(h)return;let i=0===l?t.initialPromise:void 0;try{e=i??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(y).catch(e=>{if(h)return;let i=t.retry??3*!o.S$,r=t.retryDelay??u,s="function"==typeof r?r(l,e):r,a=!0===i||"number"==typeof i&&l<i||"function"==typeof i&&i(l,e);if(n||!a)return void v(e);l++,t.onFail?.(l,e),(0,o.yy)(s).then(()=>f()?void 0:m()).then(()=>{n?v(e):b()})})};return{promise:d,cancel:e=>{h||(v(new c(e)),t.abort?.())},continue:()=>(e?.(),d),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:p,start:()=>(p()?b():m().then(b),d)}}},7165:(t,e,n)=>{n.d(e,{j:()=>i});var i=function(){let t=[],e=0,n=t=>{t()},i=t=>{t()},r=t=>setTimeout(t,0),s=i=>{e?t.push(i):r(()=>{n(i)})};return{batch:s=>{let o;e++;try{o=s()}finally{--e||(()=>{let e=t;t=[],e.length&&r(()=>{i(()=>{e.forEach(t=>{n(t)})})})})()}return o},batchCalls:t=>(...e)=>{s(()=>{t(...e)})},schedule:s,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}()},21239:(t,e,n)=>{n.d(e,{t:()=>s});var i=n(25910),r=n(52020),s=new class extends i.Q{#t=!0;#e;#n;constructor(){super(),this.#n=t=>{if(!r.S$&&window.addEventListener){let e=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#t}}},25910:(t,e,n)=>{n.d(e,{Q:()=>i});var i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},26715:(t,e,n)=>{n.d(e,{Ht:()=>u,jE:()=>o});var i=n(12115),r=n(95155),s=i.createContext(void 0),o=t=>{let e=i.useContext(s);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},u=t=>{let{client:e,children:n}=t;return i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,r.jsx)(s.Provider,{value:e,children:n})}},34560:(t,e,n)=>{n.d(e,{$:()=>u,s:()=>o});var i=n(7165),r=n(57948),s=n(6784),o=class extends r.k{#i;#r;#s;constructor(t){super(),this.mutationId=t.mutationId,this.#r=t.mutationCache,this.#i=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#i.includes(t)||(this.#i.push(t),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#i=this.#i.filter(e=>e!==t),this.scheduleGc(),this.#r.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#i.length||("pending"===this.state.status?this.scheduleGc():this.#r.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(t){this.#s=(0,s.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#o({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#r.canRun(this)});let e="pending"===this.state.status,n=!this.#s.canStart();try{if(!e){this.#o({type:"pending",variables:t,isPaused:n}),await this.#r.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#o({type:"pending",context:e,variables:t,isPaused:n})}let i=await this.#s.start();return await this.#r.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#r.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#o({type:"success",data:i}),i}catch(e){try{throw await this.#r.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#r.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#o({type:"error",error:e})}}finally{this.#r.runNext(this)}}#o(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),i.j.batch(()=>{this.#i.forEach(e=>{e.onMutationUpdate(t)}),this.#r.notify({mutation:this,type:"updated",action:t})})}};function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},50920:(t,e,n)=>{n.d(e,{m:()=>s});var i=n(25910),r=n(52020),s=new class extends i.Q{#u;#e;#n;constructor(){super(),this.#n=t=>{if(!r.S$&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#u!==t&&(this.#u=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#u?this.#u:globalThis.document?.visibilityState!=="hidden"}}},52020:(t,e,n)=>{n.d(e,{Cp:()=>p,EN:()=>f,Eh:()=>c,F$:()=>d,MK:()=>l,S$:()=>i,ZM:()=>x,ZZ:()=>E,Zw:()=>s,d2:()=>a,f8:()=>y,gn:()=>o,hT:()=>j,j3:()=>u,lQ:()=>r,nJ:()=>h,pl:()=>w,y9:()=>C,yy:()=>g});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function s(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function u(t,e){return Math.max(t+(e||0)-Date.now(),0)}function a(t,e){return"function"==typeof t?t(e):t}function c(t,e){return"function"==typeof t?t(e):t}function l(t,e){let{type:n="all",exact:i,fetchStatus:r,predicate:s,queryKey:o,stale:u}=t;if(o){if(i){if(e.queryHash!==d(o,e.options))return!1}else if(!p(e.queryKey,o))return!1}if("all"!==n){let t=e.isActive();if("active"===n&&!t||"inactive"===n&&t)return!1}return("boolean"!=typeof u||e.isStale()===u)&&(!r||r===e.state.fetchStatus)&&(!s||!!s(e))}function h(t,e){let{exact:n,status:i,predicate:r,mutationKey:s}=t;if(s){if(!e.options.mutationKey)return!1;if(n){if(f(e.options.mutationKey)!==f(s))return!1}else if(!p(e.options.mutationKey,s))return!1}return(!i||e.state.status===i)&&(!r||!!r(e))}function d(t,e){return(e?.queryKeyHashFn||f)(t)}function f(t){return JSON.stringify(t,(t,e)=>m(e)?Object.keys(e).sort().reduce((t,n)=>(t[n]=e[n],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&!Object.keys(e).some(n=>!p(t[n],e[n]))}function y(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let n in t)if(t[n]!==e[n])return!1;return!0}function v(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function m(t){if(!b(t))return!1;let e=t.constructor;if(void 0===e)return!0;let n=e.prototype;return!!b(n)&&!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function g(t){return new Promise(e=>{setTimeout(e,t)})}function w(t,e,n){return"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?function t(e,n){if(e===n)return e;let i=v(e)&&v(n);if(i||m(e)&&m(n)){let r=i?e:Object.keys(e),s=r.length,o=i?n:Object.keys(n),u=o.length,a=i?[]:{},c=0;for(let s=0;s<u;s++){let u=i?s:o[s];(!i&&r.includes(u)||i)&&void 0===e[u]&&void 0===n[u]?(a[u]=void 0,c++):(a[u]=t(e[u],n[u]),a[u]===e[u]&&void 0!==e[u]&&c++)}return s===u&&c===s?e:a}return n}(t,e):e}function C(t,e,n=0){let i=[...t,e];return n&&i.length>n?i.slice(1):i}function E(t,e,n=0){let i=[e,...t];return n&&i.length>n?i.slice(0,-1):i}var j=Symbol();function x(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==j?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}},57383:(t,e,n)=>{function i(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)t[i]=n[i]}return t}n.d(e,{A:()=>r});var r=function t(e,n){function r(t,r,s){if("undefined"!=typeof document){"number"==typeof(s=i({},n,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var u in s)s[u]&&(o+="; "+u,!0!==s[u]&&(o+="="+s[u].split(";")[0]));return document.cookie=t+"="+e.write(r,t)+o}}return Object.create({set:r,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var n=document.cookie?document.cookie.split("; "):[],i={},r=0;r<n.length;r++){var s=n[r].split("="),o=s.slice(1).join("=");try{var u=decodeURIComponent(s[0]);if(i[u]=e.read(o,u),t===u)break}catch(t){}}return t?i[t]:i}},remove:function(t,e){r(t,"",i({},e,{expires:-1}))},withAttributes:function(e){return t(this.converter,i({},this.attributes,e))},withConverter:function(e){return t(i({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(e)}})}({read:function(t){return'"'===t[0]&&(t=t.slice(1,-1)),t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},57948:(t,e,n)=>{n.d(e,{k:()=>r});var i=n(52020),r=class{#a;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.gn)(this.gcTime)&&(this.#a=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i.S$?1/0:3e5))}clearGcTimeout(){this.#a&&(clearTimeout(this.#a),this.#a=void 0)}}},63540:(t,e,n)=>{n.d(e,{sG:()=>h,hO:()=>d});var i=n(12115),r=n(47650),s=n(6101),o=n(95155),u=i.forwardRef((t,e)=>{let{children:n,...r}=t,s=i.Children.toArray(n),u=s.find(l);if(u){let t=u.props.children,n=s.map(e=>e!==u?e:i.Children.count(t)>1?i.Children.only(null):i.isValidElement(t)?t.props.children:null);return(0,o.jsx)(a,{...r,ref:e,children:i.isValidElement(t)?i.cloneElement(t,void 0,n):null})}return(0,o.jsx)(a,{...r,ref:e,children:n})});u.displayName="Slot";var a=i.forwardRef((t,e)=>{let{children:n,...r}=t;if(i.isValidElement(n)){let t=function(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,n=e&&"isReactWarning"in e&&e.isReactWarning;return n?t.ref:(n=(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?t.props.ref:t.props.ref||t.ref}(n);return i.cloneElement(n,{...function(t,e){let n={...e};for(let i in e){let r=t[i],s=e[i];/^on[A-Z]/.test(i)?r&&s?n[i]=(...t)=>{s(...t),r(...t)}:r&&(n[i]=r):"style"===i?n[i]={...r,...s}:"className"===i&&(n[i]=[r,s].filter(Boolean).join(" "))}return{...t,...n}}(r,n.props),ref:e?(0,s.t)(e,t):t})}return i.Children.count(n)>1?i.Children.only(null):null});a.displayName="SlotClone";var c=({children:t})=>(0,o.jsx)(o.Fragment,{children:t});function l(t){return i.isValidElement(t)&&t.type===c}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((t,e)=>{let n=i.forwardRef((t,n)=>{let{asChild:i,...r}=t,s=i?u:e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...r,ref:n})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function d(t,e){t&&r.flushSync(()=>t.dispatchEvent(e))}},73504:(t,e,n)=>{n.d(e,{T:()=>i});function i(){let t,e,n=new Promise((n,i)=>{t=n,e=i});function i(t){Object.assign(n,t),delete n.resolve,delete n.reject}return n.status="pending",n.catch(()=>{}),n.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},n.reject=t=>{i({status:"rejected",reason:t}),e(t)},n}}}]);