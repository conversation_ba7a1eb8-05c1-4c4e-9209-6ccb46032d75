"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7417],{14050:(e,t,r)=>{r.d(t,{b:()=>c});var n=r(12115);r(47650);var i=r(66634),s=r(95155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,a=n?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),a="horizontal",l=["horizontal","vertical"],u=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:i=a,...u}=e,c=(r=i,l.includes(r))?i:a;return(0,s.jsx)(o.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});u.displayName="Separator";var c=u},19373:(e,t,r)=>{r.d(t,{I:()=>R});var n=r(50920),i=r(7165),s=r(39853),o=r(25910),a=r(73504),l=r(52020),u=class extends o.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,a.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#n=void 0;#i=void 0;#s=void 0;#o;#a;#r;#t;#l;#u;#c;#d;#f;#h;#p=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#n.addObserver(this),c(this.#n,this.options)?this.#v():this.updateResult(),this.#m())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return d(this.#n,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return d(this.#n,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#y(),this.#g(),this.#n.removeObserver(this)}setOptions(e,t){let r=this.options,n=this.#n;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Eh)(this.options.enabled,this.#n))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#b(),this.#n.setOptions(this.options),r._defaulted&&!(0,l.f8)(this.options,r)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#n,observer:this});let i=this.hasListeners();i&&f(this.#n,n,this.options,r)&&this.#v(),this.updateResult(t),i&&(this.#n!==n||(0,l.Eh)(this.options.enabled,this.#n)!==(0,l.Eh)(r.enabled,this.#n)||(0,l.d2)(this.options.staleTime,this.#n)!==(0,l.d2)(r.staleTime,this.#n))&&this.#R();let s=this.#E();i&&(this.#n!==n||(0,l.Eh)(this.options.enabled,this.#n)!==(0,l.Eh)(r.enabled,this.#n)||s!==this.#h)&&this.#w(s)}getOptimisticResult(e){var t,r;let n=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(n,e);return t=this,r=i,(0,l.f8)(t.getCurrentResult(),r)||(this.#s=i,this.#a=this.options,this.#o=this.#n.state),i}getCurrentResult(){return this.#s}trackResult(e,t){let r={};return Object.keys(e).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})}),r}trackProp(e){this.#p.add(e)}getCurrentQuery(){return this.#n}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#v({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#s))}#v(e){this.#b();let t=this.#n.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.lQ)),t}#R(){this.#y();let e=(0,l.d2)(this.options.staleTime,this.#n);if(l.S$||this.#s.isStale||!(0,l.gn)(e))return;let t=(0,l.j3)(this.#s.dataUpdatedAt,e);this.#d=setTimeout(()=>{this.#s.isStale||this.updateResult()},t+1)}#E(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#n):this.options.refetchInterval)??!1}#w(e){this.#g(),this.#h=e,!l.S$&&!1!==(0,l.Eh)(this.options.enabled,this.#n)&&(0,l.gn)(this.#h)&&0!==this.#h&&(this.#f=setInterval(()=>{(this.options.refetchIntervalInBackground||n.m.isFocused())&&this.#v()},this.#h))}#m(){this.#R(),this.#w(this.#E())}#y(){this.#d&&(clearTimeout(this.#d),this.#d=void 0)}#g(){this.#f&&(clearInterval(this.#f),this.#f=void 0)}createResult(e,t){let r,n=this.#n,i=this.options,o=this.#s,a=this.#o,u=this.#a,d=e!==n?e.state:this.#i,{state:p}=e,v={...p},m=!1;if(t._optimisticResults){let r=this.hasListeners(),o=!r&&c(e,t),a=r&&f(e,n,t,i);(o||a)&&(v={...v,...(0,s.k)(p.data,e.options)}),"isRestoring"===t._optimisticResults&&(v.fetchStatus="idle")}let{error:y,errorUpdatedAt:g,status:b}=v;if(t.select&&void 0!==v.data)if(o&&v.data===a?.data&&t.select===this.#l)r=this.#u;else try{this.#l=t.select,r=t.select(v.data),r=(0,l.pl)(o?.data,r,t),this.#u=r,this.#t=null}catch(e){this.#t=e}else r=v.data;if(void 0!==t.placeholderData&&void 0===r&&"pending"===b){let e;if(o?.isPlaceholderData&&t.placeholderData===u?.placeholderData)e=o.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#c?.state.data,this.#c):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#t=null}catch(e){this.#t=e}void 0!==e&&(b="success",r=(0,l.pl)(o?.data,e,t),m=!0)}this.#t&&(y=this.#t,r=this.#u,g=Date.now(),b="error");let R="fetching"===v.fetchStatus,E="pending"===b,w="error"===b,C=E&&R,S=void 0!==r;return{status:b,fetchStatus:v.fetchStatus,isPending:E,isSuccess:"success"===b,isError:w,isInitialLoading:C,isLoading:C,data:r,dataUpdatedAt:v.dataUpdatedAt,error:y,errorUpdatedAt:g,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>d.dataUpdateCount||v.errorUpdateCount>d.errorUpdateCount,isFetching:R,isRefetching:R&&!E,isLoadingError:w&&!S,isPaused:"paused"===v.fetchStatus,isPlaceholderData:m,isRefetchError:w&&S,isStale:h(e,t),refetch:this.refetch,promise:this.#r}}updateResult(e){let t=this.#s,r=this.createResult(this.#n,this.options);if(this.#o=this.#n.state,this.#a=this.options,void 0!==this.#o.data&&(this.#c=this.#n),(0,l.f8)(r,t))return;if(this.options.experimental_prefetchInRender){let e=e=>{"error"===r.status?e.reject(r.error):void 0!==r.data&&e.resolve(r.data)},t=()=>{e(this.#r=r.promise=(0,a.T)())},n=this.#r;switch(n.status){case"pending":e(n);break;case"fulfilled":("error"===r.status||r.data!==n.value)&&t();break;case"rejected":("error"!==r.status||r.error!==n.reason)&&t()}}this.#s=r;let n={};e?.listeners!==!1&&(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#p.size)return!0;let n=new Set(r??this.#p);return this.options.throwOnError&&n.add("error"),Object.keys(this.#s).some(e=>this.#s[e]!==t[e]&&n.has(e))})()&&(n.listeners=!0),this.#C({...n,...e})}#b(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#n)return;let t=this.#n;this.#n=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#m()}#C(e){i.j.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#s)}),this.#e.getQueryCache().notify({query:this.#n,type:"observerResultsUpdated"})})}};function c(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&d(e,t,t.refetchOnMount)}function d(e,t,r){if(!1!==(0,l.Eh)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&h(e,t)}return!1}function f(e,t,r,n){return(e!==t||!1===(0,l.Eh)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&h(e,r)}function h(e,t){return!1!==(0,l.Eh)(t.enabled,e)&&e.isStaleByTime((0,l.d2)(t.staleTime,e))}var p=r(12115),v=r(26715);r(95155);var m=p.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),y=r(63768),g=p.createContext(!1);g.Provider;var b=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function R(e,t){return function(e,t,r){var n,s,o,a,u;let c=(0,v.jE)(r),d=p.useContext(g),f=p.useContext(m),h=c.defaultQueryOptions(e);null==(s=c.getDefaultOptions().queries)||null==(n=s._experimental_beforeQuery)||n.call(s,h),h._optimisticResults=d?"isRestoring":"optimistic",h.suspense&&("number"!=typeof h.staleTime&&(h.staleTime=1e3),"number"==typeof h.gcTime&&(h.gcTime=Math.max(h.gcTime,1e3))),(h.suspense||h.throwOnError)&&!f.isReset()&&(h.retryOnMount=!1),p.useEffect(()=>{f.clearReset()},[f]);let R=!c.getQueryCache().get(h.queryHash),[E]=p.useState(()=>new t(c,h)),w=E.getOptimisticResult(h);if(p.useSyncExternalStore(p.useCallback(e=>{let t=d?()=>void 0:E.subscribe(i.j.batchCalls(e));return E.updateResult(),t},[E,d]),()=>E.getCurrentResult(),()=>E.getCurrentResult()),p.useEffect(()=>{E.setOptions(h,{listeners:!1})},[h,E]),h?.suspense&&w.isPending)throw b(h,E,f);if((e=>{let{result:t,errorResetBoundary:r,throwOnError:n,query:i}=e;return t.isError&&!r.isReset()&&!t.isFetching&&i&&(0,y.G)(n,[t.error,i])})({result:w,errorResetBoundary:f,throwOnError:h.throwOnError,query:c.getQueryCache().get(h.queryHash)}))throw w.error;if(null==(a=c.getDefaultOptions().queries)||null==(o=a._experimental_afterQuery)||o.call(a,h,w),h.experimental_prefetchInRender&&!l.S$&&w.isLoading&&w.isFetching&&!d){let e=R?b(h,E,f):null==(u=c.getQueryCache().get(h.queryHash))?void 0:u.promise;null==e||e.catch(y.l).finally(()=>{E.hasListeners()||E.updateResult()})}return h.notifyOnChangeProps?w:E.trackResult(w)}(e,u,t)}},39853:(e,t,r)=>{r.d(t,{X:()=>a,k:()=>l});var n=r(52020),i=r(7165),s=r(6784),o=r(57948),a=class extends o.k{#S;#O;#I;#D;#x;#N;constructor(e){super(),this.#N=!1,this.#x=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#I=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#S=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#S,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#D?.promise}setOptions(e){this.options={...this.#x,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#I.remove(this)}setData(e,t){let r=(0,n.pl)(this.state.data,e,this.options);return this.#T({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#T({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#D?.promise;return this.#D?.cancel(e),t?t.then(n.lQ).catch(n.lQ):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#S)}isActive(){return this.observers.some(e=>!1!==(0,n.Eh)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.hT||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,n.j3)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#D?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#D?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#I.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#D&&(this.#N?this.#D.cancel({revert:!0}):this.#D.cancelRetry()),this.scheduleGc()),this.#I.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#T({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#D)return this.#D.continueRetry(),this.#D.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#N=!0,r.signal)})},o={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=(0,n.ZM)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return(i(r),this.#N=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};i(o),this.options.behavior?.onFetch(o,this),this.#O=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#T({type:"fetch",meta:o.fetchOptions?.meta});let a=e=>{(0,s.wm)(e)&&e.silent||this.#T({type:"error",error:e}),(0,s.wm)(e)||(this.#I.config.onError?.(e,this),this.#I.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#D=(0,s.II)({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e)return void a(Error(`${this.queryHash} data is undefined`));try{this.setData(e)}catch(e){a(e);return}this.#I.config.onSuccess?.(e,this),this.#I.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:a,onFail:(e,t)=>{this.#T({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#T({type:"pause"})},onContinue:()=>{this.#T({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#D.start()}#T(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...l(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,s.wm)(r)&&r.revert&&this.#O)return{...this.#O,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.j.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#I.notify({query:this,type:"updated",action:e})})}};function l(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,s.v_)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},42355:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},67178:(e,t,r)=>{r.d(t,{bm:()=>to,UC:()=>tn,VY:()=>ts,hJ:()=>tr,ZL:()=>tt,bL:()=>e8,hE:()=>ti,l9:()=>te});var n,i=r(12115),s=r.t(i,2);function o(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function u(...e){return i.useCallback(l(...e),e)}var c=r(95155),d=globalThis?.document?i.useLayoutEffect:()=>{},f=s[" useId ".trim().toString()]||(()=>void 0),h=0;function p(e){let[t,r]=i.useState(f());return d(()=>{e||r(e=>e??String(h++))},[e]),e||(t?`radix-${t}`:"")}var v=s[" useInsertionEffect ".trim().toString()]||d;function m(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}Symbol("RADIX:SYNC_STATE");var y=r(47650);function g(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function b(...e){return t=>{let r=!1,n=e.map(e=>{let n=g(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():g(e[t],null)}}}}function R(...e){return i.useCallback(b(...e),e)}var E=Symbol("radix.slottable");function w(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===E}var C=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,o,a=(s=r,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),l=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(l.ref=t?b(t,a):a),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...s}=e,o=i.Children.toArray(n),a=o.find(w);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,c.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=i.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i?r:t,{...s,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function S(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var O="dismissableLayer.update",I=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),D=i.forwardRef((e,t)=>{var r,s;let{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:d,onDismiss:f,...h}=e,p=i.useContext(I),[v,y]=i.useState(null),g=null!=(s=null==v?void 0:v.ownerDocument)?s:null==(r=globalThis)?void 0:r.document,[,b]=i.useState({}),E=R(t,e=>y(e)),w=Array.from(p.layers),[D]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),T=w.indexOf(D),P=v?w.indexOf(v):-1,F=p.layersWithOutsidePointerEventsDisabled.size>0,j=P>=T,L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=S(e),s=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){N("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...p.branches].some(e=>e.contains(t));j&&!r&&(null==l||l(e),null==d||d(e),e.defaultPrevented||null==f||f())},g),k=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=S(e),s=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!s.current&&N("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;![...p.branches].some(e=>e.contains(t))&&(null==u||u(e),null==d||d(e),e.defaultPrevented||null==f||f())},g);return!function(e,t=globalThis?.document){let r=function(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{P===p.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},g),i.useEffect(()=>{if(v)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(n=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(v)),p.layers.add(v),x(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=n)}},[v,g,o,p]),i.useEffect(()=>()=>{v&&(p.layers.delete(v),p.layersWithOutsidePointerEventsDisabled.delete(v),x())},[v,p]),i.useEffect(()=>{let e=()=>b({});return document.addEventListener(O,e),()=>document.removeEventListener(O,e)},[]),(0,c.jsx)(C.div,{...h,ref:E,style:{pointerEvents:F?j?"auto":"none":void 0,...e.style},onFocusCapture:m(e.onFocusCapture,k.onFocusCapture),onBlurCapture:m(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:m(e.onPointerDownCapture,L.onPointerDownCapture)})});function x(){let e=new CustomEvent(O);document.dispatchEvent(e)}function N(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&s.addEventListener(e,t,{once:!0}),i)s&&y.flushSync(()=>s.dispatchEvent(o));else s.dispatchEvent(o)}function T(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function P(...e){return t=>{let r=!1,n=e.map(e=>{let n=T(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():T(e[t],null)}}}}D.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(I),n=i.useRef(null),s=R(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(C.div,{...e,ref:s})}).displayName="DismissableLayerBranch";var F=Symbol("radix.slottable");function j(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===F}var L=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,o,a=(s=r,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),l=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(l.ref=t?P(t,a):a),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...s}=e,o=i.Children.toArray(n),a=o.find(j);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,c.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=i.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i?r:t,{...s,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function k(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var Q="focusScope.autoFocusOnMount",_="focusScope.autoFocusOnUnmount",A={bubbles:!1,cancelable:!0},M=i.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...a}=e,[l,u]=i.useState(null),d=k(s),f=k(o),h=i.useRef(null),p=function(...e){return i.useCallback(P(...e),e)}(t,e=>u(e)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(n){let e=function(e){if(v.paused||!l)return;let t=e.target;l.contains(t)?h.current=t:$(h.current,{select:!0})},t=function(e){if(v.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||$(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&$(l)});return l&&r.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,l,v.paused]),i.useEffect(()=>{if(l){q.add(v);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(Q,A);l.addEventListener(Q,d),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if($(n,{select:t}),document.activeElement!==r)return}(U(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&$(l))}return()=>{l.removeEventListener(Q,d),setTimeout(()=>{let t=new CustomEvent(_,A);l.addEventListener(_,f),l.dispatchEvent(t),t.defaultPrevented||$(null!=e?e:document.body,{select:!0}),l.removeEventListener(_,f),q.remove(v)},0)}}},[l,d,f,v]);let m=i.useCallback(e=>{if(!r&&!n||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[n,s]=function(e){let t=U(e);return[W(t,e),W(t.reverse(),e)]}(t);n&&s?e.shiftKey||i!==s?e.shiftKey&&i===n&&(e.preventDefault(),r&&$(s,{select:!0})):(e.preventDefault(),r&&$(n,{select:!0})):i===t&&e.preventDefault()}},[r,n,v.paused]);return(0,c.jsx)(L.div,{tabIndex:-1,...a,ref:p,onKeyDown:m})});function U(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function W(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function $(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}M.displayName="FocusScope";var q=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=B(e,t)).unshift(t)},remove(t){var r;null==(r=(e=B(e,t))[0])||r.resume()}}}();function B(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}function V(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var K=Symbol("radix.slottable");function H(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===K}var X=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,o,a=(s=r,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),l=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(l.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=V(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():V(e[t],null)}}}}(t,a):a),i.cloneElement(r,l)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...s}=e,o=i.Children.toArray(n),a=o.find(H);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,c.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),n=i.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i?r:t,{...s,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),Z=globalThis?.document?i.useLayoutEffect:()=>{},z=i.forwardRef((e,t)=>{var r,n;let{container:s,...o}=e,[a,l]=i.useState(!1);Z(()=>l(!0),[]);let u=s||a&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return u?y.createPortal((0,c.jsx)(X.div,{...o,ref:t}),u):null});z.displayName="Portal";var Y=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,s]=i.useState(),o=i.useRef(null),a=i.useRef(e),l=i.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return i.useEffect(()=>{let e=G(o.current);l.current="mounted"===u?e:"none"},[u]),d(()=>{let t=o.current,r=a.current;if(r!==e){let n=l.current,i=G(t);e?c("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(n){var e;let t,r=null!=(e=n.ownerDocument.defaultView)?e:window,i=e=>{let i=G(o.current).includes(e.animationName);if(e.target===n&&i&&(c("ANIMATION_END"),!a.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},s=e=>{e.target===n&&(l.current=G(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",i),n.addEventListener("animationend",i),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",i),n.removeEventListener("animationend",i)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:i.useCallback(e=>{o.current=e?getComputedStyle(e):null,s(e)},[])}}(t),s="function"==typeof r?r({present:n.isPresent}):i.Children.only(r),o=u(n.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,i=n&&"isReactWarning"in n&&n.isReactWarning;return i?e.ref:(i=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||n.isPresent?i.cloneElement(s,{ref:o}):null};function G(e){return(null==e?void 0:e.animationName)||"none"}function J(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var s;let e,o,a=(s=r,(o=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(o=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),u=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(u.ref=t?l(t,a):a),i.cloneElement(r,u)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...s}=e,o=i.Children.toArray(n),a=o.find(et);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...s,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,c.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}Y.displayName="Presence";var ee=Symbol("radix.slottable");function et(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===ee}var er=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=J(`Primitive.${t}`),n=i.forwardRef((e,n)=>{let{asChild:i,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(i?r:t,{...s,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),en=0;function ei(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var es=r(39249),eo=r(56985),ea=r(70464),el=(0,r(37548).f)(),eu=function(){},ec=i.forwardRef(function(e,t){var r=i.useRef(null),n=i.useState({onScrollCapture:eu,onWheelCapture:eu,onTouchMoveCapture:eu}),s=n[0],o=n[1],a=e.forwardProps,l=e.children,u=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,h=e.sideCar,p=e.noRelative,v=e.noIsolation,m=e.inert,y=e.allowPinchZoom,g=e.as,b=e.gapMode,R=(0,es.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=(0,ea.S)([r,t]),w=(0,es.Cl)((0,es.Cl)({},R),s);return i.createElement(i.Fragment,null,d&&i.createElement(h,{sideCar:el,removeScrollBar:c,shards:f,noRelative:p,noIsolation:v,inert:m,setCallbacks:o,allowPinchZoom:!!y,lockRef:r,gapMode:b}),a?i.cloneElement(i.Children.only(l),(0,es.Cl)((0,es.Cl)({},w),{ref:E})):i.createElement(void 0===g?"div":g,(0,es.Cl)({},w,{className:u,ref:E}),l))});ec.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},ec.classNames={fullWidth:eo.pN,zeroRight:eo.Mi};var ed=r(50514),ef=r(21515),eh=r(29874),ep=!1;if("undefined"!=typeof window)try{var ev=Object.defineProperty({},"passive",{get:function(){return ep=!0,!0}});window.addEventListener("test",ev,ev),window.removeEventListener("test",ev,ev)}catch(e){ep=!1}var em=!!ep&&{passive:!1},ey=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},eg=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),eb(e,n)){var i=eR(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},eb=function(e,t){return"v"===e?ey(t,"overflowY"):ey(t,"overflowX")},eR=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eE=function(e,t,r,n,i){var s,o=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),a=o*n,l=r.target,u=t.contains(l),c=!1,d=a>0,f=0,h=0;do{if(!l)break;var p=eR(e,l),v=p[0],m=p[1]-p[2]-o*v;(v||m)&&eb(e,l)&&(f+=m,h+=v);var y=l.parentNode;l=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return d&&(i&&1>Math.abs(f)||!i&&a>f)?c=!0:!d&&(i&&1>Math.abs(h)||!i&&-a>h)&&(c=!0),c},ew=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eC=function(e){return[e.deltaX,e.deltaY]},eS=function(e){return e&&"current"in e?e.current:e},eO=0,eI=[];let eD=(0,ed.m)(el,function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),s=i.useState(eO++)[0],o=i.useState(eh.T0)[0],a=i.useRef(e);i.useEffect(function(){a.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var t=(0,es.fX)([e.lockRef.current],(e.shards||[]).map(eS),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,s=ew(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-s[0],u="deltaY"in e?e.deltaY:o[1]-s[1],c=e.target,d=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=eg(d,c);if(!f)return!0;if(f?i=d:(i="v"===d?"h":"v",f=eg(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||u)&&(n.current=i),!i)return!0;var h=n.current||i;return eE(h,t,e,"h"===h?l:u,!0)},[]),u=i.useCallback(function(e){if(eI.length&&eI[eI.length-1]===o){var r="deltaY"in e?eC(e):ew(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(a.current.shards||[]).map(eS).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,n,i){var s={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),d=i.useCallback(function(e){r.current=ew(e),n.current=void 0},[]),f=i.useCallback(function(t){c(t.type,eC(t),t.target,l(t,e.lockRef.current))},[]),h=i.useCallback(function(t){c(t.type,ew(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return eI.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:h}),document.addEventListener("wheel",u,em),document.addEventListener("touchmove",u,em),document.addEventListener("touchstart",d,em),function(){eI=eI.filter(function(e){return e!==o}),document.removeEventListener("wheel",u,em),document.removeEventListener("touchmove",u,em),document.removeEventListener("touchstart",d,em)}},[]);var p=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(o,{styles:"\n  .block-interactivity-".concat(s," {pointer-events: none;}\n  .allow-interactivity-").concat(s," {pointer-events: all;}\n")}):null,p?i.createElement(ef.jp,{noRelative:e.noRelative,gapMode:e.gapMode}):null)});var ex=i.forwardRef(function(e,t){return i.createElement(ec,(0,es.Cl)({},e,{ref:t,sideCar:eD}))});ex.classNames=ec.classNames;var eN=r(38168),eT="Dialog",[eP,eF]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>i.createContext(e));return function(r){let n=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let s=i.createContext(n),o=r.length;r=[...r,n];let a=t=>{let{scope:r,children:n,...a}=t,l=r?.[e]?.[o]||s,u=i.useMemo(()=>a,Object.values(a));return(0,c.jsx)(l.Provider,{value:u,children:n})};return a.displayName=t+"Provider",[a,function(r,a){let l=a?.[e]?.[o]||s,u=i.useContext(l);if(u)return u;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(eT),[ej,eL]=eP(eT),ek=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:s,onOpenChange:o,modal:a=!0}=e,l=i.useRef(null),u=i.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[s,o,a]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),s=i.useRef(r),o=i.useRef(t);return v(()=>{o.current=t},[t]),i.useEffect(()=>{s.current!==r&&(o.current?.(r),s.current=r)},[r,s]),[r,n,o]}({defaultProp:t,onChange:r}),l=void 0!==e,u=l?e:s;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,n])}return[u,i.useCallback(t=>{if(l){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else o(t)},[l,e,o,a])]}({prop:n,defaultProp:null!=s&&s,onChange:o,caller:eT});return(0,c.jsx)(ej,{scope:t,triggerRef:l,contentRef:u,contentId:p(),titleId:p(),descriptionId:p(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:a,children:r})};ek.displayName=eT;var eQ="DialogTrigger",e_=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eL(eQ,r),s=u(t,i.triggerRef);return(0,c.jsx)(er.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":e2(i.open),...n,ref:s,onClick:o(e.onClick,i.onOpenToggle)})});e_.displayName=eQ;var eA="DialogPortal",[eM,eU]=eP(eA,{forceMount:void 0}),eW=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:s}=e,o=eL(eA,t);return(0,c.jsx)(eM,{scope:t,forceMount:r,children:i.Children.map(n,e=>(0,c.jsx)(Y,{present:r||o.open,children:(0,c.jsx)(z,{asChild:!0,container:s,children:e})}))})};eW.displayName=eA;var e$="DialogOverlay",eq=i.forwardRef((e,t)=>{let r=eU(e$,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,s=eL(e$,e.__scopeDialog);return s.modal?(0,c.jsx)(Y,{present:n||s.open,children:(0,c.jsx)(eV,{...i,ref:t})}):null});eq.displayName=e$;var eB=J("DialogOverlay.RemoveScroll"),eV=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eL(e$,r);return(0,c.jsx)(ex,{as:eB,allowPinchZoom:!0,shards:[i.contentRef],children:(0,c.jsx)(er.div,{"data-state":e2(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),eK="DialogContent",eH=i.forwardRef((e,t)=>{let r=eU(eK,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,s=eL(eK,e.__scopeDialog);return(0,c.jsx)(Y,{present:n||s.open,children:s.modal?(0,c.jsx)(eX,{...i,ref:t}):(0,c.jsx)(eZ,{...i,ref:t})})});eH.displayName=eK;var eX=i.forwardRef((e,t)=>{let r=eL(eK,e.__scopeDialog),n=i.useRef(null),s=u(t,r.contentRef,n);return i.useEffect(()=>{let e=n.current;if(e)return(0,eN.Eq)(e)},[]),(0,c.jsx)(ez,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault())})}),eZ=i.forwardRef((e,t)=>{let r=eL(eK,e.__scopeDialog),n=i.useRef(!1),s=i.useRef(!1);return(0,c.jsx)(ez,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var i,o;null==(i=e.onCloseAutoFocus)||i.call(e,t),t.defaultPrevented||(n.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),n.current=!1,s.current=!1},onInteractOutside:t=>{var i,o;null==(i=e.onInteractOutside)||i.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(s.current=!0));let a=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&s.current&&t.preventDefault()}})}),ez=i.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:o,...a}=e,l=eL(eK,r),d=i.useRef(null),f=u(t,d);return i.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:ei()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:ei()),en++,()=>{1===en&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),en--}},[]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:o,children:(0,c.jsx)(D,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e2(l.open),...a,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(e4,{titleId:l.titleId}),(0,c.jsx)(e9,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eY="DialogTitle",eG=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eL(eY,r);return(0,c.jsx)(er.h2,{id:i.titleId,...n,ref:t})});eG.displayName=eY;var eJ="DialogDescription",e0=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eL(eJ,r);return(0,c.jsx)(er.p,{id:i.descriptionId,...n,ref:t})});e0.displayName=eJ;var e1="DialogClose",e5=i.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eL(e1,r);return(0,c.jsx)(er.button,{type:"button",...n,ref:t,onClick:o(e.onClick,()=>i.onOpenChange(!1))})});function e2(e){return e?"open":"closed"}e5.displayName=e1;var e3="DialogTitleWarning",[e6,e7]=function(e,t){let r=i.createContext(t),n=e=>{let{children:t,...n}=e,s=i.useMemo(()=>n,Object.values(n));return(0,c.jsx)(r.Provider,{value:s,children:t})};return n.displayName=e+"Provider",[n,function(n){let s=i.useContext(r);if(s)return s;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}(e3,{contentName:eK,titleName:eY,docsSlug:"dialog"}),e4=e=>{let{titleId:t}=e,r=e7(e3),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},e9=e=>{let{contentRef:t,descriptionId:r}=e,n=e7("DialogDescriptionWarning"),s="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return i.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(s))},[s,t,r]),null},e8=ek,te=e_,tt=eW,tr=eq,tn=eH,ti=eG,ts=e0,to=e5}}]);