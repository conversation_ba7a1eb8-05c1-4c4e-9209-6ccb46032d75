"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7735],{1221:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,a=Object.defineProperties,u=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertyNames,s=Object.getOwnPropertySymbols,c=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty,f=Object.prototype.propertyIsEnumerable,p=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,v=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of i(t))d.call(e,a)||a===n||o(e,a,{get:()=>t[a],enumerable:!(r=u(t,a))||r.enumerable});return e},m={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(m,{useRouter:()=>y}),e.exports=v(o({},"__esModule",{value:!0}),m);var h=n(35695),g=n(12115),w=((e,t,n)=>(n=null!=e?r(c(e)):{},v(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)))(n(76770)),y=o(()=>{let e=(0,h.useRouter)(),t=(0,h.usePathname)();(0,g.useEffect)(()=>{w.done()},[t]);let n=(0,g.useCallback)((n,r)=>{n!==t&&w.start(),e.replace(n,r)},[e,t]),r=(0,g.useCallback)((n,r)=>{n!==t&&w.start(),e.push(n,r)},[e,t]);return a(((e,t)=>{for(var n in t||(t={}))d.call(t,n)&&p(e,n,t[n]);if(s)for(var n of s(t))f.call(t,n)&&p(e,n,t[n]);return e})({},e),l({replace:n,push:r}))},"name",{value:"useRouter",configurable:!0})},17499:(e,t,n)=>{n.d(t,{H_:()=>tF,UC:()=>tk,ty:()=>th,YJ:()=>tS,q7:()=>t_,VF:()=>tG,JU:()=>tI,ZL:()=>tL,z6:()=>tA,hN:()=>tK,bL:()=>tO,wv:()=>tB,Pb:()=>tU,G5:()=>tX,ZP:()=>tW,l9:()=>tT});var r,o=n(12115),a=n(85185),u=n(6101),l=n(46081),i=n(5845),s=n(63540),c=n(76589),d=n(94315),f=n(39033),p=n(95155),v="dismissableLayer.update",m=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=o.forwardRef((e,t)=>{var n,l;let{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:y,onDismiss:b,...E}=e,C=o.useContext(m),[x,R]=o.useState(null),M=null!=(l=null==x?void 0:x.ownerDocument)?l:null==(n=globalThis)?void 0:n.document,[,D]=o.useState({}),j=(0,u.s)(t,e=>R(e)),N=Array.from(C.layers),[P]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),O=N.indexOf(P),T=x?N.indexOf(x):-1,L=C.layersWithOutsidePointerEventsDisabled.size>0,k=T>=O,S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),a=o.useRef(!1),u=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){w("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=t,n.addEventListener("click",u.current,{once:!0})):t()}else n.removeEventListener("click",u.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",u.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));k&&!n&&(null==d||d(e),null==y||y(e),e.defaultPrevented||null==b||b())},M),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,f.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&w("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![...C.branches].some(e=>e.contains(t))&&(null==h||h(e),null==y||y(e),e.defaultPrevented||null==b||b())},M);return!function(e,t=globalThis?.document){let n=(0,f.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===C.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},M),o.useEffect(()=>{if(x)return i&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=M.body.style.pointerEvents,M.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(x)),C.layers.add(x),g(),()=>{i&&1===C.layersWithOutsidePointerEventsDisabled.size&&(M.body.style.pointerEvents=r)}},[x,M,i,C]),o.useEffect(()=>()=>{x&&(C.layers.delete(x),C.layersWithOutsidePointerEventsDisabled.delete(x),g())},[x,C]),o.useEffect(()=>{let e=()=>D({});return document.addEventListener(v,e),()=>document.removeEventListener(v,e)},[]),(0,p.jsx)(s.sG.div,{...E,ref:j,style:{pointerEvents:L?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,S.onPointerDownCapture)})});function g(){let e=new CustomEvent(v);document.dispatchEvent(e)}function w(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,s.hO)(a,u):a.dispatchEvent(u)}h.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(m),r=o.useRef(null),a=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,p.jsx)(s.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch";var y=n(92293),b="focusScope.autoFocusOnMount",E="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},x=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:l,...i}=e,[c,d]=o.useState(null),v=(0,f.c)(a),m=(0,f.c)(l),h=o.useRef(null),g=(0,u.s)(t,e=>d(e)),w=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(w.paused||!c)return;let t=e.target;c.contains(t)?h.current=t:D(h.current,{select:!0})},t=function(e){if(w.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||D(h.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,w.paused]),o.useEffect(()=>{if(c){j.add(w);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(b,C);c.addEventListener(b,v),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(D(r,{select:t}),document.activeElement!==n)return}(R(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(c))}return()=>{c.removeEventListener(b,v),setTimeout(()=>{let t=new CustomEvent(E,C);c.addEventListener(E,m),c.dispatchEvent(t),t.defaultPrevented||D(null!=e?e:document.body,{select:!0}),c.removeEventListener(E,m),j.remove(w)},0)}}},[c,v,m,w]);let y=o.useCallback(e=>{if(!n&&!r||w.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=R(e);return[M(t,e),M(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&D(a,{select:!0})):(e.preventDefault(),n&&D(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,w.paused]);return(0,p.jsx)(s.sG.div,{tabIndex:-1,...i,ref:g,onKeyDown:y})});function R(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function M(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function D(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}x.displayName="FocusScope";var j=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=N(e,t)).unshift(t)},remove(t){var n;null==(n=(e=N(e,t))[0])||n.resume()}}}();function N(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var P=n(61285),O=n(63753),T=n(47650),L=n(52712),k=o.forwardRef((e,t)=>{var n,r;let{container:a,...u}=e,[l,i]=o.useState(!1);(0,L.N)(()=>i(!0),[]);let c=a||l&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?T.createPortal((0,p.jsx)(s.sG.div,{...u,ref:t}),c):null});k.displayName="Portal";var S=n(28905),I=n(89196),_=o.forwardRef((e,t)=>{let{children:n,...r}=e,a=o.Children.toArray(n),u=a.find(K);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,p.jsx)(F,{...r,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,p.jsx)(F,{...r,ref:t,children:n})});_.displayName="Slot";var F=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return o.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?(0,u.t)(t,e):e})}return o.Children.count(n)>1?o.Children.only(null):null});F.displayName="SlotClone";var A=({children:e})=>(0,p.jsx)(p.Fragment,{children:e});function K(e){return o.isValidElement(e)&&e.type===A}var G=n(38168),B=n(39249),U=n(56985),W=n(70464),X=(0,n(37548).f)(),V=function(){},Y=o.forwardRef(function(e,t){var n=o.useRef(null),r=o.useState({onScrollCapture:V,onWheelCapture:V,onTouchMoveCapture:V}),a=r[0],u=r[1],l=e.forwardProps,i=e.children,s=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,v=e.noIsolation,m=e.inert,h=e.allowPinchZoom,g=e.as,w=e.gapMode,y=(0,B.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=(0,W.S)([n,t]),E=(0,B.Cl)((0,B.Cl)({},y),a);return o.createElement(o.Fragment,null,d&&o.createElement(p,{sideCar:X,removeScrollBar:c,shards:f,noIsolation:v,inert:m,setCallbacks:u,allowPinchZoom:!!h,lockRef:n,gapMode:w}),l?o.cloneElement(o.Children.only(i),(0,B.Cl)((0,B.Cl)({},E),{ref:b})):o.createElement(void 0===g?"div":g,(0,B.Cl)({},E,{className:s,ref:b}),i))});Y.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Y.classNames={fullWidth:U.pN,zeroRight:U.Mi};var z=n(50514),H=n(21515),Z=n(29874),q=!1;if("undefined"!=typeof window)try{var J=Object.defineProperty({},"passive",{get:function(){return q=!0,!0}});window.addEventListener("test",J,J),window.removeEventListener("test",J,J)}catch(e){q=!1}var Q=!!q&&{passive:!1},$=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ee=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),et(e,r)){var o=en(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},et=function(e,t){return"v"===e?$(t,"overflowY"):$(t,"overflowX")},en=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},er=function(e,t,n,r,o){var a,u=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=u*r,i=n.target,s=t.contains(i),c=!1,d=l>0,f=0,p=0;do{var v=en(e,i),m=v[0],h=v[1]-v[2]-u*m;(m||h)&&et(e,i)&&(f+=h,p+=m),i=i instanceof ShadowRoot?i.host:i.parentNode}while(!s&&i!==document.body||s&&(t.contains(i)||t===i));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},eo=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ea=function(e){return[e.deltaX,e.deltaY]},eu=function(e){return e&&"current"in e?e.current:e},el=0,ei=[];let es=(0,z.m)(X,function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),a=o.useState(el++)[0],u=o.useState(Z.T0)[0],l=o.useRef(e);o.useEffect(function(){l.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(0,B.fX)([e.lockRef.current],(e.shards||[]).map(eu),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var i=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!l.current.allowPinchZoom;var o,a=eo(e),u=n.current,i="deltaX"in e?e.deltaX:u[0]-a[0],s="deltaY"in e?e.deltaY:u[1]-a[1],c=e.target,d=Math.abs(i)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=ee(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ee(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||s)&&(r.current=o),!o)return!0;var p=r.current||o;return er(p,t,e,"h"===p?i:s,!0)},[]),s=o.useCallback(function(e){if(ei.length&&ei[ei.length-1]===u){var n="deltaY"in e?ea(e):eo(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(eu).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?i(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=o.useCallback(function(e){n.current=eo(e),r.current=void 0},[]),f=o.useCallback(function(t){c(t.type,ea(t),t.target,i(t,e.lockRef.current))},[]),p=o.useCallback(function(t){c(t.type,eo(t),t.target,i(t,e.lockRef.current))},[]);o.useEffect(function(){return ei.push(u),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",s,Q),document.addEventListener("touchmove",s,Q),document.addEventListener("touchstart",d,Q),function(){ei=ei.filter(function(e){return e!==u}),document.removeEventListener("wheel",s,Q),document.removeEventListener("touchmove",s,Q),document.removeEventListener("touchstart",d,Q)}},[]);var v=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(u,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,v?o.createElement(H.jp,{gapMode:e.gapMode}):null)});var ec=o.forwardRef(function(e,t){return o.createElement(Y,(0,B.Cl)({},e,{ref:t,sideCar:es}))});ec.classNames=Y.classNames;var ed=["Enter"," "],ef=["ArrowUp","PageDown","End"],ep=["ArrowDown","PageUp","Home",...ef],ev={ltr:[...ed,"ArrowRight"],rtl:[...ed,"ArrowLeft"]},em={ltr:["ArrowLeft"],rtl:["ArrowRight"]},eh="Menu",[eg,ew,ey]=(0,c.N)(eh),[eb,eE]=(0,l.A)(eh,[ey,O.Bk,I.RG]),eC=(0,O.Bk)(),ex=(0,I.RG)(),[eR,eM]=eb(eh),[eD,ej]=eb(eh),eN=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:a,onOpenChange:u,modal:l=!0}=e,i=eC(t),[s,c]=o.useState(null),v=o.useRef(!1),m=(0,f.c)(u),h=(0,d.jH)(a);return o.useEffect(()=>{let e=()=>{v.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>v.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,p.jsx)(O.bL,{...i,children:(0,p.jsx)(eR,{scope:t,open:n,onOpenChange:m,content:s,onContentChange:c,children:(0,p.jsx)(eD,{scope:t,onClose:o.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:v,dir:h,modal:l,children:r})})})};eN.displayName=eh;var eP=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eC(n);return(0,p.jsx)(O.Mz,{...o,...r,ref:t})});eP.displayName="MenuAnchor";var eO="MenuPortal",[eT,eL]=eb(eO,{forceMount:void 0}),ek=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=eM(eO,t);return(0,p.jsx)(eT,{scope:t,forceMount:n,children:(0,p.jsx)(S.C,{present:n||a.open,children:(0,p.jsx)(k,{asChild:!0,container:o,children:r})})})};ek.displayName=eO;var eS="MenuContent",[eI,e_]=eb(eS),eF=o.forwardRef((e,t)=>{let n=eL(eS,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=eM(eS,e.__scopeMenu),u=ej(eS,e.__scopeMenu);return(0,p.jsx)(eg.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(S.C,{present:r||a.open,children:(0,p.jsx)(eg.Slot,{scope:e.__scopeMenu,children:u.modal?(0,p.jsx)(eA,{...o,ref:t}):(0,p.jsx)(eK,{...o,ref:t})})})})}),eA=o.forwardRef((e,t)=>{let n=eM(eS,e.__scopeMenu),r=o.useRef(null),l=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,G.Eq)(e)},[]),(0,p.jsx)(eG,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),eK=o.forwardRef((e,t)=>{let n=eM(eS,e.__scopeMenu);return(0,p.jsx)(eG,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),eG=o.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:d,onEscapeKeyDown:f,onPointerDownOutside:v,onFocusOutside:m,onInteractOutside:g,onDismiss:w,disableOutsideScroll:b,...E}=e,C=eM(eS,n),R=ej(eS,n),M=eC(n),D=ex(n),j=ew(n),[N,P]=o.useState(null),T=o.useRef(null),L=(0,u.s)(t,T,C.onContentChange),k=o.useRef(0),S=o.useRef(""),F=o.useRef(0),A=o.useRef(null),K=o.useRef("right"),G=o.useRef(0),B=b?ec:o.Fragment,U=b?{as:_,allowPinchZoom:!0}:void 0;o.useEffect(()=>()=>window.clearTimeout(k.current),[]),(0,y.Oh)();let W=o.useCallback(e=>{var t,n;return K.current===(null==(t=A.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let u=t[e].x,l=t[e].y,i=t[a].x,s=t[a].y;l>r!=s>r&&n<(i-u)*(r-l)/(s-l)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=A.current)?void 0:n.area)},[]);return(0,p.jsx)(eI,{scope:n,searchRef:S,onItemEnter:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:o.useCallback(e=>{var t;W(e)||(null==(t=T.current)||t.focus(),P(null))},[W]),onTriggerLeave:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:F,onPointerGraceIntentChange:o.useCallback(e=>{A.current=e},[]),children:(0,p.jsx)(B,{...U,children:(0,p.jsx)(x,{asChild:!0,trapped:l,onMountAutoFocus:(0,a.m)(i,e=>{var t;e.preventDefault(),null==(t=T.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,p.jsx)(h,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:f,onPointerDownOutside:v,onFocusOutside:m,onInteractOutside:g,onDismiss:w,children:(0,p.jsx)(I.bL,{asChild:!0,...D,dir:R.dir,orientation:"vertical",loop:r,currentTabStopId:N,onCurrentTabStopIdChange:P,onEntryFocus:(0,a.m)(d,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,p.jsx)(O.UC,{role:"menu","aria-orientation":"vertical","data-state":to(C.open),"data-radix-menu-content":"",dir:R.dir,...M,...E,ref:L,style:{outline:"none",...E.style},onKeyDown:(0,a.m)(E.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=S.current+e,o=j().filter(e=>!e.disabled),a=document.activeElement,u=null==(t=o.find(e=>e.ref.current===a))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,u=(r=Math.max(a,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(u=u.filter(e=>e!==n));let l=u.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,u),i=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){S.current=t,window.clearTimeout(k.current),""!==t&&(k.current=window.setTimeout(()=>e(""),1e3))}(r),i&&setTimeout(()=>i.focus())})(e.key));let o=T.current;if(e.target!==o||!ep.includes(e.key))return;e.preventDefault();let a=j().filter(e=>!e.disabled).map(e=>e.ref.current);ef.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,a.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(k.current),S.current="")}),onPointerMove:(0,a.m)(e.onPointerMove,tl(e=>{let t=e.target,n=G.current!==e.clientX;e.currentTarget.contains(t)&&n&&(K.current=e.clientX>G.current?"right":"left",G.current=e.clientX)}))})})})})})})});eF.displayName=eS;var eB=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(s.sG.div,{role:"group",...r,ref:t})});eB.displayName="MenuGroup";var eU=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(s.sG.div,{...r,ref:t})});eU.displayName="MenuLabel";var eW="MenuItem",eX="menu.itemSelect",eV=o.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...l}=e,i=o.useRef(null),c=ej(eW,e.__scopeMenu),d=e_(eW,e.__scopeMenu),f=(0,u.s)(t,i),v=o.useRef(!1);return(0,p.jsx)(eY,{...l,ref:f,disabled:n,onClick:(0,a.m)(e.onClick,()=>{let e=i.current;if(!n&&e){let t=new CustomEvent(eX,{bubbles:!0,cancelable:!0});e.addEventListener(eX,e=>null==r?void 0:r(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?v.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),v.current=!0},onPointerUp:(0,a.m)(e.onPointerUp,e=>{var t;v.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||ed.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eV.displayName=eW;var eY=o.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:l,...i}=e,c=e_(eW,n),d=ex(n),f=o.useRef(null),v=(0,u.s)(t,f),[m,h]=o.useState(!1),[g,w]=o.useState("");return o.useEffect(()=>{let e=f.current;if(e){var t;w((null!=(t=e.textContent)?t:"").trim())}},[i.children]),(0,p.jsx)(eg.ItemSlot,{scope:n,disabled:r,textValue:null!=l?l:g,children:(0,p.jsx)(I.q7,{asChild:!0,...d,focusable:!r,children:(0,p.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...i,ref:v,onPointerMove:(0,a.m)(e.onPointerMove,tl(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,a.m)(e.onPointerLeave,tl(e=>c.onItemLeave(e))),onFocus:(0,a.m)(e.onFocus,()=>h(!0)),onBlur:(0,a.m)(e.onBlur,()=>h(!1))})})})}),ez=o.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,p.jsx)(e1,{scope:e.__scopeMenu,checked:n,children:(0,p.jsx)(eV,{role:"menuitemcheckbox","aria-checked":ta(n)?"mixed":n,...o,ref:t,"data-state":tu(n),onSelect:(0,a.m)(o.onSelect,()=>null==r?void 0:r(!!ta(n)||!n),{checkForDefaultPrevented:!1})})})});ez.displayName="MenuCheckboxItem";var eH="MenuRadioGroup",[eZ,eq]=eb(eH,{value:void 0,onValueChange:()=>{}}),eJ=o.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,a=(0,f.c)(r);return(0,p.jsx)(eZ,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,p.jsx)(eB,{...o,ref:t})})});eJ.displayName=eH;var eQ="MenuRadioItem",e$=o.forwardRef((e,t)=>{let{value:n,...r}=e,o=eq(eQ,e.__scopeMenu),u=n===o.value;return(0,p.jsx)(e1,{scope:e.__scopeMenu,checked:u,children:(0,p.jsx)(eV,{role:"menuitemradio","aria-checked":u,...r,ref:t,"data-state":tu(u),onSelect:(0,a.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});e$.displayName=eQ;var e0="MenuItemIndicator",[e1,e5]=eb(e0,{checked:!1}),e9=o.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,a=e5(e0,n);return(0,p.jsx)(S.C,{present:r||ta(a.checked)||!0===a.checked,children:(0,p.jsx)(s.sG.span,{...o,ref:t,"data-state":tu(a.checked)})})});e9.displayName=e0;var e2=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,p.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});e2.displayName="MenuSeparator";var e6=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=eC(n);return(0,p.jsx)(O.i3,{...o,...r,ref:t})});e6.displayName="MenuArrow";var e3="MenuSub",[e8,e7]=eb(e3),e4=e=>{let{__scopeMenu:t,children:n,open:r=!1,onOpenChange:a}=e,u=eM(e3,t),l=eC(t),[i,s]=o.useState(null),[c,d]=o.useState(null),v=(0,f.c)(a);return o.useEffect(()=>(!1===u.open&&v(!1),()=>v(!1)),[u.open,v]),(0,p.jsx)(O.bL,{...l,children:(0,p.jsx)(eR,{scope:t,open:r,onOpenChange:v,content:c,onContentChange:d,children:(0,p.jsx)(e8,{scope:t,contentId:(0,P.B)(),triggerId:(0,P.B)(),trigger:i,onTriggerChange:s,children:n})})})};e4.displayName=e3;var te="MenuSubTrigger",tt=o.forwardRef((e,t)=>{let n=eM(te,e.__scopeMenu),r=ej(te,e.__scopeMenu),l=e7(te,e.__scopeMenu),i=e_(te,e.__scopeMenu),s=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},v=o.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return o.useEffect(()=>v,[v]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,p.jsx)(eP,{asChild:!0,...f,children:(0,p.jsx)(eY,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":to(n.open),...e,ref:(0,u.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,a.m)(e.onPointerMove,tl(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),v()},100)))})),onPointerLeave:(0,a.m)(e.onPointerLeave,tl(e=>{var t,r;v();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,a="right"===t,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,a.m)(e.onKeyDown,t=>{let o=""!==i.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&ev[r.dir].includes(t.key)){var a;n.onOpenChange(!0),null==(a=n.content)||a.focus(),t.preventDefault()}})})})});tt.displayName=te;var tn="MenuSubContent",tr=o.forwardRef((e,t)=>{let n=eL(eS,e.__scopeMenu),{forceMount:r=n.forceMount,...l}=e,i=eM(eS,e.__scopeMenu),s=ej(eS,e.__scopeMenu),c=e7(tn,e.__scopeMenu),d=o.useRef(null),f=(0,u.s)(t,d);return(0,p.jsx)(eg.Provider,{scope:e.__scopeMenu,children:(0,p.jsx)(S.C,{present:r||i.open,children:(0,p.jsx)(eg.Slot,{scope:e.__scopeMenu,children:(0,p.jsx)(eG,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,a.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=em[s.dir].includes(e.key);if(t&&n){var r;i.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function to(e){return e?"open":"closed"}function ta(e){return"indeterminate"===e}function tu(e){return ta(e)?"indeterminate":e?"checked":"unchecked"}function tl(e){return t=>"mouse"===t.pointerType?e(t):void 0}tr.displayName=tn;var ti="DropdownMenu",[ts,tc]=(0,l.A)(ti,[eE]),td=eE(),[tf,tp]=ts(ti),tv=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:a,defaultOpen:u,onOpenChange:l,modal:s=!0}=e,c=td(t),d=o.useRef(null),[f=!1,v]=(0,i.i)({prop:a,defaultProp:u,onChange:l});return(0,p.jsx)(tf,{scope:t,triggerId:(0,P.B)(),triggerRef:d,contentId:(0,P.B)(),open:f,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),modal:s,children:(0,p.jsx)(eN,{...c,open:f,onOpenChange:v,dir:r,modal:s,children:n})})};tv.displayName=ti;var tm="DropdownMenuTrigger",th=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=tp(tm,n),i=td(n);return(0,p.jsx)(eP,{asChild:!0,...i,children:(0,p.jsx)(s.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,u.t)(t,l.triggerRef),onPointerDown:(0,a.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});th.displayName=tm;var tg=e=>{let{__scopeDropdownMenu:t,...n}=e,r=td(t);return(0,p.jsx)(ek,{...r,...n})};tg.displayName="DropdownMenuPortal";var tw="DropdownMenuContent",ty=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,u=tp(tw,n),l=td(n),i=o.useRef(!1);return(0,p.jsx)(eF,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...r,ref:t,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;i.current||null==(t=u.triggerRef.current)||t.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,a.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!u.modal||r)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ty.displayName=tw;var tb=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(eB,{...o,...r,ref:t})});tb.displayName="DropdownMenuGroup";var tE=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(eU,{...o,...r,ref:t})});tE.displayName="DropdownMenuLabel";var tC=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(eV,{...o,...r,ref:t})});tC.displayName="DropdownMenuItem";var tx=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(ez,{...o,...r,ref:t})});tx.displayName="DropdownMenuCheckboxItem";var tR=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(eJ,{...o,...r,ref:t})});tR.displayName="DropdownMenuRadioGroup";var tM=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(e$,{...o,...r,ref:t})});tM.displayName="DropdownMenuRadioItem";var tD=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(e9,{...o,...r,ref:t})});tD.displayName="DropdownMenuItemIndicator";var tj=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(e2,{...o,...r,ref:t})});tj.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(e6,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var tN=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(tt,{...o,...r,ref:t})});tN.displayName="DropdownMenuSubTrigger";var tP=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=td(n);return(0,p.jsx)(tr,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tP.displayName="DropdownMenuSubContent";var tO=tv,tT=th,tL=tg,tk=ty,tS=tb,tI=tE,t_=tC,tF=tx,tA=tR,tK=tM,tG=tD,tB=tj,tU=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:a}=e,u=td(t),[l=!1,s]=(0,i.i)({prop:r,defaultProp:a,onChange:o});return(0,p.jsx)(e4,{...u,open:l,onOpenChange:s,children:n})},tW=tN,tX=tP},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(47650),a=n(6101),u=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[a,l]=r.useState(),s=r.useRef({}),c=r.useRef(e),d=r.useRef("none"),[f,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===f?e:"none"},[f]),(0,u.N)(()=>{let t=s.current,n=c.current;if(n!==e){let r=d.current,o=i(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),c.current=e}},[e,p]),(0,u.N)(()=>{if(a){let e=e=>{let t=i(s.current).includes(e.animationName);e.target===a&&t&&o.flushSync(()=>p("ANIMATION_END"))},t=e=>{e.target===a&&(d.current=i(s.current))};return a.addEventListener("animationstart",t),a.addEventListener("animationcancel",e),a.addEventListener("animationend",e),()=>{a.removeEventListener("animationstart",t),a.removeEventListener("animationcancel",e),a.removeEventListener("animationend",e)}}p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,a.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:c}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},89196:(e,t,n)=>{n.d(t,{RG:()=>E,bL:()=>O,q7:()=>T});var r=n(12115),o=n(85185),a=n(76589),u=n(6101),l=n(46081),i=n(61285),s=n(63540),c=n(39033),d=n(5845),f=n(94315),p=n(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,a.N)(h),[b,E]=(0,l.A)(h,[y]),[C,x]=b(h),R=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(M,{...e,ref:t})})}));R.displayName=h;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:i,currentTabStopId:h,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:E=!1,...x}=e,R=r.useRef(null),M=(0,u.s)(t,R),D=(0,f.jH)(i),[j=null,N]=(0,d.i)({prop:h,defaultProp:g,onChange:y}),[O,T]=r.useState(!1),L=(0,c.c)(b),k=w(n),S=r.useRef(!1),[I,_]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,L),()=>e.removeEventListener(v,L)},[L]),(0,p.jsx)(C,{scope:n,orientation:a,dir:D,loop:l,currentTabStopId:j,onItemFocus:r.useCallback(e=>N(e),[N]),onItemShiftTab:r.useCallback(()=>T(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:O||0===I?-1:0,"data-orientation":a,...x,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),E)}}S.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),D="RovingFocusGroupItem",j=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:l,...c}=e,d=(0,i.B)(),f=l||d,v=x(D,n),m=v.currentTabStopId===f,h=w(n),{onFocusableItemAdd:y,onFocusableItemRemove:b}=v;return r.useEffect(()=>{if(a)return y(),()=>b()},[a,y,b]),(0,p.jsx)(g.ItemSlot,{scope:n,id:f,focusable:a,active:u,children:(0,p.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return N[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>P(n))}})})})});j.displayName=D;var N={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var O=R,T=j}}]);