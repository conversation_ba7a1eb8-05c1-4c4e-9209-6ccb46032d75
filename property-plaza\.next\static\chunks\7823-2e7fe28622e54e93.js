"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7823],{1184:(e,t,n)=>{n.d(t,{UO:()=>d,dK:()=>p,wE:()=>f});var r=n(12115),a=Object.defineProperty,l=Object.defineProperties,o=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d="^\\d+$",p=r.createContext({}),f=r.forwardRef((e,t)=>{var n,a,f,v,g,{value:y,onChange:b,maxLength:w,textAlign:k="left",pattern:E=d,inputMode:S="numeric",onComplete:x,pushPasswordManagerStrategy:O="increase-width",containerClassName:P,noScriptCSSFallback:j=h,render:C,children:M}=e,A=((e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n})(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[R,D]=r.useState("string"==typeof A.defaultValue?A.defaultValue:""),N=null!=y?y:R,T=function(e){let t=r.useRef();return r.useEffect(()=>{t.current=e}),t.current}(N),I=r.useCallback(e=>{null==b||b(e),D(e)},[b]),W=r.useMemo(()=>E?"string"==typeof E?new RegExp(E):E:null,[E]),_=r.useRef(null),B=r.useRef(null),L=r.useRef({value:N,onChange:I,isIOS:"undefined"!=typeof window&&(null==(a=null==(n=null==window?void 0:window.CSS)?void 0:n.supports)?void 0:a.call(n,"-webkit-touch-callout","none"))}),F=r.useRef({prev:[null==(f=_.current)?void 0:f.selectionStart,null==(v=_.current)?void 0:v.selectionEnd,null==(g=_.current)?void 0:g.selectionDirection]});r.useImperativeHandle(t,()=>_.current,[]),r.useEffect(()=>{let e=_.current,t=B.current;if(!e||!t)return;function n(){if(document.activeElement!==e){U(null),K(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,a=e.maxLength,l=e.value,o=F.current.prev,i=-1,u=-1,c;if(0!==l.length&&null!==t&&null!==n){let e=t===n,r=t===l.length&&l.length<a;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===a)i=t-1,u=t,c="backward";else if(a>1&&l.length>1){let e=0;if(null!==o[0]&&null!==o[1]){c=t<o[1]?"backward":"forward";let n=o[0]===o[1]&&o[0]<a;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&_.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,d=-1!==u?u:n,p=null!=c?c:r;U(s),K(d),F.current.prev=[s,d,p]}if(L.current.value!==e.value&&L.current.onChange(e.value),F.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&$(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";m(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),m(e.sheet,`[data-input-otp]:autofill { ${t} }`),m(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),m(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),m(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let a=new ResizeObserver(r);return a.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),a.disconnect()}},[]);let[H,z]=r.useState(!1),[G,$]=r.useState(!1),[V,U]=r.useState(null),[q,K]=r.useState(null);r.useEffect(()=>{!function(e){setTimeout(e,0),setTimeout(e,10),setTimeout(e,50)}(()=>{var e,t,n,r;null==(e=_.current)||e.dispatchEvent(new Event("input"));let a=null==(t=_.current)?void 0:t.selectionStart,l=null==(n=_.current)?void 0:n.selectionEnd,o=null==(r=_.current)?void 0:r.selectionDirection;null!==a&&null!==l&&(U(a),K(l),F.current.prev=[a,l,o])})},[N,G]),r.useEffect(()=>{void 0!==T&&N!==T&&T.length<w&&N.length===w&&(null==x||x(N))},[w,x,T,N]);let X=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:a}){let l=r.useRef({done:!1,refocused:!1}),[o,i]=r.useState(!1),[u,c]=r.useState(!1),[s,d]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&o&&u,[o,u,n]),f=r.useCallback(()=>{let r=e.current,a=t.current;if(!r||!a||s||"none"===n)return;let o=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if((0!==document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length||document.elementFromPoint(o-18,u)!==r)&&(i(!0),d(!0),!l.current.refocused&&document.activeElement===a)){let e=[a.selectionStart,a.selectionEnd];a.blur(),a.focus(),a.setSelectionRange(e[0],e[1]),l.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let a=setInterval(r,1e3);return()=>{clearInterval(a)}},[e,n]),r.useEffect(()=>{let e=a||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),l=setTimeout(f,2e3),o=setTimeout(f,5e3),i=setTimeout(()=>{d(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(l),clearTimeout(o),clearTimeout(i)}},[t,a,n,f]),{hasPWMBadge:o,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:B,inputRef:_,pushPasswordManagerStrategy:O,isFocused:G}),J=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,w);if(t.length>0&&W&&!W.test(t))return void e.preventDefault();"string"==typeof T&&t.length<T.length&&document.dispatchEvent(new Event("selectionchange")),I(t)},[w,I,T,W]),Q=r.useCallback(()=>{var e;if(_.current){let t=Math.min(_.current.value.length,w-1),n=_.current.value.length;null==(e=_.current)||e.setSelectionRange(t,n),U(t),K(n)}$(!0)},[w]),Y=r.useCallback(e=>{var t,n;let r=_.current;if(!L.current.isIOS||!e.clipboardData||!r)return;let a=e.clipboardData.getData("text/plain");e.preventDefault();let l=null==(t=_.current)?void 0:t.selectionStart,o=null==(n=_.current)?void 0:n.selectionEnd,i=(l!==o?N.slice(0,l)+a+N.slice(o):N.slice(0,l)+a+N.slice(l)).slice(0,w);if(i.length>0&&W&&!W.test(i))return;r.value=i,I(i);let u=Math.min(i.length,w-1),c=i.length;r.setSelectionRange(u,c),U(u),K(c)},[w,I,W,N]),Z=r.useMemo(()=>({position:"relative",cursor:A.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[A.disabled]),ee=r.useMemo(()=>({position:"absolute",inset:0,width:X.willPushPWMBadge?`calc(100% + ${X.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:X.willPushPWMBadge?`inset(0 ${X.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:k,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[X.PWM_BADGE_SPACE_WIDTH,X.willPushPWMBadge,k]),et=r.useMemo(()=>r.createElement("input",l(((e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e})({autoComplete:A.autoComplete||"one-time-code"},A),o({"data-input-otp":!0,"data-input-otp-mss":V,"data-input-otp-mse":q,inputMode:S,pattern:null==W?void 0:W.source,style:ee,maxLength:w,value:N,ref:_,onPaste:e=>{var t;Y(e),null==(t=A.onPaste)||t.call(A,e)},onChange:J,onMouseOver:e=>{var t;z(!0),null==(t=A.onMouseOver)||t.call(A,e)},onMouseLeave:e=>{var t;z(!1),null==(t=A.onMouseLeave)||t.call(A,e)},onFocus:e=>{var t;Q(),null==(t=A.onFocus)||t.call(A,e)},onBlur:e=>{var t;$(!1),null==(t=A.onBlur)||t.call(A,e)}}))),[J,Q,Y,S,ee,w,q,V,A,null==W?void 0:W.source,N]),en=r.useMemo(()=>({slots:Array.from({length:w}).map((e,t)=>{let n=G&&null!==V&&null!==q&&(V===q&&t===V||t>=V&&t<q),r=void 0!==N[t]?N[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:G,isHovering:!A.disabled&&H}),[G,H,w,q,V,A.disabled,N]),er=r.useMemo(()=>C?C(en):r.createElement(p.Provider,{value:en},M),[M,en,C]);return r.createElement(r.Fragment,null,null!==j&&r.createElement("noscript",null,r.createElement("style",null,j)),r.createElement("div",{ref:B,"data-input-otp-container":!0,style:Z,className:P},er,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},et)))});function m(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}f.displayName="Input";var h=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9691:(e,t,n)=>{n.d(t,{lw:()=>r.useReCaptcha}),n(10934),n(83428);var r=n(89985);n(68670)},14541:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},18804:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("MessagesSquare",[["path",{d:"M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2z",key:"jj09z8"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1",key:"1cx29u"}]])},38922:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowDownUp",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"m21 8-4-4-4 4",key:"1c9v7m"}],["path",{d:"M17 4v16",key:"7dpous"}]])},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},71007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74436:(e,t,n)=>{n.d(t,{k5:()=>s});var r=n(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},l=r.createContext&&r.createContext(a),o=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach(function(t){var r,a,l;r=e,a=t,l=n[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in r?Object.defineProperty(r,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e){return t=>r.createElement(d,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,c({key:n},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var n,{attr:a,size:l,title:u}=e,s=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,o),d=l||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,s,{className:n,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),u&&r.createElement("title",null,u),e.children)};return void 0!==l?r.createElement(l.Consumer,null,e=>t(e)):t(a)}},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85977:(e,t,n)=>{n.d(t,{BK:()=>v,H4:()=>k,_V:()=>w,bL:()=>b});var r=n(12115),a=n(46081),l=n(39033),o=n(52712),i=n(63540),u=n(95155),c="Avatar",[s,d]=(0,a.A)(c),[p,f]=s(c),m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...a}=e,[l,o]=r.useState("idle");return(0,u.jsx)(p,{scope:n,imageLoadingStatus:l,onImageLoadingStatusChange:o,children:(0,u.jsx)(i.sG.span,{...a,ref:t})})});m.displayName=c;var h="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:a,onLoadingStatusChange:c=()=>{},...s}=e,d=f(h,n),p=function(e){let[t,n]=r.useState("idle");return(0,o.N)(()=>{if(!e)return void n("error");let t=!0,r=new window.Image,a=e=>()=>{t&&n(e)};return n("loading"),r.onload=a("loaded"),r.onerror=a("error"),r.src=e,()=>{t=!1}},[e]),t}(a),m=(0,l.c)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,u.jsx)(i.sG.img,{...s,ref:t,src:a}):null});v.displayName=h;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:a,...l}=e,o=f(g,n),[c,s]=r.useState(void 0===a);return r.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>s(!0),a);return()=>window.clearTimeout(e)}},[a]),c&&"loaded"!==o.imageLoadingStatus?(0,u.jsx)(i.sG.span,{...l,ref:t}):null});y.displayName=g;var b=m,w=v,k=y},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);