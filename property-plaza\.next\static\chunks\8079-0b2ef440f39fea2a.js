"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8079],{21515:(n,t,e)=>{e.d(t,{jp:()=>v});var r=e(12115),a=e(29874),o=e(56985),i={left:0,top:0,right:0,gap:0},c=function(n){return parseInt(n||"",10)||0},u=function(n){var t=window.getComputedStyle(document.body),e=t["padding"===n?"paddingLeft":"marginLeft"],r=t["padding"===n?"paddingTop":"marginTop"],a=t["padding"===n?"paddingRight":"marginRight"];return[c(e),c(r),c(a)]},d=function(n){if(void 0===n&&(n="margin"),"undefined"==typeof window)return i;var t=u(n),e=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-e+t[2]-t[0])}},f=(0,a.T0)(),l="data-scroll-locked",s=function(n,t,e,r){var a=n.left,i=n.top,c=n.right,u=n.gap;return void 0===e&&(e="margin"),"\n  .".concat(o.E9," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(l,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===e&&"\n    padding-left: ".concat(a,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===e&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(o.Mi," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(o.pN," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(o.Mi," .").concat(o.Mi," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(o.pN," .").concat(o.pN," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(l,"] {\n    ").concat(o.xi,": ").concat(u,"px;\n  }\n")},p=function(){var n=parseInt(document.body.getAttribute(l)||"0",10);return isFinite(n)?n:0},h=function(){r.useEffect(function(){return document.body.setAttribute(l,(p()+1).toString()),function(){var n=p()-1;n<=0?document.body.removeAttribute(l):document.body.setAttribute(l,n.toString())}},[])},v=function(n){var t=n.noRelative,e=n.noImportant,a=n.gapMode,o=void 0===a?"margin":a;h();var i=r.useMemo(function(){return d(o)},[o]);return r.createElement(f,{styles:s(i,!t,o,e?"":"!important")})}},29874:(n,t,e)=>{e.d(t,{T0:()=>c});var r,a=e(12115),o=function(){var n=0,t=null;return{add:function(a){if(0==n&&(t=function(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var t=r||e.nc;return t&&n.setAttribute("nonce",t),n}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=a:o.appendChild(document.createTextNode(a)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}n++},remove:function(){--n||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},i=function(){var n=o();return function(t,e){a.useEffect(function(){return n.add(t),function(){n.remove()}},[t&&e])}},c=function(){var n=i();return function(t){return n(t.styles,t.dynamic),null}}},37548:(n,t,e)=>{e.d(t,{f:()=>o});var r=e(39249);function a(n){return n}function o(n){void 0===n&&(n={});var t,e,o,i=(void 0===t&&(t=a),e=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return e.length?e[e.length-1]:null},useMedium:function(n){var r=t(n,o);return e.push(r),function(){e=e.filter(function(n){return n!==r})}},assignSyncMedium:function(n){for(o=!0;e.length;){var t=e;e=[],t.forEach(n)}e={push:function(t){return n(t)},filter:function(){return e}}},assignMedium:function(n){o=!0;var t=[];if(e.length){var r=e;e=[],r.forEach(n),t=e}var a=function(){var e=t;t=[],e.forEach(n)},i=function(){return Promise.resolve().then(a)};i(),e={push:function(n){t.push(n),i()},filter:function(n){return t=t.filter(n),e}}}});return i.options=(0,r.Cl)({async:!0,ssr:!1},n),i}},38168:(n,t,e)=>{e.d(t,{Eq:()=>d});var r=new WeakMap,a=new WeakMap,o={},i=0,c=function(n){return n&&(n.host||c(n.parentNode))},u=function(n,t,e,u){var d=(Array.isArray(n)?n:[n]).map(function(n){if(t.contains(n))return n;var e=c(n);return e&&t.contains(e)?e:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n});o[e]||(o[e]=new WeakMap);var f=o[e],l=[],s=new Set,p=new Set(d),h=function(n){!n||s.has(n)||(s.add(n),h(n.parentNode))};d.forEach(h);var v=function(n){!n||p.has(n)||Array.prototype.forEach.call(n.children,function(n){if(s.has(n))v(n);else try{var t=n.getAttribute(u),o=null!==t&&"false"!==t,i=(r.get(n)||0)+1,c=(f.get(n)||0)+1;r.set(n,i),f.set(n,c),l.push(n),1===i&&o&&a.set(n,!0),1===c&&n.setAttribute(e,"true"),o||n.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",n,t)}})};return v(t),s.clear(),i++,function(){l.forEach(function(n){var t=r.get(n)-1,o=f.get(n)-1;r.set(n,t),f.set(n,o),t||(a.has(n)||n.removeAttribute(u),a.delete(n)),o||n.removeAttribute(e)}),--i||(r=new WeakMap,r=new WeakMap,a=new WeakMap,o={})}},d=function(n,t,e){void 0===e&&(e="data-aria-hidden");var r=Array.from(Array.isArray(n)?n:[n]),a=t||("undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live]"))),u(r,a,e,"aria-hidden")):function(){return null}}},50514:(n,t,e)=>{e.d(t,{m:()=>i});var r=e(39249),a=e(12115),o=function(n){var t=n.sideCar,e=(0,r.Tt)(n,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw Error("Sidecar medium not found");return a.createElement(o,(0,r.Cl)({},e))};function i(n,t){return n.useMedium(t),o}o.isSideCarExport=!0},56985:(n,t,e)=>{e.d(t,{E9:()=>o,Mi:()=>r,pN:()=>a,xi:()=>i});var r="right-scroll-bar-position",a="width-before-scroll-bar",o="with-scroll-bars-hidden",i="--removed-body-scroll-bar-size"},70464:(n,t,e)=>{e.d(t,{S:()=>c});var r=e(12115);function a(n,t){return"function"==typeof n?n(t):n&&(n.current=t),n}var o="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,i=new WeakMap;function c(n,t){var e,c,u,d=(e=t||null,c=function(t){return n.forEach(function(n){return a(n,t)})},(u=(0,r.useState)(function(){return{value:e,callback:c,facade:{get current(){return u.value},set current(value){var n=u.value;n!==value&&(u.value=value,u.callback(value,n))}}}})[0]).callback=c,u.facade);return o(function(){var t=i.get(d);if(t){var e=new Set(t),r=new Set(n),o=d.current;e.forEach(function(n){r.has(n)||a(n,null)}),r.forEach(function(n){e.has(n)||a(n,o)})}i.set(d,n)},[n]),d}}}]);