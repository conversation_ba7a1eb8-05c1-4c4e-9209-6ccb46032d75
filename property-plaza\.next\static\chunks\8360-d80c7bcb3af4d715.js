"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8360],{575:(e,t,r)=>{r.d(t,{o:()=>n});function n(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}},781:(e,t,r)=>{r.d(t,{I:()=>i});var n=r(39249),o=r(50793),s=Array.isArray;function i(e){return(0,o.T)(function(t){return s(t)?e.apply(void 0,(0,n.fX)([],(0,n.zs)(t))):e(t)})}},920:(e,t,r)=>{r.d(t,{L:()=>n});function n(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}},1125:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(91208);function o(e){return function(t){if((0,n.T)(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}},2981:(e,t,r)=>{r.d(t,{El:()=>l,Hi:()=>u,NL:()=>s,kN:()=>a,sq:()=>c});let n=new Set,o="checking";function s(e){if(o!==e)for(let t of(o=e,n))t()}let i=new Set;function a(e){for(let e of i)e()}let u=new Set,c=null;function l(e){for(let t of(c=e,u))t()}},10255:(e,t,r)=>{function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),r(95155),r(47650),r(85744),r(20589)},16234:(e,t,r)=>{r.d(t,{l:()=>n});var n="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"},17828:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,r(64054).createAsyncLocalStorage)()},18677:(e,t,r)=>{r.d(t,{default:()=>s});var n=r(95155);let o=(0,r(55028).default)(()=>r.e(2258).then(r.bind(r,92258)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/SanityLiveStream.js")]},ssr:!1});function s(e){return(0,n.jsx)(o,{...e})}},26269:(e,t,r)=>{r.d(t,{H:()=>v});var n=r(57173),o=r(36270),s=r(1125);function i(e,t){return void 0===t&&(t=0),(0,s.N)(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}var a=r(94354),u=r(16234),c=r(91208),l=r(74962);function d(e,t){if(!e)throw Error("Iterable cannot be null");return new a.c(function(r){(0,l.N)(r,t,function(){var n=e[Symbol.asyncIterator]();(0,l.N)(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}var h=r(82388),f=r(27745),p=r(56534),y=r(49930),m=r(67606),g=r(920),b=r(39839);function v(e,t){return t?function(e,t){if(null!=e){if((0,h.l)(e))return(0,n.Tg)(e).pipe(i(t),(0,o.Q)(t));if((0,p.X)(e))return new a.c(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})});if((0,f.y)(e))return(0,n.Tg)(e).pipe(i(t),(0,o.Q)(t));if((0,m.T)(e))return d(e,t);if((0,y.x)(e))return new a.c(function(r){var n;return(0,l.N)(r,t,function(){n=e[u.l](),(0,l.N)(r,t,function(){var e,t,o;try{t=(e=n.next()).value,o=e.done}catch(e){r.error(e);return}o?r.complete():r.next(t)},0,!0)}),function(){return(0,c.T)(null==n?void 0:n.return)&&n.return()}});if((0,b.U)(e))return d((0,b.C)(e),t)}throw(0,g.L)(e)}(e,t):(0,n.Tg)(e)}},27110:(e,t,r)=>{r.d(t,{Ms:()=>g,vU:()=>f});var n=r(39249),o=r(91208),s=r(73817),i=r(80329),a=r(58197),u=r(77020),c=l("C",void 0,void 0);function l(e,t,r){return{kind:e,value:t,error:r}}var d=r(68473),h=r(39833),f=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,(0,s.Uv)(t)&&t.add(r)):r.destination=w,r}return(0,n.C6)(t,e),t.create=function(e,t,r){return new g(e,t,r)},t.prototype.next=function(e){this.isStopped?v(l("N",e,void 0),this):this._next(e)},t.prototype.error=function(e){this.isStopped?v(l("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?v(c,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(s.yU),p=Function.prototype.bind;function y(e,t){return p.call(e,t)}var m=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){b(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){b(e)}else b(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){b(e)}},e}(),g=function(e){function t(t,r,n){var s,a,u=e.call(this)||this;return(0,o.T)(t)||!t?s={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:u&&i.$.useDeprecatedNextContext?((a=Object.create(t)).unsubscribe=function(){return u.unsubscribe()},s={next:t.next&&y(t.next,a),error:t.error&&y(t.error,a),complete:t.complete&&y(t.complete,a)}):s=t,u.destination=new m(s),u}return(0,n.C6)(t,e),t}(f);function b(e){i.$.useDeprecatedSynchronousErrorHandling?(0,h.l)(e):(0,a.m)(e)}function v(e,t){var r=i.$.onStoppedNotification;r&&d.f.setTimeout(function(){return r(e,t)})}var w={closed:!0,next:u.l,error:function(e){throw e},complete:u.l}},27745:(e,t,r)=>{r.d(t,{y:()=>o});var n=r(91208);function o(e){return(0,n.T)(null==e?void 0:e.then)}},34477:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return s.createServerReference},findSourceMapURL:function(){return o.findSourceMapURL}});let n=r(53806),o=r(31818),s=r(77197)},36270:(e,t,r)=>{r.d(t,{Q:()=>i});var n=r(74962),o=r(1125),s=r(98175);function i(e,t){return void 0===t&&(t=0),(0,o.N)(function(r,o){r.subscribe((0,s._)(o,function(r){return(0,n.N)(o,e,function(){return o.next(r)},t)},function(){return(0,n.N)(o,e,function(){return o.complete()},t)},function(r){return(0,n.N)(o,e,function(){return o.error(r)},t)}))})}},36645:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(88229)._(r(67357));function o(e,t){var r;let o={};"function"==typeof e&&(o.loader=e);let s={...o,...t};return(0,n.default)({...s,modules:null==(r=s.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38541:(e,t,r)=>{r.d(t,{s:()=>n});var n="function"==typeof Symbol&&Symbol.observable||"@@observable"},39833:(e,t,r)=>{r.d(t,{Y:()=>s,l:()=>i});var n=r(80329),o=null;function s(e){if(n.$.useDeprecatedSynchronousErrorHandling){var t=!o;if(t&&(o={errorThrown:!1,error:null}),e(),t){var r=o,s=r.errorThrown,i=r.error;if(o=null,s)throw i}}else e()}function i(e){n.$.useDeprecatedSynchronousErrorHandling&&o&&(o.errorThrown=!0,o.error=e)}},39839:(e,t,r)=>{r.d(t,{C:()=>s,U:()=>i});var n=r(39249),o=r(91208);function s(e){return(0,n.AQ)(this,arguments,function(){var t,r,o;return(0,n.YH)(this,function(s){switch(s.label){case 0:t=e.getReader(),s.label=1;case 1:s.trys.push([1,,9,10]),s.label=2;case 2:return[4,(0,n.N3)(t.read())];case 3:if(o=(r=s.sent()).value,!r.done)return[3,5];return[4,(0,n.N3)(void 0)];case 4:return[2,s.sent()];case 5:return[4,(0,n.N3)(o)];case 6:return[4,s.sent()];case 7:return s.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function i(e){return(0,o.T)(null==e?void 0:e.getReader)}},46295:(e,t,r)=>{r.d(t,{J:()=>o});var n=r(12115);function o(e){let t=(0,n.useRef)(null);return(0,n.useInsertionEffect)(()=>{t.current=e},[e]),(0,n.useCallback)((...e)=>(0,t.current)(...e),[])}},47978:(e,t,r)=>{r.d(t,{default:()=>i});var n=r(95155),o=r(12115);let s=(0,o.lazy)(()=>Promise.all([r.e(2299),r.e(2770),r.e(3288),r.e(1433)]).then(r.bind(r,73288)));function i(e){return(0,n.jsx)(o.Suspense,{fallback:null,children:(0,n.jsx)(s,{...e})})}},48552:(e,t,r)=>{r.d(t,{default:()=>tR});var n=r(95155);let o=!(typeof navigator>"u")&&"ReactNative"===navigator.product,s={timeout:o?6e4:12e4},i=function(e){let t={...s,..."string"==typeof e?{url:e}:e};if(t.timeout=function e(t){if(!1===t||0===t)return!1;if(t.connect||t.socket)return t;let r=Number(t);return isNaN(r)?e(s.timeout):{connect:r,socket:r}}(t.timeout),t.query){let{url:e,searchParams:r}=function(e){let t=e.indexOf("?");if(-1===t)return{url:e,searchParams:new URLSearchParams};let r=e.slice(0,t),n=e.slice(t+1);if(!o)return{url:r,searchParams:new URLSearchParams(n)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let s=new URLSearchParams;for(let e of n.split("&")){let[t,r]=e.split("=");t&&s.append(a(t),a(r||""))}return{url:r,searchParams:s}}(t.url);for(let[n,o]of Object.entries(t.query)){if(void 0!==o)if(Array.isArray(o))for(let e of o)r.append(n,e);else r.append(n,o);let s=r.toString();s&&(t.url=`${e}?${s}`)}}return t.method=t.body&&!t.method?"POST":(t.method||"GET").toUpperCase(),t};function a(e){return decodeURIComponent(e.replace(/\+/g," "))}let u=/^https?:\/\//i,c=function(e){if(!u.test(e.url))throw Error(`"${e.url}" is not a valid URL`)},l=["request","response","progress","error","abort"],d=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var h,f,p=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function(){if(f)return h;f=1;var e=function(e){return e.replace(/^\s+|\s+$/g,"")};return h=function(t){if(!t)return{};for(var r=Object.create(null),n=e(t).split("\n"),o=0;o<n.length;o++){var s,i=n[o],a=i.indexOf(":"),u=e(i.slice(0,a)).toLowerCase(),c=e(i.slice(a+1));typeof r[u]>"u"?r[u]=c:(s=r[u],"[object Array]"===Object.prototype.toString.call(s))?r[u].push(c):r[u]=[r[u],c]}return r}}());class y{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#e;#t;#r;#n={};#o;#s={};#i;open(e,t,r){this.#e=e,this.#t=t,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#o=void 0}abort(){this.#o&&this.#o.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#n[e]=t}setInit(e,t=!0){this.#s=e,this.#i=t}send(e){let t="arraybuffer"!==this.responseType,r={...this.#s,method:this.#e,headers:this.#n,body:e};"function"==typeof AbortController&&this.#i&&(this.#o=new AbortController,"u">typeof EventTarget&&this.#o.signal instanceof EventTarget&&(r.signal=this.#o.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#t,r).then(e=>(e.headers.forEach((e,t)=>{this.#r+=`${t}: ${e}\r
`}),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer())).then(e=>{"string"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()}).catch(e=>{"AbortError"!==e.name?this.onerror?.(e):this.onabort?.()})}}let m="function"==typeof XMLHttpRequest?"xhr":"fetch",g="xhr"===m?XMLHttpRequest:y,b=(e,t)=>{let r=e.options,n=e.applyMiddleware("finalizeOptions",r),o={},s=e.applyMiddleware("interceptRequest",void 0,{adapter:m,context:e});if(s){let e=setTimeout(t,0,null,s);return{abort:()=>clearTimeout(e)}}let i=new g;i instanceof y&&"object"==typeof n.fetch&&i.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,u=n.timeout,c=!1,l=!1,d=!1;if(i.onerror=e=>{b(i instanceof y?e instanceof Error?e:Error(`Request error while attempting to reach is ${n.url}`,{cause:e}):Error(`Request error while attempting to reach is ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},i.ontimeout=e=>{b(Error(`Request timeout while attempting to reach ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},i.onabort=()=>{f(!0),c=!0},i.onreadystatechange=function(){u&&(f(),o.socket=setTimeout(()=>h("ESOCKETTIMEDOUT"),u.socket)),!c&&i&&4===i.readyState&&0!==i.status&&function(){if(!(c||l||d)){if(0===i.status)return b(Error("Unknown XHR error"));f(),l=!0,t(null,{body:i.response||(""===i.responseType||"text"===i.responseType?i.responseText:""),url:n.url,method:n.method,headers:p(i.getAllResponseHeaders()),statusCode:i.status,statusMessage:i.statusText})}}()},i.open(n.method,n.url,!0),i.withCredentials=!!n.withCredentials,a&&i.setRequestHeader)for(let e in a)a.hasOwnProperty(e)&&i.setRequestHeader(e,a[e]);return n.rawBody&&(i.responseType="arraybuffer"),e.applyMiddleware("onRequest",{options:n,adapter:m,request:i,context:e}),i.send(n.body||null),u&&(o.connect=setTimeout(()=>h("ETIMEDOUT"),u.connect)),{abort:function(){c=!0,i&&i.abort()}};function h(t){d=!0,i.abort();let r=Error("ESOCKETTIMEDOUT"===t?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=t,e.channels.error.publish(r)}function f(e){(e||c||i&&i.readyState>=2&&o.connect)&&clearTimeout(o.connect),o.socket&&clearTimeout(o.socket)}function b(e){if(l)return;f(!0),l=!0,i=null;let r=e||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,t(r)}};var v,w,C,E,x,R=r(87358),j=r(44134).Buffer,T={exports:{}};x||(x=1,function(e,t){let r;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))}),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch{}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch{}return!e&&"u">typeof R&&"env"in R&&(e=R.env.DEBUG),e},t.useColors=function(){let e;return"u">typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch{}}(),r=!1,t.destroy=()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))},t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=(E?C:(E=1,C=function(e){function t(e){let n,o,s,i=null;function a(...e){if(!a.enabled)return;let r=Number(new Date);a.diff=r-(n||r),a.prev=n,a.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";o++;let s=t.formatters[n];if("function"==typeof s){let t=e[o];r=s.call(a,t),e.splice(o,1),o--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(o!==t.namespaces&&(o=t.namespaces,s=t.enabled(e)),s),set:e=>{i=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,r){let n=t(this.namespace+(typeof r>"u"?":":r)+e);return n.log=this.log,n}function n(e,t){let r=0,n=0,o=-1,s=0;for(;r<e.length;)if(n<t.length&&(t[n]===e[r]||"*"===t[n]))"*"===t[n]?(o=n,s=r):r++,n++;else{if(-1===o)return!1;n=o+1,r=++s}for(;n<t.length&&"*"===t[n];)n++;return n===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let r of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===r[0]?t.skips.push(r.slice(1)):t.names.push(r)},t.enabled=function(e){for(let r of t.skips)if(n(e,r))return!1;for(let r of t.names)if(n(e,r))return!0;return!1},t.humanize=function(){if(w)return v;w=1;function e(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}return v=function(t,r){r=r||{};var n,o,s=typeof t;if("string"===s&&t.length>0){var i=t;if(!((i=String(i)).length>100)){var a=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(a){var u=parseFloat(a[1]);switch((a[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*u;case"weeks":case"week":case"w":return 6048e5*u;case"days":case"day":case"d":return 864e5*u;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*u;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*u;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*u;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u}}}return}if("number"===s&&isFinite(t))return r.long?(o=Math.abs(t))>=864e5?e(t,o,864e5,"day"):o>=36e5?e(t,o,36e5,"hour"):o>=6e4?e(t,o,6e4,"minute"):o>=1e3?e(t,o,1e3,"second"):t+" ms":(n=Math.abs(t))>=864e5?Math.round(t/864e5)+"d":n>=36e5?Math.round(t/36e5)+"h":n>=6e4?Math.round(t/6e4)+"m":n>=1e3?Math.round(t/1e3)+"s":t+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}))(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(T,T.exports)),T.exports,Object.prototype.hasOwnProperty;let O=typeof j>"u"?()=>!1:e=>j.isBuffer(e);function q(e){return"[object Object]"===Object.prototype.toString.call(e)}let S=["boolean","string","number"],A={};"u">typeof globalThis?A=globalThis:"u">typeof window?A=window:"u">typeof global?A=global:"u">typeof self&&(A=self);var I=A;let $=(e={})=>{let t=e.implementation||Promise;if(!t)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new t((t,o)=>{let s=n.options.cancelToken;s&&s.promise.then(e=>{r.abort.publish(e),o(e)}),r.error.subscribe(o),r.response.subscribe(r=>{t(e.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(e){o(e)}},0)})}};class P{__CANCEL__=!0;message;constructor(e){this.message=e}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class _{promise;reason;constructor(e){if("function"!=typeof e)throw TypeError("executor must be a function.");let t=null;this.promise=new Promise(e=>{t=e}),e(e=>{this.reason||(this.reason=new P(e),t(this.reason))})}static source=()=>{let e;return{token:new _(t=>{e=t}),cancel:e}}}$.Cancel=P,$.CancelToken=_,$.isCancel=e=>!(!e||!e?.__CANCEL__);var k=(e,t,r)=>("GET"===r.method||"HEAD"===r.method)&&(e.isNetworkError||!1);function F(e){return 100*Math.pow(2,e)+100*Math.random()}let N=(e={})=>(e=>{let t=e.maxRetries||5,r=e.retryDelay||F,n=e.shouldRetry;return{onError:(e,o)=>{var s;let i=o.options,a=i.maxRetries||t,u=i.retryDelay||r,c=i.shouldRetry||n,l=i.attemptNumber||0;if(null!==(s=i.body)&&"object"==typeof s&&"function"==typeof s.pipe||!c(e,l,i)||l>=a)return e;let d=Object.assign({},o,{options:Object.assign({},i,{attemptNumber:l+1})});return setTimeout(()=>o.channels.request.publish(d),u(l)),null}}})({shouldRetry:k,...e});N.shouldRetry=k;var D=r(94354),M=r(26269),L=(0,r(68066).L)(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function U(e,t){var r="object"==typeof t;return new Promise(function(n,o){var s,i=!1;e.subscribe({next:function(e){s=e,i=!0},error:o,complete:function(){i?n(s):r?n(t.defaultValue):o(new L)}})})}var z=r(91501),H=r(39249),B=r(78906),J=r(98175),X=r(74962);function G(e,t,r){e?(0,X.N)(r,e,t):t()}var V=r(1125),Y=Array.isArray,Q=r(781),W=r(88580),K=r(88332);function Z(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(0,K.ms)(e);return r?(0,W.F)(Z.apply(void 0,(0,H.fX)([],(0,H.zs)(e))),(0,Q.I)(r)):(0,V.N)(function(t,r){var n,o,s;(n=(0,H.fX)([t],(0,H.zs)(1===e.length&&Y(e[0])?e[0]:e)),void 0===s&&(s=B.D),function(e){G(void 0,function(){for(var t=n.length,r=Array(t),i=t,a=t,u=function(t){G(o,function(){var u=(0,M.H)(n[t],o),c=!1;u.subscribe((0,J._)(e,function(n){r[t]=n,!c&&(c=!0,a--),a||e.next(s(r.slice()))},function(){--i||e.complete()}))},e)},c=0;c<t;c++)u(c)},e)})(r)})}var ee=r(50793),et=r(69647);class er extends Error{response;statusCode=400;responseBody;details;constructor(e){let t=eo(e);super(t.message),Object.assign(this,t)}}class en extends Error{response;statusCode=500;responseBody;details;constructor(e){let t=eo(e);super(t.message),Object.assign(this,t)}}function eo(e){var t,r,n;let o=e.body,s={response:e,statusCode:e.statusCode,responseBody:(t=o,-1!==(e.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(t,null,2):t),message:"",details:void 0};if(o.error&&o.message)return s.message=`${o.error} - ${o.message}`,s;if(es(r=o)&&es(r.error)&&"mutationError"===r.error.type&&"string"==typeof r.error.description||es(n=o)&&es(n.error)&&"actionError"===n.error.type&&"string"==typeof n.error.description){let e=o.error.items||[],t=e.slice(0,5).map(e=>e.error?.description).filter(Boolean),r=t.length?`:
- ${t.join(`
- `)}`:"";return e.length>5&&(r+=`
...and ${e.length-5} more`),s.message=`${o.error.description}${r}`,s.details=o.error,s}return o.error&&o.error.description?(s.message=o.error.description,s.details=o.error):s.message=o.error||o.message||function(e){let t=e.statusMessage?` ${e.statusMessage}`:"";return`${e.method}-request to ${e.url} resulted in HTTP ${e.statusCode}${t}`}(e),s}function es(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}class ei extends Error{projectId;addOriginUrl;constructor({projectId:e}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=e;let t=new URL(`https://sanity.io/manage/project/${e}/api`);if("u">typeof location){let{origin:e}=location;t.searchParams.set("cors","add"),t.searchParams.set("origin",e),this.addOriginUrl=t,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${t}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${t}`}}let ea={onResponse:e=>{if(e.statusCode>=500)throw new en(e);if(e.statusCode>=400)throw new er(e);return e}},eu={onResponse:e=>{let t=e.headers["x-sanity-warning"];return(Array.isArray(t)?t:[t]).filter(Boolean).forEach(e=>console.warn(e)),e}};function ec(e,t,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,o=(r.uri||r.url).startsWith("/data/query"),s=e.response&&(429===e.response.statusCode||502===e.response.statusCode||503===e.response.statusCode);return(!!n||!!o)&&!!s||N.shouldRetry(e,t,r)}function el(e){return"https://www.sanity.io/help/"+e}let ed=["image","file"],eh=["before","after","replace"],ef=e=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(e))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},ep=(e,t)=>{if(null===t||"object"!=typeof t||Array.isArray(t))throw Error(`${e}() takes an object of properties`)},ey=(e,t)=>{if("string"!=typeof t||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(t)||t.includes(".."))throw Error(`${e}(): "${t}" is not a valid document ID`)},em=(e,t)=>{if(!t._id)throw Error(`${e}() requires that the document contains an ID ("_id" property)`);ey(e,t._id)},eg=e=>{if(!e.dataset)throw Error("`dataset` must be provided to perform queries");return e.dataset||""},eb=e=>{if("string"!=typeof e||!/^[a-z0-9._-]{1,75}$/i.test(e))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return e},ev=e=>(function(e){let t=!1,r;return(...n)=>(t||(r=e(...n),t=!0),r)})((...t)=>console.warn(e.join(" "),...t)),ew=ev(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),eC=ev(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eE=ev(["The Sanity client is configured with the `perspective` set to `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),ex=ev(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${el("js-client-browser-token")} for more information and how to hide this warning.`]),eR=ev(["Using the Sanity client without specifying an API version is deprecated.",`See ${el("js-client-api-version")}`]),ej=(ev(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),eT=["localhost","127.0.0.1","0.0.0.0"];function eO(e){if(Array.isArray(e)){for(let t of e)if("published"!==t&&"drafts"!==t&&!("string"==typeof t&&t.startsWith("r")&&"raw"!==t))throw TypeError("Invalid API perspective value, expected `published`, `drafts` or a valid release identifier string");return}switch(e){case"previewDrafts":case"drafts":case"published":case"raw":return;default:throw TypeError("Invalid API perspective string, expected `published`, `previewDrafts` or `raw`")}}let eq=(e,t)=>{let r,n={...t,...e,stega:{..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||ej.stega,..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||{}}};n.apiVersion||eR();let o={...ej,...n},s=o.useProjectHostname;if(typeof Promise>"u"){let e=el("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${e}`)}if(s&&!o.projectId)throw Error("Configuration must contain `projectId`");if("u">typeof o.perspective&&eO(o.perspective),"encodeSourceMap"in o)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in o)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof o.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${o.stega.enabled}`);if(o.stega.enabled&&void 0===o.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(o.stega.enabled&&"string"!=typeof o.stega.studioUrl&&"function"!=typeof o.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${o.stega.studioUrl}`);let i="u">typeof window&&window.location&&window.location.hostname,a=i&&(r=window.location.hostname,-1!==eT.indexOf(r));i&&a&&o.token&&!0!==o.ignoreBrowserTokenWarning?ex():typeof o.useCdn>"u"&&eC(),s&&(e=>{if(!/^[-a-z0-9]+$/i.test(e))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")})(o.projectId),o.dataset&&ef(o.dataset),"requestTagPrefix"in o&&(o.requestTagPrefix=o.requestTagPrefix?eb(o.requestTagPrefix).replace(/\.+$/,""):void 0),o.apiVersion=`${o.apiVersion}`.replace(/^v/,""),o.isDefaultApi=o.apiHost===ej.apiHost,!0===o.useCdn&&o.withCredentials&&ew(),o.useCdn=!1!==o.useCdn&&!o.withCredentials,function(e){if("1"===e||"X"===e)return;let t=new Date(e);if(!(/^\d{4}-\d{2}-\d{2}$/.test(e)&&t instanceof Date&&t.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(o.apiVersion);let u=o.apiHost.split("://",2),c=u[0],l=u[1],d=o.isDefaultApi?"apicdn.sanity.io":l;return o.useProjectHostname?(o.url=`${c}://${o.projectId}.${l}/v${o.apiVersion}`,o.cdnUrl=`${c}://${o.projectId}.${d}/v${o.apiVersion}`):(o.url=`${o.apiHost}/v${o.apiVersion}`,o.cdnUrl=o.url),o};function eS(e){if("string"==typeof e)return{id:e};if(Array.isArray(e))return{query:"*[_id in $ids]",params:{ids:e}};if("object"==typeof e&&null!==e&&"query"in e&&"string"==typeof e.query)return"params"in e&&"object"==typeof e.params&&null!==e.params?{query:e.query,params:e.params}:{query:e.query};let t=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${t}`)}class eA{selection;operations;constructor(e,t={}){this.selection=e,this.operations=t}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return ep("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,t,r){return((e,t,r)=>{let n="insert(at, selector, items)";if(-1===eh.indexOf(e)){let e=eh.map(e=>`"${e}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${e}`)}if("string"!=typeof t)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)})(e,t,r),this._assign("insert",{[e]:t,items:r})}append(e,t){return this.insert("after",`${e}[-1]`,t)}prepend(e,t){return this.insert("before",`${e}[0]`,t)}splice(e,t,r,n){let o=t<0?t-1:t,s=typeof r>"u"||-1===r?-1:Math.max(0,t+r),i=`${e}[${o}:${o<0&&s>=0?"":s}]`;return this.insert("replace",i,n||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...eS(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,t,r=!0){return ep(e,t),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},r&&this.operations[e]||{},t)}),this}_set(e,t){return this._assign(e,t,!1)}}class eI extends eA{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new eI(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}class e$ extends eA{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new e$(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}let eP={returnDocuments:!1};class e_{operations;trxId;constructor(e=[],t){this.operations=e,this.trxId=t}create(e){return ep("create",e),this._add({create:e})}createIfNotExists(e){let t="createIfNotExists";return ep(t,e),em(t,e),this._add({[t]:e})}createOrReplace(e){let t="createOrReplace";return ep(t,e),em(t,e),this._add({[t]:e})}delete(e){return ey("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}class ek extends e_{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new ek([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},eP,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof e$)return this._add({patch:e.serialize()});if(r){let r=t(new e$(e,{},this.#a));if(!(r instanceof e$))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}class eF extends e_{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new eF([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},eP,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof eI)return this._add({patch:e.serialize()});if(r){let r=t(new eI(e,{},this.#a));if(!(r instanceof eI))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}let eN=({query:e,params:t={},options:r={}})=>{let n=new URLSearchParams,{tag:o,includeMutations:s,returnQuery:i,...a}=r;for(let[r,s]of(o&&n.append("tag",o),n.append("query",e),Object.entries(t)))n.append(`$${r}`,JSON.stringify(s));for(let[e,t]of Object.entries(a))t&&n.append(e,`${t}`);return!1===i&&n.append("returnQuery","false"),!1===s&&n.append("includeMutations","false"),`?${n}`},eD=e=>"response"===e.type,eM=e=>e.body;function eL(e,t,n,o,s={},i={}){let a="stega"in i?{...n||{},..."boolean"==typeof i.stega?{enabled:i.stega}:i.stega||{}}:n,u=a.enabled?(0,z.Q)(s):s,c=!1===i.filterResponse?e=>e:e=>e.result,{cache:l,next:d,...h}={useAbortSignal:"u">typeof i.signal,resultSourceMap:a.enabled?"withKeyArraySelector":i.resultSourceMap,...i,returnQuery:!1===i.filterResponse&&!1!==i.returnQuery},f=eV(e,t,"query",{query:o,params:u},"u">typeof l||"u">typeof d?{...h,fetch:{cache:l,next:d}}:h);return a.enabled?f.pipe(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Z.apply(void 0,(0,H.fX)([],(0,H.zs)(e)))}((0,M.H)(r.e(4195).then(r.bind(r,74195)).then(function(e){return e.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:e})=>e))),(0,ee.T)(([e,t])=>{let r=t(e.result,e.resultSourceMap,a);return c({...e,result:r})})):f.pipe((0,ee.T)(c))}function eU(e,t,r,n={}){let o={uri:eK(e,"doc",r),json:!0,tag:n.tag,signal:n.signal};return eQ(e,t,o).pipe((0,et.p)(eD),(0,ee.T)(e=>e.body.documents&&e.body.documents[0]))}function ez(e,t,r,n={}){let o={uri:eK(e,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return eQ(e,t,o).pipe((0,et.p)(eD),(0,ee.T)(e=>{let t,n,o=(t=e.body.documents||[],n=e=>e._id,t.reduce((e,t)=>(e[n(t)]=t,e),Object.create(null)));return r.map(e=>o[e]||null)}))}function eH(e,t,r,n){return em("createIfNotExists",r),eY(e,t,r,"createIfNotExists",n)}function eB(e,t,r,n){return em("createOrReplace",r),eY(e,t,r,"createOrReplace",n)}function eJ(e,t,r,n){return eV(e,t,"mutate",{mutations:[{delete:eS(r)}]},n)}function eX(e,t,r,n){let o;return eV(e,t,"mutate",{mutations:Array.isArray(o=r instanceof e$||r instanceof eI?{patch:r.serialize()}:r instanceof ek||r instanceof eF?r.serialize():r)?o:[o],transactionId:n&&n.transactionId||void 0},n)}function eG(e,t,r,n){let o=Array.isArray(r)?r:[r],s=n&&n.transactionId||void 0;return eV(e,t,"actions",{actions:o,transactionId:s,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function eV(e,t,r,n,o={}){let s="mutate"===r,i="actions"===r,a=s||i?"":eN(n),u=!s&&!i&&a.length<11264,c=u?a:"",l=o.returnFirst,{timeout:d,token:h,tag:f,headers:p,returnQuery:y,lastLiveEventId:m,cacheMode:g}=o,b={method:u?"GET":"POST",uri:eK(e,r,c),json:!0,body:u?void 0:n,query:s&&((e={})=>{let t,r;return{dryRun:e.dryRun,returnIds:!0,returnDocuments:(t=e.returnDocuments,r=!0,!1===t?void 0:typeof t>"u"?r:t),visibility:e.visibility||"sync",autoGenerateArrayKeys:e.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:e.skipCrossDatasetReferenceValidation}})(o),timeout:d,headers:p,token:h,tag:f,returnQuery:y,perspective:o.perspective,resultSourceMap:o.resultSourceMap,lastLiveEventId:Array.isArray(m)?m[0]:m,cacheMode:g,canUseCdn:"query"===r,signal:o.signal,fetch:o.fetch,useAbortSignal:o.useAbortSignal,useCdn:o.useCdn};return eQ(e,t,b).pipe((0,et.p)(eD),(0,ee.T)(eM),(0,ee.T)(e=>{if(!s)return e;let t=e.results||[];if(o.returnDocuments)return l?t[0]&&t[0].document:t.map(e=>e.document);let r=l?t[0]&&t[0].id:t.map(e=>e.id);return{transactionId:e.transactionId,results:t,[l?"documentId":"documentIds"]:r}}))}function eY(e,t,r,n,o={}){return eV(e,t,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},o))}function eQ(e,t,r){var n;let o=r.url||r.uri,s=e.config(),i=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&0===o.indexOf("/data/"):r.canUseCdn,a=(r.useCdn??s.useCdn)&&i,u=r.tag&&s.requestTagPrefix?[s.requestTagPrefix,r.tag].join("."):r.tag||s.requestTagPrefix;if(u&&null!==r.tag&&(r.query={tag:eb(u),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&0===o.indexOf("/data/query/")){let e=r.resultSourceMap??s.resultSourceMap;void 0!==e&&!1!==e&&(r.query={resultSourceMap:e,...r.query});let t=r.perspective||s.perspective;"u">typeof t&&(eO(t),r.query={perspective:Array.isArray(t)?t.join(","):t,...r.query},"previewDrafts"===t&&a&&(a=!1,eE())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let c=function(e,t={}){let r={},n=t.token||e.token;n&&(r.Authorization=`Bearer ${n}`),t.useGlobalApi||e.useProjectHostname||!e.projectId||(r["X-Sanity-Project-ID"]=e.projectId);let o=!!(typeof t.withCredentials>"u"?e.token||e.withCredentials:t.withCredentials),s=typeof t.timeout>"u"?e.timeout:t.timeout;return Object.assign({},t,{headers:Object.assign({},r,t.headers||{}),timeout:typeof s>"u"?3e5:s,proxy:t.proxy||e.proxy,json:!0,withCredentials:o,fetch:"object"==typeof t.fetch&&"object"==typeof e.fetch?{...e.fetch,...t.fetch}:t.fetch||e.fetch})}(s,Object.assign({},r,{url:eZ(e,o,a)})),l=new D.c(e=>t(c,s.requester).subscribe(e));return r.signal?l.pipe((n=r.signal,e=>new D.c(t=>{let r=()=>t.error(function(e){if(e0)return new DOMException(e?.reason??"The operation was aborted.","AbortError");let t=Error(e?.reason??"The operation was aborted.");return t.name="AbortError",t}(n));if(n&&n.aborted)return void r();let o=e.subscribe(t);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),o.unsubscribe()}}))):l}function eW(e,t,r){return eQ(e,t,r).pipe((0,et.p)(e=>"response"===e.type),(0,ee.T)(e=>e.body))}function eK(e,t,r){let n=eg(e.config()),o=`/${t}/${n}`;return`/data${r?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function eZ(e,t,r=!1){let{url:n,cdnUrl:o}=e.config();return`${r?o:n}/${t.replace(/^\//,"")}`}let e0=!!globalThis.DOMException;class e1{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return e9(this.#a,this.#u,e,t,r)}}class e3{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return U(e9(this.#a,this.#u,e,t,r).pipe((0,et.p)(e=>"response"===e.type),(0,ee.T)(e=>e.body.document)))}}function e9(e,t,r,n,o={}){var s,i;if(-1===ed.indexOf(r))throw Error(`Invalid asset type: ${r}. Must be one of ${ed.join(", ")}`);let a=o.extract||void 0;a&&!a.length&&(a=["none"]);let u=eg(e.config()),c="image"===r?"images":"files",l=(s=o,i=n,!(typeof File>"u")&&i instanceof File?Object.assign({filename:!1===s.preserveFilename?void 0:i.name,contentType:i.type},s):s),{tag:d,label:h,title:f,description:p,creditLine:y,filename:m,source:g}=l,b={label:h,title:f,description:p,filename:m,meta:a,creditLine:y};return g&&(b.sourceId=g.id,b.sourceName=g.name,b.sourceUrl=g.url),eQ(e,t,{tag:d,method:"POST",timeout:l.timeout||0,uri:`/assets/${c}/${u}`,headers:l.contentType?{"Content-Type":l.contentType}:{},query:b,body:n})}let e6=["includePreviousRevision","includeResult","includeMutations","visibility","effectFormat","tag"],e2={includeResult:!0};function e5(e,t,n={}){let{url:o,token:s,withCredentials:i,requestTagPrefix:a}=this.config(),u=n.tag&&a?[a,n.tag].join("."):n.tag,c={...Object.keys(e2).concat(Object.keys(n)).reduce((e,t)=>(e[t]=typeof n[t]>"u"?e2[t]:n[t],e),{}),tag:u},l=eN({query:e,params:t,options:{tag:u,...e6.reduce((e,t)=>(typeof c[t]>"u"||(e[t]=c[t]),e),{})}}),d=`${o}${eK(this,"listen",l)}`;if(d.length>14800)return new D.c(e=>e.error(Error("Query too large for listener")));let h=c.events?c.events:["mutation"],f=-1!==h.indexOf("reconnect"),p={};return(s||i)&&(p.withCredentials=!0),s&&(p.headers={Authorization:`Bearer ${s}`}),new D.c(e=>{let t,n,o=!1,s=!1;function i(){o||(f&&e.next({type:"reconnect"}),o||t.readyState!==t.CLOSED||(l(),clearTimeout(n),n=setTimeout(m,100)))}function a(t){e.error(function(e){var t;if(e instanceof Error)return e;let r=e8(e);return r instanceof Error?r:Error((t=r).error?t.error.description?t.error.description:"string"==typeof t.error?t.error:JSON.stringify(t.error,null,2):t.message||"Unknown listener error")}(t))}function u(t){let r=e8(t);return r instanceof Error?e.error(r):e.next(r)}function c(){o=!0,l(),e.complete()}function l(){t&&(t.removeEventListener("error",i),t.removeEventListener("channelError",a),t.removeEventListener("disconnect",c),h.forEach(e=>t.removeEventListener(e,u)),t.close())}async function y(){let{default:e}=await r.e(4406).then(r.t.bind(r,24406,19));if(s)return;let t=new e(d,p);return t.addEventListener("error",i),t.addEventListener("channelError",a),t.addEventListener("disconnect",c),h.forEach(e=>t.addEventListener(e,u)),t}function m(){y().then(e=>{e&&(t=e,s&&l())}).catch(t=>{e.error(t),g()})}function g(){o=!0,l(),s=!0}return m(),g})}function e8(e){try{let t=e.data&&JSON.parse(e.data)||{};return Object.assign({type:e.type},t)}catch(e){return e}}let e4="2021-03-26";class e7{#a;constructor(e){this.#a=e}events({includeDrafts:e=!1,tag:t}={}){let{projectId:n,apiVersion:o,token:s,withCredentials:i,requestTagPrefix:a}=this.#a.config(),u=o.replace(/^v/,"");if("X"!==u&&u<e4)throw Error(`The live events API requires API version ${e4} or later. The current API version is ${u}. Please update your API version to use this feature.`);if(e&&!s&&!i)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");if(e&&"X"!==u)throw Error("The live events API requires API version X when 'includeDrafts: true'. This API is experimental and may change or even be removed.");let c=eK(this.#a,"live/events"),l=new URL(this.#a.getUrl(c,!1)),d=t&&a?[a,t].join("."):t;d&&l.searchParams.set("tag",d),e&&l.searchParams.set("includeDrafts","true");let h=["restart","message","welcome","reconnect"],f={};return e&&s&&(f.headers={Authorization:`Bearer ${s}`}),e&&i&&(f.withCredentials=!0),new D.c(e=>{let t,o,s=!1,i=!1;function a(r){if(!s){if("data"in r){let t=te(r);e.error(Error(t.message,{cause:t}))}t.readyState===t.CLOSED&&(c(),clearTimeout(o),o=setTimeout(p,100))}}function u(t){let r=te(t);return r instanceof Error?e.error(r):e.next(r)}function c(){if(t){for(let e of(t.removeEventListener("error",a),h))t.removeEventListener(e,u);t.close()}}async function d(){let e=typeof EventSource>"u"||f.headers||f.withCredentials?(await r.e(4406).then(r.t.bind(r,24406,19))).default:EventSource;if(i)return;try{if(await fetch(l,{method:"OPTIONS",mode:"cors",credentials:f.withCredentials?"include":"omit",headers:f.headers}),i)return}catch{throw new ei({projectId:n})}let t=new e(l.toString(),f);for(let e of(t.addEventListener("error",a),h))t.addEventListener(e,u);return t}function p(){d().then(e=>{e&&(t=e,i&&c())}).catch(t=>{e.error(t),y()})}function y(){s=!0,c(),i=!0}return p(),y})}}function te(e){try{let t=e.data&&JSON.parse(e.data)||{};return{type:e.type,id:e.lastEventId,...t}}catch(e){return e}}class tt{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return tn(this.#a,this.#u,"PUT",e,t)}edit(e,t){return tn(this.#a,this.#u,"PATCH",e,t)}delete(e){return tn(this.#a,this.#u,"DELETE",e)}list(){return eW(this.#a,this.#u,{uri:"/datasets",tag:null})}}class tr{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return U(tn(this.#a,this.#u,"PUT",e,t))}edit(e,t){return U(tn(this.#a,this.#u,"PATCH",e,t))}delete(e){return U(tn(this.#a,this.#u,"DELETE",e))}list(){return U(eW(this.#a,this.#u,{uri:"/datasets",tag:null}))}}function tn(e,t,r,n,o){return ef(n),eW(e,t,{method:r,uri:`/datasets/${n}`,body:o,tag:null})}class to{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return eW(this.#a,this.#u,{uri:t})}getById(e){return eW(this.#a,this.#u,{uri:`/projects/${e}`})}}class ts{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return U(eW(this.#a,this.#u,{uri:t}))}getById(e){return U(eW(this.#a,this.#u,{uri:`/projects/${e}`}))}}class ti{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return eW(this.#a,this.#u,{uri:`/users/${e}`})}}class ta{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return U(eW(this.#a,this.#u,{uri:`/users/${e}`}))}}class tu{assets;datasets;live;projects;users;#c;#u;listen=e5;constructor(e,t=ej){this.config(t),this.#u=e,this.assets=new e1(this,this.#u),this.datasets=new tt(this,this.#u),this.live=new e7(this),this.projects=new to(this,this.#u),this.users=new ti(this,this.#u)}clone(){return new tu(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#c=eq(e,this.#c||{}),this}withConfig(e){let t=this.config();return new tu(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return eL(this,this.#u,this.#c.stega,e,t,r)}getDocument(e,t){return eU(this,this.#u,e,t)}getDocuments(e,t){return ez(this,this.#u,e,t)}create(e,t){return eY(this,this.#u,e,"create",t)}createIfNotExists(e,t){return eH(this,this.#u,e,t)}createOrReplace(e,t){return eB(this,this.#u,e,t)}delete(e,t){return eJ(this,this.#u,e,t)}mutate(e,t){return eX(this,this.#u,e,t)}patch(e,t){return new eI(e,t,this)}transaction(e){return new eF(e,this)}action(e,t){return eG(this,this.#u,e,t)}request(e){return eW(this,this.#u,e)}getUrl(e,t){return eZ(this,e,t)}getDataUrl(e,t){return eK(this,e,t)}}class tc{assets;datasets;live;projects;users;observable;#c;#u;listen=e5;constructor(e,t=ej){this.config(t),this.#u=e,this.assets=new e3(this,this.#u),this.datasets=new tr(this,this.#u),this.live=new e7(this),this.projects=new ts(this,this.#u),this.users=new ta(this,this.#u),this.observable=new tu(e,t)}clone(){return new tc(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),this.#c=eq(e,this.#c||{}),this}withConfig(e){let t=this.config();return new tc(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return U(eL(this,this.#u,this.#c.stega,e,t,r))}getDocument(e,t){return U(eU(this,this.#u,e,t))}getDocuments(e,t){return U(ez(this,this.#u,e,t))}create(e,t){return U(eY(this,this.#u,e,"create",t))}createIfNotExists(e,t){return U(eH(this,this.#u,e,t))}createOrReplace(e,t){return U(eB(this,this.#u,e,t))}delete(e,t){return U(eJ(this,this.#u,e,t))}mutate(e,t){return U(eX(this,this.#u,e,t))}patch(e,t){return new e$(e,t,this)}transaction(e){return new ek(e,this)}action(e,t){return U(eG(this,this.#u,e,t))}request(e){return U(eW(this,this.#u,e))}dataRequest(e,t,r){return U(eV(this,this.#u,e,t,r))}getUrl(e,t){return eZ(this,e,t)}getDataUrl(e,t){return eK(this,e,t)}}let tl=function(e,t){let r=((e=[],t=b)=>(function e(t,r){let n=[],o=d.reduce((e,t)=>(e[t]=e[t]||[],e),{processOptions:[i],validateOptions:[c]});function s(e){let t,n=l.reduce((e,t)=>(e[t]=function(){let e=Object.create(null),t=0;return{publish:function(t){for(let r in e)e[r](t)},subscribe:function(r){let n=t++;return e[n]=r,function(){delete e[n]}}}}(),e),{}),s=function(e,t,...r){let n="onError"===e,s=t;for(let t=0;t<o[e].length&&(s=(0,o[e][t])(s,...r),!n||s);t++);return s},i=s("processOptions",e);s("validateOptions",i);let a={options:i,channels:n,applyMiddleware:s},u=n.request.subscribe(e=>{t=r(e,(t,r)=>((e,t,r)=>{let o=e,i=t;if(!o)try{i=s("onResponse",t,r)}catch(e){i=null,o=e}(o=o&&s("onError",o,r))?n.error.publish(o):i&&n.response.publish(i)})(t,r,e))});n.abort.subscribe(()=>{u(),t&&t.abort()});let c=s("onReturn",n,a);return c===n&&n.request.publish(a),c}return s.use=function(e){if(!e)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof e)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(e.onReturn&&o.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return d.forEach(t=>{e[t]&&o[t].push(e[t])}),n.push(e),s},s.clone=()=>e(n,r),t.forEach(s.use),s})(e,t))([N({shouldRetry:ec}),...e,eu,{processOptions:e=>{let t=e.body;return!t||"function"==typeof t.pipe||O(t)||-1===S.indexOf(typeof t)&&!Array.isArray(t)&&!function(e){if(!1===q(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!1!==q(r)&&!1!==r.hasOwnProperty("isPrototypeOf")}(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{"Content-Type":"application/json"})})}},{onResponse:e=>{let t=e.headers["content-type"]||"",r=-1!==t.indexOf("application/json");return e.body&&t&&r?Object.assign({},e,{body:function(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}(e.body)}):e},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:"application/json"},e.headers)})},{onRequest:e=>{if("xhr"!==e.adapter)return;let t=e.request,r=e.context;function n(e){return t=>{let n=t.lengthComputable?t.loaded/t.total*100:-1;r.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}"upload"in t&&"onprogress"in t.upload&&(t.upload.onprogress=n("upload")),"onprogress"in t&&(t.onprogress=n("download"))}},ea,function(e={}){let t=e.implementation||I.Observable;if(!t)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(e,r)=>new t(t=>(e.error.subscribe(e=>t.error(e)),e.progress.subscribe(e=>t.next(Object.assign({type:"progress"},e))),e.response.subscribe(e=>{t.next(Object.assign({type:"response"},e)),t.complete()}),e.request.publish(r),()=>e.abort.publish()))}}({implementation:D.c})]);return{requester:r,createClient:e=>new t((t,n)=>(n||r)({maxRedirects:0,maxRetries:e.maxRetries,retryDelay:e.retryDelay,...t}),e)}}([],tc),td=(tl.requester,tl.createClient);var th=r(92862),tf=r(55028),tp=r(35695),ty=r(12115),tm=r(46295),tg=r(2981);let tb=(0,tf.default)(()=>Promise.all([r.e(2770),r.e(4203)]).then(r.bind(r,84203)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/PresentationComlink.js")]},ssr:!1}),tv=(0,tf.default)(()=>r.e(8513).then(r.bind(r,58513)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnMount.js")]},ssr:!1}),tw=(0,tf.default)(()=>r.e(6376).then(r.bind(r,26376)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnFocus.js")]},ssr:!1}),tC=(0,tf.default)(()=>r.e(2977).then(r.bind(r,32977)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnReconnect.js")]},ssr:!1}),tE=()=>window!==window.parent||!!window.opener,tx=e=>{var t;e instanceof ei?console.warn("Sanity Live is unable to connect to the Sanity API as the current origin - ".concat(window.origin," - is not in the list of allowed CORS origins for this Sanity Project."),e.addOriginUrl&&"Add it here:",null==(t=e.addOriginUrl)?void 0:t.toString()):console.error(e)};function tR(e){let{projectId:t,dataset:r,apiHost:o,apiVersion:s,useProjectHostname:i,token:a,requestTagPrefix:u,draftModeEnabled:c,draftModePerspective:l,refreshOnMount:d=!1,refreshOnFocus:h=!c&&(typeof window>"u"||window.self===window.top),refreshOnReconnect:f=!0,tag:p,onError:y=tx}=e,m=(0,ty.useMemo)(()=>td({projectId:t,dataset:r,apiHost:o,apiVersion:s,useProjectHostname:i,ignoreBrowserTokenWarning:!0,token:a,useCdn:!1,requestTagPrefix:u}),[o,s,r,t,u,a,i]),g=(0,tp.useRouter)(),b=(0,tm.J)(e=>{"message"===e.type?(0,th.Q)(e.tags):"restart"===e.type&&g.refresh()});(0,ty.useEffect)(()=>{let e=m.live.events({includeDrafts:!!a,tag:p}).subscribe({next:e=>{("message"===e.type||"restart"===e.type||"welcome"===e.type)&&b(e)},error:e=>{y(e)}});return()=>e.unsubscribe()},[m.live,b,y,p,a]),(0,ty.useEffect)(()=>{c&&l?(0,tg.NL)(l):(0,tg.NL)("unknown")},[c,l]);let[v,w]=(0,ty.useState)(!1);(0,ty.useEffect)(()=>{if(!tE()){if(c&&a)return void(0,tg.kN)("live");if(c)return void(0,tg.kN)("static");(0,tg.kN)("unknown")}},[c,a]),(0,ty.useEffect)(()=>{if(!tE())return;let e=new AbortController,t=setTimeout(()=>(0,tg.kN)("live"),3e3);return window.addEventListener("message",r=>{let{data:n}=r;n&&"object"==typeof n&&"domain"in n&&"sanity/channels"===n.domain&&"from"in n&&"presentation"===n.from&&(clearTimeout(t),(0,tg.kN)(window.opener?"presentation-window":"presentation-iframe"),w(!0),e.abort())},{signal:e.signal}),()=>{clearTimeout(t),e.abort()}},[]);let C=(0,ty.useRef)(void 0);return(0,ty.useEffect)(()=>{if(c)return clearTimeout(C.current),()=>{C.current=setTimeout(()=>{console.warn("Sanity Live: Draft mode was enabled, but is now being disabled")})}},[c]),(0,n.jsxs)(n.Fragment,{children:[c&&v&&(0,n.jsx)(tb,{draftModeEnabled:c,draftModePerspective:l}),!c&&d&&(0,n.jsx)(tv,{}),!c&&h&&(0,n.jsx)(tw,{}),!c&&f&&(0,n.jsx)(tC,{})]})}tR.displayName="SanityLive"},49930:(e,t,r)=>{r.d(t,{x:()=>s});var n=r(16234),o=r(91208);function s(e){return(0,o.T)(null==e?void 0:e[n.l])}},50793:(e,t,r)=>{r.d(t,{T:()=>s});var n=r(1125),o=r(98175);function s(e,t){return(0,n.N)(function(r,n){var s=0;r.subscribe((0,o._)(n,function(r){n.next(e.call(t,r,s++))}))})}},55028:(e,t,r)=>{r.d(t,{default:()=>o.a});var n=r(36645),o=r.n(n)},56534:(e,t,r)=>{r.d(t,{X:()=>n});var n=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},57173:(e,t,r)=>{r.d(t,{Tg:()=>y});var n=r(39249),o=r(56534),s=r(27745),i=r(94354),a=r(82388),u=r(67606),c=r(920),l=r(49930),d=r(39839),h=r(91208),f=r(58197),p=r(38541);function y(e){if(e instanceof i.c)return e;if(null!=e){var t,r,y,g,b;if((0,a.l)(e)){return t=e,new i.c(function(e){var r=t[p.s]();if((0,h.T)(r.subscribe))return r.subscribe(e);throw TypeError("Provided object does not correctly implement Symbol.observable")})}if((0,o.X)(e)){return r=e,new i.c(function(e){for(var t=0;t<r.length&&!e.closed;t++)e.next(r[t]);e.complete()})}if((0,s.y)(e)){return y=e,new i.c(function(e){y.then(function(t){e.closed||(e.next(t),e.complete())},function(t){return e.error(t)}).then(null,f.m)})}if((0,u.T)(e))return m(e);if((0,l.x)(e)){return g=e,new i.c(function(e){var t,r;try{for(var o=(0,n.Ju)(g),s=o.next();!s.done;s=o.next()){var i=s.value;if(e.next(i),e.closed)return}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}e.complete()})}if((0,d.U)(e)){return b=e,m((0,d.C)(b))}}throw(0,c.L)(e)}function m(e){return new i.c(function(t){(function(e,t){var r,o,s,i;return(0,n.sH)(this,void 0,void 0,function(){var a;return(0,n.YH)(this,function(u){switch(u.label){case 0:u.trys.push([0,5,6,11]),r=(0,n.xN)(e),u.label=1;case 1:return[4,r.next()];case 2:if((o=u.sent()).done)return[3,4];if(a=o.value,t.next(a),t.closed)return[2];u.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return s={error:u.sent()},[3,11];case 6:if(u.trys.push([6,,9,10]),!(o&&!o.done&&(i=r.return)))return[3,8];return[4,i.call(r)];case 7:u.sent(),u.label=8;case 8:return[3,10];case 9:if(s)throw s.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})})(e,t).catch(function(e){return t.error(e)})})}},58197:(e,t,r)=>{r.d(t,{m:()=>s});var n=r(80329),o=r(68473);function s(e){o.f.setTimeout(function(){var t=n.$.onUnhandledError;if(t)t(e);else throw e})}},62146:(e,t,r)=>{function n(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),r(45262)},64054:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return s},createSnapshot:function(){return a}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let o="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function s(){return o?new o:new n}function i(e){return o?o.bind(e):n.bind(e)}function a(){return o?o.snapshot():function(e,...t){return e(...t)}}},67357:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(95155),o=r(12115),s=r(62146);function i(e){return{default:e&&"default"in e?e.default:e}}r(10255);let a={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},r=(0,o.lazy)(()=>t.loader().then(i)),u=t.loading;function c(e){let i=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=!t.ssr||!!t.loading,c=a?o.Suspense:o.Fragment,l=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(s.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(c,{...a?{fallback:i}:{},children:l})}return c.displayName="LoadableComponent",c}},67606:(e,t,r)=>{r.d(t,{T:()=>o});var n=r(91208);function o(e){return Symbol.asyncIterator&&(0,n.T)(null==e?void 0:e[Symbol.asyncIterator])}},68066:(e,t,r)=>{r.d(t,{L:()=>n});function n(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}},68473:(e,t,r)=>{r.d(t,{f:()=>o});var n=r(39249),o={setTimeout:function(e,t){for(var r=[],s=2;s<arguments.length;s++)r[s-2]=arguments[s];var i=o.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,(0,n.fX)([e,t],(0,n.zs)(r))):setTimeout.apply(void 0,(0,n.fX)([e,t],(0,n.zs)(r)))},clearTimeout:function(e){var t=o.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0}},69647:(e,t,r)=>{r.d(t,{p:()=>s});var n=r(1125),o=r(98175);function s(e,t){return(0,n.N)(function(r,n){var s=0;r.subscribe((0,o._)(n,function(r){return e.call(t,r,s++)&&n.next(r)}))})}},73817:(e,t,r)=>{r.d(t,{Kn:()=>u,yU:()=>a,Uv:()=>c});var n=r(39249),o=r(91208),s=(0,r(68066).L)(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}),i=r(575),a=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,i,a,u=this._parentage;if(u)if(this._parentage=null,Array.isArray(u))try{for(var c=(0,n.Ju)(u),d=c.next();!d.done;d=c.next())d.value.remove(this)}catch(t){e={error:t}}finally{try{d&&!d.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}else u.remove(this);var h=this.initialTeardown;if((0,o.T)(h))try{h()}catch(e){a=e instanceof s?e.errors:[e]}var f=this._finalizers;if(f){this._finalizers=null;try{for(var p=(0,n.Ju)(f),y=p.next();!y.done;y=p.next()){var m=y.value;try{l(m)}catch(e){a=null!=a?a:[],e instanceof s?a=(0,n.fX)((0,n.fX)([],(0,n.zs)(a)),(0,n.zs)(e.errors)):a.push(e)}}}catch(e){r={error:e}}finally{try{y&&!y.done&&(i=p.return)&&i.call(p)}finally{if(r)throw r.error}}}if(a)throw new s(a)}},t.prototype.add=function(e){var r;if(e&&e!==this)if(this.closed)l(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!=(r=this._finalizers)?r:[]).push(e)}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&(0,i.o)(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&(0,i.o)(r,e),e instanceof t&&e._removeParent(this)},(e=new t).closed=!0,t.EMPTY=e,t}(),u=a.EMPTY;function c(e){return e instanceof a||e&&"closed"in e&&(0,o.T)(e.remove)&&(0,o.T)(e.add)&&(0,o.T)(e.unsubscribe)}function l(e){(0,o.T)(e)?e():e.unsubscribe()}},74962:(e,t,r)=>{r.d(t,{N:()=>n});function n(e,t,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var s=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(s),!o)return s}},77020:(e,t,r)=>{r.d(t,{l:()=>n});function n(){}},78830:(e,t,r)=>{r.d(t,{default:()=>i});var n=r(26038),o=r(12115),s=r(61787);function i(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return o.createElement(s.IntlProvider,(0,n._)({locale:t},r))}},78906:(e,t,r)=>{r.d(t,{D:()=>n});function n(e){return e}},80329:(e,t,r)=>{r.d(t,{$:()=>n});var n={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},82388:(e,t,r)=>{r.d(t,{l:()=>s});var n=r(38541),o=r(91208);function s(e){return(0,o.T)(e[n.s])}},85744:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=r(17828)},88332:(e,t,r)=>{r.d(t,{R0:()=>a,ms:()=>s,lI:()=>i});var n=r(91208);function o(e){return e[e.length-1]}function s(e){return(0,n.T)(o(e))?e.pop():void 0}function i(e){var t;return(t=o(e))&&(0,n.T)(t.schedule)?e.pop():void 0}function a(e,t){return"number"==typeof o(e)?e.pop():t}},88580:(e,t,r)=>{r.d(t,{F:()=>o,m:()=>s});var n=r(78906);function o(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return s(e)}function s(e){return 0===e.length?n.D:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}},91208:(e,t,r)=>{r.d(t,{T:()=>n});function n(e){return"function"==typeof e}},91501:(e,t,r)=>{r.d(t,{C:()=>i,Q:()=>c});var n={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},o={0:8203,1:8204,2:8205,3:65279},s=[,,,,].fill(String.fromCodePoint(o[0])).join("");function i(e,t,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(e))||/[a-z]/i.test(e)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(e))&&Date.parse(e)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(e))?e:`${e}${n=JSON.stringify(t),`${s}${Array.from(n).map(e=>{let t=e.charCodeAt(0);if(t>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${e} (${t})`);return Array.from(t.toString(4).padStart(4,"0")).map(e=>String.fromCodePoint(o[e])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(o).map(e=>e.reverse())),Object.fromEntries(Object.entries(n).map(e=>e.reverse()));var a=`${Object.values(n).map(e=>`\\u{${e.toString(16)}}`).join("")}`,u=RegExp(`[${a}]{4,}`,"gu");function c(e){var t,r;return e&&JSON.parse({cleaned:(t=JSON.stringify(e)).replace(u,""),encoded:(null==(r=t.match(u))?void 0:r[0])||""}.cleaned)}},94354:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(27110),o=r(73817),s=r(38541),i=r(88580),a=r(80329),u=r(91208),c=r(39833),l=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var s=this,i=!function(e){return e&&e instanceof n.vU||e&&(0,u.T)(e.next)&&(0,u.T)(e.error)&&(0,u.T)(e.complete)&&(0,o.Uv)(e)}(e)?new n.Ms(e,t,r):e;return(0,c.Y)(function(){var e=s.operator,t=s.source;i.add(e?e.call(i,t):t?s._subscribe(i):s._trySubscribe(i))}),i},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=d(t))(function(t,o){var s=new n.Ms({next:function(t){try{e(t)}catch(e){o(e),s.unsubscribe()}},error:o,complete:t});r.subscribe(s)})},e.prototype._subscribe=function(e){var t;return null==(t=this.source)?void 0:t.subscribe(e)},e.prototype[s.s]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,i.m)(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=d(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function d(e){var t;return null!=(t=null!=e?e:a.$.Promise)?t:Promise}},98175:(e,t,r)=>{r.d(t,{_:()=>o});var n=r(39249);function o(e,t,r,n,o){return new s(e,t,r,n,o)}var s=function(e){function t(t,r,n,o,s,i){var a=e.call(this,t)||this;return a.onFinalize=s,a.shouldUnsubscribe=i,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return(0,n.C6)(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null==(t=this.onFinalize)||t.call(this)}},t}(r(27110).vU)}}]);