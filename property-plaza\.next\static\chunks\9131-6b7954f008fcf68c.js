"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9131],{1702:(e,t,s)=>{s.d(t,{M:()=>i});var a=s(88693),r=s(46786),n=s(57383);let i=(0,a.vt)()((0,r.Zr)(e=>({currency:"IDR",setCurrency:t=>e({currency:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t})}),{name:"seekers-settings",storage:{getItem:e=>{let t=n.A.get(e);return t?JSON.parse(t):void 0},setItem:(e,t)=>{n.A.set(e,JSON.stringify(t),{expires:7,path:"/"})},removeItem:e=>{n.A.remove(e)}},onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}))},3157:(e,t,s)=>{},12673:(e,t,s)=>{s.d(t,{cQ:()=>n,lL:()=>o,vN:()=>l,wJ:()=>i,x_:()=>r});var a=s(74810);function r(e){return a.U$.free.includes(e)?a.U$.free:a.U$.finder.includes(e)?a.U$.finder:a.U$.archiver.includes(e)?a.U$.archiver:a.U$.free}function n(e){return e==a.U$.free?0:e==a.U$.finder?5:10*(e==a.U$.archiver)}let i=10,l={max:13,min:10};function o(e){return e==a.U$.free?l:e==a.U$.finder?{max:14,min:i}:e==a.U$.archiver?{max:15,min:i}:l}},13423:(e,t,s)=>{s.d(t,{BE:()=>m,I6:()=>g,Uz:()=>o,_s:()=>l,gk:()=>p,tb:()=>f,zj:()=>u});var a=s(95155),r=s(12115),n=s(69474),i=s(53999);let l=e=>{let{shouldScaleBackground:t=!0,...s}=e;return(0,a.jsx)(n._.Root,{shouldScaleBackground:t,...s})};l.displayName="Drawer";let o=n._.Trigger,c=n._.Portal;n._.Close;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n._.Overlay,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80",s),...r})});d.displayName=n._.Overlay.displayName;let u=r.forwardRef((e,t)=>{let{className:s,children:r,...l}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(d,{}),(0,a.jsxs)(n._.Content,{ref:t,className:(0,i.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",s),...l,children:[(0,a.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),r]})]})});u.displayName="DrawerContent";let m=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("grid gap-1.5 p-4 text-center sm:text-left",t),...s})};m.displayName="DrawerHeader";let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,i.cn)("mt-auto flex flex-col gap-2 p-4",t),...s})};f.displayName="DrawerFooter";let p=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n._.Title,{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",s),...r})});p.displayName=n._.Title.displayName;let g=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n._.Description,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",s),...r})});g.displayName=n._.Description.displayName},14570:(e,t,s)=>{s.d(t,{N$:()=>d,Q0:()=>u,Uu:()=>c,_k:()=>f,aH:()=>o,bH:()=>l,eD:()=>i,iD:()=>r,ri:()=>n,zp:()=>m});var a=s(99493);let r=(e,t)=>a.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),n=()=>a.apiClient.post("auth/logout"),i=e=>a.apiClient.post("notifications/email",e),l=e=>a.apiClient.post("auth/otp-verification",e),o=e=>a.apiClient.post("auth/forgot-password",e),c=e=>a.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),d=e=>a.apiClient.post("auth/reset-password",e),u=(e,t)=>a.apiClient.post("auth/create-password",e,t),m=e=>a.apiClient.post("users/security",e),f=e=>a.apiClient.post("auth/totp-verification",e)},14666:(e,t,s)=>{s.d(t,{Dg:()=>r,Dj:()=>m,EM:()=>l,FN:()=>f,Ix:()=>g,Nr:()=>c,Xh:()=>a,Zu:()=>o,bV:()=>u,gF:()=>i,kj:()=>d,s7:()=>p,wz:()=>n});let a="tkn",r="SEEKER",n=8,i=1,l=30,o=300,c=10,d="cookies-collection-status",u="necessary-cookies-collection-status",m="functional-cookies-collection-status",f="analytic-cookies-collection-status",p="marketing-cookies-collection-status",g={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},18618:(e,t,s)=>{s.d(t,{N$:()=>a.N$,Q0:()=>a.Q0,Uu:()=>a.Uu,_k:()=>a._k,aH:()=>a.aH,bH:()=>a.bH,eD:()=>a.eD,iD:()=>a.iD,ri:()=>a.ri,zp:()=>a.zp});var a=s(14570)},19937:(e,t,s)=>{s.d(t,{a:()=>n});var a=s(27043),r=s(55594);function n(){let e=(0,a.useTranslations)("seeker");return r.z.object({otp:r.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.otp")})}).min(5,{message:e("form.utility.enterValidField",{field:e("form.field.otp")})})})}},21780:(e,t,s)=>{s.d(t,{default:()=>W});var a=s(95155),r=s(22190),n=s(27043),i=s(12115),l=s(72236),o=s(37996),c=s(90221),d=s(30070),u=s(62177),m=s(29653),f=s(97168),p=s(14666),g=s(55594),h=s(5041),x=s(18618),v=s(53580),w=s(57383),y=s(74463),N=s(93846),b=s(9691),j=s(53999),A=s(81251),k=s(29911),E=s(83029);function _(){let e=(0,h.n)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/google?origin=DEFAULT"}}),t=(0,h.n)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/facebook?origin=DEFAULT"}}),s=async()=>{await e.mutateAsync()},r=async()=>{await t.mutateAsync()};return(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)(f.$,{className:"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 w-full rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:s,type:"button",children:[(0,a.jsx)(E.F4b,{className:"mr-2 h-4 w-4"}),"Google"]}),(0,a.jsxs)(f.$,{className:"inline-flex w-full items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:r,type:"button",children:[(0,a.jsx)(k.iYk,{className:"mr-2 h-4 w-4"}),"Facebook"]})]})}var S=s(76037);function C(e){let{isDialog:t,onClickSignUp:s,onClickResetPassword:r}=e,i=(0,n.useTranslations)("seeker"),l=function(){let e=(0,n.useTranslations)("seeker");return g.z.object({contact:g.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:g.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(p.wz,{message:e("form.utility.minimumLength",{field:e("form.field.password"),length:p.wz})})})}(),o=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"seekers",t=(0,n.useTranslations)("universal"),{toast:s}=(0,v.dj)(),{executeRecaptcha:a}=(0,b.lw)();return(0,h.n)({mutationFn:async e=>{try{let t=await a("form_submit");return(0,x.iD)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:async s=>{let a=s.data,r=await (0,N.i8)({headers:{Authorization:"Bearer ".concat(a.data.access_token)}}),n=r.type;if(!r)throw Error(t("misc.userNotFound"));if("owner"===e||"middleman"==e){if("SEEKER"==n)throw Error(t("misc.userNotFound"));if(w.A.set(p.Xh,a.data.access_token,{expires:7}),"OWNER"==n)return window.location.assign(y.dA);if("MIDDLEMAN"==n)return window.location.assign(y.yA)}else{if("OWNER"==r.type||"MIDDLEMAN"==r.type)throw Error(t("misc.userNotFound"));w.A.set(p.Xh,a.data.access_token,{expires:7}),window.location.reload()}},onError:e=>{var a;let r=null==(a=e.response)?void 0:a.data;w.A.remove(p.Xh),s({title:t("misc.foundError"),description:(null==r?void 0:r.message)||"",variant:"destructive"})}})}("seekers"),{toast:k}=(0,v.dj)(),E=(0,u.mN)({resolver:(0,c.u)(l),defaultValues:{contact:"",password:""}});async function C(e){let t=(0,j.gT)(e.contact.replaceAll(/\s+/g,"")),s={username:e.contact.trim(),password:e.password,login_with:t?"DEFAULT":"PHONE_NUMBER"};try{await o.mutateAsync(s)}catch(e){var a;k({title:i("error.failedLogin.title"),description:(null==e||null==(a=e.response)?void 0:a.data.message)||"",variant:"destructive"})}}return(0,a.jsx)("div",{className:"grid gap-4",children:(0,a.jsx)(d.lV,{...E,children:(0,a.jsxs)("form",{onSubmit:E.handleSubmit(C),className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsx)(m.A,{form:E,label:i("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,a.jsx)(A.A,{form:E,label:i("form.label.password"),name:"password",placeholder:"",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,a.jsxs)("div",{className:"text-xs text-neutral space-x-1 -mt-5",children:[(0,a.jsx)("span",{className:"ml-3",children:i("form.utility.forgotField",{field:i("form.field.password")})}),(0,a.jsx)(f.$,{variant:"link",type:"button",onClick:r,className:"p-0 text-seekers-primary font-medium hover:underline text-xs",children:i("form.utility.resetField",{field:i("form.field.password")})})]})]}),(0,a.jsx)(f.$,{className:"w-full",variant:"default-seekers",loading:o.isPending,children:i("cta.login")}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[i("auth.createAccount")," ",(0,a.jsx)(f.$,{variant:"link",onClick:s,className:"p-0 h-9 text-seekers-primary hover:underline",children:i("cta.createAccount")})]})}),(0,a.jsxs)("div",{className:"relative my-6",children:[(0,a.jsx)(S.Separator,{}),(0,a.jsxs)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:[i("conjuntion.or")," ",i("misc.continueWith")]})]}),(0,a.jsx)(_,{})]})})})}var F=s(72337),R=s(51315),U=s(87536),T=s(82940),z=s.n(T),D=s(5196),I=s(54416);let P=e=>{let{onClickLogin:t,onSuccess:s}=e,r=(0,n.useTranslations)("seeker"),l=(0,F.Y)(),{setRegister:o,setValidFormUntil:g,register:h}=(0,R.k)(),[x,w]=(0,i.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),{toast:y}=(0,v.dj)(),N=(0,U.h)(e=>{var t,a;(null==e||null==(a=e.response)||null==(t=a.data)?void 0:t.message)!=="Email verification code is already sent. Please check your email"&&y({title:r("success.sendVerification.title")+" "+h.email}),s()}),b=(0,u.mN)({resolver:(0,c.u)(l),defaultValues:{confirmPassword:h.confirm_password||"",contact:h.email||"",firstName:h.first_name||"",lastName:h.last_name||"",password:h.password||""}}),k=b.watch("password");async function E(e){let t=z()().add(30,"minutes"),s={email:e.contact||"",password:e.password,confirm_password:e.confirmPassword,first_name:e.firstName,last_name:e.lastName,type:p.Dg,otp:"00000"};o(s),g(t);try{await N.mutateAsync({email:s.email,category:"REGISTRATION"})}catch(e){var a,n,i,l;if((null==e||null==(n=e.response)||null==(a=n.data)?void 0:a.message)=="Email verification code is already sent. Please check your email")return;y({title:r("message.otpRequest.failedToast.title"),description:(null==e||null==(l=e.response)||null==(i=l.data)?void 0:i.message)||"",variant:"destructive"})}}return(0,i.useEffect)(()=>{k&&w({length:k.length>=8,number:/[0-9]/.test(k),special:/[!@#$%^&*()_+]/.test(k),notCommon:!["123456","password","qwerty"].includes(k.toLowerCase()),uppercase:/[A-Z]/.test(k),lowercase:/[a-z]/.test(k)})},[k]),(0,a.jsx)(d.lV,{...b,children:(0,a.jsxs)("form",{onSubmit:b.handleSubmit(E),className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(m.A,{form:b,label:r("form.label.firstName"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,a.jsx)(m.A,{form:b,label:r("form.label.lastName"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),(0,a.jsx)(m.A,{form:b,label:r("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsx)(A.A,{form:b,name:"password",variant:"float",label:r("form.label.password"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,a.jsx)(A.A,{form:b,name:"confirmPassword",variant:"float",label:r("form.label.confirmPassword"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"})]}),k&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,a.jsxs)("div",{className:(0,j.cn)(x.length?"text-green-500":"text-red-500"),children:[x.length?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.minimumLength")]}),(0,a.jsxs)("div",{className:(0,j.cn)(x.number?"text-green-500":"text-red-500"),children:[x.number?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.numberRequired")]}),(0,a.jsxs)("div",{className:(0,j.cn)(x.special?"text-green-500":"text-red-500"),children:[x.special?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.specialCharacter")]}),(0,a.jsxs)("div",{className:(0,j.cn)(x.notCommon?"text-green-500":"text-red-500"),children:[x.notCommon?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.notCommonWord")]}),(0,a.jsxs)("div",{className:(0,j.cn)(x.uppercase?"text-green-500":"text-red-500"),children:[x.uppercase?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.uppercaseRequired")]}),(0,a.jsxs)("div",{className:(0,j.cn)(x.lowercase?"text-green-500":"text-red-500"),children:[x.lowercase?(0,a.jsx)(D.A,{className:"inline w-3 h-3 mr-1"}):(0,a.jsx)(I.A,{className:"inline w-3 h-3 mr-1"}),r("form.utility.password.lowercaseRequired")]})]})]}),(0,a.jsx)(f.$,{className:"w-full",variant:"default-seekers",loading:N.isPending,children:r("cta.createAccount")}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[r("auth.alreadyHaveAccount")," ",(0,a.jsx)(f.$,{variant:"link",onClick:t,className:"p-0 h-9 text-seekers-primary hover:underline",children:r("cta.login")})]})}),(0,a.jsxs)("div",{className:"relative my-6",children:[(0,a.jsx)(S.Separator,{}),(0,a.jsx)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:r("conjuntion.or")})]}),(0,a.jsx)(_,{})]})})};var L=s(42355),O=s(1184),$=s(28679),V=s(32065),M=s(67943),q=s(69916),B=s(19937);function H(){let{register:e,reset:t,setSuccessSignUp:s}=(0,R.k)(),{toast:r}=(0,v.dj)(),l=(0,n.useTranslations)("seeker"),o=(0,B.a)(),m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"owner",{toast:s}=(0,v.dj)(),a=(0,n.useTranslations)("universal"),{executeRecaptcha:r}=(0,b.lw)();return(0,h.n)({mutationFn:async e=>{try{let t=await r("form_submit");return(0,$.DY)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:s=>{let a=s.data;w.A.set(p.Xh,a.data.access_token,{expires:7}),null==e||e(),"owner"==t?window.location.assign(y.dA):window.location.reload()},onError:e=>{let t=e.response.data;s({title:a("misc.foundError"),description:t.message,variant:"destructive"})}})}(()=>t(),"seekers"),g=(0,V.m)(async()=>{try{await m.mutateAsync({...e,otp:N.getValues("otp"),register_with:"EMAIL"}),s(!0)}catch(e){}}),x=(0,U.h)(t=>{if("Email verification code is already sent. Please check your email"===t.response.data.message)return void r({title:l("message.otpRequest.failedToast.title"),description:t.response.data.message||"",variant:"destructive"});r({title:l("success.sendVerification.title")+" "+e.email})}),N=(0,u.mN)({resolver:(0,c.u)(o),defaultValues:{otp:""}});async function j(t){let s={otp:t.otp,requested_by:e.email||"",type:"EMAIL"};try{await g.mutateAsync(s)}catch(e){r({title:l("error.signUp.title"),description:e.response.data.message,variant:"destructive"})}}async function A(){x.mutate({email:e.email,category:"REGISTRATION"})}return(0,i.useEffect)(()=>{let e=N.getValues("otp").length,t=document.getElementById("otp-button");e>=5&&(null==t||t.click())},[N.getValues("otp")]),(0,a.jsx)(d.lV,{...N,children:(0,a.jsxs)("form",{onSubmit:N.handleSubmit(j),className:"space-y-8",children:[(0,a.jsx)(d.zB,{control:N.control,name:"otp",render:e=>{let{field:t}=e;return(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(M.A,{label:"",children:(0,a.jsx)(q.UV,{maxLength:5,...t,pattern:O.UO,required:!0,containerClassName:"flex justify-center max-sm:justify-between",children:(0,a.jsx)(q.NV,{children:Array.from({length:5},(e,t)=>(0,a.jsx)(q.sF,{index:t,className:"w-16 h-20 text-2xl"},t))})})})})}}),(0,a.jsxs)("div",{className:"space-y-2 flex flex-col items-center",children:[(0,a.jsxs)(f.$,{id:"otp-button",className:"w-full",variant:"default-seekers",loading:g.isPending,children:[l("cta.verify")," ",l("user.account")]}),(0,a.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),A()},className:"mx-auto text-xs text-seekers-text-light",children:l("otp.resendVerificationCode")})]})]})})}var Z=s(47943),J=s(32874);function K(e){let{onBack:t}=e,s=(0,n.useTranslations)("universal"),{toast:r}=(0,v.dj)(),i=(0,J.h)(),l=(0,Z.v)(),o=(0,u.mN)({resolver:(0,c.u)(i),defaultValues:{email:""}});async function p(e){let a={email:e.email};try{await l.mutateAsync(a),r({title:s("success.requestForgotPassword.title"),description:s("success.requestForgotPassword.description")}),t()}catch(e){r({title:s("error.requestForgetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return(0,a.jsx)(d.lV,{...o,children:(0,a.jsxs)("form",{onSubmit:o.handleSubmit(p),className:"grid gap-4 ",children:[(0,a.jsx)(m.A,{form:o,label:s("form.label.email"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light"}),(0,a.jsx)(f.$,{className:"w-full",variant:"default-seekers",loading:l.isPending,children:s("cta.requestChangePassword")}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)(f.$,{variant:"link",onClick:t,className:"p-0 h-9 text-seekers-primary hover:underline",children:s("cta.goBack")})})]})})}let G={signUp:"SIGN_UP",login:"LOGIN",otp:"OTP",resetPassword:"RESET_PASSWORD"};function W(e){let{triggerClassName:t,customTrigger:s}=e,c=(0,n.useTranslations)("seeker"),[d,u]=(0,i.useState)(!1),[m,p]=(0,i.useState)(""),[g,h]=(0,i.useState)(G.signUp);return(0,i.useEffect)(()=>{switch(g){case G.signUp:p(c("form.title.signUp"));return;case G.login:p(c("form.title.login"));return;case G.otp:p(c("form.title.enterOtpCode"));return;case G.resetPassword:p(c("form.title.resetPassword"));return;default:return}},[g,c]),(0,a.jsxs)(r.A,{open:d,setOpen:u,openTrigger:s||(0,a.jsx)("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full h-10 w-14 !bg-seekers-text-light ".concat(t),children:(0,a.jsx)(l.A,{url:""})}),dialogClassName:"w-full sm:max-w-[500px] p-6",children:[(0,a.jsxs)(o.A,{className:"flex flex-col space-y-1.5 text-center mb-6",children:[(g==G.otp||g==G.resetPassword)&&(0,a.jsx)(f.$,{variant:"ghost",size:"icon",className:"absolute top-4 left-4",onClick:()=>h(g==G.otp?G.signUp:G.login),children:(0,a.jsx)(L.A,{className:"h-4 w-4"})}),(0,a.jsxs)("section",{className:"space-y-1.5",children:[(0,a.jsx)("h2",{className:"tracking-tight text-center text-2xl font-bold max-w-xs mx-auto",children:m}),(0,a.jsx)("p",{className:(0,j.cn)(g===G.otp?"hidden":"","text-sm text-muted-foreground text-center"),children:g===G.login?c("auth.login.subtitle"):g===G.signUp?c("auth.register.subtitle"):g===G.resetPassword?c("auth.resetPassword.subtitle"):""})]})]}),g==G.login?(0,a.jsx)(C,{onClickSignUp:()=>h(G.signUp),onClickResetPassword:()=>h(G.resetPassword)}):g==G.signUp?(0,a.jsx)(P,{onSuccess:()=>h(G.otp),onClickLogin:()=>h(G.login)}):g==G.otp?(0,a.jsxs)("section",{children:[g===G.otp&&(0,a.jsxs)("div",{className:"text-seekers-text-light",children:[(0,a.jsx)("p",{children:c("auth.otp.content.title")}),(0,a.jsxs)("ul",{className:"list-disc list-inside",children:[(0,a.jsx)("li",{children:c("auth.otp.item.one")}),(0,a.jsx)("li",{children:c("auth.otp.item.two")}),(0,a.jsx)("li",{children:c("auth.otp.item.three")})]}),(0,a.jsx)("p",{children:c("auth.otp.content.cantFindEmail")})]}),(0,a.jsx)(H,{})]}):g==G.resetPassword?(0,a.jsx)(K,{onBack:()=>h(G.login)}):null]})}},22190:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(95155),r=s(99840),n=s(37130),i=s(53999);s(12115);var l=s(13423);function o(e){let{children:t,openTrigger:s,open:o,setOpen:c,dialogClassName:d,drawerClassName:u,dialogOverlayClassName:m}=e;return(0,n.U)("(min-width:1024px)")?(0,a.jsxs)(r.lG,{open:o,onOpenChange:c,children:[(0,a.jsx)(r.zM,{asChild:!0,children:s}),(0,a.jsxs)(r.ZJ,{children:[(0,a.jsx)(r.LC,{className:m}),(0,a.jsx)(r.Cf,{className:(0,i.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",d),children:t})]})]}):(0,a.jsxs)(l._s,{open:o,onOpenChange:c,children:[(0,a.jsx)(l.Uz,{asChild:!0,children:s}),(0,a.jsx)(l.zj,{children:(0,a.jsx)("div",{className:(0,i.cn)("p-4 overflow-auto",u),children:t})})]})}},28679:(e,t,s)=>{s.d(t,{DY:()=>r,jp:()=>i,yN:()=>n});var a=s(99493);let r=async(e,t)=>a.apiClient.post("auth/register",e,{headers:{"g-token":t||""}}),n=async e=>a.apiClient.put("users/update",e),i=async e=>a.apiClient.get("auth/me",e)},29653:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(95155),r=s(30070),n=s(89852),i=s(67943),l=s(53999);function o(e){let{form:t,label:s,name:o,placeholder:c,description:d,type:u,inputProps:m,children:f,labelClassName:p,containerClassName:g,inputContainer:h,variant:x="default"}=e;return(0,a.jsx)(r.zB,{control:t.control,name:o,render:e=>{let{field:t}=e;return(0,a.jsx)(i.A,{label:s,description:d,labelClassName:(0,l.cn)("float"==x?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:g,variant:x,children:(0,a.jsxs)("div",{className:(0,l.cn)("flex gap-2 w-full overflow-hidden","float"==x?"":"border rounded-sm focus-within:border-neutral-light",h),children:[(0,a.jsx)(n.p,{type:u,placeholder:c,...t,...m,className:(0,l.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==x?"px-0":"",null==m?void 0:m.className)}),f]})})}})}},30070:(e,t,s)=>{s.d(t,{C5:()=>v,MJ:()=>h,Rr:()=>x,eI:()=>p,lR:()=>g,lV:()=>c,zB:()=>u});var a=s(95155),r=s(12115),n=s(66634),i=s(62177),l=s(53999),o=s(82714);let c=i.Op,d=r.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},m=()=>{let e=r.useContext(d),t=r.useContext(f),{getFieldState:s,formState:a}=(0,i.xW)(),n=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},f=r.createContext({}),p=r.forwardRef((e,t)=>{let{className:s,...n}=e,i=r.useId();return(0,a.jsx)(f.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:t,className:(0,l.cn)("space-y-2",s),...n})})});p.displayName="FormItem";let g=r.forwardRef((e,t)=>{let{className:s,...r}=e,{error:n,formItemId:i}=m();return(0,a.jsx)(o.J,{ref:t,className:(0,l.cn)(n&&"text-destructive",s),htmlFor:i,...r})});g.displayName="FormLabel";let h=r.forwardRef((e,t)=>{let{...s}=e,{error:r,formItemId:i,formDescriptionId:l,formMessageId:o}=m();return(0,a.jsx)(n.DX,{ref:t,id:i,"aria-describedby":r?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!r,...s})});h.displayName="FormControl";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e,{formDescriptionId:n}=m();return(0,a.jsx)("p",{ref:t,id:n,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",s),...r})});x.displayName="FormDescription";let v=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e,{error:i,formMessageId:o}=m(),c=i?String(null==i?void 0:i.message):r;return c?(0,a.jsx)("p",{ref:t,id:o,className:(0,l.cn)("text-[0.8rem] font-medium text-destructive",s),...n,children:c}):null});v.displayName="FormMessage"},30145:(e,t,s)=>{s.d(t,{Q:()=>r});var a=s(23464);function r(e){if(a.A.isAxiosError(e)){var t,s;if((null==(t=e.response)?void 0:t.status)===401)throw Error("Unauthorized: Invalid token or missing credentials");if((null==(s=e.response)?void 0:s.status)===404)throw Error("Not Found: The requested resource could not be found");if(e.response)throw Error("Request failed with status code ".concat(e.response.status,": ").concat(e.response.statusText));else if(e.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error("Error during request setup: ".concat(e.message))}throw Error(e)}},32065:(e,t,s)=>{s.d(t,{m:()=>l});var a=s(18618),r=s(53580),n=s(5041),i=s(27043);function l(e){let{toast:t}=(0,r.dj)(),s=(0,i.useTranslations)("universal");return(0,n.n)({mutationFn:e=>(0,a.bH)(e),onSuccess:async t=>{await e()},onError:e=>{let a=e.response.data;return t({title:s("misc.foundError"),description:a.message,variant:"destructive"}),a}})}},32874:(e,t,s)=>{s.d(t,{h:()=>n});var a=s(27043),r=s(55594);function n(){let e=(0,a.useTranslations)("universal");return r.z.object({email:r.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email()})}},35201:(e,t,s)=>{s.d(t,{H:()=>c,g:()=>o});var a=s(93846),r=s(14666),n=s(46797),i=s(19373),l=s(57383);let o="my-detail";function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{setSeekers:t,clearUser:s,setRole:c}=(0,n.k)(e=>e),d=l.A.get(r.Xh);return(0,i.I)({queryKey:[o,d||"0"],queryFn:async()=>{if(!d)return n.h;try{let e=await (0,a.i8)();return t(e),c("SEEKER"),e}catch(e){return s(),n.h}},refetchOnWindowFocus:!1,retry:!1,enabled:e})}},37130:(e,t,s)=>{s.d(t,{U:()=>r});var a=s(12115);function r(e){let[t,s]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{function t(e){s(e.matches)}let a=matchMedia(e);return a.addEventListener("change",t),s(a.matches),()=>a.removeEventListener("change",t)},[e]),t}},37996:(e,t,s)=>{s.d(t,{A:()=>l});var a=s(95155),r=s(37130),n=s(99840),i=s(13423);function l(e){let{children:t,className:s}=e;return(0,r.U)("(min-width:1024px)")?(0,a.jsx)(n.c7,{className:s,children:t}):(0,a.jsx)(i.BE,{className:s,children:t})}},46797:(e,t,s)=>{s.d(t,{h:()=>c,k:()=>d});var a=s(12673),r=s(88693),n=s(46786),i=s(57383),l=s(74810);let o={getItem:e=>{let t=i.A.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{i.A.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{i.A.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:l.U$.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:a.vN},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},d=(0,r.vt)()((0,n.Zr)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,n.KU)(()=>o)}))},47943:(e,t,s)=>{s.d(t,{v:()=>n});var a=s(18618),r=s(5041);function n(){return(0,r.n)({mutationFn:e=>(0,a.aH)(e)})}},51315:(e,t,s)=>{s.d(t,{k:()=>o});var a=s(82940),r=s.n(a),n=s(88693),i=s(46786);let l={confirm_password:"",first_name:"",last_name:"",otp:"",password:"",type:"SEEKER",email:"",phone_code:"+62",phone_number:""},o=(0,n.vt)()((0,i.Zr)(e=>({register:l,setRegister:t=>e({register:t}),verifyOtpType:"",setVerifyOtpType:t=>e({verifyOtpType:t}),reset:()=>{o.persist.clearStorage(),e({register:l})},validFormUntil:void 0,setValidFormUntil:t=>e({validFormUntil:t}),successSignUp:!1,setSuccessSignUp:t=>e({successSignUp:t}),loading:!0,setLoading:t=>e({loading:t})}),{name:"register-user",storage:(0,i.KU)(()=>localStorage),onRehydrateStorage:()=>e=>{if(null==e?void 0:e.validFormUntil){let t=r()(null==e?void 0:e.validFormUntil);r()().isAfter(t)&&e.setRegister(l)}null==e||e.setLoading(!1)}}))},53580:(e,t,s)=>{s.d(t,{dj:()=>u});var a=s(12115);let r=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=[],o={toasts:[]};function c(e){o=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?i(s):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(o,e),l.forEach(e=>{e(o)})}function d(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=a.useState(o);return a.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,s)=>{s.d(t,{ZV:()=>c,cn:()=>l,gT:()=>d,jW:()=>m,lF:()=>p,q7:()=>f,tT:()=>g,vv:()=>o,yv:()=>u});var a=s(52596),r=s(82940),n=s.n(r),i=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,i.QP)((0,a.$)(t))}s(87358);let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat((e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}})(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2*("IDR"!=t)}).format(e)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function u(e){let t=n()(e),s=n()();return t.isSame(s,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function p(e,t){return e.some(e=>t.includes(e))}let g=e=>e.charAt(0).toUpperCase()+e.slice(1)},67943:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(95155),r=s(30070),n=s(53999);function i(e){let{children:t,description:s,label:i,containerClassName:l,labelClassName:o,variant:c="default"}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)(r.eI,{className:(0,n.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",l),onClick:e=>e.stopPropagation(),children:[i&&(0,a.jsx)(r.lR,{className:o,children:i}),(0,a.jsx)(r.MJ,{className:"group relative w-full",children:t}),s&&(0,a.jsx)(r.Rr,{children:s}),"default"==c&&(0,a.jsx)(r.C5,{})]}),"float"==c&&(0,a.jsx)(r.C5,{})]})}},69663:(e,t,s)=>{s.d(t,{BK:()=>o,eu:()=>l,q5:()=>c});var a=s(95155),r=s(12115),n=s(85977),i=s(53999);let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...r})});l.displayName=n.bL.displayName;let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n._V,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",s),...r})});o.displayName=n._V.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.H4,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...r})});c.displayName=n.H4.displayName},69916:(e,t,s)=>{s.d(t,{NV:()=>c,UV:()=>o,sF:()=>d});var a=s(95155),r=s(12115),n=s(33096),i=s(1184),l=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,containerClassName:r,...n}=e;return(0,a.jsx)(i.wE,{ref:t,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",r),className:(0,l.cn)("disabled:cursor-not-allowed",s),...n})});o.displayName="InputOTP";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center",s),...r})});c.displayName="InputOTPGroup";let d=r.forwardRef((e,t)=>{let{index:s,className:n,...o}=e,{char:c,hasFakeCaret:d,isActive:u}=r.useContext(i.dK).slots[s];return(0,a.jsxs)("div",{ref:t,className:(0,l.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",u&&"z-10 ring-1 ring-ring",n),...o,children:[c,d&&(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});d.displayName="InputOTPSlot",r.forwardRef((e,t)=>{let{...s}=e;return(0,a.jsx)("div",{ref:t,role:"separator",...s,children:(0,a.jsx)(n.YTx,{})})}).displayName="InputOTPSeparator"},72236:(e,t,s)=>{s.d(t,{A:()=>d});var a=s(95155),r=s(69663),n=s(71007),i=s(46797),l=s(35201),o=s(53999),c=s(27043);function d(e){var t;let{url:s,className:d}=e;(0,l.H)();let{seekers:u}=(0,i.k)(),m=(0,c.useTranslations)("universal");return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(r.eu,{className:(0,o.cn)("w-full rounded-full bg-seekers-text-lighter flex justify-center items-center",d),children:[(0,a.jsx)(r.BK,{src:null==(t=u.accounts)?void 0:t.image,alt:m("misc.profileImageAlt")}),(0,a.jsx)(r.q5,{className:"bg-transparent text-white",children:u.code?(0,a.jsxs)("span",{children:[u.accounts.firstName[0],u.accounts.lastName[0]]}):(0,a.jsx)(n.A,{})})]})})}},72337:(e,t,s)=>{s.d(t,{Y:()=>l,g:()=>i});var a=s(14666),r=s(27043),n=s(55594);let i=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function l(){let e=(0,r.useTranslations)("seeker");return n.z.object({firstName:n.z.string().min(a.gF,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:a.gF})}).max(a.EM,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:a.EM})}),lastName:n.z.string().min(a.gF,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:a.gF})}).max(a.EM,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:a.EM})}),contact:n.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:n.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:n.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password"))}),path:["confirmPassword"]})}},74463:(e,t,s)=>{s.d(t,{DW:()=>l,Eq:()=>o,Nx:()=>d,VZ:()=>p,Zs:()=>f,ch:()=>u,dA:()=>i,gA:()=>c,i1:()=>g,jd:()=>m,yA:()=>h}),s(95155);var a=s(14541),r=s(18804),n=s(38922);let i="/owner/account",l="/profile",o="/s",c="/favorites",d="/message",u="/subscription",m="/plan",f="/billing",p="/notification",g="/security",h="/representative/account";a.A,r.A,n.A,a.A,r.A},74810:(e,t,s)=>{s.d(t,{U$:()=>a,dF:()=>r});let a={archiver:"Achiever",finder:"Finder",free:"Free"},r={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},76037:(e,t,s)=>{s.d(t,{Separator:()=>l});var a=s(95155),r=s(12115),n=s(14050),i=s(53999);let l=r.forwardRef((e,t)=>{let{className:s,orientation:r="horizontal",decorative:l=!0,...o}=e;return(0,a.jsx)(n.b,{ref:t,decorative:l,orientation:r,className:(0,i.cn)("shrink-0 bg-border","horizontal"===r?"h-[1px] w-full":"h-full w-[1px]",s),...o})});l.displayName=n.b.displayName},81251:(e,t,s)=>{s.d(t,{A:()=>m});var a=s(95155),r=s(30070),n=s(89852),i=s(67943),l=s(12115),o=s(97168),c=s(78749),d=s(92657),u=s(53999);function m(e){let{form:t,label:s,name:m,placeholder:f,description:p,inputProps:g,labelClassName:h,containerClassName:x,inputContainer:v,variant:w="default"}=e,[y,N]=(0,l.useState)(!1);return(0,a.jsx)(r.zB,{control:t.control,name:m,render:e=>{let{field:t}=e;return(0,a.jsx)(i.A,{label:s,description:p,labelClassName:(0,u.cn)("float"==w?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:x,variant:w,children:(0,a.jsxs)("div",{className:(0,u.cn)("flex gap-2 w-full overflow-hidden","float"==w?"":"border rounded-sm focus-within:border-neutral-light",v),children:[(0,a.jsx)(n.p,{type:y?"text":"password",placeholder:f,...t,...g,className:(0,u.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==w?"px-0":"",null==g?void 0:g.className)}),(0,a.jsx)(o.$,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),N(e=>!e)},children:y?(0,a.jsx)(c.A,{className:"w-4 h-4"}):(0,a.jsx)(d.A,{className:"w-4 h-4"})})]})})}})}},82714:(e,t,s)=>{s.d(t,{J:()=>c});var a=s(95155),r=s(12115),n=s(40968),i=s(74466),l=s(53999);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,l.cn)(o(),s),...r})});c.displayName=n.b.displayName},87536:(e,t,s)=>{s.d(t,{h:()=>l});var a=s(18618),r=s(53580),n=s(5041),i=s(27043);function l(e){let{toast:t}=(0,r.dj)(),s=(0,i.useTranslations)("universal");return(0,n.n)({mutationFn:e=>(0,a.eD)(e),onSuccess:t=>{e(t)},onError:a=>{let r=a.response.data;if(r.message.includes("is already sent"))return void e(a);t({title:s("misc.foundError"),description:r.message,variant:"destructive"})}})}},89852:(e,t,s)=>{s.d(t,{p:()=>i});var a=s(95155),r=s(12115),n=s(53999);let i=r.forwardRef((e,t)=>{let{className:s,type:r,...i}=e;return(0,a.jsx)("input",{type:r,className:(0,n.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...i})});i.displayName="Input"},93846:(e,t,s)=>{s.d(t,{i8:()=>i});var a=s(28679),r=s(12673);s(14666);var n=s(30145);async function i(e){try{let t=await (0,a.jp)(e);return function(e){var t,s,a,n,i,l,o,c,d,u;let m=(0,r.x_)((null==(t=e.accounts.subscription)?void 0:t.detail.name)||"");return{accounts:{about:e.accounts.about,citizenship:e.accounts.citizenship||"",credit:{amount:(null==(s=e.accounts.credit)?void 0:s.amount)||0,updatedAt:(null==(a=e.accounts.credit)?void 0:a.updated_at)||""},facebookSocial:e.accounts.facebook_social||"",firstName:e.accounts.first_name,image:e.accounts.image,isSubscriber:e.accounts.is_subscriber,language:e.accounts.language,lastName:e.accounts.last_name,membership:m,twitterSocial:e.accounts.twitter_social||"",address:e.accounts.address||"",chat:{current:0,max:(0,r.cQ)(m)},zoomFeature:(0,r.lL)(m)},has2FA:e.is_2fa,email:e.email,code:e.code,isActive:e.is_active,phoneNumber:e.phone_number,phoneCode:e.phone_code,type:e.type,setting:{messageNotif:null==(n=e.accounts.settings)?void 0:n.message_notif,newsletterNotif:null==(i=e.accounts.settings)?void 0:i.newsletter_notif,priceAlertNotif:null==(l=e.accounts.settings)?void 0:l.price_alert_notif,propertyNotif:null==(o=e.accounts.settings)?void 0:o.property_notif,soundNotif:null==(c=e.accounts.settings)?void 0:c.sound_notif,specialOfferNotif:null==(d=e.accounts.settings)?void 0:d.special_offer_notif,surveyNotif:null==(u=e.accounts.settings)?void 0:u.survey_notif}}}(t.data.data)}catch(e){throw Error((0,n.Q)(e))}}},97168:(e,t,s)=>{s.d(t,{$:()=>d});var a=s(95155),r=s(12115),n=s(66634),i=s(74466),l=s(53999),o=s(51154);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:s,variant:r,size:i,asChild:d=!1,loading:u=!1,...m}=e,f=d?n.DX:"button";return(0,a.jsx)(f,{className:(0,l.cn)(c({variant:r,size:i,className:s})),ref:t,disabled:u||m.disabled,...m,children:u?(0,a.jsx)(o.A,{className:(0,l.cn)("h-4 w-4 animate-spin")}):m.children})});d.displayName="Button"},99493:(e,t,s)=>{s.d(t,{B:()=>c,apiClient:()=>o});var a=s(14666),r=s(23464),n=s(57383),i=s(79189);let l=new(s.n(i)()).Agent({rejectUnauthorized:!1}),o=r.A.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:n.A.get(a.Xh)?"Bearer "+n.A.get(a.Xh):""},httpsAgent:l}),c=r.A.create({baseURL:"/api/",httpsAgent:l})},99840:(e,t,s)=>{s.d(t,{Cf:()=>m,Es:()=>p,L3:()=>g,LC:()=>u,ZJ:()=>d,c7:()=>f,lG:()=>o,rr:()=>h,zM:()=>c});var a=s(95155),r=s(12115),n=s(67178),i=s(33096),l=s(53999);let o=n.bL,c=n.l9,d=n.ZL;n.bm;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hJ,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});u.displayName=n.hJ.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(n.UC,{ref:t,className:(0,l.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...o,children:[r,(0,a.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(i.MKb,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.UC.displayName;let f=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-start sm:text-left",t),...s})};f.displayName="DialogHeader";let p=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...s})};p.displayName="DialogFooter";let g=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});g.displayName=n.hE.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});h.displayName=n.VY.displayName}}]);