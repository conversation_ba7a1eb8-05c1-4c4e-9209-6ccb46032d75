"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9314],{27737:(e,a,t)=>{t.d(a,{E:()=>d});var r=t(95155),i=t(53999);function d(e){let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-primary/10",a),...t})}},37777:(e,a,t)=>{t.d(a,{Tooltip:()=>o,TooltipContent:()=>c,TooltipProvider:()=>n,TooltipTrigger:()=>l});var r=t(95155),i=t(12115),d=t(70859),s=t(53999);let n=d.Kq,o=d.bL,l=d.l9,c=i.forwardRef((e,a)=>{let{className:t,sideOffset:i=4,...n}=e;return(0,r.jsx)(d.UC,{ref:a,sideOffset:i,className:(0,s.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})});c.displayName=d.UC.displayName},99314:(e,a,t)=>{t.d(a,{Bx:()=>k,Yv:()=>R,Cn:()=>_,rQ:()=>M,jj:()=>C,SidebarProvider:()=>S,SidebarTrigger:()=>z});var r=t(95155),i=t(12115),d=t(66634),s=t(74466),n=t(53999),o=t(97168),l=t(89852),c=t(76037),u=t(67178),f=t(33096);let b=u.bL;u.l9,u.bm;let p=u.ZL,m=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...i,ref:a})});m.displayName=u.hJ.displayName;let g=(0,s.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),h=i.forwardRef((e,a)=>{let{side:t="right",className:i,children:d,...s}=e;return(0,r.jsxs)(p,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(u.UC,{ref:a,className:(0,n.cn)(g({side:t}),i),...s,children:[(0,r.jsxs)(u.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(f.MKb,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),d]})]})});h.displayName=u.UC.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.hE,{ref:a,className:(0,n.cn)("text-lg font-semibold text-foreground",t),...i})}).displayName=u.hE.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.VY,{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",t),...i})}).displayName=u.VY.displayName;var x=t(27737),v=t(37777),w=t(42355),N=t(13052);let y=i.createContext(null);function j(){let e=i.useContext(y);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let S=i.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:d,onOpenChange:s,className:o,style:l,children:c,...u}=e,f=function(){let[e,a]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[b,p]=i.useState(!1),[m,g]=i.useState(t),h=null!=d?d:m,x=i.useCallback(e=>{let a="function"==typeof e?e(h):e;s?s(a):g(a),document.cookie="".concat("sidebar:state","=").concat(a,"; path=/; max-age=").concat(604800)},[s,h]),w=i.useCallback(()=>f?p(e=>!e):x(e=>!e),[f,x,p]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),w())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[w]);let N=h?"expanded":"collapsed",j=i.useMemo(()=>({state:N,open:h,setOpen:x,isMobile:f,openMobile:b,setOpenMobile:p,toggleSidebar:w}),[N,h,x,f,b,p,w]);return(0,r.jsx)(y.Provider,{value:j,children:(0,r.jsx)(v.TooltipProvider,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"0",...l},className:(0,n.cn)("group/sidebar-wrapper flex w-full has-[[data-variant=inset]]:bg-sidebar",o),ref:a,...u,children:c})})})});S.displayName="SidebarProvider";let k=i.forwardRef((e,a)=>{let{side:t="left",variant:i="sidebar",collapsible:d="offcanvas",className:s,children:o,...l}=e,{isMobile:c,state:u,openMobile:f,setOpenMobile:p}=j();return"none"===d?(0,r.jsx)("div",{className:(0,n.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",s),ref:a,...l,children:o}):c?(0,r.jsx)(b,{open:f,onOpenChange:p,...l,children:(0,r.jsx)(h,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:o})})}):(0,r.jsxs)("div",{ref:a,className:(0,n.cn)("group peer hidden text-sidebar-foreground md:block","sidebar"==i&&"h-full"),"data-state":u,"data-collapsible":"collapsed"===u?d:"","data-variant":i,"data-side":t,children:[(0,r.jsx)("div",{className:(0,n.cn)("relative h-full w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===i||"inset"===i?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,n.cn)("fixed inset-y-0 z-10 hidden h-full w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===i||"inset"===i?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",s),...l,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});k.displayName="Sidebar";let z=i.forwardRef((e,a)=>{let{className:t,onClick:i,...d}=e,{toggleSidebar:s,open:l}=j();return(0,r.jsxs)(o.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,n.cn)("h-7 w-7",t),onClick:e=>{null==i||i(e),s()},...d,children:[l?(0,r.jsx)(w.A,{}):(0,r.jsx)(N.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});z.displayName="SidebarTrigger",i.forwardRef((e,a)=>{let{className:t,...i}=e,{toggleSidebar:d}=j();return(0,r.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:d,title:"Toggle Sidebar",className:(0,n.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:w-0","[[data-side=right][data-collapsible=offcanvas]_&]:w-0",t),...i})}).displayName="SidebarRail",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("main",{ref:a,className:(0,n.cn)("relative flex min-h-full flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...i})}).displayName="SidebarInset",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(l.p,{ref:a,"data-sidebar":"input",className:(0,n.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...i})}).displayName="SidebarInput",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,n.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarHeader",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,n.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarFooter",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(c.Separator,{ref:a,"data-sidebar":"separator",className:(0,n.cn)("mx-2 w-auto bg-sidebar-border",t),...i})}).displayName="SidebarSeparator";let R=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,n.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...i})});R.displayName="SidebarContent";let _=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,n.cn)("relative flex w-full min-w-0 flex-col p-2",t),...i})});_.displayName="SidebarGroup";let C=i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...s}=e,o=i?d.DX:"div";return(0,r.jsx)(o,{ref:a,"data-sidebar":"group-label",className:(0,n.cn)("flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...s})});C.displayName="SidebarGroupLabel",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...s}=e,o=i?d.DX:"button";return(0,r.jsx)(o,{ref:a,"data-sidebar":"group-action",className:(0,n.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...s})}).displayName="SidebarGroupAction";let M=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,n.cn)("relative pl-8 before:absolute before:left-4 before:top-0 before:h-full before:w-px before:bg-border",t),...i})});M.displayName="SidebarGroupContent",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,n.cn)("flex w-full min-w-0 flex-col gap-1",t),...i})}).displayName="SidebarMenu",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,n.cn)("group/menu-item relative",t),...i})}).displayName="SidebarMenuItem";let T=(0,s.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}});i.forwardRef((e,a)=>{let{asChild:t=!1,isActive:i=!1,variant:s="default",size:o="default",tooltip:l,className:c,...u}=e,f=t?d.DX:"button",{isMobile:b,state:p}=j(),m=(0,r.jsx)(f,{ref:a,"data-sidebar":"menu-button","data-size":o,"data-active":i,className:(0,n.cn)(T({variant:s,size:o}),c),...u});return l?("string"==typeof l&&(l={children:l}),(0,r.jsxs)(v.Tooltip,{children:[(0,r.jsx)(v.TooltipTrigger,{asChild:!0,children:m}),(0,r.jsx)(v.TooltipContent,{side:"right",align:"center",hidden:"collapsed"!==p||b,...l})]})):m}).displayName="SidebarMenuButton",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,showOnHover:s=!1,...o}=e,l=i?d.DX:"button";return(0,r.jsx)(l,{ref:a,"data-sidebar":"menu-action",className:(0,n.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",s&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...o})}).displayName="SidebarMenuAction",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,n.cn)("pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuBadge",i.forwardRef((e,a)=>{let{className:t,showIcon:d=!1,...s}=e,o=i.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,r.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,n.cn)("flex h-8 items-center gap-2 rounded-md px-2",t),...s,children:[d&&(0,r.jsx)(x.E,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(x.E,{className:"h-4 max-w-[--skeleton-width] flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})}).displayName="SidebarMenuSkeleton",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,n.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuSub",i.forwardRef((e,a)=>{let{...t}=e;return(0,r.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",i.forwardRef((e,a)=>{let{asChild:t=!1,size:i="md",isActive:s,className:o,...l}=e,c=t?d.DX:"a";return(0,r.jsx)(c,{ref:a,"data-sidebar":"menu-sub-button","data-size":i,"data-active":s,className:(0,n.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===i&&"text-xs","md"===i&&"text-sm","group-data-[collapsible=icon]:hidden",o),...l})}).displayName="SidebarMenuSubButton"}}]);