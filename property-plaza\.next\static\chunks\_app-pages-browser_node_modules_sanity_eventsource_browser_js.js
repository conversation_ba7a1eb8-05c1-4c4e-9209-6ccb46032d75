/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_eventsource_browser_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/eventsource/browser.js":
/*!*****************************************************!*\
  !*** ./node_modules/@sanity/eventsource/browser.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! event-source-polyfill */ \"(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js\").EventSourcePolyfill\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L2V2ZW50c291cmNlL2Jyb3dzZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXFFIiwic291cmNlcyI6WyJDOlxcX1BSSVZBVEVcXFByb3BlcnR5IFBsYXphIC0gU2Vla2Vyc1xccHJvcGVydHktcGxhemFcXG5vZGVfbW9kdWxlc1xcQHNhbml0eVxcZXZlbnRzb3VyY2VcXGJyb3dzZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCdldmVudC1zb3VyY2UtcG9seWZpbGwnKS5FdmVudFNvdXJjZVBvbHlmaWxsXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/eventsource/browser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js":
/*!***************************************************************!*\
  !*** ./node_modules/event-source-polyfill/src/eventsource.js ***!
  \***************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/** @license\r\n * eventsource.js\r\n * Available under MIT License (MIT)\r\n * https://github.com/Yaffle/EventSource/\r\n */\r\n\r\n/*jslint indent: 2, vars: true, plusplus: true */\r\n/*global setTimeout, clearTimeout */\r\n\r\n(function (global) {\r\n  \"use strict\";\r\n\r\n  var setTimeout = global.setTimeout;\r\n  var clearTimeout = global.clearTimeout;\r\n  var XMLHttpRequest = global.XMLHttpRequest;\r\n  var XDomainRequest = global.XDomainRequest;\r\n  var ActiveXObject = global.ActiveXObject;\r\n  var NativeEventSource = global.EventSource;\r\n\r\n  var document = global.document;\r\n  var Promise = global.Promise;\r\n  var fetch = global.fetch;\r\n  var Response = global.Response;\r\n  var TextDecoder = global.TextDecoder;\r\n  var TextEncoder = global.TextEncoder;\r\n  var AbortController = global.AbortController;\r\n\r\n  if (typeof window !== \"undefined\" && typeof document !== \"undefined\" && !(\"readyState\" in document) && document.body == null) { // Firefox 2\r\n    document.readyState = \"loading\";\r\n    window.addEventListener(\"load\", function (event) {\r\n      document.readyState = \"complete\";\r\n    }, false);\r\n  }\r\n\r\n  if (XMLHttpRequest == null && ActiveXObject != null) { // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Using_XMLHttpRequest_in_IE6\r\n    XMLHttpRequest = function () {\r\n      return new ActiveXObject(\"Microsoft.XMLHTTP\");\r\n    };\r\n  }\r\n\r\n  if (Object.create == undefined) {\r\n    Object.create = function (C) {\r\n      function F(){}\r\n      F.prototype = C;\r\n      return new F();\r\n    };\r\n  }\r\n\r\n  if (!Date.now) {\r\n    Date.now = function now() {\r\n      return new Date().getTime();\r\n    };\r\n  }\r\n\r\n  // see #118 (Promise#finally with polyfilled Promise)\r\n  // see #123 (data URLs crash Edge)\r\n  // see #125 (CSP violations)\r\n  // see pull/#138\r\n  // => No way to polyfill Promise#finally\r\n\r\n  if (AbortController == undefined) {\r\n    var originalFetch2 = fetch;\r\n    fetch = function (url, options) {\r\n      var signal = options.signal;\r\n      return originalFetch2(url, {headers: options.headers, credentials: options.credentials, cache: options.cache}).then(function (response) {\r\n        var reader = response.body.getReader();\r\n        signal._reader = reader;\r\n        if (signal._aborted) {\r\n          signal._reader.cancel();\r\n        }\r\n        return {\r\n          status: response.status,\r\n          statusText: response.statusText,\r\n          headers: response.headers,\r\n          body: {\r\n            getReader: function () {\r\n              return reader;\r\n            }\r\n          }\r\n        };\r\n      });\r\n    };\r\n    AbortController = function () {\r\n      this.signal = {\r\n        _reader: null,\r\n        _aborted: false\r\n      };\r\n      this.abort = function () {\r\n        if (this.signal._reader != null) {\r\n          this.signal._reader.cancel();\r\n        }\r\n        this.signal._aborted = true;\r\n      };\r\n    };\r\n  }\r\n\r\n  function TextDecoderPolyfill() {\r\n    this.bitsNeeded = 0;\r\n    this.codePoint = 0;\r\n  }\r\n\r\n  TextDecoderPolyfill.prototype.decode = function (octets) {\r\n    function valid(codePoint, shift, octetsCount) {\r\n      if (octetsCount === 1) {\r\n        return codePoint >= 0x0080 >> shift && codePoint << shift <= 0x07FF;\r\n      }\r\n      if (octetsCount === 2) {\r\n        return codePoint >= 0x0800 >> shift && codePoint << shift <= 0xD7FF || codePoint >= 0xE000 >> shift && codePoint << shift <= 0xFFFF;\r\n      }\r\n      if (octetsCount === 3) {\r\n        return codePoint >= 0x010000 >> shift && codePoint << shift <= 0x10FFFF;\r\n      }\r\n      throw new Error();\r\n    }\r\n    function octetsCount(bitsNeeded, codePoint) {\r\n      if (bitsNeeded === 6 * 1) {\r\n        return codePoint >> 6 > 15 ? 3 : codePoint > 31 ? 2 : 1;\r\n      }\r\n      if (bitsNeeded === 6 * 2) {\r\n        return codePoint > 15 ? 3 : 2;\r\n      }\r\n      if (bitsNeeded === 6 * 3) {\r\n        return 3;\r\n      }\r\n      throw new Error();\r\n    }\r\n    var REPLACER = 0xFFFD;\r\n    var string = \"\";\r\n    var bitsNeeded = this.bitsNeeded;\r\n    var codePoint = this.codePoint;\r\n    for (var i = 0; i < octets.length; i += 1) {\r\n      var octet = octets[i];\r\n      if (bitsNeeded !== 0) {\r\n        if (octet < 128 || octet > 191 || !valid(codePoint << 6 | octet & 63, bitsNeeded - 6, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n          string += String.fromCharCode(codePoint);\r\n        }\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (octet >= 0 && octet <= 127) {\r\n          bitsNeeded = 0;\r\n          codePoint = octet;\r\n        } else if (octet >= 192 && octet <= 223) {\r\n          bitsNeeded = 6 * 1;\r\n          codePoint = octet & 31;\r\n        } else if (octet >= 224 && octet <= 239) {\r\n          bitsNeeded = 6 * 2;\r\n          codePoint = octet & 15;\r\n        } else if (octet >= 240 && octet <= 247) {\r\n          bitsNeeded = 6 * 3;\r\n          codePoint = octet & 7;\r\n        } else {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n        if (bitsNeeded !== 0 && !valid(codePoint, bitsNeeded, octetsCount(bitsNeeded, codePoint))) {\r\n          bitsNeeded = 0;\r\n          codePoint = REPLACER;\r\n        }\r\n      } else {\r\n        bitsNeeded -= 6;\r\n        codePoint = codePoint << 6 | octet & 63;\r\n      }\r\n      if (bitsNeeded === 0) {\r\n        if (codePoint <= 0xFFFF) {\r\n          string += String.fromCharCode(codePoint);\r\n        } else {\r\n          string += String.fromCharCode(0xD800 + (codePoint - 0xFFFF - 1 >> 10));\r\n          string += String.fromCharCode(0xDC00 + (codePoint - 0xFFFF - 1 & 0x3FF));\r\n        }\r\n      }\r\n    }\r\n    this.bitsNeeded = bitsNeeded;\r\n    this.codePoint = codePoint;\r\n    return string;\r\n  };\r\n\r\n  // Firefox < 38 throws an error with stream option\r\n  var supportsStreamOption = function () {\r\n    try {\r\n      return new TextDecoder().decode(new TextEncoder().encode(\"test\"), {stream: true}) === \"test\";\r\n    } catch (error) {\r\n      console.debug(\"TextDecoder does not support streaming option. Using polyfill instead: \" + error);\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // IE, Edge\r\n  if (TextDecoder == undefined || TextEncoder == undefined || !supportsStreamOption()) {\r\n    TextDecoder = TextDecoderPolyfill;\r\n  }\r\n\r\n  var k = function () {\r\n  };\r\n\r\n  function XHRWrapper(xhr) {\r\n    this.withCredentials = false;\r\n    this.readyState = 0;\r\n    this.status = 0;\r\n    this.statusText = \"\";\r\n    this.responseText = \"\";\r\n    this.onprogress = k;\r\n    this.onload = k;\r\n    this.onerror = k;\r\n    this.onreadystatechange = k;\r\n    this._contentType = \"\";\r\n    this._xhr = xhr;\r\n    this._sendTimeout = 0;\r\n    this._abort = k;\r\n  }\r\n\r\n  XHRWrapper.prototype.open = function (method, url) {\r\n    this._abort(true);\r\n\r\n    var that = this;\r\n    var xhr = this._xhr;\r\n    var state = 1;\r\n    var timeout = 0;\r\n\r\n    this._abort = function (silent) {\r\n      if (that._sendTimeout !== 0) {\r\n        clearTimeout(that._sendTimeout);\r\n        that._sendTimeout = 0;\r\n      }\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        xhr.onload = k;\r\n        xhr.onerror = k;\r\n        xhr.onabort = k;\r\n        xhr.onprogress = k;\r\n        xhr.onreadystatechange = k;\r\n        // IE 8 - 9: XDomainRequest#abort() does not fire any event\r\n        // Opera < 10: XMLHttpRequest#abort() does not fire any event\r\n        xhr.abort();\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        if (!silent) {\r\n          that.readyState = 4;\r\n          that.onabort(null);\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n      state = 0;\r\n    };\r\n\r\n    var onStart = function () {\r\n      if (state === 1) {\r\n        //state = 2;\r\n        var status = 0;\r\n        var statusText = \"\";\r\n        var contentType = undefined;\r\n        if (!(\"contentType\" in xhr)) {\r\n          try {\r\n            status = xhr.status;\r\n            statusText = xhr.statusText;\r\n            contentType = xhr.getResponseHeader(\"Content-Type\");\r\n          } catch (error) {\r\n            // IE < 10 throws exception for `xhr.status` when xhr.readyState === 2 || xhr.readyState === 3\r\n            // Opera < 11 throws exception for `xhr.status` when xhr.readyState === 2\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29121\r\n            status = 0;\r\n            statusText = \"\";\r\n            contentType = undefined;\r\n            // Firefox < 14, Chrome ?, Safari ?\r\n            // https://bugs.webkit.org/show_bug.cgi?id=29658\r\n            // https://bugs.webkit.org/show_bug.cgi?id=77854\r\n          }\r\n        } else {\r\n          status = 200;\r\n          statusText = \"OK\";\r\n          contentType = xhr.contentType;\r\n        }\r\n        if (status !== 0) {\r\n          state = 2;\r\n          that.readyState = 2;\r\n          that.status = status;\r\n          that.statusText = statusText;\r\n          that._contentType = contentType;\r\n          that.onreadystatechange();\r\n        }\r\n      }\r\n    };\r\n    var onProgress = function () {\r\n      onStart();\r\n      if (state === 2 || state === 3) {\r\n        state = 3;\r\n        var responseText = \"\";\r\n        try {\r\n          responseText = xhr.responseText;\r\n        } catch (error) {\r\n          // IE 8 - 9 with XMLHttpRequest\r\n        }\r\n        that.readyState = 3;\r\n        that.responseText = responseText;\r\n        that.onprogress();\r\n      }\r\n    };\r\n    var onFinish = function (type, event) {\r\n      if (event == null || event.preventDefault == null) {\r\n        event = {\r\n          preventDefault: k\r\n        };\r\n      }\r\n      // Firefox 52 fires \"readystatechange\" (xhr.readyState === 4) without final \"readystatechange\" (xhr.readyState === 3)\r\n      // IE 8 fires \"onload\" without \"onprogress\"\r\n      onProgress();\r\n      if (state === 1 || state === 2 || state === 3) {\r\n        state = 4;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        that.readyState = 4;\r\n        if (type === \"load\") {\r\n          that.onload(event);\r\n        } else if (type === \"error\") {\r\n          that.onerror(event);\r\n        } else if (type === \"abort\") {\r\n          that.onabort(event);\r\n        } else {\r\n          throw new TypeError();\r\n        }\r\n        that.onreadystatechange();\r\n      }\r\n    };\r\n    var onReadyStateChange = function (event) {\r\n      if (xhr != undefined) { // Opera 12\r\n        if (xhr.readyState === 4) {\r\n          if (!(\"onload\" in xhr) || !(\"onerror\" in xhr) || !(\"onabort\" in xhr)) {\r\n            onFinish(xhr.responseText === \"\" ? \"error\" : \"load\", event);\r\n          }\r\n        } else if (xhr.readyState === 3) {\r\n          if (!(\"onprogress\" in xhr)) { // testing XMLHttpRequest#responseText too many times is too slow in IE 11\r\n            // and in Firefox 3.6\r\n            onProgress();\r\n          }\r\n        } else if (xhr.readyState === 2) {\r\n          onStart();\r\n        }\r\n      }\r\n    };\r\n    var onTimeout = function () {\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 500);\r\n      if (xhr.readyState === 3) {\r\n        onProgress();\r\n      }\r\n    };\r\n\r\n    // XDomainRequest#abort removes onprogress, onerror, onload\r\n    if (\"onload\" in xhr) {\r\n      xhr.onload = function (event) {\r\n        onFinish(\"load\", event);\r\n      };\r\n    }\r\n    if (\"onerror\" in xhr) {\r\n      xhr.onerror = function (event) {\r\n        onFinish(\"error\", event);\r\n      };\r\n    }\r\n    // improper fix to match Firefox behaviour, but it is better than just ignore abort\r\n    // see https://bugzilla.mozilla.org/show_bug.cgi?id=768596\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=880200\r\n    // https://code.google.com/p/chromium/issues/detail?id=153570\r\n    // IE 8 fires \"onload\" without \"onprogress\r\n    if (\"onabort\" in xhr) {\r\n      xhr.onabort = function (event) {\r\n        onFinish(\"abort\", event);\r\n      };\r\n    }\r\n\r\n    if (\"onprogress\" in xhr) {\r\n      xhr.onprogress = onProgress;\r\n    }\r\n\r\n    // IE 8 - 9 (XMLHTTPRequest)\r\n    // Opera < 12\r\n    // Firefox < 3.5\r\n    // Firefox 3.5 - 3.6 - ? < 9.0\r\n    // onprogress is not fired sometimes or delayed\r\n    // see also #64 (significant lag in IE 11)\r\n    if (\"onreadystatechange\" in xhr) {\r\n      xhr.onreadystatechange = function (event) {\r\n        onReadyStateChange(event);\r\n      };\r\n    }\r\n\r\n    if (\"contentType\" in xhr || !(\"ontimeout\" in XMLHttpRequest.prototype)) {\r\n      url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + \"padding=true\";\r\n    }\r\n    xhr.open(method, url, true);\r\n\r\n    if (\"readyState\" in xhr) {\r\n      // workaround for Opera 12 issue with \"progress\" events\r\n      // #91 (XMLHttpRequest onprogress not fired for streaming response in Edge 14-15-?)\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, 0);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.abort = function () {\r\n    this._abort(false);\r\n  };\r\n  XHRWrapper.prototype.getResponseHeader = function (name) {\r\n    return this._contentType;\r\n  };\r\n  XHRWrapper.prototype.setRequestHeader = function (name, value) {\r\n    var xhr = this._xhr;\r\n    if (\"setRequestHeader\" in xhr) {\r\n      xhr.setRequestHeader(name, value);\r\n    }\r\n  };\r\n  XHRWrapper.prototype.getAllResponseHeaders = function () {\r\n    // XMLHttpRequest#getAllResponseHeaders returns null for CORS requests in Firefox 3.6.28\r\n    return this._xhr.getAllResponseHeaders != undefined ? this._xhr.getAllResponseHeaders() || \"\" : \"\";\r\n  };\r\n  XHRWrapper.prototype.send = function () {\r\n    // loading indicator in Safari < ? (6), Chrome < 14, Firefox\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=736723\r\n    if ((!(\"ontimeout\" in XMLHttpRequest.prototype) || (!(\"sendAsBinary\" in XMLHttpRequest.prototype) && !(\"mozAnon\" in XMLHttpRequest.prototype))) &&\r\n        document != undefined &&\r\n        document.readyState != undefined &&\r\n        document.readyState !== \"complete\") {\r\n      var that = this;\r\n      that._sendTimeout = setTimeout(function () {\r\n        that._sendTimeout = 0;\r\n        that.send();\r\n      }, 4);\r\n      return;\r\n    }\r\n\r\n    var xhr = this._xhr;\r\n    // withCredentials should be set after \"open\" for Safari and Chrome (< 19 ?)\r\n    if (\"withCredentials\" in xhr) {\r\n      xhr.withCredentials = this.withCredentials;\r\n    }\r\n    try {\r\n      // xhr.send(); throws \"Not enough arguments\" in Firefox 3.0\r\n      xhr.send(undefined);\r\n    } catch (error1) {\r\n      // Safari 5.1.7, Opera 12\r\n      throw error1;\r\n    }\r\n  };\r\n\r\n  function toLowerCase(name) {\r\n    return name.replace(/[A-Z]/g, function (c) {\r\n      return String.fromCharCode(c.charCodeAt(0) + 0x20);\r\n    });\r\n  }\r\n\r\n  function HeadersPolyfill(all) {\r\n    // Get headers: implemented according to mozilla's example code: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/getAllResponseHeaders#Example\r\n    var map = Object.create(null);\r\n    var array = all.split(\"\\r\\n\");\r\n    for (var i = 0; i < array.length; i += 1) {\r\n      var line = array[i];\r\n      var parts = line.split(\": \");\r\n      var name = parts.shift();\r\n      var value = parts.join(\": \");\r\n      map[toLowerCase(name)] = value;\r\n    }\r\n    this._map = map;\r\n  }\r\n  HeadersPolyfill.prototype.get = function (name) {\r\n    return this._map[toLowerCase(name)];\r\n  };\r\n\r\n  if (XMLHttpRequest != null && XMLHttpRequest.HEADERS_RECEIVED == null) { // IE < 9, Firefox 3.6\r\n    XMLHttpRequest.HEADERS_RECEIVED = 2;\r\n  }\r\n\r\n  function XHRTransport() {\r\n  }\r\n\r\n  XHRTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    xhr.open(\"GET\", url);\r\n    var offset = 0;\r\n    xhr.onprogress = function () {\r\n      var responseText = xhr.responseText;\r\n      var chunk = responseText.slice(offset);\r\n      offset += chunk.length;\r\n      onProgressCallback(chunk);\r\n    };\r\n    xhr.onerror = function (event) {\r\n      event.preventDefault();\r\n      onFinishCallback(new Error(\"NetworkError\"));\r\n    };\r\n    xhr.onload = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onabort = function () {\r\n      onFinishCallback(null);\r\n    };\r\n    xhr.onreadystatechange = function () {\r\n      if (xhr.readyState === XMLHttpRequest.HEADERS_RECEIVED) {\r\n        var status = xhr.status;\r\n        var statusText = xhr.statusText;\r\n        var contentType = xhr.getResponseHeader(\"Content-Type\");\r\n        var headers = xhr.getAllResponseHeaders();\r\n        onStartCallback(status, statusText, contentType, new HeadersPolyfill(headers));\r\n      }\r\n    };\r\n    xhr.withCredentials = withCredentials;\r\n    for (var name in headers) {\r\n      if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n        xhr.setRequestHeader(name, headers[name]);\r\n      }\r\n    }\r\n    xhr.send();\r\n    return xhr;\r\n  };\r\n\r\n  function HeadersWrapper(headers) {\r\n    this._headers = headers;\r\n  }\r\n  HeadersWrapper.prototype.get = function (name) {\r\n    return this._headers.get(name);\r\n  };\r\n\r\n  function FetchTransport() {\r\n  }\r\n\r\n  FetchTransport.prototype.open = function (xhr, onStartCallback, onProgressCallback, onFinishCallback, url, withCredentials, headers) {\r\n    var reader = null;\r\n    var controller = new AbortController();\r\n    var signal = controller.signal;\r\n    var textDecoder = new TextDecoder();\r\n    fetch(url, {\r\n      headers: headers,\r\n      credentials: withCredentials ? \"include\" : \"same-origin\",\r\n      signal: signal,\r\n      cache: \"no-store\"\r\n    }).then(function (response) {\r\n      reader = response.body.getReader();\r\n      onStartCallback(response.status, response.statusText, response.headers.get(\"Content-Type\"), new HeadersWrapper(response.headers));\r\n      // see https://github.com/promises-aplus/promises-spec/issues/179\r\n      return new Promise(function (resolve, reject) {\r\n        var readNextChunk = function () {\r\n          reader.read().then(function (result) {\r\n            if (result.done) {\r\n              //Note: bytes in textDecoder are ignored\r\n              resolve(undefined);\r\n            } else {\r\n              var chunk = textDecoder.decode(result.value, {stream: true});\r\n              onProgressCallback(chunk);\r\n              readNextChunk();\r\n            }\r\n          })[\"catch\"](function (error) {\r\n            reject(error);\r\n          });\r\n        };\r\n        readNextChunk();\r\n      });\r\n    })[\"catch\"](function (error) {\r\n      if (error.name === \"AbortError\") {\r\n        return undefined;\r\n      } else {\r\n        return error;\r\n      }\r\n    }).then(function (error) {\r\n      onFinishCallback(error);\r\n    });\r\n    return {\r\n      abort: function () {\r\n        if (reader != null) {\r\n          reader.cancel(); // https://bugzilla.mozilla.org/show_bug.cgi?id=1583815\r\n        }\r\n        controller.abort();\r\n      }\r\n    };\r\n  };\r\n\r\n  function EventTarget() {\r\n    this._listeners = Object.create(null);\r\n  }\r\n\r\n  function throwError(e) {\r\n    setTimeout(function () {\r\n      throw e;\r\n    }, 0);\r\n  }\r\n\r\n  EventTarget.prototype.dispatchEvent = function (event) {\r\n    event.target = this;\r\n    var typeListeners = this._listeners[event.type];\r\n    if (typeListeners != undefined) {\r\n      var length = typeListeners.length;\r\n      for (var i = 0; i < length; i += 1) {\r\n        var listener = typeListeners[i];\r\n        try {\r\n          if (typeof listener.handleEvent === \"function\") {\r\n            listener.handleEvent(event);\r\n          } else {\r\n            listener.call(this, event);\r\n          }\r\n        } catch (e) {\r\n          throwError(e);\r\n        }\r\n      }\r\n    }\r\n  };\r\n  EventTarget.prototype.addEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners == undefined) {\r\n      typeListeners = [];\r\n      listeners[type] = typeListeners;\r\n    }\r\n    var found = false;\r\n    for (var i = 0; i < typeListeners.length; i += 1) {\r\n      if (typeListeners[i] === listener) {\r\n        found = true;\r\n      }\r\n    }\r\n    if (!found) {\r\n      typeListeners.push(listener);\r\n    }\r\n  };\r\n  EventTarget.prototype.removeEventListener = function (type, listener) {\r\n    type = String(type);\r\n    var listeners = this._listeners;\r\n    var typeListeners = listeners[type];\r\n    if (typeListeners != undefined) {\r\n      var filtered = [];\r\n      for (var i = 0; i < typeListeners.length; i += 1) {\r\n        if (typeListeners[i] !== listener) {\r\n          filtered.push(typeListeners[i]);\r\n        }\r\n      }\r\n      if (filtered.length === 0) {\r\n        delete listeners[type];\r\n      } else {\r\n        listeners[type] = filtered;\r\n      }\r\n    }\r\n  };\r\n\r\n  function Event(type) {\r\n    this.type = type;\r\n    this.target = undefined;\r\n  }\r\n\r\n  function MessageEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.data = options.data;\r\n    this.lastEventId = options.lastEventId;\r\n  }\r\n\r\n  MessageEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ConnectionEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.status = options.status;\r\n    this.statusText = options.statusText;\r\n    this.headers = options.headers;\r\n  }\r\n\r\n  ConnectionEvent.prototype = Object.create(Event.prototype);\r\n\r\n  function ErrorEvent(type, options) {\r\n    Event.call(this, type);\r\n    this.error = options.error;\r\n  }\r\n\r\n  ErrorEvent.prototype = Object.create(Event.prototype);\r\n\r\n  var WAITING = -1;\r\n  var CONNECTING = 0;\r\n  var OPEN = 1;\r\n  var CLOSED = 2;\r\n\r\n  var AFTER_CR = -1;\r\n  var FIELD_START = 0;\r\n  var FIELD = 1;\r\n  var VALUE_START = 2;\r\n  var VALUE = 3;\r\n\r\n  var contentTypeRegExp = /^text\\/event\\-stream(;.*)?$/i;\r\n\r\n  var MINIMUM_DURATION = 1000;\r\n  var MAXIMUM_DURATION = 18000000;\r\n\r\n  var parseDuration = function (value, def) {\r\n    var n = value == null ? def : parseInt(value, 10);\r\n    if (n !== n) {\r\n      n = def;\r\n    }\r\n    return clampDuration(n);\r\n  };\r\n  var clampDuration = function (n) {\r\n    return Math.min(Math.max(n, MINIMUM_DURATION), MAXIMUM_DURATION);\r\n  };\r\n\r\n  var fire = function (that, f, event) {\r\n    try {\r\n      if (typeof f === \"function\") {\r\n        f.call(that, event);\r\n      }\r\n    } catch (e) {\r\n      throwError(e);\r\n    }\r\n  };\r\n\r\n  function EventSourcePolyfill(url, options) {\r\n    EventTarget.call(this);\r\n    options = options || {};\r\n\r\n    this.onopen = undefined;\r\n    this.onmessage = undefined;\r\n    this.onerror = undefined;\r\n\r\n    this.url = undefined;\r\n    this.readyState = undefined;\r\n    this.withCredentials = undefined;\r\n    this.headers = undefined;\r\n\r\n    this._close = undefined;\r\n\r\n    start(this, url, options);\r\n  }\r\n\r\n  function getBestXHRTransport() {\r\n    return (XMLHttpRequest != undefined && (\"withCredentials\" in XMLHttpRequest.prototype)) || XDomainRequest == undefined\r\n        ? new XMLHttpRequest()\r\n        : new XDomainRequest();\r\n  }\r\n\r\n  var isFetchSupported = fetch != undefined && Response != undefined && \"body\" in Response.prototype;\r\n\r\n  function start(es, url, options) {\r\n    url = String(url);\r\n    var withCredentials = Boolean(options.withCredentials);\r\n    var lastEventIdQueryParameterName = options.lastEventIdQueryParameterName || \"lastEventId\";\r\n\r\n    var initialRetry = clampDuration(1000);\r\n    var heartbeatTimeout = parseDuration(options.heartbeatTimeout, 45000);\r\n\r\n    var lastEventId = \"\";\r\n    var retry = initialRetry;\r\n    var wasActivity = false;\r\n    var textLength = 0;\r\n    var headers = options.headers || {};\r\n    var TransportOption = options.Transport;\r\n    var xhr = isFetchSupported && TransportOption == undefined ? undefined : new XHRWrapper(TransportOption != undefined ? new TransportOption() : getBestXHRTransport());\r\n    var transport = TransportOption != null && typeof TransportOption !== \"string\" ? new TransportOption() : (xhr == undefined ? new FetchTransport() : new XHRTransport());\r\n    var abortController = undefined;\r\n    var timeout = 0;\r\n    var currentState = WAITING;\r\n    var dataBuffer = \"\";\r\n    var lastEventIdBuffer = \"\";\r\n    var eventTypeBuffer = \"\";\r\n\r\n    var textBuffer = \"\";\r\n    var state = FIELD_START;\r\n    var fieldStart = 0;\r\n    var valueStart = 0;\r\n\r\n    var onStart = function (status, statusText, contentType, headers) {\r\n      if (currentState === CONNECTING) {\r\n        if (status === 200 && contentType != undefined && contentTypeRegExp.test(contentType)) {\r\n          currentState = OPEN;\r\n          wasActivity = Date.now();\r\n          retry = initialRetry;\r\n          es.readyState = OPEN;\r\n          var event = new ConnectionEvent(\"open\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onopen, event);\r\n        } else {\r\n          var message = \"\";\r\n          if (status !== 200) {\r\n            if (statusText) {\r\n              statusText = statusText.replace(/\\s+/g, \" \");\r\n            }\r\n            message = \"EventSource's response has a status \" + status + \" \" + statusText + \" that is not 200. Aborting the connection.\";\r\n          } else {\r\n            message = \"EventSource's response has a Content-Type specifying an unsupported type: \" + (contentType == undefined ? \"-\" : contentType.replace(/\\s+/g, \" \")) + \". Aborting the connection.\";\r\n          }\r\n          close();\r\n          var event = new ConnectionEvent(\"error\", {\r\n            status: status,\r\n            statusText: statusText,\r\n            headers: headers\r\n          });\r\n          es.dispatchEvent(event);\r\n          fire(es, es.onerror, event);\r\n          console.error(message);\r\n        }\r\n      }\r\n    };\r\n\r\n    var onProgress = function (textChunk) {\r\n      if (currentState === OPEN) {\r\n        var n = -1;\r\n        for (var i = 0; i < textChunk.length; i += 1) {\r\n          var c = textChunk.charCodeAt(i);\r\n          if (c === \"\\n\".charCodeAt(0) || c === \"\\r\".charCodeAt(0)) {\r\n            n = i;\r\n          }\r\n        }\r\n        var chunk = (n !== -1 ? textBuffer : \"\") + textChunk.slice(0, n + 1);\r\n        textBuffer = (n === -1 ? textBuffer : \"\") + textChunk.slice(n + 1);\r\n        if (textChunk !== \"\") {\r\n          wasActivity = Date.now();\r\n          textLength += textChunk.length;\r\n        }\r\n        for (var position = 0; position < chunk.length; position += 1) {\r\n          var c = chunk.charCodeAt(position);\r\n          if (state === AFTER_CR && c === \"\\n\".charCodeAt(0)) {\r\n            state = FIELD_START;\r\n          } else {\r\n            if (state === AFTER_CR) {\r\n              state = FIELD_START;\r\n            }\r\n            if (c === \"\\r\".charCodeAt(0) || c === \"\\n\".charCodeAt(0)) {\r\n              if (state !== FIELD_START) {\r\n                if (state === FIELD) {\r\n                  valueStart = position + 1;\r\n                }\r\n                var field = chunk.slice(fieldStart, valueStart - 1);\r\n                var value = chunk.slice(valueStart + (valueStart < position && chunk.charCodeAt(valueStart) === \" \".charCodeAt(0) ? 1 : 0), position);\r\n                if (field === \"data\") {\r\n                  dataBuffer += \"\\n\";\r\n                  dataBuffer += value;\r\n                } else if (field === \"id\") {\r\n                  lastEventIdBuffer = value;\r\n                } else if (field === \"event\") {\r\n                  eventTypeBuffer = value;\r\n                } else if (field === \"retry\") {\r\n                  initialRetry = parseDuration(value, initialRetry);\r\n                  retry = initialRetry;\r\n                } else if (field === \"heartbeatTimeout\") {\r\n                  heartbeatTimeout = parseDuration(value, heartbeatTimeout);\r\n                  if (timeout !== 0) {\r\n                    clearTimeout(timeout);\r\n                    timeout = setTimeout(function () {\r\n                      onTimeout();\r\n                    }, heartbeatTimeout);\r\n                  }\r\n                }\r\n              }\r\n              if (state === FIELD_START) {\r\n                if (dataBuffer !== \"\") {\r\n                  lastEventId = lastEventIdBuffer;\r\n                  if (eventTypeBuffer === \"\") {\r\n                    eventTypeBuffer = \"message\";\r\n                  }\r\n                  var event = new MessageEvent(eventTypeBuffer, {\r\n                    data: dataBuffer.slice(1),\r\n                    lastEventId: lastEventIdBuffer\r\n                  });\r\n                  es.dispatchEvent(event);\r\n                  if (eventTypeBuffer === \"open\") {\r\n                    fire(es, es.onopen, event);\r\n                  } else if (eventTypeBuffer === \"message\") {\r\n                    fire(es, es.onmessage, event);\r\n                  } else if (eventTypeBuffer === \"error\") {\r\n                    fire(es, es.onerror, event);\r\n                  }\r\n                  if (currentState === CLOSED) {\r\n                    return;\r\n                  }\r\n                }\r\n                dataBuffer = \"\";\r\n                eventTypeBuffer = \"\";\r\n              }\r\n              state = c === \"\\r\".charCodeAt(0) ? AFTER_CR : FIELD_START;\r\n            } else {\r\n              if (state === FIELD_START) {\r\n                fieldStart = position;\r\n                state = FIELD;\r\n              }\r\n              if (state === FIELD) {\r\n                if (c === \":\".charCodeAt(0)) {\r\n                  valueStart = position + 1;\r\n                  state = VALUE_START;\r\n                }\r\n              } else if (state === VALUE_START) {\r\n                state = VALUE;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    var onFinish = function (error) {\r\n      if (currentState === OPEN || currentState === CONNECTING) {\r\n        currentState = WAITING;\r\n        if (timeout !== 0) {\r\n          clearTimeout(timeout);\r\n          timeout = 0;\r\n        }\r\n        timeout = setTimeout(function () {\r\n          onTimeout();\r\n        }, retry);\r\n        retry = clampDuration(Math.min(initialRetry * 16, retry * 2));\r\n\r\n        es.readyState = CONNECTING;\r\n        var event = new ErrorEvent(\"error\", {error: error});\r\n        es.dispatchEvent(event);\r\n        fire(es, es.onerror, event);\r\n        if (error != undefined) {\r\n          console.error(error);\r\n        }\r\n      }\r\n    };\r\n\r\n    var close = function () {\r\n      currentState = CLOSED;\r\n      if (abortController != undefined) {\r\n        abortController.abort();\r\n        abortController = undefined;\r\n      }\r\n      if (timeout !== 0) {\r\n        clearTimeout(timeout);\r\n        timeout = 0;\r\n      }\r\n      es.readyState = CLOSED;\r\n    };\r\n\r\n    var onTimeout = function () {\r\n      timeout = 0;\r\n\r\n      if (currentState !== WAITING) {\r\n        if (!wasActivity && abortController != undefined) {\r\n          onFinish(new Error(\"No activity within \" + heartbeatTimeout + \" milliseconds.\" + \" \" + (currentState === CONNECTING ? \"No response received.\" : textLength + \" chars received.\") + \" \" + \"Reconnecting.\"));\r\n          if (abortController != undefined) {\r\n            abortController.abort();\r\n            abortController = undefined;\r\n          }\r\n        } else {\r\n          var nextHeartbeat = Math.max((wasActivity || Date.now()) + heartbeatTimeout - Date.now(), 1);\r\n          wasActivity = false;\r\n          timeout = setTimeout(function () {\r\n            onTimeout();\r\n          }, nextHeartbeat);\r\n        }\r\n        return;\r\n      }\r\n\r\n      wasActivity = false;\r\n      textLength = 0;\r\n      timeout = setTimeout(function () {\r\n        onTimeout();\r\n      }, heartbeatTimeout);\r\n\r\n      currentState = CONNECTING;\r\n      dataBuffer = \"\";\r\n      eventTypeBuffer = \"\";\r\n      lastEventIdBuffer = lastEventId;\r\n      textBuffer = \"\";\r\n      fieldStart = 0;\r\n      valueStart = 0;\r\n      state = FIELD_START;\r\n\r\n      // https://bugzilla.mozilla.org/show_bug.cgi?id=428916\r\n      // Request header field Last-Event-ID is not allowed by Access-Control-Allow-Headers.\r\n      var requestURL = url;\r\n      if (url.slice(0, 5) !== \"data:\" && url.slice(0, 5) !== \"blob:\") {\r\n        if (lastEventId !== \"\") {\r\n          // Remove the lastEventId parameter if it's already part of the request URL.\r\n          var i = url.indexOf(\"?\");\r\n          requestURL = i === -1 ? url : url.slice(0, i + 1) + url.slice(i + 1).replace(/(?:^|&)([^=&]*)(?:=[^&]*)?/g, function (p, paramName) {\r\n            return paramName === lastEventIdQueryParameterName ? '' : p;\r\n          });\r\n          // Append the current lastEventId to the request URL.\r\n          requestURL += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + lastEventIdQueryParameterName +\"=\" + encodeURIComponent(lastEventId);\r\n        }\r\n      }\r\n      var withCredentials = es.withCredentials;\r\n      var requestHeaders = {};\r\n      requestHeaders[\"Accept\"] = \"text/event-stream\";\r\n      var headers = es.headers;\r\n      if (headers != undefined) {\r\n        for (var name in headers) {\r\n          if (Object.prototype.hasOwnProperty.call(headers, name)) {\r\n            requestHeaders[name] = headers[name];\r\n          }\r\n        }\r\n      }\r\n      try {\r\n        abortController = transport.open(xhr, onStart, onProgress, onFinish, requestURL, withCredentials, requestHeaders);\r\n      } catch (error) {\r\n        close();\r\n        throw error;\r\n      }\r\n    };\r\n\r\n    es.url = url;\r\n    es.readyState = CONNECTING;\r\n    es.withCredentials = withCredentials;\r\n    es.headers = headers;\r\n    es._close = close;\r\n\r\n    onTimeout();\r\n  }\r\n\r\n  EventSourcePolyfill.prototype = Object.create(EventTarget.prototype);\r\n  EventSourcePolyfill.prototype.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.prototype.OPEN = OPEN;\r\n  EventSourcePolyfill.prototype.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.close = function () {\r\n    this._close();\r\n  };\r\n\r\n  EventSourcePolyfill.CONNECTING = CONNECTING;\r\n  EventSourcePolyfill.OPEN = OPEN;\r\n  EventSourcePolyfill.CLOSED = CLOSED;\r\n  EventSourcePolyfill.prototype.withCredentials = undefined;\r\n\r\n  var R = NativeEventSource\r\n  if (XMLHttpRequest != undefined && (NativeEventSource == undefined || !(\"withCredentials\" in NativeEventSource.prototype))) {\r\n    // Why replace a native EventSource ?\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=444328\r\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=831392\r\n    // https://code.google.com/p/chromium/issues/detail?id=260144\r\n    // https://code.google.com/p/chromium/issues/detail?id=225654\r\n    // ...\r\n    R = EventSourcePolyfill;\r\n  }\r\n\r\n  (function (factory) {\r\n    if ( true && typeof module.exports === \"object\") {\r\n      var v = factory(exports);\r\n      if (v !== undefined) module.exports = v;\r\n    }\r\n    else if (true) {\r\n      !(__WEBPACK_AMD_DEFINE_ARRAY__ = [exports], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\r\n    }\r\n    else {}\r\n  })(function (exports) {\r\n    exports.EventSourcePolyfill = EventSourcePolyfill;\r\n    exports.NativeEventSource = NativeEventSource;\r\n    exports.EventSource = R;\r\n  });\r\n}(typeof globalThis === 'undefined' ? (typeof window !== 'undefined' ? window : typeof self !== 'undefined' ? self : this) : globalThis));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/event-source-polyfill/src/eventsource.js\n"));

/***/ })

}]);