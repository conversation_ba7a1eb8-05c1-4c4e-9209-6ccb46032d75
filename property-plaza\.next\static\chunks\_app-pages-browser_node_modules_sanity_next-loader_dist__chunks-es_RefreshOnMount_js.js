"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_dist__chunks-es_RefreshOnMount_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnMount.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnMount.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RefreshOnMount; }\n/* harmony export */ });\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation.js */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nfunction RefreshOnMount() {\n  const router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)(), [mounted, mount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(() => !0, !1);\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    mounted || (mount(), router.refresh());\n  }, [mounted, router]), null;\n}\nRefreshOnMount.displayName = \"RefreshOnMount\";\n\n//# sourceMappingURL=RefreshOnMount.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L25leHQtbG9hZGVyL2Rpc3QvX2NodW5rcy1lcy9SZWZyZXNoT25Nb3VudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDRDtBQUM5QztBQUNBLGlCQUFpQiw2REFBUyx1QkFBdUIsaURBQVU7QUFDM0QsU0FBUyxnREFBUztBQUNsQjtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9uZXh0LWxvYWRlci9kaXN0L19jaHVua3MtZXMvUmVmcmVzaE9uTW91bnQuanM/ZmEwZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uLmpzXCI7XG5pbXBvcnQgeyB1c2VSZWR1Y2VyLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIFJlZnJlc2hPbk1vdW50KCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKSwgW21vdW50ZWQsIG1vdW50XSA9IHVzZVJlZHVjZXIoKCkgPT4gITAsICExKTtcbiAgcmV0dXJuIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbW91bnRlZCB8fCAobW91bnQoKSwgcm91dGVyLnJlZnJlc2goKSk7XG4gIH0sIFttb3VudGVkLCByb3V0ZXJdKSwgbnVsbDtcbn1cblJlZnJlc2hPbk1vdW50LmRpc3BsYXlOYW1lID0gXCJSZWZyZXNoT25Nb3VudFwiO1xuZXhwb3J0IHtcbiAgUmVmcmVzaE9uTW91bnQgYXMgZGVmYXVsdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVJlZnJlc2hPbk1vdW50LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnMount.js\n"));

/***/ })

}]);