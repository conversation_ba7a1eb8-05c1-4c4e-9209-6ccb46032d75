"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_sanity_next-loader_dist__chunks-es_RefreshOnReconnect_js"],{

/***/ "(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnReconnect.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnReconnect.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ RefreshOnReconnect; }\n/* harmony export */ });\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/navigation.js */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nfunction RefreshOnReconnect() {\n  const router = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_0__.useRouter)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    const controller = new AbortController(), { signal } = controller;\n    return window.addEventListener(\"online\", () => router.refresh(), { passive: !0, signal }), () => controller.abort();\n  }, [router]), null;\n}\nRefreshOnReconnect.displayName = \"RefreshOnReconnect\";\n\n//# sourceMappingURL=RefreshOnReconnect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9Ac2FuaXR5L25leHQtbG9hZGVyL2Rpc3QvX2NodW5rcy1lcy9SZWZyZXNoT25SZWNvbm5lY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQStDO0FBQ2I7QUFDbEM7QUFDQSxpQkFBaUIsNkRBQVM7QUFDMUIsU0FBUyxnREFBUztBQUNsQixnREFBZ0QsU0FBUztBQUN6RCx1RUFBdUUscUJBQXFCO0FBQzVGLEdBQUc7QUFDSDtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvQHNhbml0eS9uZXh0LWxvYWRlci9kaXN0L19jaHVua3MtZXMvUmVmcmVzaE9uUmVjb25uZWN0LmpzPzJlN2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvbi5qc1wiO1xuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiBSZWZyZXNoT25SZWNvbm5lY3QoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICByZXR1cm4gdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBjb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpLCB7IHNpZ25hbCB9ID0gY29udHJvbGxlcjtcbiAgICByZXR1cm4gd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJvbmxpbmVcIiwgKCkgPT4gcm91dGVyLnJlZnJlc2goKSwgeyBwYXNzaXZlOiAhMCwgc2lnbmFsIH0pLCAoKSA9PiBjb250cm9sbGVyLmFib3J0KCk7XG4gIH0sIFtyb3V0ZXJdKSwgbnVsbDtcbn1cblJlZnJlc2hPblJlY29ubmVjdC5kaXNwbGF5TmFtZSA9IFwiUmVmcmVzaE9uUmVjb25uZWN0XCI7XG5leHBvcnQge1xuICBSZWZyZXNoT25SZWNvbm5lY3QgYXMgZGVmYXVsdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPVJlZnJlc2hPblJlY29ubmVjdC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@sanity/next-loader/dist/_chunks-es/RefreshOnReconnect.js\n"));

/***/ })

}]);