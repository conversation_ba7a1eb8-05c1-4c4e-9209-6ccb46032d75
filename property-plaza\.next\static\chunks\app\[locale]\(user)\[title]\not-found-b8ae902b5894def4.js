(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7294],{13666:(e,t,n)=>{Promise.resolve().then(n.bind(n,45626)),Promise.resolve().then(n.bind(n,48882)),Promise.resolve().then(n.bind(n,78830)),Promise.resolve().then(n.t.bind(n,6874,23))},26038:(e,t,n)=>{"use strict";function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(null,arguments)}n.d(t,{_:()=>o})},45626:(e,t,n)=>{"use strict";n.d(t,{default:()=>u});var o=n(26038),r=n(6874),l=n.n(r),a=n(35695),i=n(12115),c=n(85808),u=(0,i.forwardRef)(function(e,t){let{defaultLocale:n,href:r,locale:u,localeCookie:s,onClick:f,prefetch:h,unprefixed:d,...p}=e,m=(0,c.A)(),v=u!==m,b=u||m,g=function(){let[e,t]=(0,i.useState)();return(0,i.useEffect)(()=>{t(window.location.host)},[]),e}(),k=g&&d&&(d.domains[g]===b||!Object.keys(d.domains).includes(g)&&m===n&&!u)?d.pathname:r,w=(0,a.usePathname)();return v&&(h&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),h=!1),i.createElement(l(),(0,o._)({ref:t,href:k,hrefLang:v?u:void 0,onClick:function(e){(function(e,t,n,o){if(!e||o===n||null==o||!t)return;let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:l,...a}=e;a.path||(a.path=""!==r?r:"/");let i="".concat(l,"=").concat(o,";");for(let[e,t]of Object.entries(a))i+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(i+="="+t),i+=";";document.cookie=i})(s,w,m,u),f&&f(e)},prefetch:h},p))})},48882:(e,t,n)=>{"use strict";n.d(t,{default:()=>f});var o=n(26038),r=n(35695),l=n(12115),a=n(85808);function i(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function c(e,t){let n;return"string"==typeof e?n=u(t,e):(n={...e},e.pathname&&(n.pathname=u(t,e.pathname))),n}function u(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}n(87358);var s=n(45626);let f=(0,l.forwardRef)(function(e,t){let{href:n,locale:u,localeCookie:f,localePrefixMode:h,prefix:d,...p}=e,m=(0,r.usePathname)(),v=(0,a.A)(),b=u!==v,[g,k]=(0,l.useState)(()=>i(n)&&("never"!==h||b)?c(n,d):n);return(0,l.useEffect)(()=>{m&&k(function(e,t){var n,o;let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,l=arguments.length>3?arguments[3]:void 0,a=arguments.length>4?arguments[4]:void 0;if(!i(e))return e;let u=(n=a,(o=l)===n||o.startsWith("".concat(n,"/")));return(t!==r||u)&&null!=a?c(e,a):e}(n,u,v,m,d))},[v,n,u,m,d]),l.createElement(s.default,(0,o._)({ref:t,href:g,locale:u,localeCookie:f},p))});f.displayName="ClientLink"},78830:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var o=n(26038),r=n(12115),l=n(61787);function a(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return r.createElement(l.IntlProvider,(0,o._)({locale:t},n))}},85808:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var o=n(35695),r=n(97526);let l="locale";function a(){let e,t=(0,o.useParams)();try{e=(0,r.useLocale)()}catch(n){if("string"!=typeof(null==t?void 0:t[l]))throw n;e=t[l]}return e}}},e=>{e.O(0,[1551,8441,5964,7358],()=>e(e.s=13666)),_N_E=e.O()}]);