(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7062],{1228:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}},26038:(e,t,r)=>{"use strict";function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(null,arguments)}r.d(t,{_:()=>s})},41477:(e,t,r)=>{Promise.resolve().then(r.bind(r,73347)),Promise.resolve().then(r.bind(r,45626)),Promise.resolve().then(r.bind(r,48882))},45626:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var s=r(26038),a=r(6874),n=r.n(a),i=r(35695),l=r(12115),o=r(85808),c=(0,l.forwardRef)(function(e,t){let{defaultLocale:r,href:a,locale:c,localeCookie:u,onClick:d,prefetch:m,unprefixed:f,...h}=e,p=(0,o.A)(),g=c!==p,b=c||p,x=function(){let[e,t]=(0,l.useState)();return(0,l.useEffect)(()=>{t(window.location.host)},[]),e}(),v=x&&f&&(f.domains[x]===b||!Object.keys(f.domains).includes(x)&&p===r&&!c)?f.pathname:a,y=(0,i.usePathname)();return g&&(m&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),m=!1),l.createElement(n(),(0,s._)({ref:t,href:v,hrefLang:g?c:void 0,onClick:function(e){(function(e,t,r,s){if(!e||s===r||null==s||!t)return;let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:n,...i}=e;i.path||(i.path=""!==a?a:"/");let l="".concat(n,"=").concat(s,";");for(let[e,t]of Object.entries(i))l+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(l+="="+t),l+=";";document.cookie=l})(u,y,p,c),d&&d(e)},prefetch:m},h))})},48882:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(26038),a=r(35695),n=r(12115),i=r(85808);function l(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function o(e,t){let r;return"string"==typeof e?r=c(t,e):(r={...e},e.pathname&&(r.pathname=c(t,e.pathname))),r}function c(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}r(87358);var u=r(45626);let d=(0,n.forwardRef)(function(e,t){let{href:r,locale:c,localeCookie:d,localePrefixMode:m,prefix:f,...h}=e,p=(0,a.usePathname)(),g=(0,i.A)(),b=c!==g,[x,v]=(0,n.useState)(()=>l(r)&&("never"!==m||b)?o(r,f):r);return(0,n.useEffect)(()=>{p&&v(function(e,t){var r,s;let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;if(!l(e))return e;let c=(r=i,(s=n)===r||s.startsWith("".concat(r,"/")));return(t!==a||c)&&null!=i?o(e,i):e}(r,c,g,p,f))},[g,r,c,p,f]),n.createElement(u.default,(0,s._)({ref:t,href:x,locale:c,localeCookie:d},h))});d.displayName="ClientLink"},53580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>d});var s=r(12115);let a=0,n=new Map,i=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},l=[],o={toasts:[]};function c(e){o=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(o,e),l.forEach(e=>{e(o)})}function u(e){let{...t}=e,r=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:r});return c({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function d(){let[e,t]=s.useState(o);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,r)=>{"use strict";r.d(t,{ZV:()=>c,cn:()=>l,gT:()=>u,jW:()=>m,lF:()=>h,q7:()=>f,tT:()=>p,vv:()=>o,yv:()=>d});var s=r(52596),a=r(82940),n=r.n(a),i=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,s.$)(t))}r(87358);let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat((e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}})(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2*("IDR"!=t)}).format(e)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function u(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function d(e){let t=n()(e),r=n()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function h(e,t){return e.some(e=>t.includes(e))}let p=e=>e.charAt(0).toUpperCase()+e.slice(1)},73347:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(95155),a=r(12115),n=r(27043),i=r(97168),l=r(66766),o=r(53580),c=r(1228);function u(){let e=(0,n.useTranslations)("ContactUs"),{toast:t}=(0,o.dj)(),[r,u]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[d,m]=(0,a.useState)(!1),f=async r=>{r.preventDefault(),m(!0);try{await new Promise(e=>setTimeout(e,1e3)),t({description:e("messageSent")}),u({name:"",email:"",subject:"",message:""})}catch(e){t({variant:"destructive",description:"Er is iets misgegaan. Probeer het later opnieuw."})}finally{m(!1)}};return(0,s.jsxs)("div",{className:"w-full bg-white",children:[(0,s.jsxs)("section",{"aria-label":"Contact Hero",className:"relative w-full h-[300px]",children:[(0,s.jsx)(l.default,{src:c.default,alt:"Contact Property Plaza",fill:!0,className:"object-cover",priority:!0}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center px-4",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:e("title")}),(0,s.jsx)("p",{className:"text-xl text-white max-w-3xl mx-auto",children:e("subtitle")})]})})]}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:(0,s.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:e("sendMessage")}),(0,s.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:e("nameField")}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.name,onChange:e=>u(t=>({...t,name:e.target.value}))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:e("emailField")}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.email,onChange:e=>u(t=>({...t,email:e.target.value}))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:e("subjectField")}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.subject,onChange:e=>u(t=>({...t,subject:e.target.value}))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:e("messageField")}),(0,s.jsx)("textarea",{id:"message",name:"message",rows:6,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.message,onChange:e=>u(t=>({...t,message:e.target.value}))})]}),(0,s.jsx)("div",{children:(0,s.jsx)(i.$,{type:"submit",variant:"default-seekers",className:"w-full md:w-auto",disabled:d,children:d?"Verzenden...":e("submitButton")})})]})]})})})]})}},85808:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(35695),a=r(97526);let n="locale";function i(){let e,t=(0,s.useParams)();try{e=(0,a.useLocale)()}catch(r){if("string"!=typeof(null==t?void 0:t[n]))throw r;e=t[n]}return e}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>u});var s=r(95155),a=r(12115),n=r(66634),i=r(74466),l=r(53999),o=r(51154);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:u=!1,loading:d=!1,...m}=e,f=u?n.DX:"button";return(0,s.jsx)(f,{className:(0,l.cn)(c({variant:a,size:i,className:r})),ref:t,disabled:d||m.disabled,...m,children:d?(0,s.jsx)(o.A,{className:(0,l.cn)("h-4 w-4 animate-spin")}):m.children})});u.displayName="Button"}},e=>{e.O(0,[586,1551,3903,7043,6766,8441,5964,7358],()=>e(e.s=41477)),_N_E=e.O()}]);