(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[803],{1806:(e,a,t)=>{"use strict";t.d(a,{A:()=>u});var l=t(4518),s=t(12115),n=t(74463),r=t(53999),i=t(1221),o=t(27043),c=t(48251),d=t(14666);function u(){let e=(0,o.useTranslations)(),a=(0,l.o)(e=>e),[t,u]=(0,s.useState)(!1),[m,g]=(0,s.useState)(null),x=(0,i.useRouter)(),p=[{content:e("seeker.listing.category.villa"),id:"1",value:c.BT.villas},{content:e("seeker.listing.category.apartment"),id:"2",value:c.BT.apartment},{content:e("seeker.listing.category.guestHouse"),id:"3",value:c.BT.rooms},{content:e("seeker.listing.category.commercial"),id:"4",value:c.BT.commercialSpace},{content:e("seeker.listing.category.cafeAndRestaurent"),id:"5",value:c.BT.cafeOrRestaurants},{content:e("seeker.listing.category.office"),id:"6",value:c.BT.offices},{content:e("seeker.listing.category.shops"),id:"7",value:c.BT.shops},{content:e("seeker.listing.category.shellAndCore"),id:"8",value:c.BT.shellAndCore},{content:e("seeker.listing.category.land"),id:"9",value:c.BT.lands}],h=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],v={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},f=(0,s.useMemo)(()=>!a.query||t?h:h.filter(e=>{let t=e.name.replace(", Bali","").toLowerCase(),l=a.query.toLowerCase();return!!t.includes(l)||(v[e.value]||[]).some(e=>e.toLowerCase().includes(l))}),[a.query,t]);return{seekersSearch:a,handleSetQuery:e=>{let t=e.split(","),l=e.length;t.length>3&&","==e.charAt(l-1)||a.setQuery(e)},handleSetType:e=>{(!(a.propertyType.length>=3)||a.propertyType.includes(e))&&a.setPropertyType(e)},propertyType:p,handleSearch:(e,t)=>{e&&a.setQuery(e),t&&a.setPropertyTypeFromArray(t);let l=e||a.query,s=t||a.propertyType;""!==a.activeSearch.query&&a.setSearchHistory({propertyType:a.activeSearch.propertyType,query:a.activeSearch.query}),a.setActiveSearch({query:l,propertyType:s});let i=(0,r.jW)(l);x.push(n.Eq+"/"+(i||"all")+"?"+d.Ix.type+"="+(s.toString()||"all"))},propertyTypeFormatHelper:e=>e.map(e=>{let a=p.find(a=>a.value==e);return null==a?void 0:a.content}),locations:h,banjars:v,getMatchingBanjars:e=>{let t=a.query;return t?(v[e]||[]).filter(e=>e.toLowerCase().includes(t.toLowerCase())):[]},showBanjars:t,setShowBanjars:u,selectedLocation:m,setSelectedLocation:g,handleSelectLocation:e=>{u(!0),g(e),a.setQuery(e)},handleBackToLocations:()=>{u(!1);let e=a.query.replace(m||"","");a.setQuery(e)},handleSetBanjar:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l=a.query.split(",").filter(e=>""!==e.trim()&&e!==m);if(l.includes(e)){let t=l.filter(a=>a!==e);a.setQuery(t.toString());return}if(!(l.length>=3)||""===l[l.length-1]){if(t){let a=l.length;l[a-1]=e}else l.push(e);a.setQuery(l.toString())}},filteredLocations:f}}},28113:(e,a,t)=>{"use strict";t.d(a,{i:()=>l});let l=(0,t(88693).vt)()(e=>({focusedListing:null,highlightedListing:null,zoom:13,mapVariantId:0,viewMode:"list",setViewMode:a=>e(()=>({viewMode:a})),setMapVariantId:a=>e(()=>({mapVariantId:a})),setZoom:a=>e(()=>({zoom:a})),setFocusedListing:a=>e(()=>({focusedListing:a})),setHighlightedListing:a=>e(()=>({highlightedListing:a}))}))},32059:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var l=t(95155),s=t(53999);function n(e){return(0,l.jsx)("div",{...e,ref:e.ref,className:(0,s.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},39453:(e,a,t)=>{"use strict";t.d(a,{B:()=>d});var l=t(64237),s=t(19373),n=t(48332),r=t(48251),i=t(40054),o=t.n(i),c=t(12673);function d(e){var a;let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=(0,n.P)(),d=null==i||null==(a=i.data)?void 0:a.data,u=["filtered-seekers-listing",e],{failureCount:m,...g}=(0,s.I)({queryKey:u,queryFn:async()=>{var a,t,s,n;let i=e.max_price||(null==d?void 0:d.priceRange.max),u=e.min_price||(null==d?void 0:d.priceRange.min)||1,m=e.building_largest||(null==d?void 0:d.buildingSizeRange.max),g=e.building_smallest||(null==d?void 0:d.buildingSizeRange.min)||1,x=e.land_largest||(null==d?void 0:d.landSizeRange.max),p=e.land_smallest||(null==d?void 0:d.landSizeRange.min)||1,h=e.garden_largest||(null==d?void 0:d.gardenSizeRange.max),v=e.garden_smallest||(null==d?void 0:d.gardenSizeRange.min)||1,f=e.area;(null==(a=e.area)?void 0:a.zoom)==c.wJ.toString()&&(f=void 0);let y=(null==(t=e.type)?void 0:t.includes("all"))?void 0:o().uniq(null==(s=e.type)?void 0:s.flatMap(e=>e!==r.BT.commercialSpace?e:[r.BT.cafeOrRestaurants,r.BT.shops,r.BT.offices])),j={...e,type:y,search:"all"==e.search||null==(n=e.search)?void 0:n.replaceAll(" , ",", "),min_price:u,max_price:i,building_largest:m,building_smallest:g,land_largest:x,land_smallest:p,garden_largest:h,garden_smallest:v,area:f||void 0,property_of_view:e.property_of_view};return e.min_price&&e.min_price!=(null==d?void 0:d.priceRange.min)||i!=(null==d?void 0:d.priceRange.max)||(j.max_price=void 0,j.min_price=void 0),e.building_smallest&&e.building_smallest!=(null==d?void 0:d.buildingSizeRange.min)||m!=(null==d?void 0:d.buildingSizeRange.max)||(j.building_largest=void 0,j.building_smallest=void 0),e.land_smallest&&e.land_smallest!=(null==d?void 0:d.landSizeRange.min)||x!=(null==d?void 0:d.landSizeRange.max)||(j.land_largest=void 0,j.land_smallest=void 0),e.garden_smallest&&e.garden_smallest!=(null==d?void 0:d.gardenSizeRange.min)||h!=(null==d?void 0:d.gardenSizeRange.max)||(j.garden_largest=void 0,j.garden_smallest=void 0),await (0,l.lx)(j)},enabled:t,retry:!1});return{query:g,filterQueryKey:u}}},49026:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>o,TN:()=>c});var l=t(95155),s=t(12115),n=t(74466),r=t(53999);let i=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-xs [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-neutral",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=s.forwardRef((e,a)=>{let{className:t,variant:s,...n}=e;return(0,l.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(i({variant:s}),t),...n})});o.displayName="Alert",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium tracking-tight",t),...s})}).displayName="AlertTitle";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",t),...s})});c.displayName="AlertDescription"},51610:(e,a,t)=>{Promise.resolve().then(t.bind(t,96012)),Promise.resolve().then(t.bind(t,64525)),Promise.resolve().then(t.bind(t,72261)),Promise.resolve().then(t.bind(t,45626)),Promise.resolve().then(t.bind(t,48882))},56040:(e,a,t)=>{"use strict";t.d(a,{A:()=>o});var l=t(95155),s=t(27737),n=t(26828),r=t(28113),i=t(27043);function o(){let{highlightedListing:e}=(0,r.i)(),{total:a,isLoading:t}=(0,n.t)(),o=(0,i.useTranslations)("seeker");return(0,l.jsx)("h3",{className:"font-semibold max-sm:text-xs text-xl text-seekers-text",children:t?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(s.E,{className:"w-40 h-6"})}):0==a?o("listing.misc.searchNoResultCount"):o("listing.misc.searchResultCount",{count:a})})}},64525:(e,a,t)=>{"use strict";t.d(a,{default:()=>j});var l=t(95155),s=t(57340),n=t(27043),r=t(53999),i=t(12115),o=t(66634),c=t(33096);let d=i.forwardRef((e,a)=>{let{...t}=e;return(0,l.jsx)("nav",{ref:a,"aria-label":"breadcrumb",...t})});d.displayName="Breadcrumb";let u=i.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("ol",{ref:a,className:(0,r.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...s})});u.displayName="BreadcrumbList";let m=i.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("li",{ref:a,className:(0,r.cn)("inline-flex items-center gap-1.5",t),...s})});m.displayName="BreadcrumbItem",i.forwardRef((e,a)=>{let{asChild:t,className:s,...n}=e,i=t?o.DX:"a";return(0,l.jsx)(i,{ref:a,className:(0,r.cn)("transition-colors hover:text-foreground",s),...n})}).displayName="BreadcrumbLink",i.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,l.jsx)("span",{ref:a,role:"link","aria-disabled":"true","aria-current":"page",className:(0,r.cn)("font-normal text-foreground",t),...s})}).displayName="BreadcrumbPage";let g=e=>{let{children:a,className:t,...s}=e;return(0,l.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,r.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",t),...s,children:null!=a?a:(0,l.jsx)(c.vKP,{})})};g.displayName="BreadcrumbSeparator";var x=t(32059),p=t(66460),h=t(56040),v=t(6874),f=t.n(v),y=t(1806);function j(e){let{query:a,types:t,conversions:i}=e,o=(0,n.useTranslations)("seeker"),{propertyTypeFormatHelper:c}=(0,y.A)();return(0,l.jsxs)(x.A,{className:(0,r.cn)("max-sm:hidden flex max-sm:flex-col items-center space-y-0 h-[100px] max-lg:!h-[80px]  gap-4 sticky md:top-[90px] lg:top-[104px] xl:top-[114px] z-[1] bg-white"),children:[(0,l.jsxs)("div",{className:"flex-grow space-y-2",children:[(0,l.jsx)(h.A,{}),(0,l.jsx)(d,{className:" hidden md:block ",children:(0,l.jsxs)(u,{className:"space-x-4 sm:gap-0 flex-nowrap",children:[(0,l.jsx)(m,{className:"text-seekers-text font-medium text-sm",children:(0,l.jsx)(f(),{href:"/",className:"flex gap-2.5 items-center",children:(0,l.jsx)(s.A,{className:"w-4 h-4",strokeWidth:1})})}),(0,l.jsx)(g,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),"all"==a?(0,l.jsx)(l.Fragment,{}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(m,{className:"capitalize text-seekers-text font-medium text-sm",children:a.replaceAll("-"," ").replaceAll("--"," ")}),(0,l.jsx)(g,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"})]}),(0,l.jsx)(m,{className:"text-seekers-text font-semibold text-sm line-clamp-1",children:t.split(",").includes("all")?(0,l.jsx)(l.Fragment,{children:o("misc.allProperty")}):(0,l.jsx)(l.Fragment,{children:c(t.replaceAll("-"," ").replaceAll("--"," ").split(",")).toString().replaceAll(",",", ")})})]})})]}),(0,l.jsx)(p.default,{conversions:i})]})}},67360:(e,a,t)=>{"use strict";t.d(a,{A:()=>g});var l=t(95155),s=t(53999),n=t(49026),r=t(28113),i=t(6874),o=t.n(i),c=t(97168),d=t(27043),u=t(74463),m=t(46797);function g(e){let{isSubscribe:a,className:t}=e,i=(0,r.i)(e=>e.viewMode),{email:g}=(0,m.k)(e=>e.seekers),x=(0,d.useTranslations)("seeker");return(0,l.jsx)(l.Fragment,{children:a?(0,l.jsx)(l.Fragment,{}):(0,l.jsx)(n.Fc,{className:(0,s.cn)("max-w-xs bg-[#B48B5599] font-semibold text-white  shadow-md absolute   z-10","map"==i?"top-4 right-4 max-sm:right-1/2 max-sm:translate-x-1/2":"top-16 right-1/2 translate-x-1/2",t),children:(0,l.jsxs)(n.TN,{className:"text-xs",children:[x("misc.subscibePropgram.searchPage.description")," "," ",(0,l.jsx)(c.$,{asChild:!0,variant:"link",size:"sm",className:"p-0 h-fit w-fit text-white underline",children:(0,l.jsx)(o(),{href:g?u.ch:u.jd,children:x("cta.subscribe")})})]})})})}},72261:(e,a,t)=>{"use strict";t.d(a,{default:()=>R});var l=t(95155),s=t(26828),n=t(19988),r=t(12115),i=t(31917),o=t(97168),c=t(54416),d=t(28113),u=t(76485),m=t(53999);function g(e){let{data:a,conversions:t}=e,[s,r]=(0,n.vH)(),{focusedListing:g,setFocusedListing:x,highlightedListing:p}=(0,d.i)();return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(n.J8,{position:{lat:a.geolocation[0],lng:a.geolocation[1]},clickable:!0,onClick:()=>x(a.code),ref:s,zIndex:p==a.code?10:1,children:(0,l.jsx)("div",{className:(0,m.cn)(p==a.code?"w-12 h-12 bg-seekers-text text-white":"w-6 h-6 bg-white","flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border"),children:(0,l.jsx)(u.A,{category:a.category||"",className:p==a.code?"":"!w-4 !h-4 text-seekers-primary"})})},a.code),g==a.code&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(n.Fu,{anchor:r,onClose:()=>x(null),headerDisabled:!0,style:{overflow:"auto !important",borderRadius:"24px !important"},className:"!rounded-xl",minWidth:240,children:(0,l.jsxs)(i.Iq,{conversion:t,data:a,children:[(0,l.jsx)(i.yM,{forceLazyloading:!0,containerClassName:"!rounded-b-none",extraHeaderAction:(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(o.$,{variant:"secondary",className:"bg-white rounded-full !h-6 !w-6 hover:bg-white/80",size:"icon",onClick:()=>x(null),children:(0,l.jsx)(c.A,{className:"!w-3 !h-3"})})})}),(0,l.jsxs)("div",{className:"space-y-2 px-2 pb-2",children:[(0,l.jsx)(i.LG,{className:"leading-6"}),(0,l.jsx)(i.f1,{}),(0,l.jsx)(i.CQ,{})]})]})})})]})}var x=t(83232);let p=e=>{let{data:a,onClick:t,setMarkerRef:s}=e,{focusedListing:i,setFocusedListing:o}=(0,d.i)(),c=(0,r.useCallback)(()=>{t(a),o(a.code)},[t,a,o]),g=(0,r.useCallback)(e=>s(e,a.code),[s,a.code]);return(0,l.jsx)(n.J8,{position:{lat:a.geolocation[0],lng:a.geolocation[1]},ref:g,onClick:c,children:(0,l.jsx)("div",{className:(0,m.cn)(i==a.code?"w-16 h-16 bg-seekers-text text-white":"hover:w-10 hover:h-10 w-8 h-8 bg-white","flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border"),children:(0,l.jsx)(u.A,{category:a.category||"",className:i==a.code?"":"!w-4 !h-4 text-seekers-primary"})})})},h=e=>{let{data:a,conversions:t}=e,[s,u]=(0,r.useState)({}),{focusedListing:m,setFocusedListing:g}=(0,d.i)(),h=(0,d.i)(e=>e.viewMode),[v,f]=(0,r.useState)(),y=(0,n.ko)(),j=(0,r.useMemo)(()=>y&&"list"!==h?new x.w1({map:y,renderer:{render:e=>{let a=document.createElement("div");return a.style.width="36px",a.style.height="36px",a.style.backgroundColor="#B48B55",a.style.borderRadius="50%",a.style.display="flex",a.style.alignItems="center",a.style.justifyContent="center",a.style.color="#FFFFFF",a.style.fontWeight="bold",a.style.fontSize="14px",a.textContent=e.count.toString(),new google.maps.marker.AdvancedMarkerElement({position:e.position,content:a})}}}):null,[y,h]);(0,r.useEffect)(()=>{if(j)return null==j||j.clearMarkers(),j.addMarkers(Object.values(s)),()=>{j.removeMarkers(Object.values(s))}},[j,s,h]);let w=(0,r.useCallback)((e,a)=>{u(t=>{if(e&&t[a]||!e&&!t[a])return t;if(e)return{...t,[a]:e};{let{[a]:e,...l}=t;return l}})},[]),b=(0,r.useCallback)(()=>{g(null)},[g]),N=(0,r.useCallback)(e=>{g(e.code),f(e)},[g]);return(0,l.jsx)(l.Fragment,{children:a.map(e=>(0,l.jsxs)(r.Fragment,{children:[(0,l.jsx)(p,{data:e,onClick:N,setMarkerRef:w},e.code),m==e.code&&(0,l.jsx)(n.Fu,{anchor:s[e.code],onCloseClick:b,headerDisabled:!0,style:{overflow:"auto !important",borderRadius:"24px !important"},className:"!rounded-xl",minWidth:240,maxWidth:240,children:(0,l.jsxs)(i.Iq,{data:e,conversion:t,children:[(0,l.jsx)(i.yM,{forceLazyloading:!0,containerClassName:"!rounded-b-none",extraHeaderAction:(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(o.$,{variant:"secondary",className:"bg-white rounded-full !h-6 !w-6 hover:bg-white/80",size:"icon",onClick:()=>g(null),children:(0,l.jsx)(c.A,{className:"!w-3 !h-3"})})})}),(0,l.jsxs)("div",{className:"space-y-2 px-2 pb-2",children:[(0,l.jsx)(i.LG,{className:"leading-6"}),(0,l.jsx)(i.f1,{}),(0,l.jsx)(i.CQ,{})]})]})})]},e.code))})};var v=t(43782),f=t(27247),y=t(67360),j=t(53580),w=t(14666),b=t(46797),N=t(74810);function k(e){let{conversions:a}=e,{createMultipleQueryString:t,searchParams:i}=(0,f.A)(),{data:o}=(0,s.t)(),{seekers:c}=(0,b.k)(),u=(0,n.ko)(),{setFocusedListing:m}=(0,d.i)(),[x,p]=(0,r.useState)(),k=(0,v.d)(x),[_,B]=(0,r.useState)(!1),[T,S]=(0,r.useState)(12),{toast:C}=(0,j.dj)();return(0,r.useEffect)(()=>{if(o.length>1){let e=o[0];null==u||u.moveCamera({center:{lat:e.geolocation[0],lng:e.geolocation[1]}})}},[o]),(0,r.useEffect)(()=>{if(null==k||"list"==i.get(w.Ix.viewMode)||null==i.get(w.Ix.viewMode))return;let e={name:"lat",value:k[0].toString()};t([e,{name:"lng",value:k[1].toString()},{name:w.Ix.zoom,value:k[2].toString()}])},[k]),(0,l.jsxs)("div",{className:"rounded-lg overflow-hidden relative w-full h-full",children:[_&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(y.A,{})}),(0,l.jsx)(n.T5,{reuseMaps:!0,mapId:"7c91273179940241",style:{width:"100%",height:"100%"},mapTypeControl:!1,fullscreenControl:!1,defaultZoom:12,defaultCenter:{lat:-8.639736,lng:115.1341357},maxZoom:c.accounts.zoomFeature.max,minZoom:c.accounts.zoomFeature.min,disableDefaultUI:!0,onDragend:e=>{if(e.map.getCenter()){var a,t;p([null==(a=e.map.getCenter())?void 0:a.lat(),null==(t=e.map.getCenter())?void 0:t.lng(),e.map.getZoom()]),m(null)}},onZoomChanged:e=>{var a,t;e.detail.zoom>=c.accounts.zoomFeature.max&&T!==e.detail.zoom&&c.accounts.membership===N.U$.free?B(!0):B(!1),p([null==(a=e.map.getCenter())?void 0:a.lat(),null==(t=e.map.getCenter())?void 0:t.lng(),e.map.getZoom()]),S(e.map.getZoom()),m(null)},children:"list"==i.get(w.Ix.viewMode)||null==i.get(w.Ix.viewMode)?o.map(e=>(0,l.jsx)(g,{conversions:a,data:e},e.code)):(0,l.jsx)(h,{conversions:a,data:o})})]})}var _=t(42355),B=t(13052),T=t(63578),S=t(15968),C=t(27043),A=t(66460),F=t(56040),z=t(32059);function R(e){let{children:a,conversions:t}=e,s=(0,C.useTranslations)("seeker"),{createQueryString:n,createMultipleQueryString:i,searchParams:c}=(0,f.A)(),[u,g]=(0,r.useState)(!0),x=(0,d.i)(e=>e.setViewMode),p=(0,d.i)(e=>e.setHighlightedListing);(0,r.useEffect)(()=>{let e=c.get(w.Ix.viewMode);e&&"list"!=e?(g(!1),x("map")):(g(!0),x("list"))},[c,x]);let h=()=>{"map"==c.get(w.Ix.viewMode)?(n(w.Ix.viewMode,"list"),g(!1)):(n(w.Ix.viewMode,"map"),g(!0)),p(null),window.scrollTo({top:0})};return(0,l.jsxs)(z.A,{className:"space-y-0 max-sm:px-0",children:[(0,l.jsxs)("div",{className:"hidden md:flex mb-6 mx-auto h-full",children:[(0,l.jsx)("div",{className:(0,m.cn)("",u?"flex-1 ":"w-0 hidden"),children:a}),(0,l.jsx)("div",{className:(0,m.cn)("sticky  md:top-[182px] lg:top-[208px] xl:top-[220px] h-[calc(100vh-220px)]",u?"min-w-[28%]":"w-full"),children:(0,l.jsxs)("div",{className:"w-full h-full relative",children:[(0,l.jsx)(o.$,{className:"absolute z-20 top-4 left-4",size:"icon",onClick:()=>{h()},variant:"outline",children:u?(0,l.jsx)(_.A,{className:"!w-5 !h-5 !text-seekers-primary"}):(0,l.jsx)(B.A,{className:"!w-5 !h-5 !text-seekers-primary"})}),(0,l.jsx)(k,{lat:-8.535522079444435,lng:115.2228026405029,conversions:t})]})})]}),(0,l.jsxs)("div",{className:"md:hidden isolate",children:[u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:" p-4 sticky space-y-2 top-[140px] z-10 bg-white",children:[(0,l.jsx)(A.default,{conversions:t}),(0,l.jsx)(F.A,{})]}),a]}):(0,l.jsx)("div",{className:(0,m.cn)("sticky h-[calc(100vh-176px)]"),children:(0,l.jsx)("div",{className:"w-full h-full relative",children:(0,l.jsx)(k,{lat:-8.535522079444435,lng:115.2228026405029,conversions:t})})}),(0,l.jsx)("button",{className:"inline-flex items-center gap-2 sticky z-10 bottom-4 left-1/2 -translate-x-1/2 text-white bg-seekers-text p-4 rounded-full",onClick:()=>h(),children:u?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(T.A,{})," ",(0,l.jsx)("span",{children:s("cta.maps")})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(S.A,{})," ",(0,l.jsx)("span",{children:s("cta.list")})]})})]})]})}},76485:(e,a,t)=>{"use strict";t.d(a,{A:()=>h});var l=t(95155),s=t(48251),n=t(53999),r=t(40483),i=t(29186),o=t(57340),c=t(55868),d=t(67312),u=t(48264),m=t(27809),g=t(53896),x=t(23343),p=t(38564);function h(e){let{category:a,className:t}=e;switch(a){case s.BT.villa:case s.BT.villas:return(0,l.jsx)(r.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.apartment:return(0,l.jsx)(i.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.homestay:case s.BT.guestHouse:case s.BT.rooms:return(0,l.jsx)(o.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.ruko:case s.BT.commercialSpace:return(0,l.jsx)(c.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.cafeOrRestaurants:return(0,l.jsx)(d.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.offices:return(0,l.jsx)(u.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.shops:return(0,l.jsx)(m.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.shellAndCore:return(0,l.jsx)(g.A,{className:(0,n.cn)("!w-6 !h-6",t)});case s.BT.lands:return(0,l.jsx)(x.A,{className:(0,n.cn)("!w-6 !h-6",t)});default:return(0,l.jsx)(p.A,{className:(0,n.cn)("!w-6 !h-6",t)})}}},96012:(e,a,t)=>{"use strict";t.d(a,{default:()=>u});var l=t(95155),s=t(31917),n=t(14289),r=t(27043),i=t(39453),o=t(26828),c=t(12115),d=t(28113);function u(e){var a,t,u,m,g,x,p,h,v,f;let{query:y,types:j,conversions:w,...b}=e,N=(0,r.useTranslations)("seeker"),k=(0,o.t)(),_=(0,d.i)(e=>e.setHighlightedListing),{query:B}=(0,i.B)({page:b.page.toString(),per_page:b.perPage||"16",search:null==y?void 0:y.replaceAll("--"," ").replaceAll("-",", "),type:j.split(","),bathroom_total:b.bathroomTotal,bedroom_total:b.bedroomTotal,max_price:b.maxPrice,min_price:0==b.minPrice?1:b.minPrice,years_of_building:b.yearsOfBuilding,area:b.lat&&b.lng?{latitude:b.lat,longitude:b.lng,zoom:b.zoom||"13"}:void 0,rental_offers:null==(a=b.rentalOffers)?void 0:a.split(","),selling_points:null==(t=b.sellingPoints)?void 0:t.split(","),features:null==(u=b.feature)?void 0:u.split(","),sort_by:b.sortBy,building_largest:b.buildingLargest,building_smallest:0==b.buildingSmallest?1:b.buildingSmallest,garden_largest:b.gardenLargest,garden_smallest:0==b.gardenSmallest?1:b.gardenSmallest,land_largest:b.LandLargest,land_smallest:0==b.LandSmallest?1:b.LandSmallest,electricity:b.electricity,parking_option:b.parkingOption,pool_option:b.poolOption,living_option:b.typeLiving,furnishing_option:b.furnishingOption,property_of_view:null==(m=b.propertyOfView)?void 0:m.split(","),location_type:b.propertyLocation,contract_duration:b.minimumContract},!0);return(0,c.useEffect)(()=>{if(B.isError)return k.setIsLoading(!1)},[B.isError]),(0,c.useEffect)(()=>{var e,a;k.setIsLoading(B.isPending),B.isSuccess&&(k.setData((null==(e=B.data)?void 0:e.data)||[]),k.setTotal((null==(a=B.data.meta)?void 0:a.total)||0))},[null==(g=B.data)?void 0:g.data]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("section",{className:"min-h-[calc(100vh-202px)]",children:(0,l.jsx)("div",{className:"grid md:grid-cols-2 xl:grid-cols-3 gap-3 max-sm:px-4 max-sm:my-4 md:mr-6 gap-x-3 gap-y-6",children:B.isPending?Array(12).fill(0).map((e,a)=>(0,l.jsx)(s.kV,{},a)):k.data&&k.data.length>0?(0,l.jsx)(l.Fragment,{children:k.data.map((e,a)=>(0,l.jsx)("div",{onMouseEnter:()=>{_(e.code)},children:(0,l.jsxs)(s.Iq,{className:"space-y-3",data:e,conversion:w,children:[(0,l.jsx)(s.yM,{heartSize:"large"}),(0,l.jsxs)("div",{className:"space-y-2 px-0.5",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(s.LG,{}),(0,l.jsx)(s.f1,{className:"!-mt-1"})]}),(0,l.jsx)(s.Ex,{className:"text-seekers-text"}),(0,l.jsx)(s.CQ,{})]})]})},a))}):(0,l.jsx)(l.Fragment,{children:(0,l.jsx)("p",{className:"col-span-full text-center font-semibold py-8",children:N("listing.misc.propertyNotFound")})})})}),(0,l.jsx)("section",{className:"!mt-12",children:B.isPending||(null==(p=B.data)||null==(x=p.data)?void 0:x.length)&&(null==(v=B.data)||null==(h=v.data)?void 0:h.length)<16&&1==B.data.meta.pageCount?(0,l.jsx)(l.Fragment,{}):(0,l.jsx)("div",{className:"w-fit mx-auto",children:(0,l.jsx)(n.v,{meta:null==B||null==(f=B.data)?void 0:f.meta,totalThreshold:16,disableRowPerPage:!0})})})]})}}},e=>{e.O(0,[586,5105,6711,7753,4935,1551,3903,7043,4134,723,7900,5521,7811,8079,7417,2803,9474,2688,6766,6389,7823,3181,6307,2043,9988,3864,9399,516,9131,2841,7239,8441,5964,7358],()=>e(e.s=51610)),_N_E=e.O()}]);