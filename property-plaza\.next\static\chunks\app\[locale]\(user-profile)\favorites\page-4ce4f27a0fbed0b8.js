(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3726],{1336:(e,a,t)=>{Promise.resolve().then(t.bind(t,93309)),Promise.resolve().then(t.bind(t,66460)),Promise.resolve().then(t.bind(t,99314)),Promise.resolve().then(t.bind(t,45626)),Promise.resolve().then(t.bind(t,48882)),Promise.resolve().then(t.bind(t,78830)),Promise.resolve().then(t.t.bind(t,6874,23))},78830:(e,a,t)=>{"use strict";t.d(a,{default:()=>n});var r=t(26038),i=t(12115),s=t(61787);function n(e){let{locale:a,...t}=e;if(!a)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return i.createElement(s.IntlProvider,(0,r._)({locale:a},t))}},93309:(e,a,t)=>{"use strict";t.d(a,{default:()=>v});var r=t(95155),i=t(14289),s=t(27043),n=t(26828),d=t(12115),l=t(31917),o=t(64237),c=t(19373),u=t(48332),f=t(48251),b=t(40054),p=t.n(b),m=t(12673),g=t(97168),h=t(6874),x=t.n(h);function v(e){var a,t,b,h,v,w;let{page:y,conversions:N,sortBy:j}=e,_=(0,s.useTranslations)("seeker"),S=(0,n.t)(),{query:R}=function(e){var a;arguments.length>1&&void 0!==arguments[1]&&arguments[1];let t=(0,u.P)(),r=null==t||null==(a=t.data)?void 0:a.data,i=["favorite-seekers-listing",e],{failureCount:s,...n}=(0,c.I)({queryKey:i,queryFn:async()=>{var a,t,i;let s=e.max_price||(null==r?void 0:r.priceRange.max),n=e.min_price||(null==r?void 0:r.priceRange.min)||1,d=e.building_largest||(null==r?void 0:r.buildingSizeRange.max),l=e.building_smallest||(null==r?void 0:r.buildingSizeRange.min)||1,c=e.land_largest||(null==r?void 0:r.landSizeRange.max),u=e.land_smallest||(null==r?void 0:r.landSizeRange.min)||1,b=e.garden_largest||(null==r?void 0:r.gardenSizeRange.max),g=e.garden_smallest||(null==r?void 0:r.gardenSizeRange.min)||1,h=e.area;(null==(a=e.area)?void 0:a.zoom)==m.wJ.toString()&&(h=void 0);let x=(null==(t=e.type)?void 0:t.includes("all"))?void 0:p().uniq(null==(i=e.type)?void 0:i.flatMap(e=>e!==f.BT.commercialSpace?e:[f.BT.cafeOrRestaurants,f.BT.shops,f.BT.offices])),v={...e,type:x,search:"all"==e.search?void 0:e.search,min_price:n,max_price:s,building_largest:d,building_smallest:l,land_largest:c,land_smallest:u,garden_largest:b,garden_smallest:g,area:h||void 0,property_of_view:e.property_of_view,sort_by:e.sort_by};return e.min_price&&e.min_price!=(null==r?void 0:r.priceRange.min)||s!=(null==r?void 0:r.priceRange.max)||(v.max_price=void 0,v.min_price=void 0),e.building_smallest&&e.building_smallest!=(null==r?void 0:r.buildingSizeRange.min)||d!=(null==r?void 0:r.buildingSizeRange.max)||(v.building_largest=void 0,v.building_smallest=void 0),e.land_smallest&&e.land_smallest!=(null==r?void 0:r.landSizeRange.min)||c!=(null==r?void 0:r.landSizeRange.max)||(v.land_largest=void 0,v.land_smallest=void 0),e.garden_smallest&&e.garden_smallest!=(null==r?void 0:r.gardenSizeRange.min)||b!=(null==r?void 0:r.gardenSizeRange.max)||(v.garden_largest=void 0,v.garden_smallest=void 0),await (0,o.Cv)(v)},enabled:!0,retry:!1});return{query:n,filterQueryKey:i}}({page:y||"1",per_page:"20",sort_by:j||"DATE_NEWEST"},!0);return(0,d.useEffect)(()=>{if(R.isError)return S.setIsLoading(!1)},[R.isError]),(0,d.useEffect)(()=>{var e,a;S.setIsLoading(R.isPending),R.isSuccess&&(S.setData((null==(e=R.data)?void 0:e.data)||[]),S.setTotal((null==(a=R.data.meta)?void 0:a.total)||0))},[null==(a=R.data)?void 0:a.data]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("section",{className:"min-h-[calc(100vh-202px)]",children:(0,r.jsx)("div",{className:"grid gap-3 gap-x-3 gap-y-6 max-sm:my-4 md:grid-cols-2 xl:grid-cols-4",children:R.isPending?Array(12).fill(0).map((e,a)=>(0,r.jsx)(l.kV,{},a)):S.data&&S.data.length>0?(0,r.jsx)(r.Fragment,{children:S.data.map((e,a)=>(0,r.jsx)("div",{children:(0,r.jsxs)(l.Iq,{className:"space-y-3",data:{...e,isFavorite:!0},conversion:N,handleFavoriteListion:e=>{if(!e){let e=S.data.filter((e,t)=>t!==a);S.setData(e||[])}},children:[(0,r.jsx)(l.yM,{heartSize:"large",allowFavoriteWhileInactive:!0}),(0,r.jsxs)("div",{className:"px-0.5 space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-base text-seekers-text font-semibold line-clamp-1",children:e.title}),(0,r.jsx)(l.Ex,{className:"text-seekers-text"})]}),(0,r.jsx)(l.CQ,{})]})]})},a))}):(0,r.jsxs)("div",{className:"col-span-4 flex flex-col justify-center items-center",children:[(0,r.jsx)("p",{className:"col-span-full text-center font-semibold max-w-md py-8",children:_("listing.misc.favoritePropertyNotFound")}),(0,r.jsx)(g.$,{variant:"default-seekers",asChild:!0,children:(0,r.jsx)(x(),{href:"/",children:_("cta.showAllProperty")})})]})})}),(0,r.jsx)("section",{className:"!my-12",children:R.isPending||(null==(b=R.data)||null==(t=b.data)?void 0:t.length)&&(null==(v=R.data)||null==(h=v.data)?void 0:h.length)<20&&1==R.data.meta.pageCount?(0,r.jsx)(r.Fragment,{}):(0,r.jsx)("div",{className:"w-fit mx-auto",children:(0,r.jsx)(i.v,{meta:null==R||null==(w=R.data)?void 0:w.meta,totalThreshold:20,disableRowPerPage:!0})})})]})}},99314:(e,a,t)=>{"use strict";t.d(a,{Bx:()=>S,Yv:()=>z,Cn:()=>k,rQ:()=>C,jj:()=>E,SidebarProvider:()=>_,SidebarTrigger:()=>R});var r=t(95155),i=t(12115),s=t(66634),n=t(74466),d=t(53999),l=t(97168),o=t(89852),c=t(76037),u=t(67178),f=t(33096);let b=u.bL;u.l9,u.bm;let p=u.ZL,m=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.hJ,{className:(0,d.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...i,ref:a})});m.displayName=u.hJ.displayName;let g=(0,n.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),h=i.forwardRef((e,a)=>{let{side:t="right",className:i,children:s,...n}=e;return(0,r.jsxs)(p,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(u.UC,{ref:a,className:(0,d.cn)(g({side:t}),i),...n,children:[(0,r.jsxs)(u.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(f.MKb,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),s]})]})});h.displayName=u.UC.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold text-foreground",t),...i})}).displayName=u.hE.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",t),...i})}).displayName=u.VY.displayName;var x=t(27737),v=t(37777),w=t(42355),y=t(13052);let N=i.createContext(null);function j(){let e=i.useContext(N);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let _=i.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:s,onOpenChange:n,className:l,style:o,children:c,...u}=e,f=function(){let[e,a]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[b,p]=i.useState(!1),[m,g]=i.useState(t),h=null!=s?s:m,x=i.useCallback(e=>{let a="function"==typeof e?e(h):e;n?n(a):g(a),document.cookie="".concat("sidebar:state","=").concat(a,"; path=/; max-age=").concat(604800)},[n,h]),w=i.useCallback(()=>f?p(e=>!e):x(e=>!e),[f,x,p]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),w())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[w]);let y=h?"expanded":"collapsed",j=i.useMemo(()=>({state:y,open:h,setOpen:x,isMobile:f,openMobile:b,setOpenMobile:p,toggleSidebar:w}),[y,h,x,f,b,p,w]);return(0,r.jsx)(N.Provider,{value:j,children:(0,r.jsx)(v.TooltipProvider,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"0",...o},className:(0,d.cn)("group/sidebar-wrapper flex w-full has-[[data-variant=inset]]:bg-sidebar",l),ref:a,...u,children:c})})})});_.displayName="SidebarProvider";let S=i.forwardRef((e,a)=>{let{side:t="left",variant:i="sidebar",collapsible:s="offcanvas",className:n,children:l,...o}=e,{isMobile:c,state:u,openMobile:f,setOpenMobile:p}=j();return"none"===s?(0,r.jsx)("div",{className:(0,d.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",n),ref:a,...o,children:l}):c?(0,r.jsx)(b,{open:f,onOpenChange:p,...o,children:(0,r.jsx)(h,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:l})})}):(0,r.jsxs)("div",{ref:a,className:(0,d.cn)("group peer hidden text-sidebar-foreground md:block","sidebar"==i&&"h-full"),"data-state":u,"data-collapsible":"collapsed"===u?s:"","data-variant":i,"data-side":t,children:[(0,r.jsx)("div",{className:(0,d.cn)("relative h-full w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===i||"inset"===i?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-full w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===i||"inset"===i?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:l})})]})});S.displayName="Sidebar";let R=i.forwardRef((e,a)=>{let{className:t,onClick:i,...s}=e,{toggleSidebar:n,open:o}=j();return(0,r.jsxs)(l.$,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,d.cn)("h-7 w-7",t),onClick:e=>{null==i||i(e),n()},...s,children:[o?(0,r.jsx)(w.A,{}):(0,r.jsx)(y.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});R.displayName="SidebarTrigger",i.forwardRef((e,a)=>{let{className:t,...i}=e,{toggleSidebar:s}=j();return(0,r.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,d.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:w-0","[[data-side=right][data-collapsible=offcanvas]_&]:w-0",t),...i})}).displayName="SidebarRail",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("main",{ref:a,className:(0,d.cn)("relative flex min-h-full flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...i})}).displayName="SidebarInset",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(o.p,{ref:a,"data-sidebar":"input",className:(0,d.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...i})}).displayName="SidebarInput",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarHeader",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarFooter",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(c.Separator,{ref:a,"data-sidebar":"separator",className:(0,d.cn)("mx-2 w-auto bg-sidebar-border",t),...i})}).displayName="SidebarSeparator";let z=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...i})});z.displayName="SidebarContent";let k=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",t),...i})});k.displayName="SidebarGroup";let E=i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...n}=e,l=i?s.DX:"div";return(0,r.jsx)(l,{ref:a,"data-sidebar":"group-label",className:(0,d.cn)("flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...n})});E.displayName="SidebarGroupLabel",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...n}=e,l=i?s.DX:"button";return(0,r.jsx)(l,{ref:a,"data-sidebar":"group-action",className:(0,d.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...n})}).displayName="SidebarGroupAction";let C=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,d.cn)("relative pl-8 before:absolute before:left-4 before:top-0 before:h-full before:w-px before:bg-border",t),...i})});C.displayName="SidebarGroupContent",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",t),...i})}).displayName="SidebarMenu",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",t),...i})}).displayName="SidebarMenuItem";let P=(0,n.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}});i.forwardRef((e,a)=>{let{asChild:t=!1,isActive:i=!1,variant:n="default",size:l="default",tooltip:o,className:c,...u}=e,f=t?s.DX:"button",{isMobile:b,state:p}=j(),m=(0,r.jsx)(f,{ref:a,"data-sidebar":"menu-button","data-size":l,"data-active":i,className:(0,d.cn)(P({variant:n,size:l}),c),...u});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(v.Tooltip,{children:[(0,r.jsx)(v.TooltipTrigger,{asChild:!0,children:m}),(0,r.jsx)(v.TooltipContent,{side:"right",align:"center",hidden:"collapsed"!==p||b,...o})]})):m}).displayName="SidebarMenuButton",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,showOnHover:n=!1,...l}=e,o=i?s.DX:"button";return(0,r.jsx)(o,{ref:a,"data-sidebar":"menu-action",className:(0,d.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...l})}).displayName="SidebarMenuAction",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,d.cn)("pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuBadge",i.forwardRef((e,a)=>{let{className:t,showIcon:s=!1,...n}=e,l=i.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,r.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,d.cn)("flex h-8 items-center gap-2 rounded-md px-2",t),...n,children:[s&&(0,r.jsx)(x.E,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(x.E,{className:"h-4 max-w-[--skeleton-width] flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":l}})]})}).displayName="SidebarMenuSkeleton",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,d.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuSub",i.forwardRef((e,a)=>{let{...t}=e;return(0,r.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",i.forwardRef((e,a)=>{let{asChild:t=!1,size:i="md",isActive:n,className:l,...o}=e,c=t?s.DX:"a";return(0,r.jsx)(c,{ref:a,"data-sidebar":"menu-sub-button","data-size":i,"data-active":n,className:(0,d.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===i&&"text-xs","md"===i&&"text-sm","group-data-[collapsible=icon]:hidden",l),...o})}).displayName="SidebarMenuSubButton"}},e=>{e.O(0,[586,5105,6711,7753,4935,1551,3903,7043,4134,723,7900,5521,7811,8079,7417,2803,9474,2688,6766,6389,7823,3181,6307,2043,3864,9399,9131,2841,7239,8441,5964,7358],()=>e(e.s=1336)),_N_E=e.O()}]);