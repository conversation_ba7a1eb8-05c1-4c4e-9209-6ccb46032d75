(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3938],{4184:(e,s,a)=>{Promise.resolve().then(a.bind(a,67116)),Promise.resolve().then(a.bind(a,60641)),Promise.resolve().then(a.bind(a,99314)),Promise.resolve().then(a.bind(a,45626)),Promise.resolve().then(a.bind(a,48882)),Promise.resolve().then(a.bind(a,78830)),Promise.resolve().then(a.t.bind(a,6874,23))},13423:(e,s,a)=>{"use strict";a.d(s,{BE:()=>u,I6:()=>p,Uz:()=>o,_s:()=>i,gk:()=>h,tb:()=>f,zj:()=>m});var t=a(95155),l=a(12115),r=a(69474),n=a(53999);let i=e=>{let{shouldScaleBackground:s=!0,...a}=e;return(0,t.jsx)(r._.Root,{shouldScaleBackground:s,...a})};i.displayName="Drawer";let o=r._.Trigger,d=r._.Portal;r._.Close;let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r._.Overlay,{ref:s,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80",a),...l})});c.displayName=r._.Overlay.displayName;let m=l.forwardRef((e,s)=>{let{className:a,children:l,...i}=e;return(0,t.jsxs)(d,{children:[(0,t.jsx)(c,{}),(0,t.jsxs)(r._.Content,{ref:s,className:(0,n.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",a),...i,children:[(0,t.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),l]})]})});m.displayName="DrawerContent";let u=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("grid gap-1.5 p-4 text-center sm:text-left",s),...a})};u.displayName="DrawerHeader";let f=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("mt-auto flex flex-col gap-2 p-4",s),...a})};f.displayName="DrawerFooter";let h=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r._.Title,{ref:s,className:(0,n.cn)("font-semibold leading-none tracking-tight",a),...l})});h.displayName=r._.Title.displayName;let p=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r._.Description,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",a),...l})});p.displayName=r._.Description.displayName},22190:(e,s,a)=>{"use strict";a.d(s,{A:()=>o});var t=a(95155),l=a(99840),r=a(37130),n=a(53999);a(12115);var i=a(13423);function o(e){let{children:s,openTrigger:a,open:o,setOpen:d,dialogClassName:c,drawerClassName:m,dialogOverlayClassName:u}=e;return(0,r.U)("(min-width:1024px)")?(0,t.jsxs)(l.lG,{open:o,onOpenChange:d,children:[(0,t.jsx)(l.zM,{asChild:!0,children:a}),(0,t.jsxs)(l.ZJ,{children:[(0,t.jsx)(l.LC,{className:u}),(0,t.jsx)(l.Cf,{className:(0,n.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",c),children:s})]})]}):(0,t.jsxs)(i._s,{open:o,onOpenChange:d,children:[(0,t.jsx)(i.Uz,{asChild:!0,children:a}),(0,t.jsx)(i.zj,{children:(0,t.jsx)("div",{className:(0,n.cn)("p-4 overflow-auto",m),children:s})})]})}},26291:(e,s,a)=>{"use strict";a.d(s,{A:()=>o});var t=a(95155),l=a(37130),r=a(99840),n=a(13423),i=a(53999);function o(e){let{children:s,className:a}=e;return(0,l.U)("(min-width:1024px)")?(0,t.jsx)(r.Es,{className:(0,i.cn)("px-0",a),children:s}):(0,t.jsx)(n.tb,{className:(0,i.cn)("px-0",a),children:s})}},29653:(e,s,a)=>{"use strict";a.d(s,{A:()=>o});var t=a(95155),l=a(30070),r=a(89852),n=a(67943),i=a(53999);function o(e){let{form:s,label:a,name:o,placeholder:d,description:c,type:m,inputProps:u,children:f,labelClassName:h,containerClassName:p,inputContainer:x,variant:g="default"}=e;return(0,t.jsx)(l.zB,{control:s.control,name:o,render:e=>{let{field:s}=e;return(0,t.jsx)(n.A,{label:a,description:c,labelClassName:(0,i.cn)("float"==g?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:p,variant:g,children:(0,t.jsxs)("div",{className:(0,i.cn)("flex gap-2 w-full overflow-hidden","float"==g?"":"border rounded-sm focus-within:border-neutral-light",x),children:[(0,t.jsx)(r.p,{type:m,placeholder:d,...s,...u,className:(0,i.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==g?"px-0":"",null==u?void 0:u.className)}),f]})})}})}},31787:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(95155),l=a(37130),r=a(13423),n=a(99840);function i(e){let{children:s,className:a}=e;return(0,l.U)("(min-width:1024px)")?(0,t.jsx)(n.L3,{className:a,children:s}):(0,t.jsx)(r.gk,{className:a,children:s})}},37130:(e,s,a)=>{"use strict";a.d(s,{U:()=>l});var t=a(12115);function l(e){let[s,a]=(0,t.useState)(!1);return(0,t.useEffect)(()=>{function s(e){a(e.matches)}let t=matchMedia(e);return t.addEventListener("change",s),a(t.matches),()=>t.removeEventListener("change",s)},[e]),s}},37996:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(95155),l=a(37130),r=a(99840),n=a(13423);function i(e){let{children:s,className:a}=e;return(0,l.U)("(min-width:1024px)")?(0,t.jsx)(r.c7,{className:a,children:s}):(0,t.jsx)(n.BE,{className:a,children:s})}},60641:(e,s,a)=>{"use strict";a.d(s,{default:()=>F});var t=a(95155),l=a(27043),r=a(51273),n=a.n(r),i=a(55594),o=a(62177),d=a(90221),c=a(46797),m=a(12115),u=a(97168),f=a(29653),h=a(30070),p=a(89852),x=a(89917),g=a(54416),N=a(5196);function j(e){let{setDisabledStatus:s,isFormDisabled:a}=e;return(0,t.jsx)(t.Fragment,{children:a?(0,t.jsx)(u.$,{variant:"ghost",className:" rounded-none bg-neutral-lightest",onClick:()=>s(!1,!1),children:(0,t.jsx)(x.A,{className:"w-4 h-4"})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.$,{variant:"ghost",className:"rounded-none bg-neutral-lightest",onClick:()=>{s(!0,!1)},children:(0,t.jsx)(g.A,{className:"w-4 h-4"})}),(0,t.jsx)(u.$,{variant:"ghost",className:"rounded-none bg-neutral-lightest",onClick:()=>s(!0,!0),children:(0,t.jsx)(N.A,{className:"w-4 h-4"})})]})})}var b=a(67943),v=a(53999);function w(e){let{form:s,label:a,name:l,placeholder:r,description:n,children:i,isEditable:o,inputProps:d}=e,[c,u]=(0,m.useState)(!0),f=(e,a)=>{a||s.reset(),u(e)};return(0,t.jsx)(h.zB,{control:s.control,name:l,render:e=>{let{field:s}=e;return(0,t.jsx)(b.A,{label:a,description:n,children:(0,t.jsxs)("div",{className:"flex items-center w-full border rounded-sm focus-within:border-neutral-light overflow-hidden",children:[(0,t.jsx)(p.p,{type:"email",placeholder:r,...s,...d,disabled:c,className:(0,v.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0",null==d?void 0:d.className)}),i,null!=o?o:(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(j,{setDisabledStatus:f,isFormDisabled:c})})]})})}})}function y(e){let{form:s,label:a,name:l,placeholder:r,description:n,children:i,isEditable:o,inputProps:d}=e,[c,u]=(0,m.useState)(!0),f=(e,a)=>{a||s.reset(),u(e)};return(0,t.jsx)(h.zB,{control:s.control,name:l,render:e=>{let{field:s}=e;return(0,t.jsx)(b.A,{label:a,description:n,children:(0,t.jsxs)("div",{className:"flex items-center w-full border rounded-sm focus-within:border-neutral-light overflow-hidden",children:[(0,t.jsx)(p.p,{type:"tel",placeholder:r,...s,...d,disabled:c,className:(0,v.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0",null==d?void 0:d.className)}),i,null!=o?o:(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(j,{setDisabledStatus:f,isFormDisabled:c})})]})})}})}var C=a(54568),k=a(35809),A=a.n(k),z=a(26291),_=a(37996),P=a(31787),E=a(22190),V=a(99840);function R(e){let{currentVal:s,onChange:a,trigger:r,type:i}=e,o=(0,l.useTranslations)("seeker"),[d,c]=(0,m.useState)(s),[f,h]=(0,m.useState)(!1),[x,g]=(0,m.useState)(null);return(0,m.useEffect)(()=>{c(s)},[s]),(0,t.jsxs)(E.A,{open:f,openTrigger:r,setOpen:h,children:[(0,t.jsxs)(_.A,{children:[(0,t.jsx)(P.A,{className:"text-base font-bold text-seekers-text",children:o("settings.personalInfo.change".concat(i.charAt(0).toUpperCase()+i.slice(1),".title"))}),(0,t.jsx)(V.rr,{className:"max-w-sm",children:o("settings.personalInfo.change".concat(i.charAt(0).toUpperCase()+i.slice(1),".description"))})]}),(0,t.jsxs)("div",{className:" py-4 w-full",children:[(0,t.jsx)("div",{className:"items-center gap-4",children:(0,t.jsx)(p.p,{id:"new-value",type:"email"===i?"email":"tel",className:"col-span-3 w-full border border-input",value:d,onChange:e=>{c(e.target.value),g(null)}})}),x&&(0,t.jsx)("p",{className:"text-red-500 text-sm",children:x})]}),(0,t.jsx)(z.A,{children:(0,t.jsx)(u.$,{type:"submit",variant:"default-seekers",onClick:()=>{if("email"===i){if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(String(d).toLowerCase()))return void g(o("form.utility.invalidFormat",{field:o("form.label.email")}))}else if("phone"===i&&!n()(d).isValid)return void g(o("form.utility.invalidFormat",{field:o("form.label.phoneNumber")}));a(d),h(!1)},children:o("cta.confirm")})})]})}function F(){let e=function(){let e=(0,l.useTranslations)("seeker");return i.z.object({id:i.z.string(),firstName:i.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.firstName")})}),lastName:i.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.lastName")})}),email:i.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}).refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),{message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),phoneNumber:i.z.string().refine(e=>n()(e).isValid,{message:e("form.utility.enterValidField",{field:"".concat(e("form.field.phoneNumber")," ")})})})}(),s=(0,l.useTranslations)("seeker"),{email:a,phoneCode:r,phoneNumber:p,code:x,accounts:{firstName:g,lastName:N}}=(0,c.k)(e=>e.seekers),j=(0,C.n)(),b=(0,o.mN)({resolver:(0,d.u)(e),defaultValues:{id:"",firstName:"",lastName:"",email:"",phoneNumber:""}});(0,m.useEffect)(()=>{a&&b.setValue("email",a),x&&b.setValue("id",x),g&&b.setValue("firstName",g),N&&b.setValue("lastName",N),r&&p&&b.setValue("phoneNumber",r+p)},[a,g,N,p,r,x,b]);let v=async e=>{var s;let a=n()(e.phoneNumber),t={phone_number:null==(s=a.phoneNumber)?void 0:s.replace(a.countryCode,""),phone_code:a.countryCode||"",first_name:e.firstName,last_name:e.lastName};await j.mutateAsync(t)};return(0,t.jsx)(h.lV,{...b,children:(0,t.jsxs)("form",{onSubmit:b.handleSubmit(v),children:[(0,t.jsxs)("div",{className:"grid max-sm:grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(f.A,{form:b,label:s("form.label.firstName"),name:"firstName",placeholder:s("form.placeholder.basePlaceholder",{field:s("form.field.firstName")}),type:"string"}),(0,t.jsx)(f.A,{form:b,label:s("form.label.lastName"),name:"lastName",placeholder:s("form.placeholder.basePlaceholder",{field:s("form.field.lastName")}),type:"string"}),(0,t.jsx)("div",{className:"flex gap-2 justify-between items-end",children:(0,t.jsx)(w,{form:b,label:s("form.label.email"),name:"email",placeholder:"",isEditable:!1,inputProps:{className:"min-w-full max-w-full"}})}),(0,t.jsxs)("div",{className:"flex gap-2 items-end",children:[(0,t.jsx)(y,{form:b,label:s("form.label.phoneNumber"),name:"phoneNumber",placeholder:"",isEditable:!1,inputProps:{className:"inputProps"}}),(0,t.jsx)(R,{onChange:e=>b.setValue("phoneNumber",e),currentVal:b.getValues("phoneNumber"),trigger:(0,t.jsx)(u.$,{type:"button",variant:"outline",className:"shadow-none h-[34px] md:h-[38px]",children:s("cta.change")}),type:"phone"})]})]}),(0,t.jsx)(u.$,{className:"my-8 min-w-40 ",variant:"default-seekers",type:"submit",loading:j.isPending,children:s("cta.saveChanges")})]})})}A().PhoneNumberUtil.getInstance()},67116:(e,s,a)=>{"use strict";a.d(s,{default:()=>j});var t=a(95155),l=a(69663),r=a(97168),n=a(27737),i=a(99493),o=a(53580),d=a(5041),c=a(27043),m=a(54568),u=a(35201),f=a(35814),h=a.n(f);async function p(e,s){try{return await new Promise((a,t)=>{new(h())(e,{quality:.5,mimeType:"image/webp",success:async e=>{a(e)},error:async e=>{t(e)},...s})})}catch(e){return e}}var x=a(46797),g=a(85977),N=a(12115);function j(){let e=(0,c.useTranslations)("seeker"),{toast:s}=(0,o.dj)(),{image:a,firstName:f,lastName:h}=(0,x.k)(e=>e.seekers.accounts),j=(0,u.H)(),b=function(){let{toast:e}=(0,o.dj)(),s=(0,c.useTranslations)();return(0,d.n)({mutationFn:e=>i.apiClient.post("file-storage",e,{headers:{"Content-Type":"multipart/form-data"}}),onSuccess:e=>e.data.data.url,onError:a=>{let t=a.response.data;e({title:s("misc.foundError"),description:t.message,variant:"destructive"})}})}(),v=(0,m.n)(),w=async a=>{let t=a.target.files;if(null==t||(null==t?void 0:t.length)==0)return void s({title:e("misc.noImageUploaded")});let l=await p(t[0],{width:240,height:240,resize:"cover"});if(void 0==l)return;let r=new FormData;r.append("file",l);try{let e=await b.mutateAsync(r);if(e){let s=e.data.data.url;v.mutate({image:s})}}catch(e){return}},y=(0,N.useRef)(null);return(0,t.jsxs)("div",{className:"relative flex justify-start items-center gap-4",children:[j.isLoading?(0,t.jsx)(n.E,{className:"w-[96px] h-[96px] rounded-full"}):(0,t.jsxs)(l.eu,{className:"w-[96px] h-[96px] relative border",children:[a?(0,t.jsx)(g.BK,{src:a,width:96,height:96,alt:"example",className:"rounded-full"}):(0,t.jsxs)(l.q5,{children:[f.charAt(0),h.charAt(0)]}),(0,t.jsx)("input",{type:"file",className:"hidden",accept:"image/*",ref:y,onChange:w})]}),(0,t.jsx)(r.$,{onClick:()=>{var e;return null==(e=y.current)?void 0:e.click()},variant:"outline",loading:b.isPending||v.isPending,className:"shadow-none",children:e("cta.changePhoto")})]})}},67943:(e,s,a)=>{"use strict";a.d(s,{A:()=>n});var t=a(95155),l=a(30070),r=a(53999);function n(e){let{children:s,description:a,label:n,containerClassName:i,labelClassName:o,variant:d="default"}=e;return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)(l.eI,{className:(0,r.cn)("w-full relative","float"==d?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",i),onClick:e=>e.stopPropagation(),children:[n&&(0,t.jsx)(l.lR,{className:o,children:n}),(0,t.jsx)(l.MJ,{className:"group relative w-full",children:s}),a&&(0,t.jsx)(l.Rr,{children:a}),"default"==d&&(0,t.jsx)(l.C5,{})]}),"float"==d&&(0,t.jsx)(l.C5,{})]})}},69663:(e,s,a)=>{"use strict";a.d(s,{BK:()=>o,eu:()=>i,q5:()=>d});var t=a(95155),l=a(12115),r=a(85977),n=a(53999);let i=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.bL,{ref:s,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),...l})});i.displayName=r.bL.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r._V,{ref:s,className:(0,n.cn)("aspect-square h-full w-full",a),...l})});o.displayName=r._V.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.H4,{ref:s,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),...l})});d.displayName=r.H4.displayName},99840:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>u,Es:()=>h,L3:()=>p,LC:()=>m,ZJ:()=>c,c7:()=>f,lG:()=>o,rr:()=>x,zM:()=>d});var t=a(95155),l=a(12115),r=a(67178),n=a(33096),i=a(53999);let o=r.bL,d=r.l9,c=r.ZL;r.bm;let m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});m.displayName=r.hJ.displayName;let u=l.forwardRef((e,s)=>{let{className:a,children:l,...o}=e;return(0,t.jsxs)(c,{children:[(0,t.jsx)(m,{}),(0,t.jsxs)(r.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...o,children:[l,(0,t.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(n.MKb,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});u.displayName=r.UC.displayName;let f=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-start sm:text-left",s),...a})};f.displayName="DialogHeader";let h=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};h.displayName="DialogFooter";let p=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});p.displayName=r.hE.displayName;let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});x.displayName=r.VY.displayName}},e=>{e.O(0,[586,5105,4139,1551,3903,7043,4134,723,7900,5521,7811,8079,7417,2803,9474,2688,8718,8983,9314,3277,8441,5964,7358],()=>e(e.s=4184)),_N_E=e.O()}]);