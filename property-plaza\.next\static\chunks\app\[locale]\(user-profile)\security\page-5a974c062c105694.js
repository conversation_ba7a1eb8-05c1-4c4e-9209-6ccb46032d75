(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8819],{12673:(e,t,s)=>{"use strict";s.d(t,{cQ:()=>i,lL:()=>o,vN:()=>l,wJ:()=>n,x_:()=>a});var r=s(74810);function a(e){return r.U$.free.includes(e)?r.U$.free:r.U$.finder.includes(e)?r.U$.finder:r.U$.archiver.includes(e)?r.U$.archiver:r.U$.free}function i(e){return e==r.U$.free?0:e==r.U$.finder?5:10*(e==r.U$.archiver)}let n=10,l={max:13,min:10};function o(e){return e==r.U$.free?l:e==r.U$.finder?{max:14,min:n}:e==r.U$.archiver?{max:15,min:n}:l}},14570:(e,t,s)=>{"use strict";s.d(t,{N$:()=>d,Q0:()=>u,Uu:()=>c,_k:()=>p,aH:()=>o,bH:()=>l,eD:()=>n,iD:()=>a,ri:()=>i,zp:()=>m});var r=s(99493);let a=(e,t)=>r.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),i=()=>r.apiClient.post("auth/logout"),n=e=>r.apiClient.post("notifications/email",e),l=e=>r.apiClient.post("auth/otp-verification",e),o=e=>r.apiClient.post("auth/forgot-password",e),c=e=>r.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),d=e=>r.apiClient.post("auth/reset-password",e),u=(e,t)=>r.apiClient.post("auth/create-password",e,t),m=e=>r.apiClient.post("users/security",e),p=e=>r.apiClient.post("auth/totp-verification",e)},18618:(e,t,s)=>{"use strict";s.d(t,{N$:()=>r.N$,Q0:()=>r.Q0,Uu:()=>r.Uu,_k:()=>r._k,aH:()=>r.aH,bH:()=>r.bH,eD:()=>r.eD,iD:()=>r.iD,ri:()=>r.ri,zp:()=>r.zp});var r=s(14570)},30070:(e,t,s)=>{"use strict";s.d(t,{C5:()=>b,MJ:()=>h,Rr:()=>v,eI:()=>f,lR:()=>x,lV:()=>c,zB:()=>u});var r=s(95155),a=s(12115),i=s(66634),n=s(62177),l=s(53999),o=s(82714);let c=n.Op,d=a.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},m=()=>{let e=a.useContext(d),t=a.useContext(p),{getFieldState:s,formState:r}=(0,n.xW)(),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...i}},p=a.createContext({}),f=a.forwardRef((e,t)=>{let{className:s,...i}=e,n=a.useId();return(0,r.jsx)(p.Provider,{value:{id:n},children:(0,r.jsx)("div",{ref:t,className:(0,l.cn)("space-y-2",s),...i})})});f.displayName="FormItem";let x=a.forwardRef((e,t)=>{let{className:s,...a}=e,{error:i,formItemId:n}=m();return(0,r.jsx)(o.J,{ref:t,className:(0,l.cn)(i&&"text-destructive",s),htmlFor:n,...a})});x.displayName="FormLabel";let h=a.forwardRef((e,t)=>{let{...s}=e,{error:a,formItemId:n,formDescriptionId:l,formMessageId:o}=m();return(0,r.jsx)(i.DX,{ref:t,id:n,"aria-describedby":a?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!a,...s})});h.displayName="FormControl";let v=a.forwardRef((e,t)=>{let{className:s,...a}=e,{formDescriptionId:i}=m();return(0,r.jsx)("p",{ref:t,id:i,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",s),...a})});v.displayName="FormDescription";let b=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e,{error:n,formMessageId:o}=m(),c=n?String(null==n?void 0:n.message):a;return c?(0,r.jsx)("p",{ref:t,id:o,className:(0,l.cn)("text-[0.8rem] font-medium text-destructive",s),...i,children:c}):null});b.displayName="FormMessage"},34303:(e,t,s)=>{"use strict";s.d(t,{default:()=>u});var r=s(95155),a=s(97168),i=s(88482),n=s(47943),l=s(53580),o=s(46797),c=s(75525),d=s(27043);function u(){let e=(0,d.useTranslations)("seeker"),t=(0,n.v)(),s=(0,o.k)(e=>e.seekers.email),{toast:u}=(0,l.dj)(),m=async()=>{try{await t.mutateAsync({email:s}),u({title:e("success.requestForgetPassword.title"),description:e("success.requestForgetPassword.description",{email:s})})}catch(t){u({title:e("error.requestForgetPassword.title")})}};return(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row  items-center justify-between space-y-0  pb-2",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(i.ZB,{className:"text-seekers-primary flex items-center",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),e("settings.profile.security.password.title")]}),(0,r.jsx)(i.BT,{children:e("settings.profile.security.password.description")})]}),(0,r.jsx)(a.$,{size:"sm",variant:"outline",className:"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>m(),children:e("cta.changePassword")})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e("setting.profile.security.password.lastChanged")," January 15, 2025"]})})]})}},46797:(e,t,s)=>{"use strict";s.d(t,{h:()=>c,k:()=>d});var r=s(12673),a=s(88693),i=s(46786),n=s(57383),l=s(74810);let o={getItem:e=>{let t=n.A.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{n.A.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{n.A.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:l.U$.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:r.vN},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},d=(0,a.vt)()((0,i.Zr)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,i.KU)(()=>o)}))},47943:(e,t,s)=>{"use strict";s.d(t,{v:()=>i});var r=s(18618),a=s(5041);function i(){return(0,a.n)({mutationFn:e=>(0,r.aH)(e)})}},51309:(e,t,s)=>{Promise.resolve().then(s.bind(s,34303)),Promise.resolve().then(s.bind(s,91531)),Promise.resolve().then(s.bind(s,99314)),Promise.resolve().then(s.bind(s,45626)),Promise.resolve().then(s.bind(s,48882)),Promise.resolve().then(s.bind(s,78830)),Promise.resolve().then(s.t.bind(s,6874,23))},67943:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(95155),a=s(30070),i=s(53999);function n(e){let{children:t,description:s,label:n,containerClassName:l,labelClassName:o,variant:c="default"}=e;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)(a.eI,{className:(0,i.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",l),onClick:e=>e.stopPropagation(),children:[n&&(0,r.jsx)(a.lR,{className:o,children:n}),(0,r.jsx)(a.MJ,{className:"group relative w-full",children:t}),s&&(0,r.jsx)(a.Rr,{children:s}),"default"==c&&(0,r.jsx)(a.C5,{})]}),"float"==c&&(0,r.jsx)(a.C5,{})]})}},69916:(e,t,s)=>{"use strict";s.d(t,{NV:()=>c,UV:()=>o,sF:()=>d});var r=s(95155),a=s(12115),i=s(33096),n=s(1184),l=s(53999);let o=a.forwardRef((e,t)=>{let{className:s,containerClassName:a,...i}=e;return(0,r.jsx)(n.wE,{ref:t,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",a),className:(0,l.cn)("disabled:cursor-not-allowed",s),...i})});o.displayName="InputOTP";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center",s),...a})});c.displayName="InputOTPGroup";let d=a.forwardRef((e,t)=>{let{index:s,className:i,...o}=e,{char:c,hasFakeCaret:d,isActive:u}=a.useContext(n.dK).slots[s];return(0,r.jsxs)("div",{ref:t,className:(0,l.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",u&&"z-10 ring-1 ring-ring",i),...o,children:[c,d&&(0,r.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});d.displayName="InputOTPSlot",a.forwardRef((e,t)=>{let{...s}=e;return(0,r.jsx)("div",{ref:t,role:"separator",...s,children:(0,r.jsx)(i.YTx,{})})}).displayName="InputOTPSeparator"},81251:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(95155),a=s(30070),i=s(89852),n=s(67943),l=s(12115),o=s(97168),c=s(78749),d=s(92657),u=s(53999);function m(e){let{form:t,label:s,name:m,placeholder:p,description:f,inputProps:x,labelClassName:h,containerClassName:v,inputContainer:b,variant:g="default"}=e,[j,w]=(0,l.useState)(!1);return(0,r.jsx)(a.zB,{control:t.control,name:m,render:e=>{let{field:t}=e;return(0,r.jsx)(n.A,{label:s,description:f,labelClassName:(0,u.cn)("float"==g?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:v,variant:g,children:(0,r.jsxs)("div",{className:(0,u.cn)("flex gap-2 w-full overflow-hidden","float"==g?"":"border rounded-sm focus-within:border-neutral-light",b),children:[(0,r.jsx)(i.p,{type:j?"text":"password",placeholder:p,...t,...x,className:(0,u.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==g?"px-0":"",null==x?void 0:x.className)}),(0,r.jsx)(o.$,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),w(e=>!e)},children:j?(0,r.jsx)(c.A,{className:"w-4 h-4"}):(0,r.jsx)(d.A,{className:"w-4 h-4"})})]})})}})}},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var r=s(95155),a=s(12115),i=s(40968),n=s(74466),l=s(53999);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.b,{ref:t,className:(0,l.cn)(o(),s),...a})});c.displayName=i.b.displayName},91531:(e,t,s)=>{"use strict";s.d(t,{default:()=>R});var r=s(95155),a=s(88145),i=s(88482),n=s(53999),l=s(46767),o=s(27043),c=s(72067),d=s(37996),u=s(31787),m=s(22190),p=s(97168),f=s(14570);async function x(e){try{return{data:(await (0,f.zp)(e)).data.data}}catch(e){var t;return{error:null!=(t=e.data.error)?t:"An unknown error occurred"}}}var h=s(19373),v=s(66766),b=s(12115),g=s(55594),j=s(62177),w=s(90221),N=s(30070),y=s(67943),A=s(69916),F=s(1184),k=s(18618),C=s(5041),I=s(53580),E=s(46797);function S(e){let{setOpenDialog:t}=e,s=(0,o.useTranslations)("seeker"),a=g.z.object({otp:g.z.string().max(6)}),{seekers:i}=(0,E.k)(),n=(0,C.n)({mutationFn:e=>(0,k._k)(e)}),l=(0,C.n)({mutationFn:e=>(0,k.zp)({...e,request_setting:"ACTIVE_2FA"})}),c=(0,j.mN)({resolver:(0,w.u)(a),defaultValues:{otp:""}}),{toast:d}=(0,I.dj)(),u=c.watch("otp"),m=async e=>{try{i.has2FA?await n.mutateAsync({otp:e.otp,requested_by:i.code}):await l.mutateAsync({otp:+e.otp,request_setting:"ACTIVE_2FA"}),d({title:s("success.activateTotp")}),null==t||t(!1),window.location.reload()}catch(t){let e=t.response.data;d({variant:"destructive",title:s("error.failedEnablingTwoFA"),description:e.message})}};return(0,b.useEffect)(()=>{if(6==u.length){var e;null==(e=document.getElementById("submit-form"))||e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))}},[u,c]),(0,r.jsx)(N.lV,{...c,children:(0,r.jsx)("form",{id:"submit-form",onSubmit:c.handleSubmit(m),children:(0,r.jsx)(N.zB,{control:c.control,name:"otp",render:e=>{let{field:t}=e;return(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsx)(y.A,{label:"",children:(0,r.jsx)(A.UV,{maxLength:6,...t,pattern:F.UO,required:!0,containerClassName:"flex justify-start",children:(0,r.jsx)(A.NV,{children:Array.from({length:6},(e,t)=>(0,r.jsx)(A.sF,{index:t,className:"w-10 h-10 text-xl"},t))})})})})}})})})}var T=s(81251);function _(e){let{onSubmit:t,isLoading:s}=e,a=(0,o.useTranslations)("seeker"),i=((0,o.useTranslations)("seeker"),g.z.object({password:g.z.string()})),{seekers:n}=(0,E.k)(),l=(0,j.mN)({resolver:(0,w.u)(i),defaultValues:{password:""}});return(0,r.jsx)(N.lV,{...l,children:(0,r.jsxs)("form",{onSubmit:l.handleSubmit(e=>{t(e.password)}),className:"space-y-4",children:[(0,r.jsx)(T.A,{label:a("form.label.password"),form:l,name:"password",placeholder:""}),(0,r.jsx)(p.$,{className:"w-full",variant:"default-seekers",loading:s,children:a("cta.get2FACode")})]})})}function U(e){var t;let{}=e,s=(0,o.useTranslations)("seeker"),[a,i]=(0,b.useState)(!1),[n,l]=(0,b.useState)(!1),{seekers:f}=(0,E.k)(),[g,j]=(0,b.useState)(!1),[w,N]=(0,b.useState)("ACTIVE_2FA"),[y,A]=(0,b.useState)({request_setting:"ACTIVE_2FA",password:""}),F=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,h.I)({queryKey:["two-fa-otp",e],queryFn:async()=>await x(e),retry:0,enabled:t})}(y,a&&n);return(0,b.useEffect)(()=>{f.has2FA&&(A(e=>({...e,request_setting:"INACTIVE_2FA"})),N("INACTIVE_2FA")),f.has2FA||(A(e=>({...e,request_setting:"REQUEST_2FA"})),N("REQUEST_2FA"))},[f.has2FA,a]),(0,b.useEffect)(()=>{if(F.isFetched&&F.isSuccess)return(l(!1),"INACTIVE_2FA"==w)?window.location.reload():void j(!0)},[F.isFetched,F.isSuccess,w]),(0,r.jsxs)(m.A,{open:a,setOpen:i,dialogClassName:"!max-w-fit !w-fit",openTrigger:(0,r.jsx)(p.$,{variant:"outline",size:"sm",className:f.has2FA?"border-red-500 text-red-500 hover:text-red-500 hover:bg-red-50":"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>{},children:s(f.has2FA?"cta.disable":"cta.enable")}),children:[(0,r.jsxs)(d.A,{className:"text-seekers-text text-center flex flex-col items-center",children:[(0,r.jsx)(u.A,{children:s("setting.profile.security.twoFA.title")}),(0,r.jsx)(c.A,{className:"text-seekers-text-light",children:s("setting.profile.security.twoFA.description")})]}),g?(0,r.jsxs)("div",{className:"flex max-sm:flex-col gap-4",children:[(0,r.jsx)("div",{className:"relative aspect-square",style:{margin:"0 auto",height:200,width:200},children:(0,r.jsx)(v.default,{src:(null==(t=F.data)?void 0:t.data.qr_image)||"",alt:"",fill:!0,style:{objectFit:"cover"}})}),(0,r.jsxs)("div",{className:"md:max-w-xs space-y-4",children:[(0,r.jsx)("h2",{className:"max-sm:text-sm max-sm:text-center text-xl font-bold",children:s("setting.profile.security.twoFA.useAuthenticatorApp")}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{children:[(0,r.jsxs)("span",{className:"font-bold",children:[s("misc.step",{count:1})," "]})," ",s("setting.profile.security.twoFA.scanQRCodeWithAuthenticatorApp")]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-bold",children:s("misc.step",{count:2})})," ",s("setting.profile.security.twoFA.enterAuthenticatorCode")]})]}),(0,r.jsx)(S,{setOpenDialog:e=>i(e)})]})]}):(0,r.jsx)("div",{children:(0,r.jsx)(_,{isLoading:F.isLoading,onSubmit:e=>{A(t=>({...t,password:e})),l(!0)}})})]})}function R(){let e=(0,o.useTranslations)("seeker"),{seekers:t}=(0,E.k)();return(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(i.ZB,{className:"text-seekers-primary flex items-center",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),e("setting.profile.security.twoFA.title")]}),(0,r.jsx)(i.BT,{children:e("setting.profile.security.twoFA.description")})]}),(0,r.jsx)(U,{enableTwoFA:t.has2FA})]}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:e("misc.status")}),(0,r.jsx)(a.E,{variant:t.has2FA?"default":"destructive",className:(0,n.cn)(t.has2FA?"bg-green-500/10 text-green-500":"bg-red-500/10 text-red-500"),children:e(t.has2FA?"cta.enable":"cta.disable")})]})})]})}}},e=>{e.O(0,[586,5105,1551,3903,7043,4134,723,7900,5521,7811,8079,7417,2803,9474,2688,6766,2098,9314,5560,8441,5964,7358],()=>e(e.s=51309)),_N_E=e.O()}]);