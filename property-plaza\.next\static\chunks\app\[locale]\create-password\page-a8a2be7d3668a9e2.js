(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8060],{14570:(e,t,s)=>{"use strict";s.d(t,{N$:()=>c,Q0:()=>m,Uu:()=>d,_k:()=>f,aH:()=>l,bH:()=>o,eD:()=>n,iD:()=>a,ri:()=>i,zp:()=>u});var r=s(99493);let a=(e,t)=>r.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),i=()=>r.apiClient.post("auth/logout"),n=e=>r.apiClient.post("notifications/email",e),o=e=>r.apiClient.post("auth/otp-verification",e),l=e=>r.apiClient.post("auth/forgot-password",e),d=e=>r.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),c=e=>r.apiClient.post("auth/reset-password",e),m=(e,t)=>r.apiClient.post("auth/create-password",e,t),u=e=>r.apiClient.post("users/security",e),f=e=>r.apiClient.post("auth/totp-verification",e)},14666:(e,t,s)=>{"use strict";s.d(t,{Dg:()=>a,Dj:()=>u,EM:()=>o,FN:()=>f,Ix:()=>h,Nr:()=>d,Xh:()=>r,Zu:()=>l,bV:()=>m,gF:()=>n,kj:()=>c,s7:()=>p,wz:()=>i});let r="tkn",a="SEEKER",i=8,n=1,o=30,l=300,d=10,c="cookies-collection-status",m="necessary-cookies-collection-status",u="functional-cookies-collection-status",f="analytic-cookies-collection-status",p="marketing-cookies-collection-status",h={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},18618:(e,t,s)=>{"use strict";s.d(t,{N$:()=>r.N$,Q0:()=>r.Q0,Uu:()=>r.Uu,_k:()=>r._k,aH:()=>r.aH,bH:()=>r.bH,eD:()=>r.eD,iD:()=>r.iD,ri:()=>r.ri,zp:()=>r.zp});var r=s(14570)},30070:(e,t,s)=>{"use strict";s.d(t,{C5:()=>x,MJ:()=>g,Rr:()=>w,eI:()=>p,lR:()=>h,lV:()=>d,zB:()=>m});var r=s(95155),a=s(12115),i=s(66634),n=s(62177),o=s(53999),l=s(82714);let d=n.Op,c=a.createContext({}),m=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(n.xI,{...t})})},u=()=>{let e=a.useContext(c),t=a.useContext(f),{getFieldState:s,formState:r}=(0,n.xW)(),i=s(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...i}},f=a.createContext({}),p=a.forwardRef((e,t)=>{let{className:s,...i}=e,n=a.useId();return(0,r.jsx)(f.Provider,{value:{id:n},children:(0,r.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",s),...i})})});p.displayName="FormItem";let h=a.forwardRef((e,t)=>{let{className:s,...a}=e,{error:i,formItemId:n}=u();return(0,r.jsx)(l.J,{ref:t,className:(0,o.cn)(i&&"text-destructive",s),htmlFor:n,...a})});h.displayName="FormLabel";let g=a.forwardRef((e,t)=>{let{...s}=e,{error:a,formItemId:n,formDescriptionId:o,formMessageId:l}=u();return(0,r.jsx)(i.DX,{ref:t,id:n,"aria-describedby":a?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!a,...s})});g.displayName="FormControl";let w=a.forwardRef((e,t)=>{let{className:s,...a}=e,{formDescriptionId:i}=u();return(0,r.jsx)("p",{ref:t,id:i,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",s),...a})});w.displayName="FormDescription";let x=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e,{error:n,formMessageId:l}=u(),d=n?String(null==n?void 0:n.message):a;return d?(0,r.jsx)("p",{ref:t,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-destructive",s),...i,children:d}):null});x.displayName="FormMessage"},53580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m});var r=s(12115);let a=0,i=new Map,n=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},o=[],l={toasts:[]};function d(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),o.forEach(e=>{e(l)})}function c(e){let{...t}=e,s=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({type:"DISMISS_TOAST",toastId:s});return d({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||r()}}}),{id:s,dismiss:r,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function m(){let[e,t]=r.useState(l);return r.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},53999:(e,t,s)=>{"use strict";s.d(t,{ZV:()=>d,cn:()=>o,gT:()=>c,jW:()=>u,lF:()=>p,q7:()=>f,tT:()=>h,vv:()=>l,yv:()=>m});var r=s(52596),a=s(82940),i=s.n(a),n=s(39688);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,r.$)(t))}s(87358);let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat((e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}})(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:2*("IDR"!=t)}).format(e)},d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function c(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function m(e){let t=i()(e),s=i()();return t.isSame(s,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function u(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function p(e,t){return e.some(e=>t.includes(e))}let h=e=>e.charAt(0).toUpperCase()+e.slice(1)},55183:(e,t,s)=>{Promise.resolve().then(s.bind(s,76349)),Promise.resolve().then(s.bind(s,45626)),Promise.resolve().then(s.bind(s,48882))},67943:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(95155),a=s(30070),i=s(53999);function n(e){let{children:t,description:s,label:n,containerClassName:o,labelClassName:l,variant:d="default"}=e;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)(a.eI,{className:(0,i.cn)("w-full relative","float"==d?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",o),onClick:e=>e.stopPropagation(),children:[n&&(0,r.jsx)(a.lR,{className:l,children:n}),(0,r.jsx)(a.MJ,{className:"group relative w-full",children:t}),s&&(0,r.jsx)(a.Rr,{children:s}),"default"==d&&(0,r.jsx)(a.C5,{})]}),"float"==d&&(0,r.jsx)(a.C5,{})]})}},72337:(e,t,s)=>{"use strict";s.d(t,{Y:()=>o,g:()=>n});var r=s(14666),a=s(27043),i=s(55594);let n=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function o(){let e=(0,a.useTranslations)("seeker");return i.z.object({firstName:i.z.string().min(r.gF,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:r.gF})}).max(r.EM,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:r.EM})}),lastName:i.z.string().min(r.gF,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:r.gF})}).max(r.EM,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:r.EM})}),contact:i.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password"))}),path:["confirmPassword"]})}},76349:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var r=s(95155),a=s(53580),i=s(27043),n=s(14666),o=s(55594),l=s(72337),d=s(62177),c=s(90221),m=s(30070),u=s(81251),f=s(97168),p=s(1221),h=s(18618),g=s(5041),w=s(12115),x=s(53999),v=s(5196),b=s(54416);function y(e){let{email:t,token:s}=e,y=(0,i.useTranslations)("universal"),{toast:N}=(0,a.dj)(),j=(0,p.useRouter)(),A=(0,i.useLocale)(),k=function(){let e=(0,i.useTranslations)("universal");return o.z.object({password:o.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(n.wz,{message:e("form.utility.minimumLength",{length:n.wz,field:e("form.field.password")})}).refine(e=>l.g.test(e),{message:e("form.utility.passwordWeak")}),confirmPassword:o.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password")," ").concat(e("conjuntion.and")," ").concat(e("form.field.confirmPassword"))}),path:["confirmPassword"]})}(),C=(0,g.n)({mutationFn:e=>(0,h.Q0)(e,{headers:{Authorization:e.token}})}),[P,D]=(0,w.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),S=(0,d.mN)({resolver:(0,c.u)(k),defaultValues:{password:"",confirmPassword:""}}),z=S.watch("password");async function R(e){let r={email:t,token:s,password:e.password,confirm_password:e.confirmPassword,locale:A};try{await C.mutateAsync(r),N({title:y("universal.success.createPassword.title"),description:y("success.createPassword.description")}),j.push("/")}catch(e){N({title:y("error.resetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return(0,w.useEffect)(()=>{z&&D({length:z.length>=8,number:/[0-9]/.test(z),special:/[!@#$%^&*()_+]/.test(z),notCommon:!["123456","password","qwerty"].includes(z.toLowerCase()),uppercase:/[A-Z]/.test(z),lowercase:/[a-z]/.test(z)})},[z]),(0,r.jsx)(m.lV,{...S,children:(0,r.jsxs)("form",{onSubmit:S.handleSubmit(R),className:"space-y-8",children:[(0,r.jsx)("div",{className:"space-y-2 text-center",children:(0,r.jsx)("h1",{className:"text-2xl font-semibold text-center",children:y("form.title.createPassword")})}),(0,r.jsxs)("div",{className:"space-y-2 md:min-w-80",children:[(0,r.jsx)(u.A,{form:S,name:"password",label:y("form.label.password"),placeholder:y("form.placeholder.basePlaceholder",{field:"".concat(y("form.field.password"))})}),(0,r.jsx)(u.A,{form:S,name:"confirmPassword",label:y("form.label.confirmPassword"),placeholder:y("form.placeholder.basePlaceholder",{field:"".concat(y("form.field.confirmPassword"))})})]}),z&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:(0,x.cn)(P.length?"text-green-500":"text-red-500"),children:[P.length?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.minimumLength")]}),(0,r.jsxs)("div",{className:(0,x.cn)(P.number?"text-green-500":"text-red-500"),children:[P.number?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.numberRequired")]}),(0,r.jsxs)("div",{className:(0,x.cn)(P.special?"text-green-500":"text-red-500"),children:[P.special?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.specialCharacter")]}),(0,r.jsxs)("div",{className:(0,x.cn)(P.notCommon?"text-green-500":"text-red-500"),children:[P.notCommon?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.notCommonWord")]}),(0,r.jsxs)("div",{className:(0,x.cn)(P.uppercase?"text-green-500":"text-red-500"),children:[P.uppercase?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.uppercaseRequired")]}),(0,r.jsxs)("div",{className:(0,x.cn)(P.lowercase?"text-green-500":"text-red-500"),children:[P.lowercase?(0,r.jsx)(v.A,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(b.A,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.lowercaseRequired")]})]}),(0,r.jsx)(f.$,{className:"w-full",variant:"default-seekers",loading:C.isPending,children:y("cta.changePassword")})]})})}},81251:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(95155),a=s(30070),i=s(89852),n=s(67943),o=s(12115),l=s(97168),d=s(78749),c=s(92657),m=s(53999);function u(e){let{form:t,label:s,name:u,placeholder:f,description:p,inputProps:h,labelClassName:g,containerClassName:w,inputContainer:x,variant:v="default"}=e,[b,y]=(0,o.useState)(!1);return(0,r.jsx)(a.zB,{control:t.control,name:u,render:e=>{let{field:t}=e;return(0,r.jsx)(n.A,{label:s,description:p,labelClassName:(0,m.cn)("float"==v?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",g),containerClassName:w,variant:v,children:(0,r.jsxs)("div",{className:(0,m.cn)("flex gap-2 w-full overflow-hidden","float"==v?"":"border rounded-sm focus-within:border-neutral-light",x),children:[(0,r.jsx)(i.p,{type:b?"text":"password",placeholder:f,...t,...h,className:(0,m.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==v?"px-0":"",null==h?void 0:h.className)}),(0,r.jsx)(l.$,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),y(e=>!e)},children:b?(0,r.jsx)(d.A,{className:"w-4 h-4"}):(0,r.jsx)(c.A,{className:"w-4 h-4"})})]})})}})}},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(95155),a=s(12115),i=s(40968),n=s(74466),o=s(53999);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.b,{ref:t,className:(0,o.cn)(l(),s),...a})});d.displayName=i.b.displayName},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(95155),a=s(12115),i=s(53999);let n=a.forwardRef((e,t)=>{let{className:s,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"},97168:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(95155),a=s(12115),i=s(66634),n=s(74466),o=s(53999),l=s(51154);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:n,asChild:c=!1,loading:m=!1,...u}=e,f=c?i.DX:"button";return(0,r.jsx)(f,{className:(0,o.cn)(d({variant:a,size:n,className:s})),ref:t,disabled:m||u.disabled,...u,children:m?(0,r.jsx)(l.A,{className:(0,o.cn)("h-4 w-4 animate-spin")}):u.children})});c.displayName="Button"},99493:(e,t,s)=>{"use strict";s.d(t,{B:()=>d,apiClient:()=>l});var r=s(14666),a=s(23464),i=s(57383),n=s(79189);let o=new(s.n(n)()).Agent({rejectUnauthorized:!1}),l=a.A.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:i.A.get(r.Xh)?"Bearer "+i.A.get(r.Xh):""},httpsAgent:o}),d=a.A.create({baseURL:"/api/",httpsAgent:o})}},e=>{e.O(0,[586,1551,3903,7043,4134,723,7900,5521,9790,8441,5964,7358],()=>e(e.s=55183)),_N_E=e.O()}]);