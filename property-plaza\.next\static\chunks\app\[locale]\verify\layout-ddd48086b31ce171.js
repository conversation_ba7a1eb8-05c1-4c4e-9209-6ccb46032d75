(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7008],{3404:(e,t,a)=>{"use strict";a.d(t,{default:()=>p});var i=a(95155),n=a(35695),s=a(32059),l=a(27043),o=a(86894);let r={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function d(e={}){let t,a,i,n,s=null,l=0,o=!1,c=!1,u=!1,p=!1;function m(){if(!i){o||a.emit("autoplay:play");let{ownerWindow:e}=a.internalEngine();e.clearTimeout(l),l=e.setTimeout(w,n[a.selectedScrollSnap()]),s=new Date().getTime(),a.emit("autoplay:timerset"),o=!0}}function f(){if(!i){o&&a.emit("autoplay:stop");let{ownerWindow:e}=a.internalEngine();e.clearTimeout(l),l=0,s=null,a.emit("autoplay:timerstopped"),o=!1}}function h(){if(v())return u=o,f();u&&m()}function v(){let{ownerDocument:e}=a.internalEngine();return"hidden"===e.visibilityState}function g(){c||f()}function x(){c||m()}function y(){c=!0,f()}function N(){c=!1,m()}function w(){let{index:e}=a.internalEngine(),i=e.clone().add(1).get(),n=a.scrollSnapList().length-1,s=t.stopOnLastSnap&&i===n;if(a.canScrollNext()?a.scrollNext(p):a.scrollTo(0,p),a.emit("autoplay:select"),s)return f();m()}return{name:"autoplay",options:e,init:function(s,l){a=s;let{mergeOptions:o,optionsAtMedia:c}=l,u=o(r,d.globalOptions);if(t=c(o(u,e)),a.scrollSnapList().length<=1)return;p=t.jump,i=!1,n=function(e,t){let a=e.scrollSnapList();return"number"==typeof t?a.map(()=>t):t(a,e)}(a,t.delay);let{eventStore:w,ownerDocument:b}=a.internalEngine(),_=!!a.internalEngine().options.watchDrag,S=function(e,t){let a=e.rootNode();return t&&t(a)||a}(a,t.rootNode);w.add(b,"visibilitychange",h),_&&a.on("pointerDown",g),_&&!t.stopOnInteraction&&a.on("pointerUp",x),t.stopOnMouseEnter&&w.add(S,"mouseenter",y),t.stopOnMouseEnter&&!t.stopOnInteraction&&w.add(S,"mouseleave",N),t.stopOnFocusIn&&a.on("slideFocusStart",f),t.stopOnFocusIn&&!t.stopOnInteraction&&w.add(a.containerNode(),"focusout",m),t.playOnInit&&!v()&&m()},destroy:function(){a.off("pointerDown",g).off("pointerUp",x).off("slideFocusStart",f),f(),i=!0,o=!1},play:function(e){void 0!==e&&(p=e),m()},stop:function(){o&&f()},reset:function(){o&&m()},isPlaying:function(){return o},timeUntilNext:function(){return s?n[a.selectedScrollSnap()]-(new Date().getTime()-s):null}}}d.globalOptions=void 0;let c=(0,a(19946).A)("Fan",[["path",{d:"M10.827 16.379a6.082 6.082 0 0 1-8.618-7.002l5.412 1.45a6.082 6.082 0 0 1 7.002-8.618l-1.45 5.412a6.082 6.082 0 0 1 8.618 7.002l-5.412-1.45a6.082 6.082 0 0 1-7.002 8.618l1.45-5.412Z",key:"484a7f"}],["path",{d:"M12 12v.01",key:"u5ubse"}]]);var u=a(61106);function p(){let e=d({delay:3e3,stopOnInteraction:!1}),t=(0,l.useTranslations)("seeker"),a=(0,n.usePathname)();return(0,i.jsxs)(i.Fragment,{children:[(a.includes("/s/"),(0,i.jsx)(i.Fragment,{})),(0,i.jsxs)("div",{className:"w-full py-3 bg-[#F7ECDC]",children:[(0,i.jsx)(s.A,{className:"md:hidden",children:(0,i.jsx)("div",{className:"w-full md:hidden",children:(0,i.jsx)(o.FN,{opts:{active:!0,loop:!0},plugins:[e],children:(0,i.jsxs)(o.Wk,{className:"text-seekers-primary",children:[(0,i.jsxs)(o.A7,{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(c,{className:"!w-5 !h-5"}),(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")})]}),(0,i.jsxs)(o.A7,{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(u.A,{className:"!w-5 !h-5"}),(0,i.jsx)("p",{className:" font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})})}),(0,i.jsx)(s.A,{className:"hidden md:block text-seekers-primary",children:(0,i.jsxs)("div",{className:"hidden md:flex gap-4 md:justify-between",children:[(0,i.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")}),(0,i.jsx)(c,{className:"!w-4 !h-4"})]}),(0,i.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(u.A,{className:"!w-4 !h-4"}),(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})]})]})}},26258:(e,t,a)=>{Promise.resolve().then(a.bind(a,3404)),Promise.resolve().then(a.bind(a,43666)),Promise.resolve().then(a.bind(a,84109)),Promise.resolve().then(a.bind(a,76037)),Promise.resolve().then(a.bind(a,45626)),Promise.resolve().then(a.bind(a,48882)),Promise.resolve().then(a.bind(a,78830)),Promise.resolve().then(a.t.bind(a,33063,23)),Promise.resolve().then(a.bind(a,88327))},61106:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(19946).A)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},79076:(e,t,a)=>{"use strict";a.d(t,{R:()=>o});var i=a(82940),n=a.n(i),s=a(88693),l=a(46786);let o=(0,s.vt)()((0,l.Zr)(e=>({currentLayout:"list",setlayout:t=>e({currentLayout:t}),roomId:void 0,setRoomId:t=>e({roomId:t}),chatDetail:[],setchatDetail:t=>e({chatDetail:t}),updatechatDetail:t=>e(e=>{let a=e.chatDetail.length;return e.chatDetail[a-1].id==t.id?{}:{chatDetail:[...e.chatDetail,t]}}),participant:void 0,setParticipant:t=>e({participant:t}),allChat:[],setAllChat:t=>e({allChat:t}),updateSpecificAllChat:(t,a,i)=>e(e=>{let{allChat:s}=e,l=e=>e.sort((e,t)=>n()(t.lastMessages.createdAt).unix()-n()(e.lastMessages.createdAt).unix());if(a)return{allChat:l([...s,t])};if(i){let e=s.findIndex(e=>e.code===i);if("roomId"in t)if(e<0)return{allChat:l([...s,t])};else{let a=[...s];return a[e]=t,{allChat:l(a)}}if("id"in t)if(e>=0)return{allChat:l(s.map((a,i)=>i===e?{...a,lastMessages:t}:a))};else return{allChat:s}}if("roomId"in t){let e=s.findIndex(e=>e.code===t.code);if(e<0)return{allChat:l([...s,t].sort((e,t)=>n()(t.lastMessages.createdAt).unix()-n()(e.lastMessages.createdAt).unix()))};{let a=[...s];return a[e]=t,{allChat:l(a)}}}if("id"in t){let e=s.findIndex(e=>e.code===t.code);if(e>=0)return{allChat:l(s.map((a,i)=>i===e?{...a,lastMessages:t}:a))}}return{allChat:s}}),updateParticipantStatus:t=>e(e=>e.participant?{participant:{...e.participant,status:t}}:{})}),{name:"messagingLayout",storage:(0,l.KU)(()=>sessionStorage)}))},80717:(e,t,a)=>{"use strict";a.d(t,{s:()=>l});var i=a(14298),n=a(57383),s=a(14666);let l=(0,i.Ay)("https://dev.property-plaza.id/",{extraHeaders:{"auth-token":n.A.get(s.Xh)||""},autoConnect:!1})},83120:(e,t,a)=>{"use strict";a.d(t,{aH:()=>s,t8:()=>l,tP:()=>o});var i=a(82940),n=a.n(i);function s(e){return e.map(e=>{var t,a,i,n,s,l;let o=e.messages[0];if(o)return{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:(null==o?void 0:o.created_at)||e.created_at,displayAs:(null==o?void 0:o.display_as)||"",displayName:(null==o?void 0:o.display_name)||"",text:(null==o?void 0:o.text)||"",isRead:(null==o?void 0:o.is_read)||!1,isSent:(null==o?void 0:o.is_send)||!1,id:(null==o?void 0:o.id)||"",code:(null==o?void 0:o.code)||""},participant:{email:(null==(t=e.participants.info)?void 0:t.email)||"",fullName:(null==(a=e.participants.info)?void 0:a.display_name)||"",phoneNumber:(null==(i=e.participants.info)?void 0:i.phone_number)||"",image:e.participants.info.image||"",id:(null==(n=e.participants.info)?void 0:n.id)||"",category:e.category,status:e.status,property:{title:(null==(s=e.ref_data)?void 0:s.title)||void 0,image:(null==(l=e.ref_data)?void 0:l.images[0].image)||void 0},otherProperty:[]},status:e.status,updatedAt:e.updated_at}}).filter(e=>void 0!==e).sort((e,t)=>n()(t.lastMessages.createdAt).unix()-n()(e.lastMessages.createdAt).unix())}function l(e){return{createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code}}function o(e){var t,a,i,n,s,l,o,r,d,c,u,p;console.log(e.messages);let m=e.messages[(null==(t=e.messages)?void 0:t.length)-1]||void 0,f=e.messages.map(e=>({createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code})),h=null==(i=e.ref_data)||null==(a=i.extended_list)?void 0:a.map(e=>{var t;return{id:e.code,image:(null==(t=e.images[0])?void 0:t.image)||"",title:e.title}});return{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:m.created_at,displayAs:m.display_as,displayName:m.display_name,text:m.text,isRead:m.is_read,isSent:m.is_send,id:m.id,code:m.code||""},participant:{email:(null==(n=e.participants.info)?void 0:n.email)||"",fullName:(null==(s=e.participants.info)?void 0:s.display_name)||"",phoneNumber:(null==(l=e.participants.info)?void 0:l.phone_number)||"",image:(null==(o=e.participants.info)?void 0:o.image)||"",id:(null==(r=e.participants.info)?void 0:r.id)||"",category:e.category,status:e.status,property:{id:(null==(d=e.ref_data)?void 0:d.code)||"",image:(null==(u=e.ref_data)||null==(c=u.images[0])?void 0:c.image)||"",title:(null==(p=e.ref_data)?void 0:p.title)||""},moreProperty:h||[]},allMessages:f,updatedAt:e.updated_at}}},84109:(e,t,a)=>{"use strict";a.d(t,{default:()=>m});var i=a(95155),n=a(83120),s=a(80717),l=a(53580),o=a(79076),r=a(88693),d=a(46786);let c=(0,r.vt)()((0,d.Zr)(e=>({hasNotificationSound:void 0,setNotificationSound:t=>e({hasNotificationSound:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t}),editingStatus:[],setEditingStatus:(t,a)=>e(e=>{let i=e.editingStatus;if("add"==a)return i.includes(t)?{...e}:(i.push(t),{...e,editingStatus:i});if("remove"==a){let a=i.filter(e=>e!==t);return{...e,editingStatus:a}}return{...e}}),removeEditingStatus:()=>e({editingStatus:[]})}),{name:"settings",storage:(0,d.KU)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}));var u=a(12115),p=a(27043);function m(e){let{isSeeker:t=!1}=e;(0,p.useTranslations)("universal");let{toast:a}=(0,l.dj)(),[r,d]=(0,u.useState)(!1),{updatechatDetail:m,updateSpecificAllChat:f}=(0,o.R)(e=>e),{hasNotificationSound:h,isLoading:v}=c(e=>e),{enableSoundNotification:g,playSound:x,popUpNotification:y}=(()=>{let{setNotificationSound:e}=c(e=>e),[t,a]=(0,u.useState)(null);return(0,u.useEffect)(()=>{let e=new Audio;e.src="/sounds/notification.mp3",a(e)},[]),{enableSoundNotification:t=>{e(t)},playSound:()=>{let e=new Audio;e.src="/sounds/notification.mp3",e.volume=1,e.play().then(()=>{}).catch(e=>{console.error("sound error",e)})},popUpNotification:(e,t)=>{if(!("Notification"in window))return void console.warn("This browser does not support desktop notifications.");"granted"===Notification.permission?new Notification(e,{body:t||""}):"default"===Notification.permission?Notification.requestPermission().then(a=>{"granted"===a?new Notification(e,{body:t||""}):console.warn("Notification permission denied.")}):"denied"===Notification.permission&&console.warn("Notifications are denied by the user.")}}})();return(0,u.useEffect)(()=>{s.s.connected||s.s.connect();let e=e=>{let t=(0,n.t8)(e);a({title:"new message from "+t.displayName,description:t.text}),window.dispatchEvent(new CustomEvent("newMessage",{detail:t}))};return s.s.on("newChatNotif",e),()=>{s.s.off("newChatNotif",e)}},[]),(0,u.useEffect)(()=>{let e=e=>{x(),y("new messsage from "+e.detail.displayName,e.detail.text),m(e.detail),f(e.detail)};return window.addEventListener("newMessage",e),()=>{window.removeEventListener("newMessage",e)}},[x]),(0,u.useEffect)(()=>{v||(void 0==h?d(!0):d(!1))},[h]),(0,i.jsx)(i.Fragment,{})}},86894:(e,t,a)=>{"use strict";a.d(t,{A7:()=>h,FN:()=>m,Oj:()=>g,Q8:()=>v,Wk:()=>f,ZZ:()=>x});var i=a(95155),n=a(12115),s=a(85005),l=a(53999),o=a(97168),r=a(42355),d=a(13052),c=a(27043);let u=n.createContext(null);function p(){let e=n.useContext(u);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let m=n.forwardRef((e,t)=>{let{orientation:a="horizontal",opts:o,setApi:r,plugins:d,className:c,children:p,...m}=e,[f,h]=(0,s.A)({...o,axis:"horizontal"===a?"x":"y"},d),[v,g]=n.useState(!1),[x,y]=n.useState(!1),[N,w]=n.useState(0),b=n.useCallback(e=>{e&&(g(e.canScrollPrev()),y(e.canScrollNext()),w(e.selectedScrollSnap()))},[]),_=n.useCallback(()=>{null==h||h.scrollPrev()},[h]),S=n.useCallback(()=>{null==h||h.scrollNext()},[h]),j=n.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),_()):"ArrowRight"===e.key&&(e.preventDefault(),S())},[_,S]),C=n.useCallback(e=>{null==h||h.scrollTo(e)},[h]);return n.useEffect(()=>{h&&r&&r(h)},[h,r]),n.useEffect(()=>{if(h)return b(h),h.on("reInit",b),h.on("select",b),()=>{null==h||h.off("select",b)}},[h,b]),(0,i.jsx)(u.Provider,{value:{carouselRef:f,api:h,opts:o,orientation:a||((null==o?void 0:o.axis)==="y"?"vertical":"horizontal"),scrollPrev:_,scrollNext:S,canScrollPrev:v,canScrollNext:x,selectedIndex:N,scrollTo:C},children:(0,i.jsx)("div",{...m,ref:t,onKeyDownCapture:j,className:(0,l.cn)("relative",c),role:"region","aria-roledescription":"carousel",children:p})})});m.displayName="Carousel";let f=n.forwardRef((e,t)=>{let{className:a,...n}=e,{carouselRef:s,orientation:o}=p();return(0,i.jsx)("div",{ref:s,className:"overflow-hidden",children:(0,i.jsx)("div",{...n,ref:t,className:(0,l.cn)("flex","horizontal"===o?"-ml-4":"-mt-4 flex-col",a)})})});f.displayName="CarouselContent";let h=n.forwardRef((e,t)=>{let{className:a,...n}=e,{orientation:s}=p();return(0,i.jsx)("div",{...n,ref:t,role:"group","aria-roledescription":"slide",className:(0,l.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",a)})});h.displayName="CarouselItem";let v=n.forwardRef((e,t)=>{let{iconClassName:a,className:n,variant:s="outline",size:d="icon",...u}=e,{orientation:m,scrollPrev:f,canScrollPrev:h}=p(),v=(0,c.useTranslations)("universal");return(0,i.jsxs)(o.$,{...u,ref:t,variant:s,size:d,className:(0,l.cn)("absolute  h-6 w-6 rounded-full","horizontal"===m?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",n),disabled:!h,onClick:f,children:[(0,i.jsx)(r.A,{className:(0,l.cn)("h-4 w-4",a)}),(0,i.jsx)("span",{className:"sr-only",children:v("cta.previous")})]})});v.displayName="CarouselPrevious";let g=n.forwardRef((e,t)=>{let{iconClassName:a,className:n,variant:s="outline",size:r="icon",...u}=e,{orientation:m,scrollNext:f,canScrollNext:h}=p(),v=(0,c.useTranslations)("seeker");return(0,i.jsxs)(o.$,{...u,ref:t,variant:s,size:r,className:(0,l.cn)("absolute h-6 w-6 rounded-full","horizontal"===m?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",n),disabled:!h,onClick:f,children:[(0,i.jsx)(d.A,{className:(0,l.cn)("h-4 w-4",a)}),(0,i.jsx)("span",{className:"sr-only",children:v("cta.next")})]})});g.displayName="CarouselNext";let x=n.forwardRef((e,t)=>{let{className:a,carouselDotClassName:n,...s}=e,{selectedIndex:r,scrollTo:d,api:c}=p();return(0,i.jsx)("div",{ref:t,className:(0,l.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",a),...s,children:null==c?void 0:c.scrollSnapList().map((e,t)=>(0,i.jsx)(o.$,{size:"icon",className:(0,l.cn)(n,"embla__dot h-2 w-2 rounded-full ",t===r?"bg-white/90 ":"bg-black/10"),onClick:()=>null==d?void 0:d(t)},t))})});x.displayName="CarouselDots"}},e=>{e.O(0,[586,5105,6711,7753,4935,8533,1551,3903,7043,4134,723,7900,5521,7811,8079,7417,2803,9474,2688,6766,6389,7823,3181,6307,2043,7083,7735,9165,1258,9131,3666,8441,5964,7358],()=>e(e.s=26258)),_N_E=e.O()}]);