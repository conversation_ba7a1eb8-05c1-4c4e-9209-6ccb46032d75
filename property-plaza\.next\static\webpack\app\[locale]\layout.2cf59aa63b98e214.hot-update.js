"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./app/[locale]/globals.css":
/*!**********************************!*\
  !*** ./app/[locale]/globals.css ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"de485c1666ff\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9bbG9jYWxlXS9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL1tsb2NhbGVdL2dsb2JhbHMuY3NzPzg5NzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkZTQ4NWMxNjY2ZmZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/providers/notification-provider.tsx":
/*!********************************************************!*\
  !*** ./components/providers/notification-provider.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _core_infrastructures_messages_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/core/infrastructures/messages/transform */ \"(app-pages-browser)/./core/infrastructures/messages/transform.ts\");\n/* harmony import */ var _core_utils_socket__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/core/utils/socket */ \"(app-pages-browser)/./core/utils/socket.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _stores_messaging_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/messaging.store */ \"(app-pages-browser)/./stores/messaging.store.ts\");\n/* harmony import */ var _stores_setting_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/stores/setting.store */ \"(app-pages-browser)/./stores/setting.store.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _hooks_use_notification__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-notification */ \"(app-pages-browser)/./hooks/use-notification.ts\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction NotificationProvider(param) {\n    let { isSeeker = false } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"seeker\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { updatechatDetail, updateSpecificAllChat } = (0,_stores_messaging_store__WEBPACK_IMPORTED_MODULE_4__.useMessagingStore)((state)=>state);\n    const { hasNotificationSound, isLoading } = (0,_stores_setting_store__WEBPACK_IMPORTED_MODULE_5__.useSettingStore)((state)=>state);\n    const { enableSoundNotification, playSound, popUpNotification } = (0,_hooks_use_notification__WEBPACK_IMPORTED_MODULE_7__.useNotification)();\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (!_core_utils_socket__WEBPACK_IMPORTED_MODULE_2__.socket.connected) {\n            _core_utils_socket__WEBPACK_IMPORTED_MODULE_2__.socket.connect();\n        }\n        const receiveMessageHandler = (data)=>{\n            // if(!hasNotificationSound) return\n            const message = (0,_core_infrastructures_messages_transform__WEBPACK_IMPORTED_MODULE_1__.transformMessageText)(data);\n            toast({\n                title: t(\"message.newMessage\") + message.displayName,\n                description: message.text\n            });\n            window.dispatchEvent(new CustomEvent(\"newMessage\", {\n                detail: message\n            }));\n        };\n        _core_utils_socket__WEBPACK_IMPORTED_MODULE_2__.socket.on(\"newChatNotif\", receiveMessageHandler);\n        // Clean up to remove listeners and prevent duplicate subscriptions\n        return ()=>{\n            _core_utils_socket__WEBPACK_IMPORTED_MODULE_2__.socket.off(\"newChatNotif\", receiveMessageHandler);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        const handleNewMessage = (event)=>{\n            playSound();\n            popUpNotification(t(\"message.newMessage\") + event.detail.displayName, event.detail.text);\n            updatechatDetail(event.detail);\n            updateSpecificAllChat(event.detail);\n        };\n        // Listen for custom \"newMessage\" event\n        window.addEventListener(\"newMessage\", handleNewMessage);\n        return ()=>{\n            window.removeEventListener(\"newMessage\", handleNewMessage);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        playSound\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (isLoading) return;\n        if (hasNotificationSound == undefined) {\n            setOpen(true);\n        } else {\n            setOpen(false);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        hasNotificationSound\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n}\n_s(NotificationProvider, \"6uVnDas+cvbvE2nx8Fc2r6UFZas=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast,\n        _stores_messaging_store__WEBPACK_IMPORTED_MODULE_4__.useMessagingStore,\n        _stores_setting_store__WEBPACK_IMPORTED_MODULE_5__.useSettingStore,\n        _hooks_use_notification__WEBPACK_IMPORTED_MODULE_7__.useNotification\n    ];\n});\n_c = NotificationProvider;\nvar _c;\n$RefreshReg$(_c, \"NotificationProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzL25vdGlmaWNhdGlvbi1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHZ0Y7QUFDcEM7QUFDQTtBQUNnQjtBQUNKO0FBQ2I7QUFJZTtBQUVmO0FBRTVCLFNBQVNTLHFCQUFxQixLQUE0QztRQUE1QyxFQUFFQyxXQUFXLEtBQUssRUFBMEIsR0FBNUM7O0lBQzNDLE1BQU1DLElBQUlILDBEQUFlQSxDQUFDO0lBQzFCLE1BQU0sRUFBRUksS0FBSyxFQUFFLEdBQUdWLDBEQUFRQTtJQUMxQixNQUFNLENBQUNXLE1BQU1DLFFBQVEsR0FBR1IsK0NBQVFBLENBQUM7SUFDakMsTUFBTSxFQUFFUyxnQkFBZ0IsRUFBRUMscUJBQXFCLEVBQUUsR0FBR2IsMEVBQWlCQSxDQUFDYyxDQUFBQSxRQUFTQTtJQUMvRSxNQUFNLEVBQUVDLG9CQUFvQixFQUFFQyxTQUFTLEVBQUUsR0FBR2Ysc0VBQWVBLENBQUNhLENBQUFBLFFBQVNBO0lBQ3JFLE1BQU0sRUFBRUcsdUJBQXVCLEVBQUVDLFNBQVMsRUFBRUMsaUJBQWlCLEVBQUUsR0FBR2Ysd0VBQWVBO0lBQ2pGRixnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0osc0RBQU1BLENBQUNzQixTQUFTLEVBQUU7WUFDckJ0QixzREFBTUEsQ0FBQ3VCLE9BQU87UUFDaEI7UUFDQSxNQUFNQyx3QkFBd0IsQ0FBQ0M7WUFDN0IsbUNBQW1DO1lBQ25DLE1BQU1DLFVBQVUzQiw4RkFBb0JBLENBQUMwQjtZQUNyQ2QsTUFBTTtnQkFDSmdCLE9BQU9qQixFQUFFLHdCQUF3QmdCLFFBQVFFLFdBQVc7Z0JBQ3BEQyxhQUFhSCxRQUFRSSxJQUFJO1lBQzNCO1lBQ0FDLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGNBQWM7Z0JBQUVDLFFBQVFSO1lBQVE7UUFDdkU7UUFFQTFCLHNEQUFNQSxDQUFDbUMsRUFBRSxDQUFDLGdCQUFnQlg7UUFFMUIsbUVBQW1FO1FBQ25FLE9BQU87WUFDTHhCLHNEQUFNQSxDQUFDb0MsR0FBRyxDQUFDLGdCQUFnQlo7UUFDN0I7SUFFQSx1REFBdUQ7SUFDekQsR0FBRyxFQUFFO0lBQ0xwQixnREFBU0EsQ0FBQztRQUNSLE1BQU1pQyxtQkFBbUIsQ0FBQ0M7WUFDeEJsQjtZQUNBQyxrQkFBa0JYLEVBQUUsd0JBQXdCNEIsTUFBTUosTUFBTSxDQUFDTixXQUFXLEVBQUVVLE1BQU1KLE1BQU0sQ0FBQ0osSUFBSTtZQUN2RmhCLGlCQUFpQndCLE1BQU1KLE1BQU07WUFDN0JuQixzQkFBc0J1QixNQUFNSixNQUFNO1FBRXBDO1FBQ0EsdUNBQXVDO1FBQ3ZDSCxPQUFPUSxnQkFBZ0IsQ0FBQyxjQUFjRjtRQUV0QyxPQUFPO1lBQ0xOLE9BQU9TLG1CQUFtQixDQUFDLGNBQWNIO1FBQzNDO0lBRUEsdURBQXVEO0lBQ3pELEdBQUc7UUFBQ2pCO0tBQVU7SUFFZGhCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWMsV0FBVztRQUNmLElBQUlELHdCQUF3QndCLFdBQVc7WUFDckM1QixRQUFRO1FBQ1YsT0FBTztZQUNMQSxRQUFRO1FBQ1Y7SUFFQSx1REFBdUQ7SUFDekQsR0FBRztRQUFDSTtLQUFxQjtJQUV6QixxQkFBTztBQThCVDtHQXpGd0JUOztRQUNaRCxzREFBZUE7UUFDUE4sc0RBQVFBO1FBRTBCQyxzRUFBaUJBO1FBQ3pCQyxrRUFBZUE7UUFDT0csb0VBQWVBOzs7S0FOM0RFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvcHJvdmlkZXJzL25vdGlmaWNhdGlvbi1wcm92aWRlci50c3g/YmVkNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxyXG5cclxuaW1wb3J0IHsgTWVzc2FnZVRleHREdG8gfSBmcm9tIFwiQC9jb3JlL2luZnJhc3RydWN0dXJlcy9tZXNzYWdlcy9kdG9cIlxyXG5pbXBvcnQgeyB0cmFuc2Zvcm1NZXNzYWdlVGV4dCB9IGZyb20gXCJAL2NvcmUvaW5mcmFzdHJ1Y3R1cmVzL21lc3NhZ2VzL3RyYW5zZm9ybVwiXHJcbmltcG9ydCB7IHNvY2tldCB9IGZyb20gXCJAL2NvcmUvdXRpbHMvc29ja2V0XCJcclxuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tIFwiQC9ob29rcy91c2UtdG9hc3RcIlxyXG5pbXBvcnQgeyB1c2VNZXNzYWdpbmdTdG9yZSB9IGZyb20gXCJAL3N0b3Jlcy9tZXNzYWdpbmcuc3RvcmVcIlxyXG5pbXBvcnQgeyB1c2VTZXR0aW5nU3RvcmUgfSBmcm9tIFwiQC9zdG9yZXMvc2V0dGluZy5zdG9yZVwiXHJcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgRGlhbG9nV3JhcHBlciBmcm9tIFwiLi4vZGlhbG9nLXdyYXBwZXIvZGlhbG9nLXdyYXBwZXJcIlxyXG5pbXBvcnQgeyBEaWFsb2dUaXRsZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtZGlhbG9nXCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIi4uL3VpL2J1dHRvblwiXHJcbmltcG9ydCB7IHVzZU5vdGlmaWNhdGlvbiB9IGZyb20gXCJAL2hvb2tzL3VzZS1ub3RpZmljYXRpb25cIlxyXG5pbXBvcnQgRGlhbG9nSGVhZGVyV3JhcHBlciBmcm9tIFwiLi4vZGlhbG9nLXdyYXBwZXIvZGlhbG9nLWhlYWRlci13cmFwcGVyXCJcclxuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb25zIH0gZnJvbSBcIm5leHQtaW50bFwiXHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RpZmljYXRpb25Qcm92aWRlcih7IGlzU2Vla2VyID0gZmFsc2UgfTogeyBpc1NlZWtlcj86IGJvb2xlYW4gfSkge1xyXG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJzZWVrZXJcIilcclxuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpXHJcbiAgY29uc3QgW29wZW4sIHNldE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXHJcbiAgY29uc3QgeyB1cGRhdGVjaGF0RGV0YWlsLCB1cGRhdGVTcGVjaWZpY0FsbENoYXQgfSA9IHVzZU1lc3NhZ2luZ1N0b3JlKHN0YXRlID0+IHN0YXRlKVxyXG4gIGNvbnN0IHsgaGFzTm90aWZpY2F0aW9uU291bmQsIGlzTG9hZGluZyB9ID0gdXNlU2V0dGluZ1N0b3JlKHN0YXRlID0+IHN0YXRlKVxyXG4gIGNvbnN0IHsgZW5hYmxlU291bmROb3RpZmljYXRpb24sIHBsYXlTb3VuZCwgcG9wVXBOb3RpZmljYXRpb24gfSA9IHVzZU5vdGlmaWNhdGlvbigpXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghc29ja2V0LmNvbm5lY3RlZCkge1xyXG4gICAgICBzb2NrZXQuY29ubmVjdCgpXHJcbiAgICB9XHJcbiAgICBjb25zdCByZWNlaXZlTWVzc2FnZUhhbmRsZXIgPSAoZGF0YTogTWVzc2FnZVRleHREdG8pID0+IHtcclxuICAgICAgLy8gaWYoIWhhc05vdGlmaWNhdGlvblNvdW5kKSByZXR1cm5cclxuICAgICAgY29uc3QgbWVzc2FnZSA9IHRyYW5zZm9ybU1lc3NhZ2VUZXh0KGRhdGEpO1xyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IHQoJ21lc3NhZ2UubmV3TWVzc2FnZScpICsgbWVzc2FnZS5kaXNwbGF5TmFtZSxcclxuICAgICAgICBkZXNjcmlwdGlvbjogbWVzc2FnZS50ZXh0XHJcbiAgICAgIH0pO1xyXG4gICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoXCJuZXdNZXNzYWdlXCIsIHsgZGV0YWlsOiBtZXNzYWdlIH0pKTtcclxuICAgIH07XHJcblxyXG4gICAgc29ja2V0Lm9uKFwibmV3Q2hhdE5vdGlmXCIsIHJlY2VpdmVNZXNzYWdlSGFuZGxlcik7XHJcblxyXG4gICAgLy8gQ2xlYW4gdXAgdG8gcmVtb3ZlIGxpc3RlbmVycyBhbmQgcHJldmVudCBkdXBsaWNhdGUgc3Vic2NyaXB0aW9uc1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgc29ja2V0Lm9mZihcIm5ld0NoYXROb3RpZlwiLCByZWNlaXZlTWVzc2FnZUhhbmRsZXIpO1xyXG4gICAgfTtcclxuXHJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXHJcbiAgfSwgW10pXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGhhbmRsZU5ld01lc3NhZ2UgPSAoZXZlbnQ6IGFueSkgPT4ge1xyXG4gICAgICBwbGF5U291bmQoKVxyXG4gICAgICBwb3BVcE5vdGlmaWNhdGlvbih0KCdtZXNzYWdlLm5ld01lc3NhZ2UnKSArIGV2ZW50LmRldGFpbC5kaXNwbGF5TmFtZSwgZXZlbnQuZGV0YWlsLnRleHQpXHJcbiAgICAgIHVwZGF0ZWNoYXREZXRhaWwoZXZlbnQuZGV0YWlsKTtcclxuICAgICAgdXBkYXRlU3BlY2lmaWNBbGxDaGF0KGV2ZW50LmRldGFpbClcclxuXHJcbiAgICB9XHJcbiAgICAvLyBMaXN0ZW4gZm9yIGN1c3RvbSBcIm5ld01lc3NhZ2VcIiBldmVudFxyXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJuZXdNZXNzYWdlXCIsIGhhbmRsZU5ld01lc3NhZ2UpXHJcblxyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJuZXdNZXNzYWdlXCIsIGhhbmRsZU5ld01lc3NhZ2UpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFtwbGF5U291bmRdKVxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzTG9hZGluZykgcmV0dXJuXHJcbiAgICBpZiAoaGFzTm90aWZpY2F0aW9uU291bmQgPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgIHNldE9wZW4odHJ1ZSlcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldE9wZW4oZmFsc2UpXHJcbiAgICB9XHJcblxyXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xyXG4gIH0sIFtoYXNOb3RpZmljYXRpb25Tb3VuZF0pXHJcblxyXG4gIHJldHVybiA8PlxyXG4gICAgey8qIFRlbXBvcmFyeSBjb21tZW50IHRoaXMgICovfVxyXG4gICAgey8qIDxEaWFsb2dXcmFwcGVyXHJcbiAgICAgIG9wZW49e29wZW59XHJcbiAgICAgIHNldE9wZW49e3NldE9wZW59XHJcbiAgICAgIG9wZW5UcmlnZ2VyPXs8PjwvPn1cclxuICAgICAgZGlhbG9nQ2xhc3NOYW1lPVwibGc6bWF4LXctc21cIlxyXG4gICAgPlxyXG4gICAgICA8RGlhbG9nSGVhZGVyV3JhcHBlciBjbGFzc05hbWU9XCJ0ZXh0LXN0YXJ0IHB4LTBcIj5cclxuICAgICAgICA8RGlhbG9nVGl0bGUgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZFwiPnt0KCdtaXNjLmVuYWJsZVNvdW5kTm90aWZpY2F0aW9uLnRpdGxlJyl9PC9EaWFsb2dUaXRsZT5cclxuICAgICAgPC9EaWFsb2dIZWFkZXJXcmFwcGVyPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBcIj5cclxuICAgICAgICA8cCBjbGFzc05hbWU9XCJtYXgtc206dGV4dC1jZW50ZXIgbWF4LXNtOm1heC13LVsyNTZweF0gbWF4LXNtOm14LWF1dG9cIj57dCgnbWlzYy5lbmFibGVTb3VuZE5vdGlmaWNhdGlvbi5kZXNjcmlwdGlvbicpfTwvcD5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggbWF4LXNtOmZsZXgtY29sIGdhcC0yXCI+XHJcbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9e1wiZ2hvc3RcIn0gY2xhc3NOYW1lPVwibWF4LXNtOnctZnVsbCBtYXgtc206b3JkZXItbGFzdFwiIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgZW5hYmxlU291bmROb3RpZmljYXRpb24oZmFsc2UpXHJcbiAgICAgICAgICAgIHNldE9wZW4oZmFsc2UpXHJcbiAgICAgICAgICB9fT57dCgnY3RhLmRpc2FibGUnKX08L0J1dHRvbj5cclxuICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXNtOnctZnVsbCBtYXgtc206b3JkZXItZmlyc3RcIlxyXG4gICAgICAgICAgICB2YXJpYW50PXtpc1NlZWtlciA/IFwiZGVmYXVsdC1zZWVrZXJzXCIgOiAnZGVmYXVsdCd9XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICBlbmFibGVTb3VuZE5vdGlmaWNhdGlvbih0cnVlKVxyXG4gICAgICAgICAgICAgIHNldE9wZW4oZmFsc2UpXHJcblxyXG4gICAgICAgICAgICB9fT57dCgnY3RhLmVuYWJsZScpfTwvQnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvRGlhbG9nV3JhcHBlcj4gKi99XHJcbiAgPC8+XHJcbn0iXSwibmFtZXMiOlsidHJhbnNmb3JtTWVzc2FnZVRleHQiLCJzb2NrZXQiLCJ1c2VUb2FzdCIsInVzZU1lc3NhZ2luZ1N0b3JlIiwidXNlU2V0dGluZ1N0b3JlIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VOb3RpZmljYXRpb24iLCJ1c2VUcmFuc2xhdGlvbnMiLCJOb3RpZmljYXRpb25Qcm92aWRlciIsImlzU2Vla2VyIiwidCIsInRvYXN0Iiwib3BlbiIsInNldE9wZW4iLCJ1cGRhdGVjaGF0RGV0YWlsIiwidXBkYXRlU3BlY2lmaWNBbGxDaGF0Iiwic3RhdGUiLCJoYXNOb3RpZmljYXRpb25Tb3VuZCIsImlzTG9hZGluZyIsImVuYWJsZVNvdW5kTm90aWZpY2F0aW9uIiwicGxheVNvdW5kIiwicG9wVXBOb3RpZmljYXRpb24iLCJjb25uZWN0ZWQiLCJjb25uZWN0IiwicmVjZWl2ZU1lc3NhZ2VIYW5kbGVyIiwiZGF0YSIsIm1lc3NhZ2UiLCJ0aXRsZSIsImRpc3BsYXlOYW1lIiwiZGVzY3JpcHRpb24iLCJ0ZXh0Iiwid2luZG93IiwiZGlzcGF0Y2hFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwib24iLCJvZmYiLCJoYW5kbGVOZXdNZXNzYWdlIiwiZXZlbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVuZGVmaW5lZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/providers/notification-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./core/infrastructures/messages/transform.ts":
/*!****************************************************!*\
  !*** ./core/infrastructures/messages/transform.ts ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transformMessage: function() { return /* binding */ transformMessage; },\n/* harmony export */   transformMessageDetail: function() { return /* binding */ transformMessageDetail; },\n/* harmony export */   transformMessageText: function() { return /* binding */ transformMessageText; }\n/* harmony export */ });\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! moment */ \"(app-pages-browser)/./node_modules/moment/moment.js\");\n/* harmony import */ var moment__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(moment__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_transform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/transform */ \"(app-pages-browser)/./core/infrastructures/utils/transform.ts\");\n\n\nfunction transformMessage(dto) {\n    let locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    const messages = dto.map((item)=>{\n        var _item_ref_data, _item_participants_info, _item_participants_info1, _item_participants_info2, _item_participants_info3, _item_ref_data1, _item_ref_data2;\n        const lastMessage = item.messages[0];\n        if (!lastMessage) return undefined;\n        console.log(locale, (0,_utils_transform__WEBPACK_IMPORTED_MODULE_1__.ValueBasedOnLocale)((_item_ref_data = item.ref_data) === null || _item_ref_data === void 0 ? void 0 : _item_ref_data.title, locale));\n        return {\n            code: item.code,\n            category: item.category,\n            roomId: item.participants.room_id,\n            lastMessages: {\n                createdAt: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.created_at) || item.created_at,\n                displayAs: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.display_as) || \"\",\n                displayName: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.display_name) || \"\",\n                text: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.text) || \"\",\n                isRead: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.is_read) || false,\n                isSent: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.is_send) || false,\n                id: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.id) || \"\",\n                code: (lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.code) || \"\"\n            },\n            participant: {\n                email: ((_item_participants_info = item.participants.info) === null || _item_participants_info === void 0 ? void 0 : _item_participants_info.email) || \"\",\n                fullName: ((_item_participants_info1 = item.participants.info) === null || _item_participants_info1 === void 0 ? void 0 : _item_participants_info1.display_name) || \"\",\n                phoneNumber: ((_item_participants_info2 = item.participants.info) === null || _item_participants_info2 === void 0 ? void 0 : _item_participants_info2.phone_number) || \"\",\n                image: item.participants.info.image || \"\",\n                id: ((_item_participants_info3 = item.participants.info) === null || _item_participants_info3 === void 0 ? void 0 : _item_participants_info3.id) || \"\",\n                category: item.category,\n                status: item.status,\n                property: {\n                    title: (0,_utils_transform__WEBPACK_IMPORTED_MODULE_1__.ValueBasedOnLocale)((_item_ref_data1 = item.ref_data) === null || _item_ref_data1 === void 0 ? void 0 : _item_ref_data1.title, locale) || undefined,\n                    image: ((_item_ref_data2 = item.ref_data) === null || _item_ref_data2 === void 0 ? void 0 : _item_ref_data2.images[0].image) || undefined\n                },\n                otherProperty: []\n            },\n            status: item.status,\n            updatedAt: item.updated_at\n        };\n    });\n    const filteredMessages = messages.filter((item)=>item !== undefined);\n    const sortedMessage = filteredMessages.sort((a, b)=>moment__WEBPACK_IMPORTED_MODULE_0___default()(b.lastMessages.createdAt).unix() - moment__WEBPACK_IMPORTED_MODULE_0___default()(a.lastMessages.createdAt).unix());\n    return sortedMessage;\n}\nfunction transformMessageText(dto) {\n    return {\n        createdAt: dto.created_at,\n        displayAs: dto.display_as,\n        displayName: dto.display_name,\n        isRead: dto.is_read,\n        isSent: dto.is_send,\n        text: dto.text,\n        id: dto.id,\n        code: dto.code\n    };\n}\nfunction transformMessageDetail(dto) {\n    let locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    var _dto_messages, _dto_ref_data_extended_list, _dto_ref_data, _dto_participants_info, _dto_participants_info1, _dto_participants_info2, _dto_participants_info3, _dto_participants_info4, _dto_ref_data1, _dto_ref_data_images_, _dto_ref_data2, _dto_ref_data3;\n    console.log(dto.messages);\n    const lastMessage = dto.messages[((_dto_messages = dto.messages) === null || _dto_messages === void 0 ? void 0 : _dto_messages.length) - 1] || undefined;\n    const messages = dto.messages.map((item)=>({\n            createdAt: item.created_at,\n            displayAs: item.display_as,\n            displayName: item.display_name,\n            isRead: item.is_read,\n            isSent: item.is_send,\n            text: item.text,\n            id: item.id,\n            code: item.code\n        }));\n    const moreProperty = (_dto_ref_data = dto.ref_data) === null || _dto_ref_data === void 0 ? void 0 : (_dto_ref_data_extended_list = _dto_ref_data.extended_list) === null || _dto_ref_data_extended_list === void 0 ? void 0 : _dto_ref_data_extended_list.map((item)=>{\n        var _item_images_;\n        return {\n            id: item.code,\n            image: ((_item_images_ = item.images[0]) === null || _item_images_ === void 0 ? void 0 : _item_images_.image) || \"\",\n            title: (0,_utils_transform__WEBPACK_IMPORTED_MODULE_1__.ValueBasedOnLocale)(item.title, locale)\n        };\n    });\n    return {\n        code: dto.code,\n        category: dto.category,\n        roomId: dto.participants.room_id,\n        lastMessages: {\n            createdAt: lastMessage.created_at,\n            displayAs: lastMessage.display_as,\n            displayName: lastMessage.display_name,\n            text: lastMessage.text,\n            isRead: lastMessage.is_read,\n            isSent: lastMessage.is_send,\n            id: lastMessage.id,\n            code: lastMessage.code || \"\"\n        },\n        participant: {\n            email: ((_dto_participants_info = dto.participants.info) === null || _dto_participants_info === void 0 ? void 0 : _dto_participants_info.email) || \"\",\n            fullName: ((_dto_participants_info1 = dto.participants.info) === null || _dto_participants_info1 === void 0 ? void 0 : _dto_participants_info1.display_name) || \"\",\n            phoneNumber: ((_dto_participants_info2 = dto.participants.info) === null || _dto_participants_info2 === void 0 ? void 0 : _dto_participants_info2.phone_number) || \"\",\n            image: ((_dto_participants_info3 = dto.participants.info) === null || _dto_participants_info3 === void 0 ? void 0 : _dto_participants_info3.image) || \"\",\n            id: ((_dto_participants_info4 = dto.participants.info) === null || _dto_participants_info4 === void 0 ? void 0 : _dto_participants_info4.id) || \"\",\n            category: dto.category,\n            status: dto.status,\n            property: {\n                id: ((_dto_ref_data1 = dto.ref_data) === null || _dto_ref_data1 === void 0 ? void 0 : _dto_ref_data1.code) || \"\",\n                image: ((_dto_ref_data2 = dto.ref_data) === null || _dto_ref_data2 === void 0 ? void 0 : (_dto_ref_data_images_ = _dto_ref_data2.images[0]) === null || _dto_ref_data_images_ === void 0 ? void 0 : _dto_ref_data_images_.image) || \"\",\n                title: (0,_utils_transform__WEBPACK_IMPORTED_MODULE_1__.ValueBasedOnLocale)((_dto_ref_data3 = dto.ref_data) === null || _dto_ref_data3 === void 0 ? void 0 : _dto_ref_data3.title, locale) || \"\"\n            },\n            moreProperty: moreProperty || []\n        },\n        allMessages: messages,\n        updatedAt: dto.updated_at\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./core/infrastructures/messages/transform.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./core/infrastructures/utils/transform.ts":
/*!*************************************************!*\
  !*** ./core/infrastructures/utils/transform.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ValueBasedOnLocale: function() { return /* binding */ ValueBasedOnLocale; },\n/* harmony export */   transformMeta: function() { return /* binding */ transformMeta; }\n/* harmony export */ });\nfunction transformMeta(dto) {\n    const meta = {\n        nextPage: dto.next_page,\n        page: dto.page,\n        pageCount: dto.page_count,\n        perPage: dto.per_page,\n        prevPage: dto.prev_page,\n        total: dto.total\n    };\n    return meta;\n}\nfunction ValueBasedOnLocale(values) {\n    let locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en\";\n    if (!values) return \"\";\n    if (typeof values == \"string\") return values;\n    const selectedItem = values.find((item)=>item.lang === locale);\n    return (selectedItem === null || selectedItem === void 0 ? void 0 : selectedItem.value) || values[0].value;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./core/infrastructures/utils/transform.ts\n"));

/***/ })

});