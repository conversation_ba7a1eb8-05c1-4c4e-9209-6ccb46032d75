"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/verify/verify-page-client.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyPageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_verify_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/verify-hero */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-hero.tsx\");\n/* harmony import */ var _components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/verify-how-it-works */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\");\n/* harmony import */ var _components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/verify-pricing */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-pricing.tsx\");\n/* harmony import */ var _components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/verify-booking-form */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-booking-form.tsx\");\n/* harmony import */ var _lib_scroll_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/scroll-utils */ \"(app-pages-browser)/./lib/scroll-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VerifyPageClient(param) {\n    let { conversions } = param;\n    _s();\n    const [selectedTier, setSelectedTier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const handleSelectTier = (tier)=>{\n        setSelectedTier(tier);\n        // Scroll to booking form with header offset\n        setTimeout(()=>{\n            (0,_lib_scroll_utils__WEBPACK_IMPORTED_MODULE_6__.scrollToBookingForm)();\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversions: conversions,\n                onSelectTier: handleSelectTier\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedTier: selectedTier,\n                conversions: conversions\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyPageClient, \"K2K/KEbLathiJWwPWNbGkzua4QM=\");\n_c = VerifyPageClient;\nvar _c;\n$RefreshReg$(_c, \"VerifyPageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx\n"));

/***/ })

});