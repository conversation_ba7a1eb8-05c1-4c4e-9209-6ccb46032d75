"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx":
/*!****************************************************************!*\
  !*** ./app/[locale]/verify/components/verify-how-it-works.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyHowItWorks; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction VerifyHowItWorks() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"verify\");\n    const howItWorks = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 10,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.book.title\"),\n            description: t(\"howItWorks.steps.book.description\"),\n            result: t(\"howItWorks.steps.book.result\")\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 16,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.inspect.title\"),\n            description: t(\"howItWorks.steps.inspect.description\"),\n            result: [\n                t(\"howItWorks.steps.inspect.result.basic\"),\n                t(\"howItWorks.steps.inspect.result.smart\"),\n                t(\"howItWorks.steps.inspect.result.fullShield\")\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 26,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.report.title\"),\n            description: t(\"howItWorks.steps.report.description\"),\n            result: [\n                t(\"howItWorks.steps.report.result.basic\"),\n                t(\"howItWorks.steps.report.result.smart\"),\n                t(\"howItWorks.steps.report.result.fullShield\")\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-seekers-foreground/50 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-seekers-text mb-4\",\n                            children: t(\"howItWorks.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-seekers-text-light\",\n                            children: t(\"howItWorks.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-[1200px] mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 relative\",\n                        children: howItWorks.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"group relative bg-white p-6 rounded-2xl border border-gray-100 hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative shrink-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center text-seekers-primary group-hover:scale-110 transition-transform duration-300\",\n                                                        children: step.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full group-hover:blur-2xl transition-all duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-lg font-semibold text-gray-900 group-hover:text-seekers-primary transition-colors duration-300\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm leading-relaxed flex-1 whitespace-pre-line text-left\",\n                                        children: step.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 border-2 border-transparent group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg md:text-xl font-semibold text-seekers-text mb-4\",\n                            children: t(\"howItWorks.whyChoose.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-base text-seekers-text-light\",\n                            children: t(\"howItWorks.whyChoose.description\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHowItWorks, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations\n    ];\n});\n_c = VerifyHowItWorks;\nvar _c;\n$RefreshReg$(_c, \"VerifyHowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\n"));

/***/ })

});