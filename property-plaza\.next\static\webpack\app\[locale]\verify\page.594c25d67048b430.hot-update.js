"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-pricing.tsx":
/*!***********************************************************!*\
  !*** ./app/[locale]/verify/components/verify-pricing.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyPricing; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _stores_seekers_settings_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/stores/seekers-settings.store */ \"(app-pages-browser)/./stores/seekers-settings.store.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction VerifyPricing(param) {\n    let { conversions, onSelectTier } = param;\n    _s();\n    const { currency: currencyStored, isLoading } = (0,_stores_seekers_settings_store__WEBPACK_IMPORTED_MODULE_4__.useSeekersSettingsStore)();\n    const [currency, setCurrency] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"IDR\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"verify\");\n    const pricingTiers = [\n        {\n            id: \"basic\",\n            name: t(\"pricing.tiers.basic.name\"),\n            price: 1900000,\n            features: [\n                t(\"pricing.tiers.basic.features.0\"),\n                t(\"pricing.tiers.basic.features.1\"),\n                t(\"pricing.tiers.basic.features.2\"),\n                t(\"pricing.tiers.basic.features.3\"),\n                t(\"pricing.tiers.basic.features.4\")\n            ]\n        },\n        {\n            id: \"standard\",\n            name: t(\"pricing.tiers.standard.name\"),\n            price: 4500000,\n            popular: true,\n            features: [\n                t(\"pricing.tiers.standard.features.0\"),\n                t(\"pricing.tiers.standard.features.1\"),\n                t(\"pricing.tiers.standard.features.2\"),\n                t(\"pricing.tiers.standard.features.3\"),\n                t(\"pricing.tiers.standard.features.4\")\n            ]\n        },\n        {\n            id: \"premium\",\n            name: t(\"pricing.tiers.premium.name\"),\n            price: 7000000,\n            features: [\n                t(\"pricing.tiers.premium.features.0\"),\n                t(\"pricing.tiers.premium.features.1\"),\n                t(\"pricing.tiers.premium.features.2\"),\n                t(\"pricing.tiers.premium.features.3\")\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(()=>{\n        if (!isLoading && currencyStored) {\n            setCurrency(currencyStored);\n        }\n    }, [\n        currencyStored,\n        isLoading\n    ]);\n    const formatPrice = (price)=>{\n        const convertedPrice = price * (conversions[currency] || 1);\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatCurrency)(convertedPrice, currency, locale);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-white\",\n        \"aria-labelledby\": \"pricing-title\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            id: \"pricing-title\",\n                            className: \"text-3xl md:text-4xl font-bold text-seekers-text mb-4\",\n                            children: t(\"pricing.title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-seekers-text-light\",\n                            children: t(\"pricing.subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto\",\n                    children: pricingTiers.map((tier)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg flex flex-col \".concat(tier.popular ? \"border-seekers-primary shadow-lg\" : \"border-neutral-200\"),\n                            children: [\n                                tier.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold\",\n                                        children: t(\"pricing.tiers.standard.popular\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-xl font-bold text-seekers-text mb-1\",\n                                            children: tier.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-seekers-text-light mb-3 whitespace-pre-line\",\n                                            children: t(\"pricing.tiers.\".concat(tier.id, \".subtitle\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-seekers-primary\",\n                                            children: formatPrice(tier.price)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3 mb-8 flex-grow\",\n                                    children: tier.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-seekers-primary mt-1\",\n                                                    children: \"✓\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-seekers-text-light\",\n                                                    children: feature\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            onClick: ()=>onSelectTier(tier),\n                                            className: \"w-full py-3 font-semibold transition-all duration-200 mb-3 \".concat(tier.popular ? \"bg-seekers-primary hover:bg-seekers-primary/90 text-white\" : \"bg-neutral-100 hover:bg-neutral-200 text-seekers-text border border-neutral-300\"),\n                                            variant: tier.popular ? \"default\" : \"outline\",\n                                            children: t(\"pricing.cta\", {\n                                                tierName: tier.name\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        tier.id === \"premium\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500 italic text-center\",\n                                            children: t(\"pricing.tiers.\".concat(tier.id, \".footnote\"))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, tier.id, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-pricing.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyPricing, \"8ZNWXC2ErYoFuc1IlGUAAdeNByM=\", false, function() {\n    return [\n        _stores_seekers_settings_store__WEBPACK_IMPORTED_MODULE_4__.useSeekersSettingsStore,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations\n    ];\n});\n_c = VerifyPricing;\nvar _c;\n$RefreshReg$(_c, \"VerifyPricing\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-pricing.tsx\n"));

/***/ })

});