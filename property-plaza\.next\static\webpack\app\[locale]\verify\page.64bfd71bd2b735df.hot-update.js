"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx":
/*!****************************************************************!*\
  !*** ./app/[locale]/verify/components/verify-how-it-works.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerifyHowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seekers-content-layout/default-layout-content */ \"(app-pages-browser)/./components/seekers-content-layout/default-layout-content.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction VerifyHowItWorks() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"verify\");\n    const howItWorks = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.book.title\"),\n            description: t(\"howItWorks.steps.book.description\"),\n            result: t(\"howItWorks.steps.book.result\")\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 17,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.inspect.title\"),\n            description: t(\"howItWorks.steps.inspect.description\"),\n            result: [\n                t(\"howItWorks.steps.inspect.result.basic\"),\n                t(\"howItWorks.steps.inspect.result.smart\"),\n                t(\"howItWorks.steps.inspect.result.fullShield\")\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.report.title\"),\n            description: t(\"howItWorks.steps.report.description\"),\n            result: [\n                t(\"howItWorks.steps.report.result.basic\"),\n                t(\"howItWorks.steps.report.result.smart\"),\n                t(\"howItWorks.steps.report.result.fullShield\")\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-seekers-foreground/50 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"!mt-2\",\n                title: t(\"howItWorks.title\"),\n                description: t(\"howItWorks.subtitle\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-[1200px] mx-auto px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 relative\",\n                            children: howItWorks.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-white p-6 rounded-2xl border border-gray-100 hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative shrink-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-seekers-primary/10 rounded-xl flex items-center justify-center text-seekers-primary group-hover:scale-110 transition-transform duration-300\",\n                                                        children: step.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full group-hover:blur-2xl transition-all duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 text-center mb-3 group-hover:text-seekers-primary transition-colors duration-300\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm leading-relaxed text-center mb-4 flex-1\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-seekers-primary/5 rounded-lg p-4 border border-seekers-primary/20 shadow-sm mt-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-seekers-text mb-2 text-sm\",\n                                                    children: \"\\uD83D\\uDCCB What you get:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 21\n                                                }, this),\n                                                Array.isArray(step.result) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-seekers-text-light space-y-1\",\n                                                    children: step.result.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-seekers-primary mt-0.5\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                    lineNumber: 87,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-seekers-text-light\",\n                                                    children: step.result\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 border-2 border-transparent group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl md:text-2xl font-bold text-seekers-text mb-4\",\n                                children: t(\"howItWorks.whyChoose.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-seekers-text-light\",\n                                children: t(\"howItWorks.whyChoose.description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHowItWorks, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = VerifyHowItWorks;\nvar _c;\n$RefreshReg$(_c, \"VerifyHowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\n"));

/***/ })

});