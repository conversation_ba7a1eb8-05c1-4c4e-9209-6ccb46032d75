"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx":
/*!****************************************************************!*\
  !*** ./app/[locale]/verify/components/verify-how-it-works.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerifyHowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seekers-content-layout/default-layout-content */ \"(app-pages-browser)/./components/seekers-content-layout/default-layout-content.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction VerifyHowItWorks() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"verify\");\n    const howItWorks = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.book.title\"),\n            description: t(\"howItWorks.steps.book.description\"),\n            result: t(\"howItWorks.steps.book.result\")\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 17,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.inspect.title\"),\n            description: t(\"howItWorks.steps.inspect.description\"),\n            result: [\n                t(\"howItWorks.steps.inspect.result.basic\"),\n                t(\"howItWorks.steps.inspect.result.smart\"),\n                t(\"howItWorks.steps.inspect.result.fullShield\")\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.report.title\"),\n            description: t(\"howItWorks.steps.report.description\"),\n            result: [\n                t(\"howItWorks.steps.report.result.basic\"),\n                t(\"howItWorks.steps.report.result.smart\"),\n                t(\"howItWorks.steps.report.result.fullShield\")\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-seekers-foreground/50 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"!mt-2\",\n                title: t(\"howItWorks.title\"),\n                description: t(\"howItWorks.subtitle\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-[1200px] mx-auto px-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute hidden xl:block left-1/2 top-24 h-[calc(100%-6rem)] w-0.5 bg-gradient-to-b from-seekers-primary/20 via-seekers-primary/10 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-8 relative\",\n                                children: howItWorks.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group relative bg-white p-6 rounded-2xl border border-gray-100\\n                  hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md\\n                  \".concat(index === 0 ? 'xl:translate-x-[-15%]' : '', \"\\n                  \").concat(index === 1 ? 'xl:translate-x-[15%] xl:mt-16' : '', \"\\n                  \").concat(index === 2 ? 'xl:translate-x-[-15%] xl:mt-[-4rem]' : '', \"\\n                  min-h-[280px]\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute hidden xl:block top-1/2\\n                  \".concat(index % 2 === 0 ? 'right-0 translate-x-1/2' : 'left-0 -translate-x-1/2', \"\\n                  w-4 h-4 rounded-full bg-seekers-primary/20 group-hover:bg-seekers-primary transition-colors\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-1 bg-white rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-2 bg-seekers-primary/40 group-hover:bg-seekers-primary rounded-full transition-colors\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative shrink-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center text-seekers-primary group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: step.icon\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                        lineNumber: 72,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full group-hover:blur-2xl transition-all duration-300\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                lineNumber: 71,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-2xl font-bold text-seekers-primary/20 group-hover:text-seekers-primary transition-colors\",\n                                                                children: [\n                                                                    \"0\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                lineNumber: 79,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-gray-900 mb-3 group-hover:text-seekers-primary transition-colors duration-300\",\n                                                                children: step.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm leading-relaxed mb-4\",\n                                                                children: step.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-seekers-primary/5 rounded-lg p-4 border border-seekers-primary/20 shadow-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-seekers-text mb-2 text-sm\",\n                                                                        children: \"\\uD83D\\uDCCB What you get:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    Array.isArray(step.result) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm text-seekers-text-light space-y-1\",\n                                                                        children: step.result.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                className: \"flex items-start gap-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-seekers-primary mt-0.5\",\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                                        lineNumber: 104,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: item\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                                        lineNumber: 105,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, idx, true, {\n                                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                                lineNumber: 103,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                        lineNumber: 101,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-seekers-text-light\",\n                                                                        children: step.result\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                        lineNumber: 110,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 border-2 border-transparent group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl md:text-2xl font-bold text-seekers-text mb-4\",\n                                children: t(\"howItWorks.whyChoose.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-seekers-text-light\",\n                                children: t(\"howItWorks.whyChoose.description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHowItWorks, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = VerifyHowItWorks;\nvar _c;\n$RefreshReg$(_c, \"VerifyHowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/seekers-content-layout/default-layout-content.tsx":
/*!**********************************************************************!*\
  !*** ./components/seekers-content-layout/default-layout-content.tsx ***!
  \**********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DefaultLayoutContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DefaultLayoutContent(param) {\n    let { title, description, action, ...rest } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ...rest,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-6\", rest.className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"capitalize text-seekers-text text-2xl font-bold tracking-[0.5%]\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \" text-seekers-text-light text-base font-semibold tracking-[0.5%]\",\n                                children: description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 7\n                    }, this),\n                    action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                        variant: \"link\",\n                        className: \"text-seekers-primary-foreground\",\n                        onClick: action.action,\n                        children: action.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 18\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n                lineNumber: 16,\n                columnNumber: 5\n            }, this),\n            rest.children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\seekers-content-layout\\\\default-layout-content.tsx\",\n        lineNumber: 15,\n        columnNumber: 10\n    }, this);\n}\n_c = DefaultLayoutContent;\nvar _c;\n$RefreshReg$(_c, \"DefaultLayoutContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/seekers-content-layout/default-layout-content.tsx\n"));

/***/ })

});