"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/verify/verify-page-client.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyPageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_verify_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/verify-hero */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-hero.tsx\");\n/* harmony import */ var _components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/verify-how-it-works */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\");\n/* harmony import */ var _components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/verify-pricing */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-pricing.tsx\");\n/* harmony import */ var _components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/verify-booking-form */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-booking-form.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction VerifyPageClient(param) {\n    let { conversions } = param;\n    _s();\n    const [selectedTier, setSelectedTier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const handleSelectTier = (tier)=>{\n        setSelectedTier(tier);\n        // Scroll to booking title\n        setTimeout(()=>{\n            var _document_getElementById;\n            (_document_getElementById = document.getElementById(\"booking-title\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                behavior: \"smooth\",\n                block: \"start\"\n            });\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversions: conversions,\n                onSelectTier: handleSelectTier\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedTier: selectedTier,\n                conversions: conversions\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyPageClient, \"K2K/KEbLathiJWwPWNbGkzua4QM=\");\n_c = VerifyPageClient;\nvar _c;\n$RefreshReg$(_c, \"VerifyPageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx\n"));

/***/ })

});