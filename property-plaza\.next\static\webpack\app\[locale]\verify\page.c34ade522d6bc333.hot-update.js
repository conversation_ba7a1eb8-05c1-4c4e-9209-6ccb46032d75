"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-disclaimer.tsx":
/*!**************************************************************!*\
  !*** ./app/[locale]/verify/components/verify-disclaimer.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyDisclaimer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nfunction VerifyDisclaimer() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"verify\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 bg-gray-50 border-t border-gray-200\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: t(\"disclaimer.title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-disclaimer.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 leading-relaxed\",\n                        children: t(\"disclaimer.content\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-disclaimer.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-disclaimer.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-disclaimer.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-disclaimer.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyDisclaimer, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations\n    ];\n});\n_c = VerifyDisclaimer;\nvar _c;\n$RefreshReg$(_c, \"VerifyDisclaimer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9bbG9jYWxlXS92ZXJpZnkvY29tcG9uZW50cy92ZXJpZnktZGlzY2xhaW1lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3RjtBQUM1QztBQUU3QixTQUFTRTs7SUFDdEIsTUFBTUMsSUFBSUYsMERBQWVBLENBQUM7SUFFMUIscUJBQ0UsOERBQUNHO1FBQVFDLFdBQVU7a0JBQ2pCLDRFQUFDTCw4RkFBaUJBO3NCQUNoQiw0RUFBQ007Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FDWEYsRUFBRTs7Ozs7O2tDQUVMLDhEQUFDSzt3QkFBRUgsV0FBVTtrQ0FDVkYsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1mO0dBakJ3QkQ7O1FBQ1pELHNEQUFlQTs7O0tBREhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9bbG9jYWxlXS92ZXJpZnkvY29tcG9uZW50cy92ZXJpZnktZGlzY2xhaW1lci50c3g/NWY2OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTWFpbkNvbnRlbnRMYXlvdXQgZnJvbSBcIkAvY29tcG9uZW50cy9zZWVrZXJzLWNvbnRlbnQtbGF5b3V0L21haW4tY29udGVudC1sYXlvdXRcIjtcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gXCJuZXh0LWludGxcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVmVyaWZ5RGlzY2xhaW1lcigpIHtcbiAgY29uc3QgdCA9IHVzZVRyYW5zbGF0aW9ucyhcInZlcmlmeVwiKTtcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTEyIGJnLWdyYXktNTAgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICA8TWFpbkNvbnRlbnRMYXlvdXQ+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG8gdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAge3QoXCJkaXNjbGFpbWVyLnRpdGxlXCIpfVxuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAge3QoXCJkaXNjbGFpbWVyLmNvbnRlbnRcIil9XG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvTWFpbkNvbnRlbnRMYXlvdXQ+XG4gICAgPC9zZWN0aW9uPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIk1haW5Db250ZW50TGF5b3V0IiwidXNlVHJhbnNsYXRpb25zIiwiVmVyaWZ5RGlzY2xhaW1lciIsInQiLCJzZWN0aW9uIiwiY2xhc3NOYW1lIiwiZGl2IiwiaDMiLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-disclaimer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx":
/*!****************************************************!*\
  !*** ./app/[locale]/verify/verify-page-client.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyPageClient; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_verify_hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/verify-hero */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-hero.tsx\");\n/* harmony import */ var _components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/verify-how-it-works */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\");\n/* harmony import */ var _components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/verify-pricing */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-pricing.tsx\");\n/* harmony import */ var _components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/verify-booking-form */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-booking-form.tsx\");\n/* harmony import */ var _components_verify_disclaimer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/verify-disclaimer */ \"(app-pages-browser)/./app/[locale]/verify/components/verify-disclaimer.tsx\");\n/* harmony import */ var _lib_scroll_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/scroll-utils */ \"(app-pages-browser)/./lib/scroll-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction VerifyPageClient(param) {\n    let { conversions } = param;\n    _s();\n    const [selectedTier, setSelectedTier] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const handleSelectTier = (tier)=>{\n        setSelectedTier(tier);\n        // Scroll to booking form with header offset\n        setTimeout(()=>{\n            (0,_lib_scroll_utils__WEBPACK_IMPORTED_MODULE_7__.scrollToBookingForm)();\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_hero__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_how_it_works__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_pricing__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                conversions: conversions,\n                onSelectTier: handleSelectTier\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_booking_form__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                selectedTier: selectedTier,\n                conversions: conversions\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_verify_disclaimer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-page-client.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyPageClient, \"K2K/KEbLathiJWwPWNbGkzua4QM=\");\n_c = VerifyPageClient;\nvar _c;\n$RefreshReg$(_c, \"VerifyPageClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/verify-page-client.tsx\n"));

/***/ })

});