"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx":
/*!****************************************************************!*\
  !*** ./app/[locale]/verify/components/verify-how-it-works.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ VerifyHowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seekers-content-layout/default-layout-content */ \"(app-pages-browser)/./components/seekers-content-layout/default-layout-content.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,FileText,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction VerifyHowItWorks() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"verify\");\n    const howItWorks = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 11,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.book.title\"),\n            description: t(\"howItWorks.steps.book.description\"),\n            result: t(\"howItWorks.steps.book.result\")\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 17,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.inspect.title\"),\n            description: t(\"howItWorks.steps.inspect.description\"),\n            result: [\n                t(\"howItWorks.steps.inspect.result.basic\"),\n                t(\"howItWorks.steps.inspect.result.smart\"),\n                t(\"howItWorks.steps.inspect.result.fullShield\")\n            ]\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_FileText_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"w-6 h-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 27,\n                columnNumber: 13\n            }, this),\n            title: t(\"howItWorks.steps.report.title\"),\n            description: t(\"howItWorks.steps.report.description\"),\n            result: [\n                t(\"howItWorks.steps.report.result.basic\"),\n                t(\"howItWorks.steps.report.result.smart\"),\n                t(\"howItWorks.steps.report.result.fullShield\")\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-seekers-foreground/50 py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_default_layout_content__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"!mt-2\",\n                title: t(\"howItWorks.title\"),\n                description: t(\"howItWorks.subtitle\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative max-w-[1200px] mx-auto px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-8 relative\",\n                            children: howItWorks.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"group relative bg-white p-6 rounded-2xl border border-gray-100 hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md flex flex-col text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center text-seekers-primary group-hover:scale-110 transition-transform duration-300\",\n                                                            children: step.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                            lineNumber: 57,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full group-hover:blur-2xl transition-all duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-lg font-semibold text-gray-900 group-hover:text-seekers-primary transition-colors duration-300\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm leading-relaxed flex-1\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 border-2 border-transparent group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl md:text-2xl font-bold text-seekers-text mb-4\",\n                                children: t(\"howItWorks.whyChoose.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-seekers-text-light\",\n                                children: t(\"howItWorks.whyChoose.description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-how-it-works.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHowItWorks, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = VerifyHowItWorks;\nvar _c;\n$RefreshReg$(_c, \"VerifyHowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-how-it-works.tsx\n"));

/***/ })

});