"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/components/verify-hero.tsx":
/*!********************************************************!*\
  !*** ./app/[locale]/verify/components/verify-hero.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyHero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nfunction VerifyHero() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"verify\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16\",\n        \"aria-labelledby\": \"hero-title\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl md:text-2xl\",\n                                        children: \"\\uD83D\\uDEA8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm md:text-base\",\n                                        children: t(\"hero.badge\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                id: \"hero-title\",\n                                className: \"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight\",\n                                children: t(\"hero.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base md:text-lg text-seekers-text-light leading-relaxed\",\n                                children: t(\"hero.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 md:space-y-4\",\n                                children: [\n                                    t(\"hero.benefits.0\"),\n                                    t(\"hero.benefits.1\"),\n                                    t(\"hero.benefits.2\"),\n                                    t(\"hero.benefits.3\")\n                                ].map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start lg:items-center gap-3 justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-seekers-primary text-lg mt-0.5 lg:mt-0\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm md:text-base text-seekers-text font-medium text-left\",\n                                                children: benefit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 md:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 font-medium text-sm md:text-base text-center lg:text-left\",\n                                    children: [\n                                        t(\"hero.warning\"),\n                                        \" \",\n                                        t(\"hero.cta\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 md:pt-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto\",\n                                        onClick: ()=>{\n                                            var _document_getElementById;\n                                            (_document_getElementById = document.getElementById(\"book-inspection\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        children: \"\\uD83D\\uDCCB Book Professional Villa Inspection Now\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3\",\n                                        children: \"✅ Protect your investment • Avoid scams • Get peace of mind\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center order-1 lg:order-2 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm bg-neutral-100 rounded-lg border-2 border-dashed border-neutral-300 flex items-center justify-center\",\n                            style: {\n                                aspectRatio: \"9/16\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-2 p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl\",\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-neutral-500 font-medium\",\n                                        children: \"TikTok Video\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-400 leading-relaxed\",\n                                        children: [\n                                            \"Vertical video showing\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 41\n                                            }, this),\n                                            \"villa inspection process\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 43\n                                            }, this),\n                                            \"and red flags found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\components\\\\verify-hero.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHero, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = VerifyHero;\nvar _c;\n$RefreshReg$(_c, \"VerifyHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/components/verify-hero.tsx\n"));

/***/ })

});