import { But<PERSON> } from "@/components/ui/button"
import { billingUrl, plansUrl } from "@/lib/constanta/route"
import { useTranslations } from "next-intl"
import { Link } from "@/lib/locale/routing";

export default function ExtraAnswerContent({ extraContentId }: { extraContentId?: string }) {
  const t = useTranslations("seeker")
  if (!extraContentId) return <></>
  switch (extraContentId) {
    case "subscription-offer-url":
      return <Button asChild variant={'link'} className="w-fit h-fit p-0 text-seekers-primary">
        <Link href={plansUrl}>{t('faq.subscriptionOffer.extraContent')}</Link>
      </Button>
    case "cancelation-subscription-url":
      return <Button asChild variant={'link'} className="w-fit h-fit p-0 text-seekers-primary">
        <Link href={billingUrl}>{t('faq.cancelSubscription.extraContent')}</Link>
      </Button>
    default:
      return <></>
  }
  return <></>
}