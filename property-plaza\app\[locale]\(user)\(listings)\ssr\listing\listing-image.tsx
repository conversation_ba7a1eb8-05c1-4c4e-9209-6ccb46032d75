"use client"
import { Carousel, CarouselContent, CarouselDots, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers";
import { cn } from "@/lib/utils";
import ListingHeader from "./listing-header";
import { Fragment, useState } from "react";
import { handleClickListing } from "../utils";
import Image from "next/image";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import { useUserStore } from "@/stores/user.store";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { noLoginPlanUrl } from "@/lib/constanta/route";
import { packages } from "@/core/domain/subscription/subscription";
import useIntersectionObserver from "@/hooks/use-Intersection-observer";
import { Link } from "@/lib/locale/routing";
interface ListingImageProps {
  heartSize?: "small" | "large",
  containerClassName?: string,
  extraHeaderAction?: React.ReactNode,
  listing: ListingListSeekers,
  forceLazyLoad?: boolean
}
export default function ListingImage({ containerClassName, extraHeaderAction, heartSize, listing, forceLazyLoad = false }: ListingImageProps) {
  const t = useTranslations("seeker")
  const isInView = useIntersectionObserver()
  const [hasInteracted, setHasInteracted] = useState(false)
  const [listing_, setListing] = useState(listing)
  const { seekers } = useUserStore(state => state)
  const handleSetClientFavorite = (val: boolean) => {
    setListing(prev => ({ ...prev, isFavorite: val }))
  }

  return <div ref={isInView.sectionRef} className="aspect-[4/3]">
    <Carousel
      opts={{
        loop: seekers.accounts.membership == packages.free ? false : true
      }}
      onMouseEnter={() => setHasInteracted(true)}
      onTouchStart={() => setHasInteracted(true)}
      onFocus={() => setHasInteracted(true)}
      className={cn("group isolate w-full aspect-[4/3] relative rounded-xl overflow-hidden", containerClassName)}>
      <ListingHeader
        listing={listing_}
        updateClientFavorite={handleSetClientFavorite}
        isFavorite={listing_.isFavorite} code={listing_.code} size={heartSize}
        extraAction={extraHeaderAction} />
      <CarouselContent className="absolute top-0 left-0 w-full h-full ml-0 -z-20">
        {listing_.thumbnail.map((item, idx) => (idx === 0 || hasInteracted) ?

          <CarouselItem
            key={item.id + idx}
            className="relative"
            onClick={(e) => {
              e.stopPropagation()
              handleClickListing(listing_.title, listing_.code)
            }}>
            <div className="absolute inset-0 z-10 pointer-events-none watermark-overlay" />

            <Image
              src={item.image}
              alt={listing_.title}
              fill
              sizes="(max-width:480px) 40vw,(max-width: 768px) 40vw, (max-width: 1200px) 30vw, 25vw"
              loading={idx == 0 && isInView.isVisible ? "eager" : "lazy"}
              blurDataURL={imagePlaceholder}
              style={{ objectFit: 'cover' }}
              quality={10}
              priority={idx == 0 && isInView.isVisible}

              placeholder="blur"
            />
          </CarouselItem> :
          <Fragment key={`${item.id}-${idx}`} />
        )}
        {seekers.accounts.membership == packages.free && <CarouselItem
          className="relative isolate"
        >
          <Image
            className="-z-10 brightness-50 blur-md"
            src={imagePlaceholder}
            alt=""
            fill
            sizes="300px"
            loading="lazy"
            blurDataURL={imagePlaceholder}
            style={{ objectFit: 'cover' }}
            placeholder="blur" />
          <div className="z-10 text-white absolute top-1/2 left-1/2 text-center flex flex-col items-center -translate-x-1/2 -translate-y-1/2 min-w-[200px]">
            <p className="text-center">
              {t('misc.subscibePropgram.detailPage.description')} {" "}
            </p>
            <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
              <Link href={noLoginPlanUrl}>{t('cta.subscribe')}</Link>
            </Button>
          </div>
        </CarouselItem>
        }
      </CarouselContent>

      {listing_.thumbnail.length <= 1 && seekers.accounts.membership !== packages.free ? <></> :
        <div className="flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3">
          <CarouselPrevious className="left-3 !opacity-0 group-hover:!opacity-100  transition duration-75 ease-in" />
          <CarouselNext className="right-3 !opacity-0 group-hover:!opacity-100  transition duration-75 ease-in" />
        </div>
      }
      <div className="flex absolute bottom-4 left-0 w-full items-center justify-center">
        <CarouselDots carouselDotClassName="hover:bg-seekers-primary" />
      </div>
      <div
        className="pointer-events-none absolute w-full h-full top-0 left-0 bg-gradient-to-b from-neutral-900/40 via-neutral-900/5 to-neutral-100/0 -z-10 group-hover:opacity-0 transition-all duration-200 ease-in-out">

      </div>
    </Carousel>
  </div>
}