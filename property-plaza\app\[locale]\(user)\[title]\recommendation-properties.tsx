"use client"

import { useGetFilteredSeekersListing } from "@/core/applications/queries/listing/use-get-filtered-seeker-listing"
import useIntersectionObserver from "@/hooks/use-Intersection-observer"
import { useEffect, useState } from "react"
import ListingItem, { ListingLoader } from "../(listings)/listing-item"
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { useTranslations } from "next-intl"
import { ListingListSeekers } from "@/core/domain/listing/listing-seekers"
import { useUserStore } from "@/stores/user.store"
import { packages } from "@/core/domain/subscription/subscription"



export default function RecommendationProperties({ lat, lng, currency = "EUR", locale = "en", conversions, currentPropertyCode }: { lat?: string, lng?: string, currency: string, locale: string, conversions: { [key: string]: number }, currentPropertyCode: string }) {
  const t = useTranslations("seeker")
  const { seekers } = useUserStore()
  const { isVisible, sectionRef, firstTimeVisible, setFirstTimeVisible } = useIntersectionObserver()
  const [filteredListings, setFilteredList] = useState<ListingListSeekers[]>([])
  const { query: data } = useGetFilteredSeekersListing({
    page: "1",
    per_page: "12",
    // category: listing?.detail.type,
    area: (lat && lng) ? {
      latitude: lat,
      longitude: lng
    } : undefined,

  },
    (isVisible && firstTimeVisible),
    // (seekers.email !== "" && seekers.accounts.membership !== packages.free), 
    locale)

  useEffect(() => {
    if (data.isFetching) setFirstTimeVisible(false)
  }, [data.isFetching, setFirstTimeVisible])
  useEffect(() => {
    if (data.data?.data && data.data?.data?.length > 0) {
      const properties = data.data.data.filter(item => item.code !== currentPropertyCode)
      setFilteredList(properties)
    }
  }, [currentPropertyCode, data?.data?.data])



  { currentPropertyCode }
  return <DefaultLayoutContent title={t('misc.popularPropertyNearby')}>
    <Carousel opts={{
      align: "end"
    }}>
      <CarouselContent ref={sectionRef} className="w-full h-full -ml-2 -z-20">
        {data.isPending ?
          [0, 1, 2, 3].map(item => <CarouselItem key={item} className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 ">
            <ListingLoader key={item} />
          </CarouselItem>
          )
          :
          filteredListings ?
            filteredListings.map((item, idx) => <CarouselItem key={idx} className="md:basis-1/2 lg:basis-1/3 xl:basis-1/4 ">
              <ListingItem disabledSubscriptionAction conversion={conversions} data={item} maxImage={1} forceLazyloading />
            </CarouselItem>
            )
            : <></>
        }
      </CarouselContent>
      {data.data?.data && data.data.data.length >= 1 ? <div className="flex absolute 
        top-[128px] max-sm:-translate-y-1/2  max-sm:left-0 
        w-full justify-between px-3"
      >
        <CarouselPrevious onClick={e => e.stopPropagation()} className="-left-1.5 md:-left-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70" iconClassName="w-6" />
        <CarouselNext onClick={e => e.stopPropagation()} className="-right-1.5 md:-right-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70" iconClassName="w-6" />
      </div>
        : <></>
      }
    </Carousel>
  </DefaultLayoutContent>
}