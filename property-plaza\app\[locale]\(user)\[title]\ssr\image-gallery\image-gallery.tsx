"use client"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Carousel, CarouselContent, CarouselDots, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel";
import { ListingImage } from "@/core/domain/listing/listing";
import { useTranslations } from "next-intl";
import ImageCarouselItem from "./image-carousel-item";
import ImageGalleryDialogTrigger from "./image-gallery-dialog-trigger";
import ImageDetailCarouselTrigger from "./image-detail-carousel-trigger";
import ImageItem from "./image-item";
import { UserDetail } from "@/core/domain/users/user";
import { packages } from "@/core/domain/subscription/subscription";
import Image from "next/image";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import { Button } from "@/components/ui/button";
import { Link } from "@/lib/locale/routing";;
import { noLoginPlanUrl } from "@/lib/constanta/route";

export default function ImageGallery({ images, user }: { images: ListingImage[], user?: UserDetail }) {
  let displayedImages = images.map(item => item.image) || []
  if (!user?.accounts.membership || user.accounts.membership === packages.free) {
    displayedImages = displayedImages.splice(0, 3)
  }
  const t = useTranslations("seeker")
  return <MainContentLayout className="h-fit  max-sm:w-full max-sm:px-0">
    {/* mobile-view */}
    <div className="hidden max-sm:block relative">
      <Carousel
        opts={{
          loop:
            user?.accounts.membership !== packages.free ? true : false,

        }}
        className={"group isolate w-full aspect-[4/3] relative  overflow-hidden"}
      >
        <CarouselContent
          className="absolute top-0 left-0 w-full h-full ml-0 -z-20"
        >
          {
            displayedImages.map(
              (item, idx) => <ImageCarouselItem index={idx} imageUrl={item} key={idx} isPriorityImage={idx == 0} />
            )
          }
          {user?.accounts.membership == packages.free && <CarouselItem
            className="relative isolate"
            onClick={(e) => {
              e.stopPropagation()
            }}>
            <Image
              className="-z-10 brightness-50 blur-md"
              src={imagePlaceholder}
              alt=""
              fill
              sizes="300px"
              loading="lazy"
              blurDataURL={imagePlaceholder}
              placeholder="blur" />
            <div className="z-10 text-white absolute top-1/2 left-1/2 text-center flex flex-col items-center -translate-x-1/2 -translate-y-1/2 min-w-[200px]">
              <p className="text-center">
                {t('misc.subscibePropgram.detailPage.description')} {" "}
              </p>
              <Button asChild variant={"link"} size={"sm"} className="p-0 h-fit w-fit text-white underline">
                <Link href={noLoginPlanUrl}>{t('cta.subscribe')}</Link>
              </Button>
            </div>
          </CarouselItem>
          }
        </CarouselContent>

        {displayedImages.length <= 1 ? <></> :
          <div className="flex absolute top-1/2 -translate-y-1/2 left-0 w-full justify-between px-3">
            <CarouselPrevious className="left-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in" />
            <CarouselNext className="right-3 group-hover:opacity-100 opacity-0 transition duration-75 ease-in" />
          </div>
        }
        <div className="flex absolute bottom-4 left-0 w-full items-center justify-center">
          <CarouselDots />
        </div>
      </Carousel>

    </div>
    {/* desktop-view */}
    <div className="isolate w-full max-sm:hidden flex gap-3 relative md:max-lg:h-[35vh] h-[60vh] max-h-[580px]">
      <ImageGalleryDialogTrigger imageUrls={displayedImages} />
      <div className="h-full flex-grow rounded-xl overflow-hidden">
        {
          displayedImages[0] &&
          <div className="aspect-video relative w-full overflow-hidden h-[60vh] max-h-[580px]">
            <ImageItem imageUrl={displayedImages[0]} />
          </div>
        }
      </div>
      <div className="flex flex-col min-w-[30%] gap-3 ">
        {
          displayedImages[1] &&
          <div className="relative aspect-video flex-grow max-h-full rounded-xl overflow-hidden">
            <ImageItem imageUrl={displayedImages[1]} />
          </div>
        }
        {
          displayedImages[2] &&
          <div className="relative aspect-video flex-grow max-h-full rounded-xl overflow-hidden isolate">
            <ImageItem imageUrl={displayedImages[2]} />
          </div>
        }
      </div>
    </div>


    <ImageDetailCarouselTrigger imagesUrl={displayedImages} isSubscribe={user?.accounts.membership && user.accounts.membership !== packages.free} />
  </MainContentLayout>
}