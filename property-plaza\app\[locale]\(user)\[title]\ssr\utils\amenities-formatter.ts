import { getTranslations } from "next-intl/server";

export default async function amenitiesFormatter(amenities: string[]) {
  const t = await getTranslations("seeker");
  const handleTranslationAmenities = (): string[] => {
    const amenitiesTranslations = amenities.map((item) =>
      translateAmenities(item)
    );
    return amenitiesTranslations as string[];
  };
  const translateAmenities = (value: string) => {
    switch (value) {
      case "PLUMBING":
        return t("listing.feature.additionalFeature.plumbing");
      case "GAZEBO":
        return t("listing.feature.additionalFeature.gazebo");
      case "CONSTRUCTION_NEARBY":
        return t("listing.feature.additionalFeature.constructionNearby");
      case "PET_ALLOWED":
        return t("listing.feature.additionalFeature.petAllowed");
      case "SUBLEASE_ALLOWED":
        return t("listing.feature.additionalFeature.subleaseAllowed");
      case "RECENTLY_RENOVATED":
        return t("listing.feature.additionalFeature.recentlyRenovated");
      case "ROOFTOP_TERRACE":
        return t("listing.feature.additionalFeature.rooftopTerrace");
      case "GARDEN_BACKYARD":
        return t("listing.feature.additionalFeature.garden");
      case "BATHUB":
        return t("listing.feature.additionalFeature.bathub");
      case "TERRACE":
        return t("listing.feature.additionalFeature.terrace");
      case "AIR_CONDITION":
        return t("listing.feature.additionalFeature.airCondition");
      case "BALCONY":
        return t("listing.feature.additionalFeature.balcony");
      case "MUNICIPAL_WATERWORK":
        return t("listing.feature.additionalFeature.municipalWaterwork");
      default:
        return value;
    }
  };
  return { handleTranslationAmenities, translateAmenities };
}
