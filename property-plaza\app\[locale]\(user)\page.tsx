import { Metadata } from "next";
import dynamic from 'next/dynamic';
import { getLocale, getTranslations } from "next-intl/server";
import { getHomepageSeekersListingService } from "@/core/infrastructures/listing/service";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import PropertiesContent from "./(listings)/ssr/properties-content";
import ClearSearchHelper from "./clear-search-helper";
import SocialAuthFormatter from "../(auth)/social-auth";
import { BaseMetadataProps } from "@/types/base";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { routing } from "@/lib/locale/routing";


const BlogContent = dynamic(() => import("./(listings)/ssr/blog-content"))
const SeekerFaQContent = dynamic(() => import("./(listings)/faq/content"))
const SeekerSeo = dynamic(() => import("@/components/footer/seeker-seo"))
const CategoryContent = dynamic(() => import("./(listings)/category-content"))
const HowItWorksContent = dynamic(() => import("./(listings)/seekers-how-it-works"))

export async function generateMetadata({ params, searchParams }: BaseMetadataProps<{}>): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"

  return {
    title: t('metadata.rootLayout.title'),
    description: t('metadata.rootLayout.description'),
    alternates: {
      canonical: baseUrl + locale + "",
      languages: {
        id: baseUrl + "id",
        en: baseUrl + "en",
        "x-default": baseUrl,
      },
    },
    keywords: t('metadata.rootLayout.keyword'), // Or .join(", ") if it's an array
    openGraph: {
      title: t('metadata.rootLayout.title'),
      description: t('metadata.rootLayout.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t('metadata.rootLayout.title'),
      description: t('metadata.rootLayout.description'),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: true,
      follow: true,
      nocache: false,
    },
  }
}

export default async function Home({ searchParams }: { searchParams: { [key: string]: string | undefined } }) {
  const { at, status_code, ma } = searchParams
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const getHomepageListing = await getHomepageSeekersListingService(locale)
  const currencyConversion = await getCurrencyConversion()
  const conversion = currencyConversion.data
  return (<>
    <h1 className="sr-only">{t('srOnly.FindBestPropertyOnBali')}</h1>
    <SocialAuthFormatter accessToken={at} status={status_code} expired={ma || "28800"} />
    <div className="min-h-screen space-y-12 mt-8">
      <MainContentLayout>
        <ClearSearchHelper />
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.popular || []} title={t('listing.popularProperty.title')} />
      </MainContentLayout>
      <CategoryContent />
      <MainContentLayout>
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.newest || []} title={t('listing.newestProperty.title')} forceLazyLoad />
      </MainContentLayout>
      <HowItWorksContent />
      {/* <MainContentLayout>
        <PropertiesContent data={getHomepageListing.data?.commercial || []} title={t('listing.newestCommercialProperty.title')} />
      </MainContentLayout> */}
      <BlogContent />
      <MainContentLayout>
        <PropertiesContent conversions={conversion} data={getHomepageListing.data?.featured || []} title={t('listing.featuredProperty.title')} forceLazyLoad />
      </MainContentLayout>

      <SeekerFaQContent />
    </div>
    <SeekerSeo />
  </>
  );
}
