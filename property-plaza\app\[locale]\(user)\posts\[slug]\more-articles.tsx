import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Blog } from "@/core/services/sanity/types";
import { imagePlaceholder } from "@/lib/constanta/image-placeholder";
import moment from "moment";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Link } from "@/lib/locale/routing";;

export default function MoreArticles({ moreContent }: { moreContent: Blog[] }) {
  const t = useTranslations("seeker")
  return <MainContentLayout className="xl:max-w-screen-lg space-y-6 pb-6">
    <h2 className="text-2xl font-bold text-seekers-text">{t('blog.moreArticles.title')}</h2>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4  h-fit">
      {
        moreContent.map((item) => item.mainImage?.asset?.url ? <MoreArticleCard
          slug={item.slug.current}
          key={item._id}
          alt={item.title}
          description={item.metadata}
          imageUrl={item.mainImage?.asset?.url || imagePlaceholder}
          publishedAt={item.publishedAt}
          title={item.title}

        /> : <></>)
      }
    </div>
  </MainContentLayout >
}

function MoreArticleCard({ alt, imageUrl, publishedAt, title, slug }: {
  imageUrl: string,
  alt: string,
  title: string,
  description: string,
  publishedAt: string,
  slug: string
}) {

  return <Link href={slug} className="w-full space-y-4 text-seekers-text">
    <div className="relative aspect-[4/3] w-full rounded-xl overflow-hidden">
      <Image
        src={imageUrl}
        alt={alt}
        fill
        style={{ objectFit: "cover" }}
      />
    </div>
    <div className="px-0.5">
      <p className="text-seekers-text-light">{moment(publishedAt).format("DD MMM YYYY")}</p>
      <h3 className="text-base font-semibold">
        {title}
      </h3>
    </div>

  </Link>
}