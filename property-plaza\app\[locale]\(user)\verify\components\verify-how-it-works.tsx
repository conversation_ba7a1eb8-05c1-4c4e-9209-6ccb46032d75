import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { useTranslations } from "next-intl";
import { Calendar, Search, FileText } from "lucide-react";

export default function VerifyHowItWorks() {
  const t = useTranslations("verify");

  const howItWorks = [
    {
      icon: <Calendar className="w-6 h-6" />,
      title: t("howItWorks.steps.book.title"),
      description: t("howItWorks.steps.book.description"),
      result: t("howItWorks.steps.book.result")
    },
    {
      icon: <Search className="w-6 h-6" />,
      title: t("howItWorks.steps.inspect.title"),
      description: t("howItWorks.steps.inspect.description"),
      result: [
        t("howItWorks.steps.inspect.result.basic"),
        t("howItWorks.steps.inspect.result.smart"),
        t("howItWorks.steps.inspect.result.fullShield")
      ]
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t("howItWorks.steps.report.title"),
      description: t("howItWorks.steps.report.description"),
      result: [
        t("howItWorks.steps.report.result.basic"),
        t("howItWorks.steps.report.result.smart"),
        t("howItWorks.steps.report.result.fullShield")
      ]
    }
  ];

  return (
    <section className="bg-seekers-foreground/50 py-12" aria-labelledby="how-it-works-title">
      <MainContentLayout>
        <div className="space-y-6">
          {/* Centered Header */}
          <div className="text-center space-y-2">
            <h2 id="how-it-works-title" className="text-3xl md:text-4xl font-bold text-seekers-text">
              {t("howItWorks.title")}
            </h2>
            <p className="text-base font-semibold tracking-[0.5%] text-seekers-text-light">
              {t("howItWorks.subtitle")}
            </p>
          </div>

          <div className="relative max-w-[1200px] mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
              {/* Connection Line - Hidden on mobile */}
              <div className="hidden md:block absolute top-16 left-1/2 transform -translate-x-1/2 w-full h-0.5 bg-seekers-primary/20 -z-10"></div>
              
              {howItWorks.map((step, index) => (
                <div key={index} className="relative">
                  {/* Step Number Circle */}
                  <div className="flex justify-center mb-4">
                    <div className="w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center text-white font-bold text-lg relative z-10">
                      {index + 1}
                    </div>
                  </div>
                  
                  {/* Step Content */}
                  <div className="text-center space-y-4">
                    <div className="flex justify-center text-seekers-primary">
                      {step.icon}
                    </div>
                    
                    <h3 className="text-xl font-bold text-seekers-text">
                      {step.title}
                    </h3>
                    
                    <p className="text-seekers-text-light leading-relaxed">
                      {step.description}
                    </p>
                    
                    {/* Result Box */}
                    <div className="bg-white rounded-lg p-4 border border-seekers-primary/20 shadow-sm">
                      <h4 className="font-semibold text-seekers-text mb-2 text-sm">
                        📋 What you get:
                      </h4>
                      {Array.isArray(step.result) ? (
                        <ul className="text-sm text-seekers-text-light space-y-1">
                          {step.result.map((item, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <span className="text-seekers-primary mt-0.5">•</span>
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-seekers-text-light">
                          {step.result}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Why Choose Section */}
          <div className="mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto">
            <h3 className="text-xl md:text-2xl font-bold text-seekers-text mb-4">
              {t("howItWorks.whyChoose.title")}
            </h3>
            <p className="text-lg text-seekers-text-light">
              {t("howItWorks.whyChoose.description")}
            </p>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
