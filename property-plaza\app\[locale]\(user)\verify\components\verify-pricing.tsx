"use client";
import React from "react";
import { useTranslations } from "next-intl";
import { Check } from "lucide-react";

interface PricingTier {
  id: string;
  name: string;
  subtitle: string;
  price: number;
  popular?: boolean;
  features: string[];
}

export default function VerifyPricing() {
  const t = useTranslations("verify.pricing");

  const tiers: PricingTier[] = [
    {
      id: "basic",
      name: t("tiers.basic.name"),
      subtitle: t("tiers.basic.subtitle"),
      price: parseInt(t("tiers.basic.price")),
      features: [
        t("tiers.basic.features.0"),
        t("tiers.basic.features.1"),
        t("tiers.basic.features.2"),
        t("tiers.basic.features.3"),
        t("tiers.basic.features.4"),
      ],
    },
    {
      id: "standard",
      name: t("tiers.standard.name"),
      subtitle: t("tiers.standard.subtitle"),
      price: parseInt(t("tiers.standard.price")),
      popular: true,
      features: [
        t("tiers.standard.features.0"),
        t("tiers.standard.features.1"),
        t("tiers.standard.features.2"),
        t("tiers.standard.features.3"),
        t("tiers.standard.features.4"),
        t("tiers.standard.features.5"),
        t("tiers.standard.features.6"),
      ],
    },
    {
      id: "premium",
      name: t("tiers.premium.name"),
      subtitle: t("tiers.premium.subtitle"),
      price: parseInt(t("tiers.premium.price")),
      features: [
        t("tiers.premium.features.0"),
        t("tiers.premium.features.1"),
        t("tiers.premium.features.2"),
        t("tiers.premium.features.3"),
        t("tiers.premium.features.4"),
        t("tiers.premium.features.5"),
        t("tiers.premium.features.6"),
        t("tiers.premium.features.7"),
        t("tiers.premium.features.8"),
      ],
    },
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(price);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            {t("title")}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t("subtitle")}
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              className={`relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg ${
                tier.popular ? 'border-blue-500 shadow-lg' : 'border-gray-200'
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    {t("popular")}
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {tier.name}
                </h3>
                <div className="text-3xl font-bold text-blue-600">
                  {formatPrice(tier.price)}
                </div>
                <p className="text-gray-600 text-sm mt-2">
                  {tier.subtitle}
                </p>
              </div>

              <ul className="space-y-3 mb-8">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors duration-200 ${
                  tier.popular
                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                }`}
              >
                Choose {tier.name}
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
