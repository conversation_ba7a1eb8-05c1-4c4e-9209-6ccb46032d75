"use client"
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useReCaptcha } from "next-recaptcha-v3";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl, FormField } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import DefaultInput from "@/components/input-form/default-input";
import BaseInputLayout from "@/components/input-form/base-input";
import { useVerifyBookingFormSchema } from "./use-verify-booking-form.schema";
import SelectInput from "@/components/input-form/select-input";
import { BaseSelectInputValue } from "@/types/base";

interface PricingTier {
  id: string;
  name: string;
  price: number;
}

interface VerifyBookingFormProps {
  selectedTier?: PricingTier;
  conversions: { [key: string]: number };
}

export default function VerifyBookingForm({ selectedTier, conversions }: VerifyBookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { executeRecaptcha } = useReCaptcha();
  const { toast } = useToast();
  const t = useTranslations("verify");
  const formSchema = useVerifyBookingFormSchema()
  type formSchemaType = z.infer<typeof formSchema>


  const tiers: BaseSelectInputValue<string>[] = [
    {
      id: "basic",
      content: t("booking.form.tier.options.basic"),
      value: "basic"
    },
    {
      id: "smart",
      content: t("booking.form.tier.options.smart"),
      value: "smart"
    },
    {
      id: "full-shield",
      content: t("booking.form.tier.options.fullShield"),
      value: "full-shield"
    },
  ];

  const form = useForm<formSchemaType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      whatsappNumber: "",
      villaAddress: "",
      preferredDate: "",
      tier: selectedTier?.id || ""
    }
  });

  // Update form when selectedTier changes
  useEffect(() => {
    if (selectedTier) {
      form.setValue("tier", selectedTier.id);
    }
  }, [selectedTier, form]);

  const onSubmit = async (data: formSchemaType) => {
    setIsSubmitting(true);

    try {
      const token = await executeRecaptcha("verify_booking");

      const response = await fetch("/api/verify-booking-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
        }),
      });

      const result = await response.json();

      if (response.ok && result.url) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        throw new Error(result.error || "Failed to create checkout session");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast({
        title: t("booking.form.error.title"),
        description: t("booking.form.error.message"),
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  };

  // Calculate minimum date (tomorrow)
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <section id="booking-form" className="py-16 bg-white isolate" aria-labelledby="booking-title">
      <MainContentLayout>
        <div className="max-w-lg mx-auto">
          {/* Header - Originele stijl behouden */}
          <div className="text-center mb-8">
            <h2 id="booking-title" className="text-3xl md:text-4xl font-bold text-seekers-text mb-4">
              {t("booking.title")}
            </h2>
            <p className="text-lg text-seekers-text-light">
              {t("booking.subtitle")}
            </p>
          </div>

          {/* Form with Login Style */}
          <div className="w-full space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">

                {/* Name Fields */}
                <div className="grid grid-cols-2 gap-4">
                  <DefaultInput
                    form={form}
                    label={t("booking.form.firstName.label")}
                    name="firstName"
                    placeholder=""
                    type="text"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                  />
                  <DefaultInput
                    form={form}
                    label={t("booking.form.lastName.label")}
                    name="lastName"
                    placeholder=""
                    type="text"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                  />
                </div>

                {/* Email */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.email.label")}
                  name="email"
                  placeholder=""
                  type="email"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* WhatsApp */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.whatsappNumber.label")}
                  name="whatsappNumber"
                  placeholder=""
                  type="tel"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* Villa Address */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.villaAddress.label")}
                  name="villaAddress"
                  placeholder=""
                  type="text"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* Date and Tier */}
                <div className="grid grid-cols-2 gap-4">
                  <DefaultInput
                    form={form}
                    label={t("booking.form.preferredDate.label")}
                    name="preferredDate"
                    placeholder=""
                    type="date"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                    inputProps={{ min: minDate }}
                  />
                  <SelectInput
                    form={form}
                    label={t("booking.form.tier.label")}
                    name="tier"
                    placeholder={t("booking.form.tier.placeholder")}
                    selectList={tiers}
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                  />
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full"
                  loading={isSubmitting}
                >
                  {t("booking.form.cta")}
                </Button>

                {/* Disclaimer */}
                <div className="text-xs text-neutral space-x-1 !mt-2 text-center">
                  <span>{t("booking.form.disclaimer")}</span>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
