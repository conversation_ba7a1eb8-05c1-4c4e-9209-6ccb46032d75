import SeekersFooter from "@/components/footer/seeker-footer";
import SeekersBanner from "@/components/navbar/seekers-banner";
import SeekersNavbar from "@/components/navbar/seekers-navbar-2";
import React from "react";
import NotificationProvider from "@/components/providers/notification-provider";

export default function VerifyLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <NotificationProvider isSeeker />
      <SeekersBanner />
      <div className="w-full sticky top-0 z-10 bg-white !mt-0">
        <SeekersNavbar currency_="IDR" localeId="en" />
      </div>
      <div className="!mt-0 relative min-h-screen max-sm:max-w-screen-sm">
        {children}
      </div>
      <div className="!mt-0">
        <SeekersFooter />
      </div>
    </>
  );
}
