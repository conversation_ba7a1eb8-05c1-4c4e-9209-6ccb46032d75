"use client"
import { DataPagination } from "@/components/utility/seekers-pagination"
import { useLocale, useTranslations } from "next-intl"
import { useSeekerFilterResultStore } from "@/stores/seeker-filter-result.store"
import { useEffect } from "react"
import { ListingImage, ListingLoader, ListingLocation, ListingPrice, ListingWrapper } from "../../(user)/(listings)/listing-item"
import { useGetFavoriteSeekersListing } from "@/core/applications/queries/listing/use-get-favorite-listing"
import { SearchParams } from "@/hooks/use-seekers-filter"
import { Button } from "@/components/ui/button"
import { Link } from "@/lib/locale/routing";
import { useUserStore } from "@/stores/user.store"

const MAX_LIMIT = 20

export default function FavoriteListingContent({ page, conversions, sortBy }: SearchParams & { conversions: { [key: string]: number } }) {
  const t = useTranslations("seeker")
  const store = useSeekerFilterResultStore()
  const { seekers } = useUserStore()
  const locale = useLocale()

  const { query: properties } = useGetFavoriteSeekersListing({
    page: page || "1",
    per_page: MAX_LIMIT.toString(),
    sort_by: sortBy || "DATE_NEWEST"
  }, seekers?.email != "", locale)

  useEffect(() => {
    if (properties.isError) return store.setIsLoading(false)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [properties.isError])
  useEffect(() => {

    store.setIsLoading(properties.isPending)
    if (!properties.isSuccess) return
    store.setData(properties.data?.data || [])
    store.setTotal(properties.data.meta?.total || 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [properties.data?.data]) // don't include store on depedencies, it cause infinity rendering\


  return <>
    <section className="min-h-[calc(100vh-202px)]">
      <div className="grid gap-3 gap-x-3 gap-y-6 max-sm:my-4 md:grid-cols-2 xl:grid-cols-4">
        {properties.isPending ?
          Array(12).fill(0).map((_, idx) => <ListingLoader key={idx} />)
          :
          store.data && store.data.length > 0 ?
            <>
              {store.data.map((item, idx) => <div
                key={idx}
              >
                <ListingWrapper
                  className="space-y-3"
                  data={{ ...item, isFavorite: true }}
                  conversion={conversions}
                  handleFavoriteListing={(val) => {
                    if (!val) {
                      const data = store.data.filter((_, index) => index !== idx);
                      store.setData(data || [])
                    }
                  }}>
                  <ListingImage heartSize="large" allowFavoriteWhileInactive />
                  <div className="px-0.5 space-y-2">
                    <div>
                      <h2 className="text-base text-seekers-text font-semibold line-clamp-1">{item.title}</h2>
                      <ListingLocation className="text-seekers-text" />
                    </div>
                    <ListingPrice />
                  </div>
                </ListingWrapper>
              </div>
              )}

            </>
            : <div className="col-span-4 flex flex-col justify-center items-center">
              <p className="col-span-full text-center font-semibold max-w-md py-8">{t('listing.misc.favoritePropertyNotFound')}</p>
              <Button variant={"default-seekers"} asChild>
                <Link href="/">
                  {t('cta.showAllProperty')}
                </Link>
              </Button>
            </div>
        }
      </div >
    </section>
    <section className="!my-12">
      {
        properties.isPending || (properties.data?.data?.length && properties.data?.data?.length < MAX_LIMIT && properties.data.meta.pageCount == 1) ? <></>
          :
          <div className="w-fit mx-auto">
            <DataPagination meta={properties?.data?.meta}
              totalThreshold={MAX_LIMIT}
              disableRowPerPage />
          </div>
      }
    </section>
  </>
}