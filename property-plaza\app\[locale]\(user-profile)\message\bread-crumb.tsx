import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Breadcrumb, BreadcrumbI<PERSON>, B<PERSON><PERSON>rumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { Home } from "lucide-react";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/locale/routing";;

export default function MessageBreadCrumb() {
  const t = useTranslations("seeker")
  return <MainContentLayout className={cn("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0 w-full")}>
    <SidebarTrigger className="items-end -ml-2" />

    <Breadcrumb className="">
      <BreadcrumbList className="space-x-4 sm:gap-0">
        <BreadcrumbItem className="text-seekers-text font-medium text-sm">
          <Link href={"/"} className="flex gap-2.5 items-center">
            <Home className="w-4 h-4" strokeWidth={1} />
            {t('misc.home')}
          </Link>
        </BreadcrumbItem>
        <BreadcrumbSeparator className="text-seekers-text font-medium text-sm w-3 h-fit text-center">
          /
        </BreadcrumbSeparator>
        <BreadcrumbItem className="capitalize text-seekers-text font-medium text-sm">
          {t('accountAndProfile.message')}
        </BreadcrumbItem>
      </BreadcrumbList>
    </Breadcrumb>
  </MainContentLayout>
}