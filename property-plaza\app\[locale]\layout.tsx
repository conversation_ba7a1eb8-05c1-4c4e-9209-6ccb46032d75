import type { Viewport } from "next";
import { Inter } from "next/font/google";
import { NextIntlClientProvider } from 'next-intl';
import "./globals.css";
import { getMessages } from "next-intl/server";
import GoogleMapsProvider from "@/components/providers/google-maps-provider";
import TanstackQueryProvider from "@/components/providers/tanstack-query-provider";
import { Toaster } from "@/components/ui/toaster"
import NextTopLoader from "nextjs-toploader";
import MomentLocale from "@/components/locale/moment-locale";
import FacebookPixel from "./facebook-pixel";
import { Suspense } from "react";
import NotificationProvider from "@/components/providers/notification-provider";
import CookieConsent from "@/components/cookie-consent/cookie-consent";
import RecaptchaProvider from "@/components/providers/recaptcha-provider";

const inter = Inter({ subsets: ["latin"] });
export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover"
}

export default async function RootLayout({
  children, params
}: Readonly<{
  children: React.ReactNode, params: { locale: string }
}>) {
  const messages = await getMessages();
  const { locale } = params
  return (
    <html lang={locale}>
      <meta name="google-site-verification" content="4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw" />
      <NextIntlClientProvider messages={messages}>
        <RecaptchaProvider>
          <body className={`${inter.className} relative`}>
            <NotificationProvider />
            <Suspense fallback="null">
              <FacebookPixel />
            </Suspense>
            <MomentLocale />
            <NextTopLoader
              showSpinner={false}
            />
            <TanstackQueryProvider>
              <GoogleMapsProvider>
                {children}
                <Toaster />
              </GoogleMapsProvider>
            </TanstackQueryProvider>
            <CookieConsent />
          </body>
        </RecaptchaProvider>
      </NextIntlClientProvider>
    </html>
  );
}
