"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function StripeTestPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleTestPayment = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/verify-booking-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: "Test",
          lastName: "User",
          email: "<EMAIL>",
          whatsappNumber: "+62812345678",
          villaAddress: "Test Villa Address, Bali",
          preferredDate: "2024-02-01",
          tier: "basic",
          recaptchaToken: "test-token"
        }),
      });

      const result = await response.json();

      if (response.ok && result.url) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        throw new Error(result.error || "Failed to create checkout session");
      }
    } catch (error: any) {
      console.error("Checkout error:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>🧪 Stripe Test</CardTitle>
          <CardDescription>
            Test de Stripe integratie met een eenvoudige checkout
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600">
            <p><strong>Test Data:</strong></p>
            <ul className="list-disc list-inside space-y-1 mt-2">
              <li>Naam: Test User</li>
              <li>Email: <EMAIL></li>
              <li>Tier: Basic (IDR 4,500,000)</li>
              <li>Villa: Test Villa Address, Bali</li>
            </ul>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          <Button 
            onClick={handleTestPayment}
            disabled={loading}
            className="w-full"
          >
            {loading ? "Creating Checkout..." : "🚀 Test Stripe Checkout"}
          </Button>

          <div className="text-xs text-gray-500 space-y-1">
            <p><strong>Test Credit Cards:</strong></p>
            <p>• 4242 4242 4242 4242 (Visa)</p>
            <p>• 5555 5555 5555 4444 (Mastercard)</p>
            <p>• Expiry: Any future date</p>
            <p>• CVC: Any 3 digits</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
