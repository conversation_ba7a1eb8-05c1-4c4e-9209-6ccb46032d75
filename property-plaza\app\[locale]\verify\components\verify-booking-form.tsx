"use client"
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import phone from "phone";
import { useReCaptcha } from "next-recaptcha-v3";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";

interface PricingTier {
  id: string;
  name: string;
  price: number;
}

interface VerifyBookingFormProps {
  selectedTier?: PricingTier;
  conversions: { [key: string]: number };
}

export default function VerifyBookingForm({ selectedTier, conversions }: VerifyBookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { executeRecaptcha } = useReCaptcha();
  const { toast } = useToast();
  const t = useTranslations("verify");

  const formSchema = z.object({
    firstName: z.string()
      .min(1, t("booking.form.firstName.required"))
      .min(2, t("booking.form.firstName.minLength")),
    lastName: z.string()
      .min(1, t("booking.form.lastName.required"))
      .min(2, t("booking.form.lastName.minLength")),
    email: z.string()
      .min(1, t("booking.form.email.required"))
      .email(t("booking.form.email.invalid")),
    whatsappNumber: z.string()
      .min(1, t("booking.form.whatsappNumber.required"))
      .refine(value => {
        const phoneChecker = phone(value);
        return phoneChecker.isValid;
      }, t("booking.form.whatsappNumber.invalid")),
    villaAddress: z.string()
      .min(1, t("booking.form.villaAddress.required"))
      .min(10, t("booking.form.villaAddress.minLength")),
    preferredDate: z.string()
      .min(1, t("booking.form.preferredDate.required")),
    tier: z.string()
      .min(1, t("booking.form.tier.required"))
  });

  type FormData = z.infer<typeof formSchema>;

  const tiers = [
    { id: "basic", name: t("booking.form.tier.options.basic"), price: 4500000 },
    { id: "smart", name: t("booking.form.tier.options.smart"), price: 6000000 },
    { id: "full-shield", name: t("booking.form.tier.options.fullShield"), price: 8500000 }
  ];

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      whatsappNumber: "",
      villaAddress: "",
      preferredDate: "",
      tier: selectedTier?.id || ""
    }
  });

  // Update form when selectedTier changes
  React.useEffect(() => {
    if (selectedTier) {
      form.setValue("tier", selectedTier.id);
    }
  }, [selectedTier, form]);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    try {
      const token = await executeRecaptcha("verify_booking");

      const response = await fetch("/api/verify-booking-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          recaptchaToken: token,
        }),
      });

      const result = await response.json();

      if (response.ok && result.url) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        throw new Error(result.error || "Failed to create checkout session");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast({
        title: t("booking.form.error.title"),
        description: t("booking.form.error.message"),
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  };

  // Calculate minimum date (tomorrow)
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <section id="booking-form" className="py-16 bg-white" aria-labelledby="booking-title">
      <MainContentLayout>
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h2 id="booking-title" className="text-3xl md:text-4xl font-bold text-seekers-text mb-4">
              {t("booking.title")}
            </h2>
            <p className="text-lg text-seekers-text-light">
              {t("booking.subtitle")}
            </p>
          </div>

          <div className="bg-seekers-foreground/20 rounded-lg p-6 md:p-8">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.firstName.label")} *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("booking.form.firstName.placeholder")}
                            {...field}
                            className="border-neutral-300 focus:border-seekers-primary"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.lastName.label")} *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("booking.form.lastName.placeholder")}
                            {...field}
                            className="border-neutral-300 focus:border-seekers-primary"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Email and WhatsApp */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.email.label")} *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder={t("booking.form.email.placeholder")}
                            {...field}
                            className="border-neutral-300 focus:border-seekers-primary"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="whatsappNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.whatsappNumber.label")} *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder={t("booking.form.whatsappNumber.placeholder")}
                            {...field}
                            className="border-neutral-300 focus:border-seekers-primary"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Villa Address */}
                <FormField
                  control={form.control}
                  name="villaAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-seekers-text font-semibold">
                        {t("booking.form.villaAddress.label")} *
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("booking.form.villaAddress.placeholder")}
                          {...field}
                          className="border-neutral-300 focus:border-seekers-primary"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date and Tier */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="preferredDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.preferredDate.label")} *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            min={minDate}
                            {...field}
                            className="border-neutral-300 focus:border-seekers-primary"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="tier"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-seekers-text font-semibold">
                          {t("booking.form.tier.label")} *
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-neutral-300 focus:border-seekers-primary">
                              <SelectValue placeholder={t("booking.form.tier.placeholder")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {tiers.map((tier) => (
                              <SelectItem key={tier.id} value={tier.id}>
                                {tier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-seekers-primary hover:bg-seekers-primary/90 text-white py-3 text-lg font-semibold"
                  loading={isSubmitting}
                >
                  {isSubmitting ? t("booking.form.submitting") : t("booking.form.cta")}
                </Button>

                <p className="text-xs text-seekers-text-light text-center">
                  {t("booking.form.disclaimer")}
                </p>
              </form>
            </Form>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
