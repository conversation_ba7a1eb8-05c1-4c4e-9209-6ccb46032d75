"use client"
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import phone from "phone";
import { useReCaptcha } from "next-recaptcha-v3";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useTranslations } from "next-intl";
import DefaultInput from "@/components/input-form/default-input";
import BaseInputLayout from "@/components/input-form/base-input";

interface PricingTier {
  id: string;
  name: string;
  price: number;
}

interface VerifyBookingFormProps {
  selectedTier?: PricingTier;
  conversions: { [key: string]: number };
}

export default function VerifyBookingForm({ selectedTier, conversions }: VerifyBookingFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { executeRecaptcha } = useReCaptcha();
  const { toast } = useToast();
  const t = useTranslations("verify");

  const formSchema = z.object({
    firstName: z.string()
      .min(1, t("booking.form.firstName.required"))
      .min(2, t("booking.form.firstName.minLength")),
    lastName: z.string()
      .min(1, t("booking.form.lastName.required"))
      .min(2, t("booking.form.lastName.minLength")),
    email: z.string()
      .min(1, t("booking.form.email.required"))
      .email(t("booking.form.email.invalid")),
    whatsappNumber: z.string()
      .min(1, t("booking.form.whatsappNumber.required"))
      .refine(value => {
        const phoneChecker = phone(value);
        return phoneChecker.isValid;
      }, t("booking.form.whatsappNumber.invalid")),
    villaAddress: z.string()
      .min(1, t("booking.form.villaAddress.required"))
      .min(10, t("booking.form.villaAddress.minLength")),
    preferredDate: z.string()
      .min(1, t("booking.form.preferredDate.required")),
    tier: z.string()
      .min(1, t("booking.form.tier.required"))
  });

  type FormData = z.infer<typeof formSchema>;

  const tiers = [
    { id: "basic", name: t("booking.form.tier.options.basic"), price: 4500000 },
    { id: "smart", name: t("booking.form.tier.options.smart"), price: 6000000 },
    { id: "full-shield", name: t("booking.form.tier.options.fullShield"), price: 8500000 }
  ];

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      whatsappNumber: "",
      villaAddress: "",
      preferredDate: "",
      tier: selectedTier?.id || ""
    }
  });

  // Update form when selectedTier changes
  React.useEffect(() => {
    if (selectedTier) {
      form.setValue("tier", selectedTier.id);
    }
  }, [selectedTier, form]);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);

    try {
      const token = await executeRecaptcha("verify_booking");

      const response = await fetch("/api/verify-booking-checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          recaptchaToken: token,
        }),
      });

      const result = await response.json();

      if (response.ok && result.url) {
        // Redirect to Stripe checkout
        window.location.href = result.url;
      } else {
        throw new Error(result.error || "Failed to create checkout session");
      }
    } catch (error) {
      console.error("Checkout error:", error);
      toast({
        title: t("booking.form.error.title"),
        description: t("booking.form.error.message"),
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  };

  // Calculate minimum date (tomorrow)
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];

  return (
    <section id="booking-form" className="py-16 bg-white" aria-labelledby="booking-title">
      <MainContentLayout>
        <div className="max-w-lg mx-auto">
          {/* Header */}
          <div className="!mb-12">
            <h2 id="booking-title" className="text-2xl font-semibold text-center">
              {t("booking.title")}
            </h2>
            <p className="text-center text-muted-foreground">
              {t("booking.subtitle")}
            </p>
          </div>

          {/* Form with Login Style */}
          <div className="w-full space-y-6">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">

                {/* Name Fields */}
                <div className="grid grid-cols-2 gap-4">
                  <DefaultInput
                    form={form}
                    label={t("booking.form.firstName.label")}
                    name="firstName"
                    placeholder=""
                    type="text"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                  />
                  <DefaultInput
                    form={form}
                    label={t("booking.form.lastName.label")}
                    name="lastName"
                    placeholder=""
                    type="text"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                  />
                </div>

                {/* Email */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.email.label")}
                  name="email"
                  placeholder=""
                  type="email"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* WhatsApp */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.whatsappNumber.label")}
                  name="whatsappNumber"
                  placeholder=""
                  type="tel"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* Villa Address */}
                <DefaultInput
                  form={form}
                  label={t("booking.form.villaAddress.label")}
                  name="villaAddress"
                  placeholder=""
                  type="text"
                  variant="float"
                  labelClassName="text-xs text-seekers-text-light font-normal"
                />

                {/* Date and Tier */}
                <div className="grid grid-cols-2 gap-4">
                  <DefaultInput
                    form={form}
                    label={t("booking.form.preferredDate.label")}
                    name="preferredDate"
                    placeholder=""
                    type="date"
                    variant="float"
                    labelClassName="text-xs text-seekers-text-light font-normal"
                    inputProps={{ min: minDate }}
                  />

                  <FormField
                    control={form.control}
                    name="tier"
                    render={({ field }) => (
                      <BaseInputLayout
                        label={t("booking.form.tier.label")}
                        labelClassName="absolute -top-2 left-2 px-1 text-xs bg-background z-10 text-seekers-text-light font-normal"
                        variant="float"
                      >
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-none focus:outline-none shadow-none focus-visible:ring-0 px-0">
                              <SelectValue placeholder={t("booking.form.tier.placeholder")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {tiers.map((tier) => (
                              <SelectItem key={tier.id} value={tier.id}>
                                {tier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </BaseInputLayout>
                    )}
                  />
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full"
                  loading={isSubmitting}
                >
                  {t("booking.form.cta")}
                </Button>

                {/* Disclaimer */}
                <div className="text-xs text-neutral space-x-1 !mt-2 text-center">
                  <span>{t("booking.form.disclaimer")}</span>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </MainContentLayout>
    </section>
  );
}
