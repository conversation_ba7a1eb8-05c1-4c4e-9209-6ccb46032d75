import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { useTranslations } from "next-intl";

export default function VerifyDisclaimer() {
  const t = useTranslations("verify");

  return (
    <section className="py-12 bg-gray-50 border-t border-gray-200">
      <MainContentLayout>
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {t("disclaimer.title")}
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            {t("disclaimer.content")}
          </p>
        </div>
      </MainContentLayout>
    </section>
  );
}
