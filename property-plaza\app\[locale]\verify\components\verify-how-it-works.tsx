import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";
import { useTranslations } from "next-intl";
import { Calendar, Search, FileText } from "lucide-react";

export default function VerifyHowItWorks() {
  const t = useTranslations("verify");

  const howItWorks = [
    {
      icon: <Calendar className="w-6 h-6" />,
      title: t("howItWorks.steps.book.title"),
      description: t("howItWorks.steps.book.description"),
      result: t("howItWorks.steps.book.result")
    },
    {
      icon: <Search className="w-6 h-6" />,
      title: t("howItWorks.steps.inspect.title"),
      description: t("howItWorks.steps.inspect.description"),
      result: [
        t("howItWorks.steps.inspect.result.basic"),
        t("howItWorks.steps.inspect.result.smart"),
        t("howItWorks.steps.inspect.result.fullShield")
      ]
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t("howItWorks.steps.report.title"),
      description: t("howItWorks.steps.report.description"),
      result: [
        t("howItWorks.steps.report.result.basic"),
        t("howItWorks.steps.report.result.smart"),
        t("howItWorks.steps.report.result.fullShield")
      ]
    }
  ];

  return (
    <section className="bg-seekers-foreground/50 py-12">
      <MainContentLayout>
        <DefaultLayoutContent className="!mt-2"
          title={t("howItWorks.title")}
          description={t("howItWorks.subtitle")}
        >
          <div className="relative max-w-[1200px] mx-auto px-4">
            {/* Vertical connection line for desktop */}
            <div className="absolute hidden xl:block left-1/2 top-24 h-[calc(100%-6rem)] w-0.5 bg-gradient-to-b from-seekers-primary/20 via-seekers-primary/10 to-transparent" />

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8 relative">
              {howItWorks.map((step, index) => (
                <div
                  key={index}
                  className={`group relative bg-white p-6 rounded-2xl border border-gray-100
                  hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md
                  ${index === 0 ? 'xl:translate-x-[-15%]' : ''}
                  ${index === 1 ? 'xl:translate-x-[15%] xl:mt-16' : ''}
                  ${index === 2 ? 'xl:translate-x-[-15%] xl:mt-[-4rem]' : ''}
                  min-h-[280px]`}
                >
                  {/* Connection point with line */}
                  <div className={`absolute hidden xl:block top-1/2
                  ${index % 2 === 0 ? 'right-0 translate-x-1/2' : 'left-0 -translate-x-1/2'}
                  w-4 h-4 rounded-full bg-seekers-primary/20 group-hover:bg-seekers-primary transition-colors`}>
                    <div className="absolute inset-1 bg-white rounded-full" />
                    <div className="absolute inset-2 bg-seekers-primary/40 group-hover:bg-seekers-primary rounded-full transition-colors" />
                  </div>

                  <div className="flex flex-col h-full">
                    {/* Icon and number container */}
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative shrink-0">
                        <div className="w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center
                        text-seekers-primary group-hover:scale-110 transition-transform duration-300">
                          {step.icon}
                        </div>
                        <div className="absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full
                        group-hover:blur-2xl transition-all duration-300" />
                      </div>
                      <span className="text-2xl font-bold text-seekers-primary/20
                      group-hover:text-seekers-primary transition-colors">
                        0{index + 1}
                      </span>
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3
                      group-hover:text-seekers-primary transition-colors duration-300">
                        {step.title}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed mb-4">
                        {step.description}
                      </p>

                      {/* Result Box */}
                      <div className="bg-seekers-primary/5 rounded-lg p-4 border border-seekers-primary/20 shadow-sm">
                        <h4 className="font-semibold text-seekers-text mb-2 text-sm">
                          📋 What you get:
                        </h4>
                        {Array.isArray(step.result) ? (
                          <ul className="text-sm text-seekers-text-light space-y-1">
                            {step.result.map((item, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <span className="text-seekers-primary mt-0.5">•</span>
                                <span>{item}</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-seekers-text-light">
                            {step.result}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Hover effect border */}
                  <div className="absolute inset-0 border-2 border-transparent
                  group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300" />
                </div>
              ))}
            </div>
          </div>

          {/* Why Choose Section */}
          <div className="mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto">
            <h3 className="text-xl md:text-2xl font-bold text-seekers-text mb-4">
              {t("howItWorks.whyChoose.title")}
            </h3>
            <p className="text-lg text-seekers-text-light">
              {t("howItWorks.whyChoose.description")}
            </p>
          </div>
        </DefaultLayoutContent>
      </MainContentLayout>
    </section>
  );
}
