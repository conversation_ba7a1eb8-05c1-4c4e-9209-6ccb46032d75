import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import DefaultLayoutContent from "@/components/seekers-content-layout/default-layout-content";
import { useTranslations } from "next-intl";
import { Calendar, Search, FileText } from "lucide-react";

export default function VerifyHowItWorks() {
  const t = useTranslations("verify");

  const howItWorks = [
    {
      icon: <Calendar className="w-6 h-6" />,
      title: t("howItWorks.steps.book.title"),
      description: t("howItWorks.steps.book.description"),
      result: t("howItWorks.steps.book.result")
    },
    {
      icon: <Search className="w-6 h-6" />,
      title: t("howItWorks.steps.inspect.title"),
      description: t("howItWorks.steps.inspect.description"),
      result: [
        t("howItWorks.steps.inspect.result.basic"),
        t("howItWorks.steps.inspect.result.smart"),
        t("howItWorks.steps.inspect.result.fullShield")
      ]
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: t("howItWorks.steps.report.title"),
      description: t("howItWorks.steps.report.description"),
      result: [
        t("howItWorks.steps.report.result.basic"),
        t("howItWorks.steps.report.result.smart"),
        t("howItWorks.steps.report.result.fullShield")
      ]
    }
  ];

  return (
    <section className="bg-seekers-foreground/50 py-12">
      <MainContentLayout>
        <DefaultLayoutContent className="!mt-2"
          title={t("howItWorks.title")}
          description={t("howItWorks.subtitle")}
        >
          <div className="relative max-w-[1200px] mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
              {howItWorks.map((step, index) => (
                <div
                  key={index}
                  className="group relative bg-white p-6 rounded-2xl border border-gray-100
                  hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md
                  flex flex-col text-center"
                >
                  {/* Icon and Title */}
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <div className="relative shrink-0">
                      <div className="w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center
                      text-seekers-primary group-hover:scale-110 transition-transform duration-300">
                        {step.icon}
                      </div>
                      <div className="absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full
                      group-hover:blur-2xl transition-all duration-300" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900
                    group-hover:text-seekers-primary transition-colors duration-300">
                      {step.title}
                    </h4>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm leading-relaxed flex-1">
                    {step.description}
                  </p>

                  {/* Hover effect border */}
                  <div className="absolute inset-0 border-2 border-transparent
                  group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300" />
                </div>
              ))}
            </div>
          </div>

          {/* Why Choose Section */}
          <div className="mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto">
            <h3 className="text-lg md:text-xl font-semibold text-seekers-text mb-4">
              {t("howItWorks.whyChoose.title")}
            </h3>
            <p className="text-base text-seekers-text-light">
              {t("howItWorks.whyChoose.description")}
            </p>
          </div>
        </DefaultLayoutContent>
      </MainContentLayout>
    </section>
  );
}
