"use client"
import React from "react";
import { useTranslations } from "next-intl";
import { Star } from "lucide-react";

interface Review {
  name: string;
  location: string;
  rating: number;
  text: string;
  propertyType: string;
}

export default function VerifySocialProof() {
  const t = useTranslations("verify.socialProof");

  const reviews: Review[] = [
    {
      name: t("reviews.review1.name"),
      location: t("reviews.review1.location"),
      rating: t("reviews.review1.rating") as unknown as number,
      text: t("reviews.review1.text"),
      propertyType: t("reviews.review1.propertyType"),
    },
    {
      name: t("reviews.review2.name"),
      location: t("reviews.review2.location"),
      rating: t("reviews.review2.rating") as unknown as number,
      text: t("reviews.review2.text"),
      propertyType: t("reviews.review2.propertyType"),
    },
    {
      name: t("reviews.review3.name"),
      location: t("reviews.review3.location"),
      rating: t("reviews.review3.rating") as unknown as number,
      text: t("reviews.review3.text"),
      propertyType: t("reviews.review3.propertyType"),
    },
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating
            ? "fill-yellow-400 text-yellow-400"
            : "fill-gray-200 text-gray-200"
        }`}
      />
    ));
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t("title")}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t("subtitle")}
          </p>
        </div>

        {/* Reviews Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300"
            >
              {/* Rating */}
              <div className="flex items-center mb-4">
                <div className="flex space-x-1">
                  {renderStars(review.rating)}
                </div>
              </div>

              {/* Review Text */}
              <blockquote className="text-gray-700 mb-6 leading-relaxed">
                "{review.text}"
              </blockquote>

              {/* Property Type */}
              <div className="mb-4">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {review.propertyType}
                </span>
              </div>

              {/* Author */}
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                  {review.name.charAt(0)}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-semibold text-gray-900">
                    {review.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {review.location}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Trust Indicators */}
        <div className="mt-12 text-center">
          <div className="flex flex-wrap justify-center items-center gap-4 sm:gap-6 text-sm text-gray-500">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>{t("trustIndicators.verified")}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>{t("trustIndicators.realClients")}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>{t("trustIndicators.actualInspections")}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
