import { useTranslations } from "next-intl";
import phone from "phone";
import { z } from "zod";

export function useVerifyBookingFormSchema() {
  const t = useTranslations("seeker");
  const formSchema = z.object({
    firstName: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.firstName"),
        }),
      })
      .min(
        2,
        t("form.utility.minimumLength", {
          field: t("form.field.firstName"),
          length: 2,
        })
      ),
    lastName: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.lastName"),
        }),
      })
      .min(
        2,
        t("form.utility.minimumLength", {
          field: t("form.field.lastName"),
          length: 2,
        })
      ),
    email: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.email"),
        }),
      })
      .email({
        message: t("form.utility.invalidFormat", {
          field: t("form.field.email"),
        }),
      }),
    whatsappNumber: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.phoneOrWhatsappNumber"),
        }),
      })
      .refine(
        (value) => {
          const phoneChecker = phone(value);
          return phoneChecker.isValid;
        },
        t("form.utility.invalidFormat", {
          field: t("form.field.phoneOrWhatsappNumber"),
        })
      ),
    villaAddress: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.villaAddress"),
        }),
      })
      .min(
        10,
        t("form.utility.minimumLength", {
          field: t("form.field.villaAddress"),
          length: 10,
        })
      ),
    preferredDate: z
      .string({
        message: t("form.utility.fieldRequired", {
          values: t("form.field.preferredDate"),
        }),
      })
      .date(
        t("form.utility.invalidFormat", {
          field: t("form.field.preferredDate"),
        })
      ),
    tier: z.string({
      message: t("form.utility.fieldRequired", {
        values: t("form.field.tier"),
      }),
    }),
  });
  return formSchema;
}
