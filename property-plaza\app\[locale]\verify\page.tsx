import { Metadata } from "next";
import VerifyHero from "./components/verify-hero";
import VerifyHowItWorks from "./components/verify-how-it-works";
import VerifyPricing from "./components/verify-pricing";
import VerifyBookingForm from "./components/verify-booking-form";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import VerifyPageClient from "./verify-page-client";

import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("verify.seo");

  return {
    title: t("title"),
    description: t("description"),
    keywords: t("keywords"),
    openGraph: {
      title: t("title"),
      description: t("description"),
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: t("title"),
      description: t("description"),
    },
  };
}

export default async function VerifyPage() {
  // Use server-side currency conversion
  const currencyConversion = await getCurrencyConversion();
  const conversions = currencyConversion.data || {
    IDR: 1,
    EUR: 0.000063,
    USD: 0.000067,
    GBP: 0.000053,
    AUD: 0.000099
  };

  return (
    <>
      {/* JSON-LD Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Bali Villa Inspection Service",
            "description": "Professional villa inspection service in Bali. We inspect legal/title red flags, structural issues & hidden costs, then deliver full video + risk report.",
            "provider": {
              "@type": "Organization",
              "name": "Property Plaza",
              "url": "https://property-plaza.com"
            },
            "areaServed": {
              "@type": "Place",
              "name": "Bali, Indonesia"
            },
            "serviceType": "Property Inspection",
            "offers": [
              {
                "@type": "Offer",
                "name": "Basic Villa Inspection",
                "price": "4500000",
                "priceCurrency": "IDR",
                "description": "Video walkthrough, title deed validation, photo checklist, short voice summary"
              },
              {
                "@type": "Offer",
                "name": "Smart Villa Inspection",
                "price": "6000000",
                "priceCurrency": "IDR",
                "description": "Everything in Basic, detailed Risk-Score Report, highlight video, call with legal advisor"
              },
              {
                "@type": "Offer",
                "name": "Full Shield Villa Inspection",
                "price": "8500000",
                "priceCurrency": "IDR",
                "description": "Everything in Smart, contract review, BPN-certified endorsement, urgent booking priority"
              }
            ]
          })
        }}
      />
      <VerifyPageClient conversions={conversions} />
    </>
  );
}
