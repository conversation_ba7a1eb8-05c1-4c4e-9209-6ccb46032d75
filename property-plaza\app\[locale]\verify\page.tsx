import { Metadata } from "next";
import VerifyHero from "./components/verify-hero";
import VerifyHowItWorks from "./components/verify-how-it-works";
import VerifyPricing from "./components/verify-pricing";
import VerifyBookingForm from "./components/verify-booking-form";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import VerifyPageClient from "./verify-page-client";

import { getTranslations } from "next-intl/server";

export async function generateMetadata({ params }: { params: { locale: string } }): Promise<Metadata> {
  const t = await getTranslations("verify.seo");
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://property-plaza.com";
  const verifyUrl = "/verify";

  return {
    title: t("title"),
    description: t("description"),
    keywords: t("keywords"),
    openGraph: {
      title: t("title"),
      description: t("description"),
      type: "website",
      url: `${baseUrl}/${params.locale}${verifyUrl}`,
      images: [
        {
          url: `${baseUrl}/og-verify.png`,
          width: 1200,
          height: 630,
          alt: "Bali Villa Inspection Service - Property Plaza",
        }
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: t("title"),
      description: t("description"),
      images: [`${baseUrl}/og-verify.png`],
    },
    alternates: {
      canonical: `${baseUrl}/${params.locale}${verifyUrl}`,
      languages: {
        "id": `${baseUrl}/id${verifyUrl}`,
        "en": `${baseUrl}/en${verifyUrl}`,
        "x-default": `${baseUrl}${verifyUrl}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export default async function VerifyPage() {
  // Use server-side currency conversion
  const currencyConversion = await getCurrencyConversion();
  const conversions = currencyConversion.data || {
    IDR: 1,
    EUR: 0.000063,
    USD: 0.000067,
    GBP: 0.000053,
    AUD: 0.000099
  };

  return (
    <>
      {/* JSON-LD Schema Markup */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Service",
            "name": "Bali Villa Inspection Service",
            "description": "Professional villa inspection service in Bali. We inspect legal/title red flags, structural issues & hidden costs, then deliver full video + risk report.",
            "provider": {
              "@type": "Organization",
              "name": "Property Plaza",
              "url": "https://property-plaza.com"
            },
            "areaServed": {
              "@type": "Place",
              "name": "Bali, Indonesia"
            },
            "serviceType": "Property Inspection",
            "offers": [
              {
                "@type": "Offer",
                "name": "Basic Package",
                "price": "1900000",
                "priceCurrency": "IDR",
                "description": "Appointment & villa visit, rental agreement draft, landlord identity verification, photos of the property, short voice summary"
              },
              {
                "@type": "Offer",
                "name": "Standard Package",
                "price": "4500000",
                "priceCurrency": "IDR",
                "description": "Includes Basic Package, plus: general property inspection, written inspection report, video walkthrough of the property, call to discuss key findings"
              },
              {
                "@type": "Offer",
                "name": "Premium Package",
                "price": "7000000",
                "priceCurrency": "IDR",
                "description": "Includes Standard Package, plus: ownership verification via BPN (Indonesian Land Registry), long-term rental agreement template, priority booking for inspections"
              }
            ]
          })
        }}
      />
      <VerifyPageClient conversions={conversions} />
    </>
  );
}
