import { Suspense } from "react";
import { useTranslations } from "next-intl";
import { CheckCircle, Calendar, MapPin, Phone, Mail } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";

function SuccessContent() {
  const t = useTranslations("verify.success");

  return (
    <MainContentLayout>
      <div className="min-h-screen py-16 bg-gradient-to-br from-green-50 to-emerald-50">
        <div className="max-w-2xl mx-auto text-center">
          {/* Success Icon */}
          <div className="mb-8">
            <CheckCircle className="w-20 h-20 text-green-500 mx-auto mb-4" />
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t("title")}
            </h1>
            <p className="text-lg text-gray-600">
              {t("subtitle")}
            </p>
          </div>

          {/* Success Details */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8 text-left">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {t("details.title")}
            </h2>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Mail className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">{t("details.confirmation")}</p>
                  <p className="text-sm text-gray-600">{t("details.confirmationDesc")}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Calendar className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">{t("details.scheduling")}</p>
                  <p className="text-sm text-gray-600">{t("details.schedulingDesc")}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Phone className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">{t("details.contact")}</p>
                  <p className="text-sm text-gray-600">{t("details.contactDesc")}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              {t("nextSteps.title")}
            </h3>
            <ul className="text-sm text-blue-800 space-y-2 text-left">
              <li>• {t("nextSteps.step1")}</li>
              <li>• {t("nextSteps.step2")}</li>
              <li>• {t("nextSteps.step3")}</li>
              <li>• {t("nextSteps.step4")}</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild className="bg-seekers-primary hover:bg-seekers-primary/90">
              <Link href="/">
                {t("actions.backHome")}
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/contact">
                {t("actions.contactUs")}
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </MainContentLayout>
  );
}

export default function VerifySuccessPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SuccessContent />
    </Suspense>
  );
}
