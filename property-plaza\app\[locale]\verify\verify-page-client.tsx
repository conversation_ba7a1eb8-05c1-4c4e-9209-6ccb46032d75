"use client"
import React, { useState } from "react";
import VerifyHero from "./components/verify-hero";
import VerifyHowItWorks from "./components/verify-how-it-works";
import VerifyPricing from "./components/verify-pricing";
import VerifyBookingForm from "./components/verify-booking-form";
import VerifySocialProof from "./components/verify-social-proof";
import VerifyDisclaimer from "./components/verify-disclaimer";
import { scrollToBookingForm } from "@/lib/scroll-utils";

interface PricingTier {
  id: string;
  name: string;
  price: number;
  popular?: boolean;
  features: string[];
}

interface VerifyPageClientProps {
  conversions: { [key: string]: number };
}

export default function VerifyPageClient({ conversions }: VerifyPageClientProps) {
  const [selectedTier, setSelectedTier] = useState<PricingTier | undefined>();

  const handleSelectTier = (tier: PricingTier) => {
    setSelectedTier(tier);
    // Scroll to booking form with header offset
    setTimeout(() => {
      scrollToBookingForm();
    }, 100);
  };

  return (
    <main className="min-h-screen">
      <VerifyHero />
      <VerifyHowItWorks />
      <VerifyPricing
        conversions={conversions}
        onSelectTier={handleSelectTier}
      />
      <VerifyBookingForm
        selectedTier={selectedTier}
        conversions={conversions}
      />
      <VerifySocialProof />
      <VerifyDisclaimer />
    </main>
  );
}
