"use client"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";

interface PricingTier {
  id: string;
  name: string;
  price: number; // in IDR
  popular?: boolean;
  features: string[];
}

interface VerifyPricingProps {
  conversions: { [key: string]: number };
  onSelectTier: (tier: PricingTier) => void;
}

export default function VerifyPricing({ conversions, onSelectTier }: VerifyPricingProps) {
  const { currency: currencyStored, isLoading } = useSeekersSettingsStore();
  const [currency, setCurrency] = useState("IDR");
  const locale = useLocale();
  const t = useTranslations("verify");

  const pricingTiers: PricingTier[] = [
    {
      id: "basic",
      name: t("pricing.tiers.basic.name"),
      price: 1900000, // IDR 1,900,000
      features: [
        t("pricing.tiers.basic.features.0"),
        t("pricing.tiers.basic.features.1"),
        t("pricing.tiers.basic.features.2"),
        t("pricing.tiers.basic.features.3"),
        t("pricing.tiers.basic.features.4")
      ]
    },
    {
      id: "standard",
      name: t("pricing.tiers.standard.name"),
      price: 4500000, // IDR 4,500,000
      popular: true,
      features: [
        t("pricing.tiers.standard.features.0"),
        t("pricing.tiers.standard.features.1"),
        t("pricing.tiers.standard.features.2"),
        t("pricing.tiers.standard.features.3"),
        t("pricing.tiers.standard.features.4")
      ]
    },
    {
      id: "premium",
      name: t("pricing.tiers.premium.name"),
      price: 7000000, // IDR 7,000,000
      features: [
        t("pricing.tiers.premium.features.0"),
        t("pricing.tiers.premium.features.1"),
        t("pricing.tiers.premium.features.2"),
        t("pricing.tiers.premium.features.3")
      ]
    }
  ];

  useEffect(() => {
    if (!isLoading && currencyStored) {
      setCurrency(currencyStored);
    }
  }, [currencyStored, isLoading]);

  const formatPrice = (price: number) => {
    const convertedPrice = price * (conversions[currency] || 1);
    return formatCurrency(convertedPrice, currency, locale);
  };

  return (
    <section className="py-16 bg-white" aria-labelledby="pricing-title">
      <MainContentLayout>
        <div className="text-center mb-12">
          <h2 id="pricing-title" className="text-3xl md:text-4xl font-bold text-seekers-text mb-4">
            {t("pricing.title")}
          </h2>
          <p className="text-lg text-seekers-text-light">
            {t("pricing.subtitle")}
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto items-stretch">
          {pricingTiers.map((tier) => (
            <div
              key={tier.id}
              className={`relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg flex flex-col h-full ${
                tier.popular ? 'border-seekers-primary shadow-lg' : 'border-neutral-200'
              }`}
              itemScope
              itemType="https://schema.org/Offer"
            >
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold">
                    {t("pricing.tiers.standard.popular")}
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-seekers-text mb-1" itemProp="name">
                  {tier.name}
                </h3>
                <p className="text-sm text-seekers-text-light mb-3 whitespace-pre-line" itemProp="description">
                  {t(`pricing.tiers.${tier.id}.subtitle`)}
                </p>
                <div className="text-3xl font-bold text-seekers-primary" itemProp="price" content={tier.price.toString()}>
                  {formatPrice(tier.price)}
                  <meta itemProp="priceCurrency" content="IDR" />
                </div>
              </div>

              <div className="flex-grow">
                <ul className="space-y-3">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <span className="text-seekers-primary mt-1">✓</span>
                      <span className="text-seekers-text-light">{feature}</span>
                    </li>
                  ))}
                </ul>

                  <Button
                    onClick={() => onSelectTier(tier)}
                    className={`w-full py-3 font-semibold transition-all duration-200 ${tier.id === 'premium' ? 'mt-9' : 'mt-8'} ${
                      tier.popular
                        ? 'bg-seekers-primary hover:bg-seekers-primary/90 text-white'
                        : 'bg-neutral-100 hover:bg-neutral-200 text-seekers-text border border-neutral-300'
                    }`}
                    variant={tier.popular ? "default" : "outline"}
                  >
                    {t("pricing.cta", { tierName: tier.name })}
                  </Button>

                  {/* Footnote space - always present to maintain consistent height */}
                  <div className="text-xs text-gray-500 italic text-center min-h-[1.5rem] pt-3 flex items-center justify-center">
                    {tier.id === "premium" && t(`pricing.tiers.${tier.id}.footnote`)}
                  </div>
                </div>

              {/* Spacer to push content to bottom */}
              <div className="flex-grow"></div>
            </div>
          ))}
        </div>
      </MainContentLayout>
    </section>
  );
}
