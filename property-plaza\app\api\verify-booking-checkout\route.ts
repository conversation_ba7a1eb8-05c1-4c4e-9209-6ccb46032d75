import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2024-12-18.acacia",
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      firstName, 
      lastName, 
      email, 
      whatsappNumber, 
      villaAddress, 
      preferredDate, 
      tier, 
      recaptchaToken 
    } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !whatsappNumber || !villaAddress || !preferredDate || !tier) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      );
    }

    // Verify reCAPTCHA token
    if (recaptchaToken) {
      const recaptchaResponse = await fetch(
        `https://www.google.com/recaptcha/api/siteverify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${recaptchaToken}`,
        }
      );

      const recaptchaResult = await recaptchaResponse.json();
      
      if (!recaptchaResult.success) {
        return NextResponse.json(
          { error: "reCAPTCHA verification failed" },
          { status: 400 }
        );
      }
    }

    // Define pricing tiers with Stripe Price IDs
    const pricingTiers = {
      basic: {
        priceId: "price_1Rn8usIbbtIeXHA0Kuf0ree5",
        price: 1900000,
        name: "Basic Package"
      },
      standard: {
        priceId: "price_1Rn8wRIbbtIeXHA0pZiDGwsh",
        price: 4500000,
        name: "Standard Package"
      },
      premium: {
        priceId: "price_1Rn8x7IbbtIeXHA0vSaoOEoD",
        price: 7000000,
        name: "Premium Package"
      }
    };

    const selectedTier = pricingTiers[tier as keyof typeof pricingTiers];
    if (!selectedTier) {
      return NextResponse.json(
        { error: "Invalid inspection tier" },
        { status: 400 }
      );
    }

    // Create Stripe checkout session
    const session = await stripe.checkout.sessions.create({
      payment_method_types: ["card"],
      line_items: [
        {
          price: selectedTier.priceId,
          quantity: 1,
        },
      ],
      mode: "payment",
      customer_email: email,
      metadata: {
        firstName,
        lastName,
        email,
        whatsappNumber,
        villaAddress,
        preferredDate,
        tier,
        service_type: "villa_inspection",
      },
      success_url: `${process.env.SUCCESS_RETURN_URL || request.nextUrl.origin}/verify/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.CANCEL_RETURN_URL || request.nextUrl.origin}/verify?cancelled=true`,
      billing_address_collection: "required",
      phone_number_collection: {
        enabled: true,
      },
    });

    // Log the booking for internal tracking
    console.log("Villa Inspection Booking Created:", {
      sessionId: session.id,
      firstName,
      lastName,
      email,
      whatsappNumber,
      villaAddress,
      preferredDate,
      tier,
      amount: selectedTier.price,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json(
      { 
        sessionId: session.id,
        url: session.url,
        message: "Checkout session created successfully"
      },
      { status: 200 }
    );

  } catch (error: any) {
    console.error("Stripe checkout error:", error);
    return NextResponse.json(
      { 
        error: "Failed to create checkout session",
        details: error.message 
      },
      { status: 500 }
    );
  }
}
