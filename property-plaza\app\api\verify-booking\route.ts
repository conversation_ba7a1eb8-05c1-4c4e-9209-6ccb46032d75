import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { whatsappNumber, villaAddress, preferredDate, tier, recaptchaToken } = body;

    // Validate required fields
    if (!whatsappNumber || !villaAddress || !preferredDate || !tier) {
      return NextResponse.json(
        { error: "All fields are required" },
        { status: 400 }
      );
    }

    // Verify reCAPTCHA token
    if (recaptchaToken) {
      const recaptchaResponse = await fetch(
        `https://www.google.com/recaptcha/api/siteverify`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
          body: `secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${recaptchaToken}`,
        }
      );

      const recaptchaResult = await recaptchaResponse.json();
      
      if (!recaptchaResult.success) {
        return NextResponse.json(
          { error: "reCAPTCHA verification failed" },
          { status: 400 }
        );
      }
    }

    // Here you would typically:
    // 1. Save the booking to your database
    // 2. Send notification emails/WhatsApp messages
    // 3. Integrate with your booking system
    
    // For now, we'll just log the booking and return success
    console.log("Villa Inspection Booking:", {
      whatsappNumber,
      villaAddress,
      preferredDate,
      tier,
      timestamp: new Date().toISOString(),
    });

    // You could also send this data to a webhook, email service, or database
    // Example: await sendBookingNotification(body);

    return NextResponse.json(
      { 
        message: "Booking submitted successfully",
        bookingId: `VIB-${Date.now()}` // Generate a simple booking ID
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error processing verify booking:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
