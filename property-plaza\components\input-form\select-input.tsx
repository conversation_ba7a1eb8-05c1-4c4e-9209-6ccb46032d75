"use client"
import { FormControl, FormField } from '@/components/ui/form'
import { FieldValues } from 'react-hook-form'
import { ComponentProps, ReactNode, useState } from 'react'
import { BaseInputForm, BaseSelectInputValue } from '@/types/base'
import BaseInputLayout from './base-input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { cn } from '@/lib/utils'

interface IndividualInputProps<T extends FieldValues> extends BaseInputForm<T> {
  label: string,
  placeholder: string
  description?: string
  selectList?: BaseSelectInputValue<string>[]
  children?: ReactNode
  disabled?: boolean,
  inputContainer?: string,
  containerClassName?: string,
  labelClassName?: string,
  variant?: "default" | "float",
  inputProps?: ComponentProps<"input">,

}

export default function SelectInput<T extends FieldValues>({ form, label, name, placeholder, description, selectList, children, disabled, containerClassName, inputContainer, inputProps, labelClassName, variant }: IndividualInputProps<T>) {
  return <FormField
    control={form.control}
    name={name}
    render={({ field }) => (
      <BaseInputLayout
        label={label}
        description={description}
        labelClassName={cn(variant == "float" ? "absolute -top-2 left-2 px-1 text-xs bg-background z-10" : "", labelClassName)}
        containerClassName={containerClassName}
        variant={variant}
      >
        <Select onValueChange={field.onChange} name={field.name} value={field.value} disabled={field.disabled || disabled} >
          <FormControl>
            <SelectTrigger
              className={cn('border-none focus:outline-none shadow-none focus-visible:ring-0 w-full', variant == "float" ? "px-0" : "", inputProps?.className)}
            >
              <SelectValue placeholder={placeholder}
              />
            </SelectTrigger>
          </FormControl>
          <SelectContent
            // className='z-50' 
            onClick={e => {
              e.stopPropagation()
            }}>
            {
              Array.isArray(selectList) &&
              selectList.map(item => <SelectItem onClick={e => {
                e.stopPropagation()
              }} key={item.id} value={item.value}>
                {item.content}
              </SelectItem>
              )
            }
            {children}
          </SelectContent>
        </Select>
      </BaseInputLayout>

    )}
  />
}