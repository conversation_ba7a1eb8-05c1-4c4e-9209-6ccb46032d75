"use client"
import { motion } from "framer-motion"
import { useEffect, useRef, useState } from "react"
import CurrencyForm from "../locale/currency-form"
import SeekersLocaleForm from "../locale/seekers-locale-form"
import Cookies from "js-cookie"
import { ACCESS_TOKEN } from "@/lib/constanta/constant"
import { useUserStore } from "@/stores/user.store"
import { useLogout } from "@/core/applications/mutations/auth/use-logout"
import { useTranslations } from "next-intl"
import DialogWrapper from "../dialog-wrapper/dialog-wrapper"
import DialogHeaderWrapper from "../dialog-wrapper/dialog-header-wrapper"
import { DialogFooter } from "../ui/dialog"
import { Button } from "../ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../ui/dropdown-menu"
import SeekersProfile from "./seekers-profile"
import SeekerAuthDialog from "@/app/[locale]/(user)/(auth)/seekers-auth-dialog"
import { cn } from "@/lib/utils"
import { favoriteUrl, profileUrl, seekersMessageUrl } from "@/lib/constanta/route"
import { Link } from "@/lib/locale/routing";

export default function SeekersRightNavbar({ localeId = "EN", currency_ = "EUR" }: { localeId?: string, currency_?: string }) {
  const contentRef = useRef<HTMLDivElement | null>(null)
  const languageRef = useRef<HTMLButtonElement | null>(null)
  const currencyRef = useRef<HTMLButtonElement | null>(null)
  const role = useUserStore(state => state.role)
  const [showOption, setShowOption] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const [activeForm, setActiveForm] = useState<'currency' | 'language' | null>(null)

  useEffect(() => {
    const clickOutsideElement = (event: MouseEvent) => {
      const target = event.target as Node

      // Cek apakah klik terjadi di dalam form yang aktif
      const isCurrencyClick = currencyRef.current?.contains(target)
      const isLanguageClick = languageRef.current?.contains(target)

      if (isCurrencyClick) {
        setActiveForm('currency')
        setShowOption(true)
        return
      }

      if (isLanguageClick) {
        setActiveForm('language')
        setShowOption(true)
        return
      }

      // Jika klik di luar kedua form
      if (!contentRef.current?.contains(target)) {
        setShowOption(false)
        setActiveForm(null)
      }
    }

    window.addEventListener("mousedown", clickOutsideElement)
    return () => {
      window.removeEventListener("mousedown", clickOutsideElement)
    }
  }, [])
  useEffect(() => {
    const handleScroll = () => {
      if (scrollY === window.scrollY - 4) return
      setScrollY(window.scrollY)
      setShowOption(false)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [scrollY, setShowOption])
  return <div ref={contentRef}
    className="flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]"
  >
    <div className="w-fit">
      <motion.div
        className="overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex"
        initial={{ width: "110px" }}
        animate={{ width: showOption ? "166px" : "112px" }}
        transition={{ duration: 0.1 }}
      >
        <div className="flex items-center justify-center gap-2 py-2 w-full"
        >
          <CurrencyForm
            triggerClassName={cn("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0", showOption ? "w-full" : "pl-3 max-w-[48px]")}
            defaultCurrency={currency_}
            ref={currencyRef}
            onClick={(e) => {
              e.stopPropagation()
              setActiveForm('currency')
              setShowOption(true)
            }}
            showCaret={showOption && activeForm === 'currency'}
          />
          <div className="w-[2px] h-[24px] bg-seekers-text-lighter"></div>
          <SeekersLocaleForm
            triggerClassName={cn(
              "rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",
              showOption ? "w-full" : "pl-2 max-w-[32px]"
            )}
            defaultValue={localeId}
            ref={languageRef}
            onClick={(e) => {
              e.stopPropagation()
              setActiveForm('language')
              setShowOption(true)
            }}
            showCaret={showOption && activeForm === 'language'}
          />
        </div>
      </motion.div>
    </div>
    <>
      {(Cookies.get(ACCESS_TOKEN) && role == "SEEKER") ? (
        <ProfileDropdown
          trigger={
            <button
              className={`border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10`}
            >
              <SeekersProfile url={""} />
            </button>
          }
        />
      ) : (
        <div
        >
          <SeekerAuthDialog triggerClassName={cn("!w-10 rounded-full overflow-hidden")} />
        </div>
      )}
    </>
  </div>
}


function ProfileDropdown({ trigger }: { trigger: React.ReactNode }) {
  const t = useTranslations("seeker")
  const handleOpenLogoutDialog = () => {
    const button = document.getElementById("open-logout-dialog")
    button?.click()
  }
  return <>
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="!w-[256px]">
        <DropdownMenuItem asChild>
          <Link href={profileUrl}>
            {t('accountAndProfile.profile')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link href={favoriteUrl}>
            {t('accountAndProfile.favorite')}
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem className="w-full" asChild>
          <Link href={seekersMessageUrl}>
            <div className="flex justify-between items-center w-full ">
              {t('accountAndProfile.message')}
              {/* TODO:
                Make this works as when we working on Messaging page
              */}
              {/* <div className="text-xs aspect-square w-6 rounded-full bg-seekers-primary p-1 text-white items-center justify-center text-center">
                2
              </div> */}
            </div>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={e => {
          e.preventDefault()
          handleOpenLogoutDialog()
        }}>{t('accountAndProfile.logout.title')}</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
    <LogoutDialog
      trigger={<button id="open-logout-dialog"></button>
      }
    />
  </>
}

function LogoutDialog({ trigger }: { trigger: React.ReactNode }) {
  const [open, setOpen] = useState(false)
  const logoutQuery = useLogout("seekers")
  const t = useTranslations('seeker')
  const handleLogout = () => {
    if (!Cookies.get(ACCESS_TOKEN)) {
      window.location.assign('')
      return
    } else {
      logoutQuery.mutate()
    }
  }
  return <DialogWrapper
    open={open}
    setOpen={setOpen}
    openTrigger={
      trigger
    }
    dialogClassName="max-w-md"
  >
    <DialogHeaderWrapper className="text-start px-0">
      <h2 className="max-sm:text-center font-semibold">
        {t('accountAndProfile.logout.title')}
      </h2>
      <p className="max-sm:text-center max-sm:mb-4">{t('owner.accountAndProfile.logout.description')}</p>
    </DialogHeaderWrapper>
    <DialogFooter>
      <Button
        variant={"default-seekers"}
        loading={logoutQuery.isPending}
        className="min-w-20 max-sm:order-last"
        onClick={() => setOpen(false)}
      >
        {t("cta.cancel")}
      </Button>
      <Button
        variant={"ghost"}
        onClick={handleLogout}
        loading={logoutQuery.isPending}
        className="min-w-20"
      >
        {t("cta.logout")}
      </Button>
    </DialogFooter>
  </DialogWrapper>
}