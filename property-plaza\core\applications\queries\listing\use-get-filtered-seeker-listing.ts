import { GetFilteredSeekersListingDto } from "@/core/infrastructures/listing/dto";
import { getFilteredSeekersListingService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";
import { useGetFilterParameter } from "./use-get-filter-parameters";
import { listingCategory } from "@/core/domain/listing/listing-seekers";
import _ from "lodash";
import { minZoom } from "@/core/domain/users/user";

export const FILTERED_SEEKERS_LISTING_QUERY_KEY = "filtered-seekers-listing";
export function useGetFilteredSeekersListing(
  data: GetFilteredSeekersListingDto,
  isEnabled: boolean = false,
  locale = "en"
) {
  const filterParameterQuery = useGetFilterParameter();
  const filterParameter = filterParameterQuery?.data?.data;
  const filterQueryKey = [FILTERED_SEEKERS_LISTING_QUERY_KEY, data];
  const { failureCount, ...query } = useQuery({
    queryKey: filterQueryKey,
    queryFn: async () => {
      const maxPriceFilter = data.max_price || filterParameter?.priceRange.max;
      const minPriceFilter =
        data.min_price || filterParameter?.priceRange.min || 1;

      const maxBuildingSizeFilter =
        data.building_largest || filterParameter?.buildingSizeRange.max;
      const minBuildingSizeFilter =
        data.building_smallest || filterParameter?.buildingSizeRange.min || 1;

      const maxLandSizeFilter =
        data.land_largest || filterParameter?.landSizeRange.max;
      const minLandSizeFilter =
        data.land_smallest || filterParameter?.landSizeRange.min || 1;

      const maxGardenSizeFilter =
        data.garden_largest || filterParameter?.gardenSizeRange.max;
      const minGardenSizeFilter =
        data.garden_smallest || filterParameter?.gardenSizeRange.min || 1;

      let area = data.area;
      if (data.area?.zoom == minZoom.toString()) {
        area = undefined;
      }
      const typeProperty = data.type?.includes("all")
        ? undefined
        : _.uniq(
            data.type?.flatMap((item) => {
              if (item !== listingCategory.commercialSpace) return item;
              return [
                listingCategory.cafeOrRestaurants,
                listingCategory.shops,
                listingCategory.offices,
              ];
            })
          );

      const request: GetFilteredSeekersListingDto = {
        ...data,
        type: typeProperty,
        search:
          data.search == "all"
            ? undefined
            : data.search?.replaceAll(" , ", ", "),
        min_price: minPriceFilter,
        max_price: maxPriceFilter,
        building_largest: maxBuildingSizeFilter,
        building_smallest: minBuildingSizeFilter,
        land_largest: maxLandSizeFilter,
        land_smallest: minLandSizeFilter,
        garden_largest: maxGardenSizeFilter,
        garden_smallest: minGardenSizeFilter,
        area: area || undefined,
        property_of_view: data.property_of_view,
      };
      if (
        (!data.min_price ||
          data.min_price == filterParameter?.priceRange.min) &&
        maxPriceFilter == filterParameter?.priceRange.max
      ) {
        request.max_price = undefined;
        request.min_price = undefined;
      }
      if (
        (!data.building_smallest ||
          data.building_smallest == filterParameter?.buildingSizeRange.min) &&
        maxBuildingSizeFilter == filterParameter?.buildingSizeRange.max
      ) {
        request.building_largest = undefined;
        request.building_smallest = undefined;
      }
      if (
        (!data.land_smallest ||
          data.land_smallest == filterParameter?.landSizeRange.min) &&
        maxLandSizeFilter == filterParameter?.landSizeRange.max
      ) {
        request.land_largest = undefined;
        request.land_smallest = undefined;
      }
      if (
        (!data.garden_smallest ||
          data.garden_smallest == filterParameter?.gardenSizeRange.min) &&
        maxGardenSizeFilter == filterParameter?.gardenSizeRange.max
      ) {
        request.garden_largest = undefined;
        request.garden_smallest = undefined;
      }
      const response = await getFilteredSeekersListingService(request,locale);

      return response;
    },
    enabled: isEnabled,
    retry: false,
  });
  return { query, filterQueryKey };
}
