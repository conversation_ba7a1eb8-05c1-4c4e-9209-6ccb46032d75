import { getDetailListingSeekersService } from "@/core/infrastructures/listing/service";
import { useQuery } from "@tanstack/react-query";

export const LISTING_DETAIL_SEEKERS_QUERY_KEY = "detail-listing-seekers";
export function useGettListingDetailSeekers(id: string, locale = "en") {
  const query = useQuery({
    queryKey: [LISTING_DETAIL_SEEKERS_QUERY_KEY, id],
    queryFn: async () => {
      const data = await getDetailListingSeekersService(id, locale);
      return data;
    },
    retry: 0,
    refetchOnWindowFocus: false,
  });
  return query;
}
