import { getChatRoomDetailService } from "@/core/infrastructures/messages/services";
import { useQuery } from "@tanstack/react-query";

export const CHAT_DETAIL = "chat-detail";
export default function useGetChatDetail(
  id: string,
  isEnable: boolean = true,
  locale = "en"
) {
  const query = useQuery({
    queryKey: [CHAT_DETAIL, id],
    queryFn: () => getChatRoomDetailService(id, locale),
    retry: 0,
    enabled: isEnable,
  });
  return query;
}
