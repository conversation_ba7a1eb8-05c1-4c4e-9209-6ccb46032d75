import { GetChatsDto } from "@/core/infrastructures/messages/dto";
import { getChatListService } from "@/core/infrastructures/messages/services";
import { useQuery } from "@tanstack/react-query";

export const CHAT_LIST = "chat-list";
export default function useGetChatList(data: GetChatsDto, locale = "en") {
  const { search, status } = data;
  console.log(locale);

  const query = useQuery({
    queryKey: [CHAT_LIST, search, status, locale],
    queryFn: async () => {
      const searchData = {
        search: search || "",
      };
      return await getChatListService(searchData, locale);
    },
    retry: 0,
  });
  return query;
}
