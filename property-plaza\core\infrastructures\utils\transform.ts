import { BaseMeta } from "@/core/domain/utils/utils";
import { BaseMetaDto, ItemWithMultipleLanguageDto } from "./dto";

export function transformMeta(dto: BaseMetaDto): BaseMeta {
  const meta: BaseMeta = {
    nextPage: dto.next_page,
    page: dto.page,
    pageCount: dto.page_count,
    perPage: dto.per_page,
    prevPage: dto.prev_page,
    total: dto.total,
  };
  return meta;
}

export function ValueBasedOnLocale(
  values: ItemWithMultipleLanguageDto[],
  locale = "en"
) {
  if (!values) return "";
  if (typeof values == "string") return values;
  const selectedItem = values.find((item) => item.lang === locale);

  return selectedItem?.value || values[0].value;
}
