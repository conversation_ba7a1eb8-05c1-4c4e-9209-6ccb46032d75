import { NavigationMenu } from "@/types/layout";
import {
  ArrowDownUp,
  LayoutGrid,
  MessagesSquare,
} from "lucide-react";

export const baseUrl = "/";
export const loginUrl = "/owner/login";
export const signUpUrl = "/owner/sign-up";
export const userLoginUrl = "/login"
export const userSignUpUrl = "/sign-up"
export const otpUrl = "/owner/otp"
export const resetPassword = "/reset-password"

export const accountUrl = "/owner/account";
export const listingsUrl = "/owner/listing";
export const messagesUrl = "/owner/messages";
export const transactionUrl = "/owner/transactions";
export const joinOwnerUrl = "/join";
export const onboardingOwner = "/owner/onboarding";
export const successPaymentCreditUrl = "/owner/success-credit-payment"
export const failedPaymentCreditUrl = "/owner/failed-credit-payment"

export const checkOutUrl = "/checkout"
export const faqOwnerUrl = "/join/faq"
export const pricingOwnerUrl = "/join/pricing"
export const termOwnerUrl = "/join/terms"
export const privacyOwnerUrl = "/join/privacy-policy"

export const assetsUrl = "/assets";
export const userhomepageUrl = "/"
export const profileUrl = "/profile";
export const searchUrl = "/s";
export const favoriteUrl = "/favorites"
export const seekersMessageUrl = "/message"
export const plansUrl = "/subscription"
export const noLoginPlanUrl = "/plan"
export const billingUrl = "/billing"
export const notificationSettingUrl = "/notification"
export const securitySettingUrl = "/security"
export const privacySeekerUrl = "/privacy-policy"
export const termSeekerUrl = "/terms-of-use"
export const contactUsUrl = "/contact-us"
export const userDataDeletionUrl = "/user-data-deletion"
export const aboutUsUrl = "/about-us"
export const verifyUrl = "/verify"
export const accountMiddlemanUrl = "/representative/account";
export const messageMiddlemanUrl = "/representative/messages"
export const listingsMiddlemanUrl = "/representative/listing";
export const postUrl = "/posts"


export const BASE_ADMIN_ROUTE = [
  joinOwnerUrl,
  accountUrl,
  listingsUrl,
  messagesUrl,
  transactionUrl,
  signUpUrl,
  loginUrl,
  onboardingOwner,
  userLoginUrl,
  userSignUpUrl,
];
export const BASE_MIDDLEMAN_ROUTE = [
  accountMiddlemanUrl,
  messageMiddlemanUrl,
  listingsMiddlemanUrl
]

export const AUTHENTICATED_USER_ROUTE = [
  securitySettingUrl,
  profileUrl,
  messagesUrl,
  favoriteUrl,
  billingUrl,
  seekersMessageUrl
]
export const BASE_USER_ROUTE = [
  profileUrl,
  searchUrl,
  securitySettingUrl,
  notificationSettingUrl,
  billingUrl,
  plansUrl,
  seekersMessageUrl,
  favoriteUrl,
  resetPassword
];

const listingSubMenu: NavigationMenu = {
  id: "listing",
  icon: <LayoutGrid strokeWidth={1.5} />,
  name: "Listing",
  link: listingsUrl,
  localeKey: "owner.sidebar.listing",
};
const messagesSubMenu: NavigationMenu = {
  id: "message",
  icon: <MessagesSquare strokeWidth={1.5} />,
  name: "Message",
  link: messagesUrl,
  localeKey: "owner.sidebar.message",
};
const balanceAndTransactionSubMenu: NavigationMenu = {
  id: "balanaceAndTransaction",
  icon: <ArrowDownUp strokeWidth={1.5} />,
  name: "Balance & Transaction",
  link: transactionUrl,
  localeKey: "owner.sidebar.balance",
};


const listingRepresentativeSubMenu: NavigationMenu = {
  id: "listing",
  icon: <LayoutGrid strokeWidth={1.5} />,
  name: "Listing",
  link: listingsMiddlemanUrl,
  localeKey: "owner.sidebar.listing",
};
const messagesRepresentativeSubMenu: NavigationMenu = {
  id: "message",
  icon: <MessagesSquare strokeWidth={1.5} />,
  name: "Message",
  link: messageMiddlemanUrl,
  localeKey: "owner.sidebar.message",
};

export const OWNER_MENU: NavigationMenu[] = [
  listingSubMenu,
  messagesSubMenu,
  balanceAndTransactionSubMenu,
];

export const REPRESENTATIVE_MENU: NavigationMenu[] = [
  listingRepresentativeSubMenu,
  messagesRepresentativeSubMenu
]


export const NEED_AUTHENTICATED_PAGE = [
  accountUrl,
  listingsUrl,
  messagesUrl,
  transactionUrl,
  onboardingOwner,
  assetsUrl,
  profileUrl,
  checkOutUrl,
  seekersMessageUrl,
  billingUrl,
  favoriteUrl,
  securitySettingUrl,
  notificationSettingUrl,
  plansUrl
]

