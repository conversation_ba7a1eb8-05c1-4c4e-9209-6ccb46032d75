import {defineRouting} from 'next-intl/routing';
import {createNavigation} from 'next-intl/navigation';
import { locales } from './i18n-config';
 
export const routing = {
  locales: ['en', 'id', 'nl'],
  defaultLocale: 'en'
};
 
// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const {Link, redirect, usePathname, useRouter} =
  createNavigation(routing);