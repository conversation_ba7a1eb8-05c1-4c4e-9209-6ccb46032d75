/**
 * Scroll to an element with header offset compensation
 * @param elementId - The ID of the element to scroll to
 * @param headerHeight - Height of the sticky header (default: 80px)
 * @param additionalOffset - Additional offset if needed (default: 20px for padding)
 */
export function scrollToElementWithOffset(
  elementId: string, 
  headerHeight: number = 80, 
  additionalOffset: number = 20
) {
  const element = document.getElementById(elementId);
  if (element) {
    const elementPosition = element.offsetTop - headerHeight - additionalOffset;
    window.scrollTo({
      top: Math.max(0, elementPosition), // Ensure we don't scroll to negative position
      behavior: 'smooth'
    });
  }
}

/**
 * Scroll to booking form with proper header offset
 */
export function scrollToBookingForm() {
  scrollToElementWithOffset('booking-title', 80, 20);
}
