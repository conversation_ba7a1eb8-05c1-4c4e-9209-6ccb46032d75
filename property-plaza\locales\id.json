{"component": {"dataTable": {"filterData": "Filter data", "viewTableOptions": "Tampilan", "toggleColumns": "Atur kolom", "pagination": {"rowPerPage": "Data per halaman", "goToFIrstPage": "Hal<PERSON> pertama", "goToPreviousPage": "Halaman sebelumnya", "goToNextPage": "Halaman berik<PERSON>", "goToLastPage": "<PERSON><PERSON> te<PERSON>"}, "dateRangeFIlter": {"placeholder": "<PERSON><PERSON><PERSON> tanggal"}, "page": "<PERSON>"}, "cta": {"seeOriginal": "<PERSON><PERSON>", "seeTranslation": "<PERSON><PERSON>"}}, "conjuntion": {"or": "atau", "and": "dan", "of": "dari", "for": "untuk"}, "appName": "Property Plaza", "seeker": {"banner": {"seekers": {"discoverDreamHome": {"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, atau Tanah di Bali"}, "connectToPropertyOwner": {"title": "Terhubung Langsung dengan Pemilik Properti Terverifikasi"}}}, "seekersLandingPage": {"seo": {"tabs": {"optionOne": {"title": "Properti Dijual di Bali", "content": {"one": {"title": "Villa Mewah", "description": "Hunian Premium di Bali"}, "two": {"title": "<PERSON><PERSON><PERSON>", "description": "Properti Bali Harga Terjangkau"}, "three": {"title": "Villa dengan Pemandangan Laut", "description": "Properti Tepi Pantai yang Menakjubkan"}, "four": {"title": "Villa dengan Pemandangan Gunung", "description": "<PERSON><PERSON><PERSON>gan <PERSON>"}, "five": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>"}, "six": {"title": "Villa dengan pemandangan Alam", "description": "Tempat peristirahatan di Hutan Tropis"}, "seven": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> dan nyaman untuk peliharaan"}, "eight": {"title": "<PERSON><PERSON><PERSON> dengan F<PERSON>", "description": "<PERSON><PERSON> luar ruangan yang indah"}, "nine": {"title": "<PERSON><PERSON> yang <PERSON>", "description": "<PERSON><PERSON> rumah impian <PERSON>"}, "ten": {"title": "Properti Tepi Pantai", "description": "<PERSON><PERSON><PERSON> langsung ke pantai"}, "eleven": {"title": "<PERSON><PERSON><PERSON>gan <PERSON>", "description": "Siap untuk dihuni"}, "twelve": {"title": "Villa dengan Taman Pribadi", "description": "Taman tropis yang rimbun"}, "thirteen": {"title": "<PERSON><PERSON><PERSON>", "description": "Pemandangan langit yang indah"}, "fourteen": {"title": "Properti untuk Bisnis", "description": "Peluang investasi properti komersial"}, "fifteen": {"title": "<PERSON><PERSON>", "description": "Unit komersial yang ringkas"}, "sixteen": {"title": "<PERSON><PERSON>", "description": "Lingkungan bisnis yang profesional"}, "seventeen": {"title": "Unit Komersial Ukuran Besar", "description": "<PERSON><PERSON> bisnis yang luas"}, "eighteen": {"title": "<PERSON><PERSON><PERSON> yang <PERSON>", "description": "Properti yang baru diperbarui"}, "nineteen": {"title": "Villa dengan waktu sewa fleksibel", "description": "Opsi penyewaan yang fleksibel"}, "twenty": {"title": "<PERSON><PERSON><PERSON> dengan fasilitas Air Kota", "description": "Pasokan air yang dapat diandalkan"}, "twentyOne": {"title": "Villa yang Baru <PERSON>", "description": "Konstruksi modern yang kekinian"}, "twentyTwo": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> kamar mandi mewah"}, "twentyThree": {"title": "Tempat Parkir Pribadi", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> kendaraan yang aman"}, "twentyFour": {"title": "<PERSON><PERSON><PERSON> dengan fasilitas <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> renang p<PERSON>i"}}}, "optionTwo": {"title": "Sewa Jangka Pendek untuk Liburan", "content": {"one": {"title": "<PERSON><PERSON>", "description": "Tempat Tinggal Ramah Biaya"}, "two": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dalam perse<PERSON>i", "description": "Dapatkan keuntungan maksimal"}, "three": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pengoperasian tanpa kerumitan."}, "four": {"title": "<PERSON><PERSON><PERSON>", "description": "Penginapan liburan premium."}, "five": {"title": "Penyewaan Tepi Pantai", "description": "Tinggallah di tepi pantai"}, "six": {"title": "Villa Jangka Panjang", "description": "Menginap di vila jangka Panjang"}, "seven": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> sewa ta<PERSON>an"}, "eight": {"title": "Studio yang Terjangkau", "description": "<PERSON><PERSON><PERSON> dengan anggaran yang ringkas"}, "nine": {"title": "<PERSON><PERSON><PERSON> den<PERSON>", "description": "Lingkungan hijau yang damai"}, "ten": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> dengan pemandangan langit"}, "eleven": {"title": "<PERSON><PERSON><PERSON><PERSON> dengan <PERSON>", "description": "Pemandangan pedesaan yang indah"}, "twelve": {"title": "Villa Keluarga Besar", "description": "<PERSON><PERSON><PERSON> keluarga yang luas"}, "thirteen": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hidup yang terjangkau"}, "fourteen": {"title": "Fasilitas <PERSON>", "description": "<PERSON><PERSON><PERSON> kolam renang eksklus<PERSON>"}, "fifteen": {"title": "Tersedia Tempat Parkir", "description": "Tersedia tempat parkir yang aman"}, "sixteen": {"title": "Sewa Fleksibel", "description": "Ketentuan sewa yang fleksibel"}}}, "optionThree": {"title": "Nomaden Digital", "content": {"one": {"title": "Penyewaan dengan wifi", "description": "Akses internet cepat"}, "two": {"title": "Coworking Terdekat", "description": "<PERSON><PERSON> kerja yang tersedia"}, "three": {"title": "Villa untuk kerja jarak jauh", "description": "<PERSON><PERSON><PERSON> dengan suasana indah"}, "four": {"title": "Studio Digital Nomad", "description": "<PERSON><PERSON> yang terhu<PERSON>ng"}, "five": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ruang kerja yang fleksibel"}, "six": {"title": "<PERSON><PERSON> Ker<PERSON> di Tepi Pantai", "description": "Lingkungan kerja dengan pemandangan laut"}, "seven": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> kerja yang terinspirasi dari alam"}, "eight": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Area kerja khusus"}, "nine": {"title": "<PERSON><PERSON>", "description": "Pengalaman kerja yang lebih tinggi"}, "ten": {"title": "<PERSON><PERSON><PERSON> un<PERSON>", "description": "Siap dengan gaya hidup digital"}, "eleven": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> yang berfokus pada komunitas"}, "twelve": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>uang produktif bersih"}, "thirteen": {"title": "<PERSON><PERSON>", "description": "Lingkungan kerja yang profesional"}, "fourteen": {"title": "Penyewaan Studio Kreatif", "description": "<PERSON><PERSON> k<PERSON>ti<PERSON> yang <PERSON>"}, "fifteen": {"title": "Temapt Tinggal Jangka Panjang untuk Nomaden", "description": "<PERSON><PERSON><PERSON><PERSON> nomaden jangka panjang"}, "sixteen": {"title": "Tempat Ker<PERSON> ya<PERSON>", "description": "<PERSON><PERSON><PERSON> l<PERSON>n kerja yang <PERSON>"}}}, "optionFour": {"title": "Pengembangan Off-Plan", "content": {"one": {"title": "Perkembangan Ma<PERSON>", "description": "Proyek properti mendatang"}, "two": {"title": "Properti Investasi", "description": "<PERSON><PERSON>uang investasi strategis"}, "three": {"title": "Villa Pra-Konstruksi", "description": "<PERSON><PERSON><PERSON> investasi awal"}, "four": {"title": "<PERSON><PERSON><PERSON> Off-Plan", "description": "Hunian masa depan yang premium"}, "five": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> hidup masa depan"}, "six": {"title": "Off-Plan di Tepi Pantai", "description": "<PERSON><PERSON><PERSON> masa depan di tepi laut"}, "seven": {"title": "Perkembangan Mountain View", "description": "Retret gunung yang akan datang"}, "eight": {"title": "Pengembangan Pemandangan Sawah", "description": "<PERSON><PERSON><PERSON> pedesaan masa depan"}, "nine": {"title": "Hutan Off-Plan", "description": "Liburan tropis di masa depan"}, "ten": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Pengembangan kehidupan yang ramah lingkungan"}, "eleven": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> berteknologi masa depan"}, "twelve": {"title": "Pengembangan Teras Atap", "description": "Properti dengan pemandangan langit"}, "thirteen": {"title": "Bisnis Off-Plan", "description": "<PERSON><PERSON> komersial masa depan"}, "fourteen": {"title": "Proyek Berkelanjutan", "description": "<PERSON><PERSON><PERSON> masa depan ramah ling<PERSON>ngan"}, "fifteen": {"title": "Pengembangan Bertingkat Tinggi", "description": "<PERSON><PERSON><PERSON><PERSON> vertikal masa depan"}, "sixteen": {"title": "Pengembangan yang <PERSON>", "description": "Properti masa depan yang fleksibel"}}}}, "title": "Temukan Semua Jenis Real Estate"}}, "subscription": {"upgradeSubscription": {"title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Le<PERSON>", "description": "Buka foto daftar tambahan, perbesar lokasi tepa<PERSON>, dan ng<PERSON><PERSON> langsung dengan pemilik. \n<PERSON><PERSON><PERSON> properti impian Anda dengan lebih cepat dan mudah!"}, "benefit": {"weeklyEmailUpdate": "Pembaruan Email <PERSON>", "realtimeNotification": "Notifikasi Realtime", "priceHistory": {"lastThreeMonth": "3 <PERSON><PERSON><PERSON>", "fullPriceHistory": "<PERSON><PERSON><PERSON><PERSON>"}, "virtualTour": {"limitedAccess": "<PERSON><PERSON><PERSON>", "fullAccess": "<PERSON><PERSON><PERSON>"}, "neightborInsight": {"detail": "Detail Data Lingkungan", "marketTrendAndPrediction": "<PERSON>ren dan <PERSON><PERSON><PERSON><PERSON>"}, "comparisonTool": {"compareUpToThreeProperty": "Bandingkan Hingga 3 Properti", "compareUnlimitedProperty": "Bandingkan Properti Tanpa Batas"}, "expertConsultant": {"email": "Konsultasi Email", "realtime": "Konsultan Realtime"}, "fivePerWeeks": "5 pemilik baru/minggu", "fifteenPerWeeks": "15 Kali per Minggu"}, "downgrade": {"title": "<PERSON><PERSON> ke paket {package} ", "description": "<PERSON><PERSON> tinjau per<PERSON>han yang akan terjadi saat menurunkan paket dari {currentPackage} ke {downgradePackageName}", "content": {"title": "<PERSON><PERSON>, <PERSON><PERSON> akan kehilangan akses ke fitur -fitur ini:", "downgradeEffectiveDate": "Downgrade <PERSON>a akan mulai berlaku pada {effectedDate}, satu hari sebelum tanggal penagihan Anda berikut<PERSON> {nextBillingDate}", "optionSeven": "<PERSON><PERSON><PERSON> ke OFF - Daftar dan <PERSON><PERSON> (akan terbatas)", "optionSix": "Perbandingan Properti Tidak Terbatas (akan dibatasi hingga 3)", "optionOne": "Daftar Si<PERSON> (akan dibatasi hingga 20)", "optionTwo": "Pemberitahuan waktu nyata (akan be<PERSON>h menjadi pembaruan email mingguan)", "optionThree": "Riwayat <PERSON> (akan dibatasi hingga 3 bulan terakhir)", "optionFour": "<PERSON><PERSON><PERSON> tur <PERSON> penuh (akan terbatas)", "optionFive": "Tren dan prediksi pasar (akan diganti dengan data lingkungan terperinci)"}}, "cancel": {"content": {"optionOne": "Semua fitur premium dari rencana Anda saat ini", "optionTwo": "Aks<PERSON> ke daftar yang disimpan", "optionFour": "<PERSON><PERSON><PERSON> dan <PERSON>", "optionFive": "<PERSON><PERSON><PERSON>", "optionSix": "Alat perbandingan properti", "optionSeven": "<PERSON>ks<PERSON> ke daftar dan transaksi di luar pasar"}, "title": "Batalkan langganan", "description": "<PERSON><PERSON> tinjau konsekuensi dari membatalkan langganan anda"}, "signUp": {"title": "Masukkan Data Anda", "description": "Informasi ini akan kami gunakan untuk konfirmasi dan notifikasi pesanan"}}, "popup": {"followInstagram": {"title": "Mari terhubung", "description": "<PERSON><PERSON><PERSON> kami untuk mengetahui suasana <PERSON>, tips orang dalam, dan kabar terbaru eksklusif!"}}, "blog": {"sectionTitle": "Panduan utama Anda untuk menemukan dan berinvestasi di properti Bali", "content": {"optionOne": {"title": "Masa Depan Investasi Real Estate"}, "optionTwo": {"title": "5 Tips untuk Membeli Properti Pertama Kali"}, "optionThree": {"title": "10 Kota Teratas untuk Investasi Real Estate pada tahun 2024"}, "optionFour": {"title": "Bagaimana Mempersiapkan Rumah Anda untuk Dijual"}}, "moreArticles": {"title": "<PERSON><PERSON><PERSON>"}, "author": {"defaultName": "Property Plaza"}, "badge": {"title": "Blog"}}, "transaction": {"dataTable": {"amount": "Biaya", "transactionDate": "Tanggal", "transactionId": "ID transaksi", "invoiceNumber": "<PERSON><PERSON> faktur", "nextBillingDate": "<PERSON><PERSON><PERSON>", "plan": "Tipe", "action": "<PERSON><PERSON><PERSON>", "noResult": "Tidak ada riwayat transaksi", "invoiceNumber ": "<PERSON><PERSON> faktur", "transactionDate ": "Tanggal"}, "seekers": {"dataTable": {"download": "<PERSON><PERSON><PERSON>"}}}, "misc": {"subscribeEmail": {"title": "Berlangganan untuk informasi terupdate"}, "page": "<PERSON>", "faqNotFound": "Tidak dapat menemukan pertanyaan terkait", "enableSoundNotification": {"title": "Aktifkan notifikasi suara?", "description": "<PERSON><PERSON> menyar<PERSON> Anda untuk mengaktifkan notifikasi suara untuk melakukan tindakan segera"}, "today": "<PERSON> ini", "messageTooShort": {"chatWithCs": {}}, "priceCantBeZero": "Harga tidak boleh 0", "successUpdateListingPrice": "<PERSON><PERSON><PERSON><PERSON> men<PERSON> harga properti", "loading": "Memuat ...", "noResult": "Tidak ada data", "noImageUploaded": "Tidak ada gambar yang <PERSON>h", "preparingBuyCredit": "Mempersiapkan pembelian kredit...", "foundError": "<PERSON>s, kami menemukan kes<PERSON>han", "failedUpdatingPrice": "Terda<PERSON>t masalah saat memperbarui harga", "meter": "<PERSON>er", "recentSearch": "<PERSON><PERSON><PERSON>", "hide": "Sembunyikan", "ascendingOrder": "A-Z", "descendingOrder": "Z-A", "verificationAlreadySent": "Kode verifikasimu sudah terkirim", "popularProperty": "Properti populer", "popularSearch": "Pencarian populer", "allType": "<PERSON><PERSON><PERSON> tipe", "startFrom": "<PERSON><PERSON> dari", "currency": "<PERSON>", "language": "Bahasa", "minimum": "Minimum", "home": "Home", "more": "selengkapnya", "allProperty": "<PERSON><PERSON><PERSON>", "suggestion": "Saran lokasi", "comparisonType": {"lessThan": "<PERSON><PERSON> dari", "moreThan": "<PERSON><PERSON><PERSON> dari"}, "copy": {"successCopyContent": "<PERSON><PERSON><PERSON><PERSON> {konten} ke papan klip"}, "month": "{count, plural, satu {bulan} other {bulan}}", "year": "{count, plural, one {year} other {years}}", "mapLocation": "<PERSON><PERSON>", "userNotFound": "Pengguna tidak ditemukan", "ownerProperty": "<PERSON><PERSON><PERSON><PERSON>", "error": {"tooManyRequest": {"title": "Terlalu banyak permintaan", "description": "Tidak dapat meminta properti, silakan coba lagi nanti"}, "propertyNotFound": {"title": "<PERSON>et kredit tidak ditemukan", "description": "Tidak dapat menemukan properti yang diminta"}, "packageNotFound": {"description": "Tidak dapat menemukan paket kredit yang diminta, silakan gunakan paket lain"}, "seekers": {"propertyNotFound": {"title": "Ups, ada sesuatu yang salah"}}}, "subscibePropgram": {"searchPage": {"description": "<PERSON><PERSON><PERSON><PERSON>an untuk melakukan zoom lebih banyak, melihat lebih banyak foto dari properti, dan masih banyak lagi."}, "detailPage": {"description": "<PERSON><PERSON> kunci akses penuh"}, "favorite": {"title": "Simpan favorit <PERSON><PERSON>!", "description": "<PERSON><PERSON><PERSON><PERSON>an sekarang untuk menyimpan dan meninjau kembali properti favorit Anda kapan saja! "}}, "status": "Status:", "continueWith": "Lanjutkan", "primary": "<PERSON><PERSON><PERSON>", "vat": "VAT", "expires": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "perMonth": "/bulan", "yourCurrentPlan": "Rencana saat ini", "upgradeTo": "Tingkatkan ke {plan}", "downgradeTo": "<PERSON><PERSON><PERSON> ke {plan}", "free": "<PERSON><PERSON><PERSON>", "region": "Wilayah", "areas": "Area", "findYourPerfectProperty": "<PERSON><PERSON><PERSON>ti idaman <PERSON>", "notAvailable": "Tidak tersedia", "review": "<PERSON><PERSON><PERSON>", "priceIsNegotiable": "Harga dan/atau durasi sewa dapat dinegosiasikan", "yearWithCount": "{count, plural, satu {tahun} other {tahun}}", "shareProperty": {"title": "Bagikan properti"}, "chatCustomerSupport": "Hubungi layanan customer support", "readLess": "Baca lebih sedikit", "readMore": "Baca selengkapnya", "any": "Apapun", "unlimited": "Tak terbatas", "limitedAccess": "<PERSON><PERSON><PERSON>", "fullAccess": "<PERSON><PERSON><PERSON>", "mostPopular": "<PERSON><PERSON>", "representedBy": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "step": "<PERSON><PERSON><PERSON> {count}:", "view": "Lihat detail", "importantNotice": "Pemberitahuan penting", "middlemanProperty": "Properti perantara", "otherProperties": "Pro<PERSON>i la<PERSON>", "popularPropertyNearby": "Properti serupa", "officiallyRepresenting": "<PERSON><PERSON>a resmi mewakili", "rentalTerms": "<PERSON><PERSON><PERSON><PERSON> sewa", "propertyType": "Tipe property", "ofThreePicture": "untuk 3 gambar", "notPossibleToFavorite": "Tidak memungkinkan untuk menyimpan", "seeAllPhoto": "; <PERSON><PERSON> semua foto", "seeExactLocation": "; lihat lokasi sesuai posisi"}, "metadata": {"listingDetailPage": {"description": "{propertyType} ini untuk {listingType} adalah properti terverifikasi di Bali. Real estat yang aman dan tepercaya.", "title": "Listing Properti Terverifikasi di Bali", "keywords": "listing properti terverifikasi Bali, beli vila di Bali, agen properti terpercaya Bali, pasar properti Bali, investasi properti aman di Bali"}, "searchPage": {"title": "Temukan properti Anda di Bali | \n Property Plaza", "description": "Cari {category} te<PERSON><PERSON><PERSON> <PERSON>, term<PERSON><PERSON> vila mewah, toko k<PERSON>, dan investasi real estat. <PERSON><PERSON><PERSON><PERSON> daftar, terhu<PERSON><PERSON> dengan pemilik, dan temukan properti sempurna Anda hari ini!", "multipleCategoryOrLocation": {"title": "Menemukan {category} di {location} | Property Plaza"}}, "rootLayout": {"title": "Vila dan tanah mewah untuk dijual | Properti Plaza ", "description": "Temukan peluang investasi eksklusif di properti utama Bali. <PERSON><PERSON><PERSON><PERSON> vila mewah, r<PERSON> k<PERSON>, dan tanah untuk dijual untuk mengembangkan bisnis Anda atau memiliki surga tropis.", "keyword": "Plaza Properti, Real Estat, Tinggal Jangka Panjang, Nomad Digital, Bali, Tinggal Terjangkau"}, "subsriptionPlan": {"title": "Berlangganan Property Plaza | Akses Properti Terverifikasi di Bali", "description": "Dapatkan manfaat eksklusif dengan berlangganan Property Plaza. Akses awal ke listing properti terverifikasi, dukungan premium, dan pencarian properti yang aman di Bali."}, "favorite": {"title": "Properti favorit Anda | Property Plaza", "description": "<PERSON><PERSON><PERSON> vila, toko, dan peluang investasi favorit <PERSON><PERSON>. \nDengan mudah mengakses properti yang disimpan dan mengunjungi kembali kapan saja untuk membuat keputusan berdasarkan informasi!"}, "messagesPage": {"title": "Pengirim pesan di alun-alun properti", "description": "<PERSON><PERSON><PERSON>, pen<PERSON><PERSON>, atau tim kami"}, "message": {"title": "Obrolan dengan pemilik properti - tanyakan tentang property | Property Plaza", "description": "Mudah berkomunikasi dengan pemilik properti dan tim kami tentang vila, toko, dan peluang real estat di Bali. \nDapatkan tanggapan instan terhadap pertanyaan Anda dan mengamankan properti ideal Anda hari ini!!"}, "notificationSetting": {"title": "Peringatan Pemberitahuan Pemberitahuan", "description": "<PERSON><PERSON><PERSON> semua pember<PERSON>"}, "profile": {"title": "Profil Anda | Property Plaza", "description": "<PERSON>kses profil Anda untuk mengelola detail akun, lihat properti yang disimpan, dan terhubung dengan pemilik properti. Menyesuaikan perjalanan real estat Anda dengan cara Anda!"}, "security": {"title": "Amankan <PERSON> - Pengaturan Login & Keamanan | Property Plaza", "description": "<PERSON><PERSON>la kredensial login <PERSON>, per<PERSON><PERSON> kata sandi <PERSON>, dan tingkatkan keamanan akun <PERSON>. \nAktifkan otentikasi dua faktor (2FA) dan melindungi akun plaza properti Anda dari akses yang tidak sah."}, "termsOfUse": {"title": "Ketentuan Penggunaan | Property Plaza ", "description": "Baca Ketentuan Penggunaan untuk Property Plaza. \n<PERSON><PERSON><PERSON> pedoman, tanggung jawab pengguna, dan perjan<PERSON>an hukum saat menggunakan platform kami untuk daftar properti, pencarian real estat, dan transaksi."}, "privacyPolicy": {"title": "Kebijakan Privasi | Property Plaza ", "description": "Pelajari bagaimana properti Plaza men<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan melindungi informasi pribadi Anda. \nBaca Kebijakan Privasi kami untuk memahami hak -hak Anda dan bagaimana kami memastikan keamanan data untuk pencari dan pemilik properti."}, "userDataDeletion": {"title": "Penghapusan Data Pengguna | Properti Plaza ", "description": "Properti Plaza menghormati hak Anda untuk mengontrol data pribadi Anda. \nDokumen ini menguraikan proses untuk meminta penghapusan data pengguna Anda dari sistem kami."}, "aboutUs": {"title": "Tentang Property Plaza | Agen Properti Terpercaya & Listing Terverifikasi di Bali.", "description": "Kenali Property Plaza, platform properti terpercaya di Bali dengan listing terverifikasi. Te<PERSON>i tim profesional kami yang berkomitmen untuk transaksi properti yang aman dan terpercaya.", "keyword": "Properti Plaza, Bali Real Estat, Platform Properti, Pencarian Properti Transparan, Tim <PERSON>"}}, "listing": {"detail": {"images": {"showAllImages": "<PERSON><PERSON><PERSON><PERSON> semua gambar", "title": "Gambar"}, "contactCount": "{count} orang sudah menghubungi pemiliknya", "popularFacility": {"title": "<PERSON><PERSON> jual"}, "mainFacilities": {"title": "<PERSON>asi<PERSON><PERSON> utama", "bathroom": {"content": "{count} <PERSON><PERSON> mandi"}, "bedroom": "{count} kamar tidur", "shellAndCore": "Bangunan saja", "gardenSize": "Ukuran taman", "landSize": "<PERSON><PERSON><PERSON> tanah", "view": "Pemandangan", "yearsOfBuild": "<PERSON><PERSON>", "furnishing": {"furnished": "<PERSON><PERSON><PERSON><PERSON>"}, "living": {"privateLiving": "<PERSON><PERSON><PERSON> tertutup", "openLiving": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>uka", "sharedLiving": "<PERSON><PERSON><PERSON>"}, "parking": {"publicParking": "<PERSON><PERSON> umum", "privateParking": "<PERSON><PERSON>"}, "pool": {"available": "<PERSON><PERSON> yang tersedia"}}, "rentalPricingIncluding": {"title": "<PERSON><PERSON> sewa term<PERSON>", "villageFeeIncluded": "<PERSON><PERSON> termasuk biaya desa", "wifi": "Wi-Fi", "gardenFeeIncluded": "<PERSON><PERSON> termasuk biaya sampah", "waterFeeIncluded": "Sudah termasuk biaya air"}}, "misc": {"minimumRent": "Sewa minimum", "propertyNotFound": "<PERSON>mi tidak dapat menemukan properti yang Anda cari", "searchNoResultCount": "Tidak ada yang sama persis", "searchResultCount": "{count} properti", "favoritePropertyNotFound": "Sepertinya Anda tidak punya! \nBuka halaman Properties dan klik hati untuk menyimpan item", "availabelAt": "Tersedia pada", "maximumRent": "<PERSON><PERSON> maksimum"}, "pricing": {"suffix": {"leasehold": "untuk {count} {durationType}"}}, "category": {"villa": "Villa", "apartment": "Apartemen", "guestHouse": "Guesthouse", "commercial": "<PERSON><PERSON>", "cafeAndRestaurent": "<PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "shops": "<PERSON><PERSON>", "shellAndCore": "Bangunan saja", "land": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "homestay": "Wisma", "rooms": "<PERSON><PERSON>", "badge": {"title": "<PERSON><PERSON><PERSON>"}}, "feature": {"additionalFeature": {"buildingSize": "Ukuran bangunan", "bedroom": "<PERSON><PERSON>", "bathroom": "<PERSON><PERSON> mandi", "land": "<PERSON><PERSON><PERSON> tanah", "plumbing": "Pipa saluran air", "airCondition": "AC", "balcony": "Balkon", "bathub": "<PERSON><PERSON>", "constructionNearby": "<PERSON><PERSON> dengan konstr<PERSON>", "garden": "<PERSON><PERSON>", "gazebo": "Gazebo", "petAllowed": "<PERSON><PERSON>", "recentlyRenovated": "<PERSON><PERSON>", "rooftopTerrace": "Teras atap", "subleaseAllowed": "Sublease <PERSON><PERSON><PERSON>", "terrace": "Teras", "municipalWaterwork": "Air Kota"}}, "homepage": {"howItWorks": "<PERSON><PERSON><PERSON>", "howItWorksDescription": "Temukan properti ideal Anda dengan mudah dan terhubung langsung dengan pemiliknya tanpa perantara dan repot."}, "filter": {"elictricity": {"optionOne": {"title": "<PERSON><PERSON>"}, "optionTwo": {"title": "< 5kW"}, "optionThree": {"title": "5 kW - 10 kW"}, "optionFour": {"title": "10 kW - 20 kW"}, "optionFive": {"title": "> 20 kW"}}, "others": {"elictricity": {"title": "Kelistrikan (KW)"}, "minimumContract": {"title": "Kontrak minimum"}, "title": "<PERSON> lain", "typeContract": {"title": "<PERSON><PERSON><PERSON>"}, "yearsOfBuild": {"title": "<PERSON><PERSON> p<PERSON>n"}, "parking": {"title": "Status parkir"}, "pool": {"title": "<PERSON><PERSON><PERSON><PERSON> kolam"}, "closeOrOpenLiving": {"title": "<PERSON><PERSON>"}, "furnished": {"title": "Status perabotan"}}, "minimumContract": {"optionOne": {"title": "<PERSON><PERSON><PERSON>"}, "optionTwo": {"title": "< 1 <PERSON>hun"}, "optionThree": {"title": "1 > 3 Tahun"}, "optionFour": {"title": "3 > 5 Tahun"}, "optionFive": {"title": "> 5 Tahun"}}, "priceRange": {"title": "<PERSON><PERSON><PERSON> ha<PERSON>", "description": "<PERSON><PERSON><PERSON>"}, "typeProperty": {"optionThree": {"subOption": {"optionOne": {"title": "<PERSON><PERSON><PERSON>"}, "description": "Ruang komersial dengan ukuran bangunan {comparisonType} {count}", "optionTwo": {"title": "Sedang"}, "optionThree": {"title": "Besar"}}, "title": "Bisnis/<PERSON>ek<PERSON>", "description": "<PERSON><PERSON><PERSON> dan <PERSON>"}, "optionTwo": {"subOption": {"optionOne": {"title": "Villa"}, "optionTwo": {"title": "Apartemen"}, "optionThree": {"title": "<PERSON><PERSON>"}}, "title": "Tempat tinggal", "description": "<PERSON><PERSON><PERSON>"}, "optionOne": {"title": "Apa pun"}, "optionFour": {"title": "<PERSON><PERSON>"}, "title": "Mau cari apa?", "description": "Te<PERSON><PERSON> yang <PERSON>a <PERSON>"}, "view": {"optionOne": {"title": "Apa pun"}, "optionTwo": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "optionThree": {"title": "<PERSON><PERSON>"}, "optionFour": {"title": "<PERSON><PERSON>"}, "optionFive": {"title": "Hutan"}}, "typeView": {"title": "Pemandangan"}, "sortBy": {"higherPrice": "<PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "lowerPrice": "Ha<PERSON> (Terendah Pertama)", "newestFirst": "<PERSON><PERSON>bahkan (Terbaru Dulu)", "oldest": "<PERSON><PERSON> (<PERSON>)", "smallest": "Ukuran Properti (Terkecil Pertama)", "largest": "<PERSON><PERSON><PERSON> (Terbesar <PERSON>)", "mostViewed": "Popularitas (Paling Banyak Dilihat)", "mostFavorited": "Popularitas (Paling Favorit)", "natureView": "Pemandangan/Pemandangan (Pemandangan Alam Terlebih Dahulu)"}, "category": {"all": {"title": "<PERSON><PERSON><PERSON>"}}, "yearsOfBuild": {"optionAny": {"title": "<PERSON><PERSON> pun"}, "optionOne": {"title": "< 2015"}, "optionTwo": {"title": "2016 - 2019"}, "optionThree": {"title": "2020 - 2024"}, "optionFour": {"title": "<PERSON><PERSON> ini"}}, "othersFeature": {"title": "<PERSON><PERSON>"}, "propertySize": {"title": "Ukuran properti", "landSize": {"title": "<PERSON><PERSON><PERSON> tanah"}, "buildingSize": {"title": "Building size"}, "gardenSize": {"title": "Ukuran taman"}}}, "featureFilter": {"optionOne": {"title": "<PERSON><PERSON>"}, "optionTwo": {"title": "AC"}, "optionThree": {"title": "<PERSON><PERSON>"}, "optionFour": {"title": "Halaman belakang taman"}, "optionFive": {"title": "Gazebo"}, "optionSix": {"title": "Teras atap"}, "optionSeven": {"title": "Balkon"}, "optionEight": {"title": "Terrance"}, "title": "<PERSON><PERSON>"}, "locationFilter": {"optionOne": {"title": "Jalan utama"}, "optionTwo": {"title": "Dekat dengan pantai"}, "title": "<PERSON><PERSON>"}, "propertyCondition": {"optionOne": {"title": "Disewakan diperbolehkan"}, "optionTwo": {"title": "Konstruksi di dekatnya"}, "optionThree": {"title": "Saluran air kota"}, "optionFour": {"title": "Pipa saluran air"}, "optionFive": {"title": "<PERSON><PERSON> saja direnova<PERSON>"}, "title": "<PERSON><PERSON><PERSON>"}, "rentalIncludeFilter": {"optionOne": {"title": "Wi-fi"}, "optionTwo": {"title": "Biaya Sampah"}, "optionThreetitle": "Air", "optionFour": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON><PERSON>wa"}, "popularProperty": {"title": "Properti populer"}, "newestProperty": {"title": "Properti terbaru"}, "newestCommercialProperty": {"title": "Properti komersial terbaru"}, "featuredProperty": {"title": "Properti unggulan"}, "search": {"placeholder": "Temukan lokasi spesifik", "title": "<PERSON><PERSON>"}, "viewAllProperties": "<PERSON><PERSON> semua <PERSON>"}, "cta": {"login": "<PERSON><PERSON><PERSON>", "createAccount": "<PERSON><PERSON><PERSON> akun", "continueWith": "<PERSON><PERSON><PERSON> {field}", "edit": "Ganti foto", "updatePassword": "Ganti", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seeTranslation": "<PERSON><PERSON>", "seeOriginal": "Lihat teks asli", "filter": "Filter", "chatCustomerService": "Butuh bantuan? \nhubungi CS kami", "requestCreateListing": "<PERSON><PERSON><PERSON><PERSON> daftar <PERSON>", "createListing": "Buat iklan properti", "contactAccountmanager": "<PERSON><PERSON><PERSON><PERSON>", "disableListing": "Non aktifkan properti", "finishReview": "<PERSON><PERSON><PERSON><PERSON>", "saveAsDraft": "Simpan sebagai draf", "addReview": "Tambah<PERSON> ul<PERSON>n", "save": "Simpan", "back": "Kembali", "joinWaitngList": "<PERSON>kut daftar tunggu", "logout": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "signUpNow": "<PERSON><PERSON><PERSON>", "sendResetPassword": "Ubah kata sandi", "checkOut": "Check-out", "contactToWhatsapp": "<PERSON><PERSON><PERSON><PERSON> kami di <PERSON>", "createYourAccount": "Buat A<PERSON>n", "subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signUp": "Mendaftar", "goBack": "Kembali", "search": "<PERSON><PERSON>", "removeSearch": "Hapus", "disable": "Non-aktifkan", "enable": "Aktifkan", "sendRequest": "<PERSON><PERSON>", "closeChat": "<PERSON><PERSON><PERSON>", "sellPropertyFast": "<PERSON><PERSON> atau sewakan properti <PERSON>a dengan cepat", "next": "Selanjutnya", "activateListing": "Aktivasi iklan", "activate": "Aktivasi", "changeStatus": "Ubah status", "changePrice": "<PERSON><PERSON> harga", "pay": "Bayar", "topUp": "<PERSON><PERSON>", "viewAllProperty": "<PERSON><PERSON> semua <PERSON>", "readMore": "Selengkapnya ...", "changePassword": "Ubah kata sandi", "filters": "Filter", "clearAll": "<PERSON><PERSON>", "previous": "Sebelumnya", "maps": "Map", "list": "List", "contactOwner": "Hubungi Pemilik", "share": "Bagikan", "readLess": "Baca lebih sedikit", "copyLink": "<PERSON><PERSON>an", "close": "<PERSON><PERSON><PERSON>", "findOtherProperty": "<PERSON><PERSON>", "findOtherPackage": "<PERSON><PERSON> paket lainnya", "followUsOnInstagram": "<PERSON><PERSON><PERSON> kami di Instagram", "changePhoto": "Ubah foto", "saveChanges": "<PERSON><PERSON><PERSON>", "change": "Ubah", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON>a", "signOutAll": "<PERSON><PERSON><PERSON> dari semua per<PERSON>", "signOutDevice": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "addPaymentMethod": "Tambahkan metode pembayaran", "requestChangePassword": "<PERSON>a ubah kata sandi", "showAllProperty": "<PERSON><PERSON><PERSON>", "saved": "Disimpan", "get2FACode": "Menghasilkan kode 2FA", "downgrade": "<PERSON><PERSON> paket", "cancelSubscription": "Batalkan langganan", "contactMiddleman": "Perwakilan Kontak", "saveUpTo": "<PERSON><PERSON>", "remove": "Hapus", "setPrimary": "Ditetapkan sebagai primer", "editBilling": "Edit", "continueCheckout": "Lanjutkan ke pembayaran", "detail": "Detail"}, "conjuntion": {"or": "atau", "and": "dan", "of": "dari", "for": "untuk"}, "propertyDetail": {"totalBathroom": {"title": "Total kamar mandi"}, "totalBedroom": {"title": "Total kamar tidur"}, "buildingSize": {"title": "Ukuran bangunan"}, "cascoStatus": {"title": "Status casco"}, "gardenSize": {"title": "Ukuran taman"}, "landSize": {"title": "<PERSON><PERSON><PERSON> tanah"}, "viewOfProperty": {"title": "Pemandangan properti"}, "yearsOfBuild": {"title": "<PERSON><PERSON> p<PERSON>n"}, "villageFee": {"title": "<PERSON><PERSON>ya desa"}, "wifi": {"title": "Wifi"}, "garbageFee": {"title": "Biaya sampah"}, "waterFee": {"title": "Biaya air"}, "electricity": {"title": "<PERSON><PERSON>"}, "furnishingStatus": {"title": "Status perabotan"}, "livingStatus": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "parking": {"title": "Status parkir"}}, "detail": {"rentalPricingIncluding": {"electricity": "Kwh Listrik"}, "mainFacilities": {"furnishing": {"unfurnished": "Tidak berperabot"}}}, "form": {"login": {"title": "<PERSON><PERSON><PERSON>", "description": "Lanjutkan ke Properti Anda"}, "signUp": {"description": "Masukkan detail <PERSON>a untuk memulai", "title": "<PERSON><PERSON><PERSON> akun"}, "utility": {"fieldRequired": "{field} wajib diisi", "invalidFormat": "{field} format tidak sesuai", "eitherFieldRequired": "Salah satu {fields} harus disediakan", "enterValidField": "<PERSON><PERSON><PERSON> masukkan {field} yang valid", "forgotField": "<PERSON><PERSON> {field} And<PERSON>?", "resetField": "<PERSON><PERSON> {field}", "minimumLength": "{field} memerlukan minimum {length} karakter", "fieldNotMatch": "{field} tidak sama", "optional": "(Opsional)", "coordinateRequired": "Anda perlu memasukkan longitude dan latitude yang dipisahkan dengan koma", "wrongFormatCoordinate": "Format koordinat salah, Anda perlu memasukkan longitude dan latitude yang dipisahkan dengan koma", "maximumLength": "Tidak dapat memasukkan {field} lebih dari {length} karakter", "passwordWeak": "Kata sandi terlalu lemah", "password": {"minimumLength": "Minimum 8 karakter", "numberRequired": "<PERSON>mal satu angka", "notCommonWord": "<PERSON><PERSON>n kata umum", "specialCharacter": "Minimal satu simbol", "uppercaseRequired": "Setidaknya satu huruf besar", "lowercaseRequired": "Setidaknya satu huruf kecil"}}, "field": {"email": "E-mail", "phoneNumber": "Nomor telepon", "password": "<PERSON>a sandi", "confirmPassword": "Konfirmasi kata sandi", "username": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "aboutYou": "<PERSON>tang anda", "language": "Bahasa", "message": "<PERSON><PERSON>", "typeProperty": "<PERSON><PERSON><PERSON> properti", "yearsOfBuild": "<PERSON><PERSON> p<PERSON>n", "propertyView": "<PERSON><PERSON><PERSON>", "landSize": "<PERSON><PERSON><PERSON> tanah", "buildingSize": "Ukuran bangunan", "gardenSize": "Ukuran taman", "typeBedroom": "<PERSON><PERSON>e kamar tidur", "totalBathroom": "<PERSON><PERSON><PERSON> ka<PERSON> mandi", "wifi": "Kecepatan Wi-fi", "cleaning": "Total bersih bersih", "cleaningTime": "<PERSON><PERSON><PERSON>", "review": "<PERSON><PERSON><PERSON>", "postalCode": "<PERSON><PERSON> pos", "coordinate": "Ko<PERSON>inat", "propertyLocation": "Lokasi <PERSON>", "roadSize": "Ukuran jalan masuk", "addressLocation": "<PERSON><PERSON><PERSON>", "distric": "Kecamatan", "city": "Kota atau kabupaten", "province": "<PERSON><PERSON><PERSON>", "typeContract": "<PERSON><PERSON>e kontrak", "minimumDuration": "<PERSON><PERSON><PERSON> minimal", "minimumPrice": "<PERSON><PERSON> per<PERSON>", "maximumDuration": "<PERSON><PERSON><PERSON> maksimum", "elictricity": "Kelistrikan", "furnishingStatus": "Status perabotan", "parking": "<PERSON><PERSON><PERSON>", "poolAvailability": "<PERSON><PERSON><PERSON><PERSON>", "livingStatus": "<PERSON><PERSON><PERSON><PERSON> di sekitar", "role": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON> le<PERSON>", "name": "<PERSON><PERSON>", "otp": "OTP", "statusListing": "Status properti", "excerpt": "Ku<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "banjar": "Banjar"}, "title": {"login": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON> akun", "staffLogin": "Staf Autentikasi", "resetPassword": "<PERSON><PERSON> ulang kata sandi", "enterOtpCode": "Masukkan kode OTP"}, "label": {"emailOrPhone": "Email atau nomor telepon", "password": "<PERSON>a sandi", "username": "<PERSON><PERSON>", "confirmPassword": "Konfirmasi kata sandi", "firstName": "<PERSON><PERSON> de<PERSON>", "lastName": "<PERSON><PERSON>", "aboutYou": "<PERSON>tang anda", "email": "E-mail", "address": "<PERSON><PERSON><PERSON>", "phoneNumber": "Nomor telepon", "language": "Bahasa yang digunakan", "PropertyType": "<PERSON><PERSON>", "cascoProperty": "Properti casco", "yearsOfBuild": "<PERSON><PERSON> p<PERSON>n", "propertyView": "<PERSON><PERSON><PERSON>", "landSize": "<PERSON><PERSON><PERSON> tanah", "buildingSize": "Ukuran bangunan", "gardenSize": "Ukuran taman", "totalBedroom": "<PERSON><PERSON><PERSON> ka<PERSON> t<PERSON>ur", "totalBathroom": "<PERSON><PERSON><PERSON> ka<PERSON> mandi", "wifi": "Wi-Fi", "cleaning": "total bersih bersih", "coordinate": "Ko<PERSON>inat", "propertyLocation": "Lokasi <PERSON>", "roadSize": "Ukuran jalan masuk", "addressLocation": "<PERSON><PERSON><PERSON>", "postalCode": "kode pos", "distric": "Kecamatan", "city": "Kota atau kabupaten", "province": "<PERSON><PERSON><PERSON>", "typeContract": "<PERSON><PERSON>e kontrak", "availableContract": "<PERSON><PERSON><PERSON> k<PERSON>", "minimumDuration": "<PERSON><PERSON><PERSON> minimal", "minimumPrice": "<PERSON><PERSON> per<PERSON>", "maximumDuration": "<PERSON><PERSON><PERSON> maksimum", "elictricity": "Kelistrikan", "furnishingStatus": "Status perabotan", "parkingStatus": "<PERSON><PERSON> parkir", "poolAvailability": "<PERSON><PERSON><PERSON><PERSON> kolam renang", "livingStatus": "<PERSON><PERSON><PERSON><PERSON> di sekitar", "fullName": "<PERSON><PERSON> le<PERSON>", "name": "<PERSON><PERSON>", "requestCreateListing": "Berikan sedikit informasi kepada manajer akun kami tentang properti anda", "propertyDescription": "<PERSON><PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "type": "Tipe", "amount": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "date": "Tanggal pembelian", "requestUpdateListing": "<PERSON>ri tahu kami tentang hal-hal yang perlu Anda ubah", "promoCode": "Kode promo", "status": "Status properti", "excerpt": "Ku<PERSON><PERSON>", "propertyTitle": "<PERSON><PERSON><PERSON>", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "userId": "ID Pengguna", "banjar": "Banjar"}, "placeholder": {"basePlaceholder": "<PERSON><PERSON><PERSON><PERSON> {field} <PERSON><PERSON>", "aboutYou": "Ceritakan sedikit mengenai dirimu", "pickLanguage": "<PERSON><PERSON><PERSON> bahasa", "baseSelectPlaceholder": "<PERSON><PERSON><PERSON> {field}", "baseNumberPlaceholder": "Tetap<PERSON> {field} anda", "availableContract": "Tetapkan kapan properti tersedia untuk dikontrak", "searchQuestion": "<PERSON><PERSON>", "example": {"requestHelpToCs": "<PERSON>a sudah isi kredit, namun kredit saya tidak bertambah", "email": "<EMAIL>", "phoneNumber": "+62 8123 4567 8901", "firtName": "I Ketut", "lastName": "<PERSON><PERSON><PERSON>", "requestCreateListing": "Villa 4 Kamar Tidur 3 Kamar Mandi dengan kolam renang pribadi berlokasi di canggu, Badung", "requestUpdateListing": "<PERSON>a ingin mengubah jumlah kamar mandi"}, "enterPromoCode": "Masukkan kode promo", "seekersFindPropertyLocation": "<PERSON><PERSON> t<PERSON>"}, "description": {"resetPassword": "<PERSON><PERSON> akan mengirim permintaan ganti password anda lewat email", "passwordRequirement": "Password minimal terdiri dari 8 <PERSON><PERSON><PERSON>, 1 <PERSON><PERSON><PERSON>, 1 <PERSON><PERSON><PERSON>, 1 Angka dan 1 Simbol", "userId": "Ini adalah pengidentifikasi unik Anda dalam sistem kami"}}, "auth": {"login": {"subtitle": "Lanjutkan ke properti Anda"}, "register": {"subtitle": "Masukkan detail <PERSON>a untuk memulai"}, "subtitle": "JBergabunglah dengan Program Pencari Properti kami", "resetPassword": {"subtitle": "Atur Ulang Kata Sandi Akun And<PERSON>"}, "createAccount": "Tidak punya akun?", "alreadyHaveAccount": "Sudah punya akun?", "otp": {"content": {"cantFindEmail": "Tidak dapat menemukan emailnya? \nPeriksa folder spam Anda atau kirim ulang kode di bawah ini.", "title": "<PERSON><PERSON> telah mengirim tautan ajaib ke kotak masuk Anda. \nKonfirmasikan email Anda ke:"}, "item": {"one": "Akses Daftar Properti Bali Eksklusif", "three": "Dapatkan peringatan yang dipersonalisasi untuk peluang baru", "two": "Simpan vila favorit Anda"}}}, "error": {"failedLogin": {"title": "Ups kami memiliki masalah saat akun login"}, "signUp": {"title": "Ups, gagal mendaftar akun"}, "messageTooShort": {"title": "<PERSON><PERSON> terlalu singkat", "description": "<PERSON><PERSON> berikan lebih banyak konteks pada pesan"}, "failedSendMessage": {"title": "Ups, ada kesalahan saat mengirim pesan"}, "updateNotification": {"title": "<PERSON><PERSON> {field}"}, "requestForgetPassword": {"title": "Ups, permintaan gagal lupakan kata sandi"}, "Subscribing": "Ups, ada sesuatu kesalahan saat berlangganan rencana", "failedEnablingTwoFA": "<PERSON>s, gagal menerapkan dua faktor otenti<PERSON>i", "failedUpdatePayment": "Tidak dapat memperbarui metode pembayaran"}, "success": {"sendVerification": {"title": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>m kode verifikasi ke"}, "copyUrl": {"title": "Sberhasil meng-Copy URL Properti", "description": "Anda dapat membagikan ini kepada siapa pun"}, "sendMessageToCs": {"title": "<PERSON><PERSON><PERSON> pesan ke tim kami", "description": "<PERSON><PERSON> akan segera men<PERSON>"}, "sendMessageToOwner": {"title": "<PERSON><PERSON><PERSON>m pesan ke pemilik properti", "description": "Harap tunggu pesan pemilik property"}, "updateNotification": {"title": "<PERSON><PERSON><PERSON><PERSON> {field}"}, "requestForgetPassword": {"title": "Permintaan <PERSON>kan Kata Sandi Suks<PERSON>", "description": "<PERSON><PERSON><PERSON> per<PERSON> email <PERSON><PERSON> {email}"}, "activateTotp": "Keberhasilan Mengaktifkan Otentikasi Dua Faktor", "upgradeSubscription": "Langganan Upgrade Sukses", "cancelSubscription": "<PERSON><PERSON><PERSON>", "downGrade": "Sukses Downgrade Plan", "updatePayment": "Metode Pembayaran Pembaruan Sukses"}, "message": {"otpRequest": {"failedToast": {"title": "Ups, gagal meminta OTP"}}, "utils": {"messageTooLongError": {}}, "waitingResponse": "Menunggu tanggapan", "notMessageList": "<PERSON><PERSON>h sepi nih", "searchChat": "<PERSON><PERSON> pesan", "chatCs": {"title": "<PERSON><PERSON><PERSON><PERSON> Anda me<PERSON>an bantuan atau pertanyaan?", "description": "<PERSON><PERSON> tahu kami dan kami akan menja<PERSON> balasan segera."}, "chatOwner": {"title": "<PERSON><PERSON> den<PERSON> pemilik", "description": "Tanya<PERSON> apapun tentang properti"}, "category": {"customerSupport": "Layanan Customer Support", "seekers": "<PERSON><PERSON><PERSON>", "accountManager": "<PERSON><PERSON><PERSON> akun", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "chatEnded": "<PERSON><PERSON><PERSON>", "textChatEnded": "<PERSON><PERSON> kasih telah menjan<PERSON>u Dukungan Properti-Plaza. \n<PERSON><PERSON> mengh<PERSON> waktu <PERSON>, dan kami ber<PERSON>p <PERSON>a sukses selalu kedepannya. \n🙏 Tim Properti-Plaza", "subscriptionSignUp": {"failedToast": {"title": "Ups, ada yang salah"}}}, "user": {"account": "<PERSON><PERSON><PERSON>"}, "otp": {"resendVerificationCode": "<PERSON><PERSON> ul<PERSON> kode verifikasi OTP"}, "HowItWorks": {"optionOne": {"description": "Temukan properti yang tersedia yang terdaftar di platform kami", "title": "<PERSON><PERSON><PERSON> k<PERSON>an anda"}, "optionTwo": {"title": "<PERSON><PERSON> atau beli jangka panjang", "description": "Sewa atau beli jangka panjang tergantung kesepakatan Anda."}, "optionThree": {"title": "Bernegosiasi dan tentukan", "description": "Terhubung langsung dengan pemilik properti terverifikasi"}, "optionFour": {"title": "Pindah dan nikmat<PERSON>h", "description": "<PERSON><PERSON><PERSON> ruang baru Anda dengan mudah dan mulailah menjalani kehidupan terbaik Anda."}}, "footer": {"tabsOne": {"content": {"optionOne": {"title": "Villa"}, "optionTwo": {"title": "Apartemen"}, "optionThree": {"title": "Wisma"}, "optionFour": {"title": "Penginapan"}, "optionFive": {"title": "<PERSON><PERSON>"}, "optionSix": {"title": "<PERSON><PERSON><PERSON>"}, "optionSeven": {"title": "<PERSON>oran"}}}, "tabsTwo": {"content": {"optionOne": {"title": "Daftarkan properti Anda"}, "optionTwo": {"title": "FAQ untuk pemilik"}}}, "tabsThree": {"content": {"optionOne": {"title": "<PERSON><PERSON><PERSON> kean<PERSON>an"}, "optionTwo": {"title": "Properti untuk bisnis"}, "optionThree": {"title": "Properti untuk perumahan"}, "optionFour": {}}}, "tabsFour": {"content": {"optionOne": {"title": "Layanan Customer Support"}, "optionTwo": {"title": "Layanan Customer Support Pemilik Properti"}, "optionThree": {"title": "<PERSON>tang kami"}, "optionFour": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "optionFive": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}}, "slogan": "Menghubungkan orang dengan properti", "exploreProperties": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "properyOwner": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "FaQ": {"title": "FAQ"}, "help": {"title": "Bantuan"}, "copyright": "2025 Property Plaza. \nSe<PERSON>a hak dilindungi undang-undang."}, "setting": {"subscriptionStatus": {"billing": {"billingHistory": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON>hat dan unduh riwayat penagihan Anda"}, "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>la metode pembayaran Anda dan lihat riwayat penagihan Anda", "paymentMethod": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> metode pembayaran Anda dan informasi penagihan"}, "billingInformation": {"title": "Informasi <PERSON>"}}, "subscription": {"title": "<PERSON><PERSON><PERSON>", "features": {"optionOne": "Hubungi Pemilik", "optionTwo": "Foto", "optionThree": "<PERSON><PERSON>", "optionFour": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optionFive": "<PERSON><PERSON><PERSON> yang disimpan", "optionSix": "<PERSON>ftar <PERSON>", "optionSeven": "<PERSON><PERSON><PERSON><PERSON>", "optionEight": "<PERSON>r Virtual (Lokasi dan Video)", "optionNine": "<PERSON><PERSON><PERSON>", "optionTen": "Alat Perbandingan", "optionEleven": "Konsultasi den<PERSON>", "optionTwelve": "Daftar Pasar di Luar", "optionThirdteen": "Transaksi", "optionFourteen": "Properti Favorit", "optionFifteen": "<PERSON><PERSON>", "optionSixteen": "Mengobrol dengan pemilik"}, "monthly": "Bulanan", "quarterly": "Triwulanan", "description": "<PERSON><PERSON><PERSON> dan kelola rencana lang<PERSON>"}, "title": "Status Berlangganan"}, "favorites": {"savedItems": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> dan kelola <PERSON>ti te<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON><PERSON>", "favoriteSellers": {"title": "<PERSON><PERSON><PERSON>"}}, "notification": {"soundNotification": {"title": "Notifikasi <PERSON>", "description": "Aktifkan notifikasi suara saat Anda menerima pemberitahuan"}, "emailForNewMessages": {"title": "Notifikasi Email untuk Pesan Baru", "description": "Terima pemberitahuan melalui email saat Anda mendapatkan pesan baru"}, "emailForNewProperties": {"title": "Notifikasi Email untuk Properti Baru", "description": "Terima pemberitahuan melalui email saat properti baru cocok dengan pencarian yang Anda simpan"}, "priceChangeAlert": {"title": "Pemberitahuan <PERSON>", "description": "Dapatkan pemberitahuan saat harga berubah untuk properti yang Anda simpan"}, "newsletter": {"title": "Nawala", "description": "Terima buletin bulanan kami dengan pembaruan dan tips pasar"}, "specialOffer": {"title": "<PERSON><PERSON><PERSON>", "description": "Terima pemberitahuan tentang penawaran dan promosi secara khsusus"}, "surveys": {"title": "Survei", "description": "Berpartisipasi dalam survei untuk membantu kami meningkatkan layanan kami"}}, "profile": {"notification": {"title": "Pemberitahuan"}, "personalInfo": {"title": "Info <PERSON>", "changeEmail": {"title": "Ganti email", "description": "<PERSON><PERSON><PERSON><PERSON> email baru <PERSON>a di bawah ini. <PERSON><PERSON> akan men<PERSON> email verifikasi untuk mengonfirmasi perubahan."}, "changePhone": {"title": "Ganti nomor telepon", "description": "Ma<PERSON>kkan nomor telepon baru Anda di bawah ini. <PERSON><PERSON> akan mengirimkan SMS verifikasi untuk mengonfirmasi perubahan."}}, "security": {"loginHistory": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> kata sandi Anda agar akun Anda aman", "table": {"date": "Tanggal", "device": "<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "status": "Status"}}, "connectedDevice": {"title": "Otentikasi Dua Faktor", "description": "Tambahkan lapisan keamanan tambahan ke akun Anda", "lastActive": "<PERSON><PERSON><PERSON>", "currentDevice": "Perangkat saat ini"}, "twoFA": {"title": "Otentikasi Dua Faktor", "description": "Tambahkan lapisan keamanan tambahan ke akun Anda", "enterAuthenticatorCode": "Ma<PERSON><PERSON><PERSON> kode di bawah ini dari aplikasi Anda.", "scanQRCodeWithAuthenticatorApp": "Pindai kode QR dengan aplikasi Authenticator Anda.", "useAuthenticatorApp": "Gunakan aplikasi Aunteticator untuk mengaktifkan 2FA"}, "title": "<PERSON><PERSON> dan <PERSON>", "password": {"lastChanged": "<PERSON><PERSON><PERSON>"}}, "title": "Profil", "notifications": {"title": "Notif<PERSON><PERSON>"}}, "accountAndProfile": {"security": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> pengaturan keamanan akun dan preferensi login Anda"}}, "messages": {"title": "<PERSON><PERSON>", "messages": {"title": "<PERSON><PERSON>"}}}, "accountAndProfile": {"favorite": "<PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON>", "notification": "Pemberitahuan", "profile": "Profil", "security": "<PERSON><PERSON><PERSON>", "logout": {"title": "<PERSON><PERSON><PERSON> dari akun"}}, "info": {"": {"messageTooLongError": {}}, "messageTooLongError": {"description": "<PERSON><PERSON> kirim pesan kurang dari {count} karakter"}, "messageTooLong": {"title": "Pesan terlalu panjang", "description": "<PERSON><PERSON> balas pesan dengan jelas dan bermakna"}, "noPaymentMethodsAdded": "Tidak ada metode pembayaran"}, "settings": {"profile": {"notification": {"description": "<PERSON><PERSON><PERSON> preferensi pemberitahuan Anda"}, "personalInfo": {"description": "Kelola informasi pribadi dan detail kontak Anda"}, "security": {"password": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> akun kata sandi <PERSON>a"}}}, "personalInfo": {"changeEmail": {"title": "Ubah Email", "description": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> email baru <PERSON>a di bawah ini. <PERSON><PERSON> akan men<PERSON> email verifikasi untuk mengonfirmasi perubahan tersebut."}, "changePhone": {"title": "Ubah nomor telepon", "description": "Ma<PERSON>kkan nomor telepon baru Anda di bawah ini. <PERSON><PERSON> akan mengirimkan SMS verifikasi untuk mengonfirmasi perubahan tersebut."}}}, "navbar": {"search": {"locationTitle": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "flexibleLocation": "<PERSON><PERSON> fl<PERSON>", "propertyType": "<PERSON><PERSON><PERSON> kategori <PERSON>ti"}}, "owner": {"accountAndProfile": {"logout": {"description": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar dari akun <PERSON>?"}}}, "component": {"pagination": {"rowPerPage": "Baris per halaman", "goToFirstPage": "Pergi ke halaman pertama", "goToPreviousPage": "<PERSON>gi ke halaman sebelumnya", "goToNextPage": "<PERSON>gi ke halaman berikutnya", "goToLastPage": "<PERSON>gi ke halaman terakhir"}, "dataTable": {"pagination": {}}}, "faq": {"additionalFee": {"description": "Property Plaza tidak membebankan biaya tambahan di luar langganan yang diperlukan untuk menghubungi pemilik properti. \n<PERSON><PERSON><PERSON> lain, seperti biaya layanan atau pajak, harus dibahas secara langsung dengan pemilik.", "title": "A<PERSON><PERSON>h ada biaya tambahan yang harus saya ketahui?"}, "badge": "Sering bertanya dan me<PERSON>n", "cancelSubscription": {"description": "<PERSON>, <PERSON><PERSON> dapat memba<PERSON>kan kapan saja, dan lang<PERSON>an <PERSON>a akan tetap aktif sampai akhir siklus penagihan saat ini. \nUntuk mengel<PERSON> langganan <PERSON>, kun<PERSON>gi:", "extraContent": "<PERSON><PERSON><PERSON>.", "title": "<PERSON><PERSON><PERSON><PERSON> saya membatalkan langganan saya selama sebulan?"}, "checkOwnerisVerified": {"description": "Kami memverifikasi identitas pemilik dan memeriksa dokumen kepemilikan sebelum mengizinkan daftar di platform kami. \n<PERSON><PERSON>, kami sangat menyarankan agar Anda bertemu dengan pemilik dan mengunjungi properti secara langsung sebelum melakukan pembayaran.", "title": "Bagaimana saya tahu jika pemilik telah diverifikasi?"}, "contactOwner": {"description": "Setiap daftar di situs web termasuk fitur obrolan yang memungkinkan Anda untuk menghubungi pemilik langsung melalui akun Property Plaza Anda.", "title": "Bagaimana cara menghubungi pemilik properti?"}, "contactOwnerDirectly": {"description": "<PERSON>, set<PERSON>h membuat akun dan berlangganan salah satu rencana kami, <PERSON><PERSON> dapat mengobrol langsung dengan pemilik properti. \n<PERSON><PERSON>, harap hanya menghubungi pemilik properti yang benar -<PERSON><PERSON> <PERSON><PERSON>, karena ada batasan jumlah pesan yang dapat Anda kirim per minggu.", "title": "<PERSON><PERSON><PERSON><PERSON> saya menghubungi pemilik secara langsung?"}, "disputeWithOwner": {"title": "Bagaimana jika saya memiliki perselisihan dengan pemiliknya?"}, "group": {"contactingPropertyOwner": "Menghubungi Pemilik Properti", "paymentAndFee": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "propertyVerificationAndSafety": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "propertyVisitAndContract": "Kunjun<PERSON> dan <PERSON>", "usingThePlatform": "Menggunakan Platform"}, "handlingPayment": {"description": "Pembayaran diatur langsung dengan pemilik. \n<PERSON><PERSON> sangat menyarankan agar tidak mentransfer uang sebelum melihat properti dan bertemu dengan pemilik secara langsung. \n<PERSON><PERSON>, layanan escrow tersedia dari penyedia pihak ketiga.", "title": "Bagaimana cara menangani pembayaran atau deposit?"}, "ifOwnerDoesntResponse": {"description": "Jika pemilik tidak membalas dalam waktu tiga hari, upaya kontak tidak akan diperhitungkan menuju batas pesan mingguan Anda. \nSistem kami melacak pemilik yang tidak aktif, dan akun dengan tidak aktif berulang dapat dihapus dari platform.", "title": "Bagaimana jika pemilik tidak menanggapi pesan saya?"}, "issueWhenRenting": {"description": "Property Plaza hanyalah platform yang menghubungkan pembeli/penyewa dengan pemilik. \nMasalah apa pun harus diselesaikan secara langsung dengan pemilik properti. \nSangat penting untuk menyetujui semua persyaratan dengan jelas dalam kontrak sebelum menyelesaikan kesepakatan.", "title": "Apa yang terjadi jika saya memiliki masalah dengan properti setelah menyewa atau membeli?"}, "legalContracts": {"description": "<PERSON><PERSON><PERSON><PERSON> harus diselesaikan langsung antara Anda dan pemilik. \nProperty Plaza tidak memediasi dalam konflik."}, "limitContactingOwner": {"description": "<PERSON><PERSON><PERSON> pesan awal yang dapat Anda kirim per minggu tergantung pada paket berlangganan Anda. \n<PERSON>at ini, Anda dapat menghubungi hingga 15 pemilik per minggu, dengan batasan ulang ini mengatur ulang setiap tujuh hari.", "title": "Apa batasan pemilik yang menghubungi?"}, "negotiatingPrice": {"description": "Negosiasi berlangsung melalui o<PERSON>lan. \nJika kedua belah pihak setuju, mereka dapat bertukar detail kontak untuk diskusi lebih lanjut.", "title": "Bagaimana cara menegosiasikan harga atau persyaratan dengan pemiliknya?"}, "payForSubscription": {"description": "<PERSON><PERSON> be<PERSON> opsi pem<PERSON>, term<PERSON><PERSON> kartu kredit, <PERSON><PERSON><PERSON>, Google Pay, dan <PERSON>.", "title": "Bagaimana cara membayar langganan?"}, "platformOfferLegalAdvice": {"description": "<PERSON><PERSON><PERSON>, tetapi kami berkolaborasi dengan berbagai penyedia layanan hukum dan keuangan. \n<PERSON><PERSON>a me<PERSON> reko<PERSON>, silakan hubungi dukungan pelanggan untuk informasi lebih lanjut.", "title": "Apakah platform menawarkan nasihat hukum atau keuangan?"}, "reasonOfLimitContactingOwner": {"description": "Pembatasan ini membantu mencegah pemilik kewalahan dengan pesan dan memastikan pengalaman yang lebih baik bagi pemilik dan pembeli/penyewa potensial.", "title": "Mengapa ada batasan yang menghubungi pemilik?"}, "receivingNotification": {"description": "<PERSON>, <PERSON>a dapat mengatur pemberitahuan untuk kriteria properti tertentu di pengaturan profil Anda.", "title": "<PERSON>patkah saya menerima pemberitahuan untuk daftar baru?"}, "requestContract": {"description": "<PERSON>mi tidak member<PERSON>n kontrak, tetapi template kontrak yang dapat diunduh tersedia. \n<PERSON><PERSON>, tidak ada hak yang dapat diperoleh dari templat ini. \nPemilik mungkin memiliki mitra hukum yang dapat membantu menyusun kontrak resmi.", "title": "<PERSON><PERSON><PERSON><PERSON> saya meminta kontrak melalui platform?"}, "safeToDeposit": {"description": "Karena kami memverifikasi identitas setiap pemilik, seharusnya aman untuk membayar uang deposit. \n<PERSON><PERSON>, kami sangat merekomendasikan menjaga jumlah deposit serendah mungkin dan memastikan bahwa nama pada transfer bank cocok dengan rincian yang diverifikasi dengan pemilik.", "title": "Bagaimana jika pemilik meminta uang deposit?"}, "savingProperty": {"description": "Ya, <PERSON>a dapat menambahkan properti ke daftar favorit <PERSON><PERSON>, yang dapat diakses di profil <PERSON>a.", "title": "<PERSON><PERSON><PERSON><PERSON> saya menyimpan properti yang saya minati?"}, "schedulePropertyVisit": {"description": "<PERSON>, <PERSON><PERSON> dapat mengatur kunjungan dengan menghubungi pemilik melalui fitur obrolan dan menyiapkan waktu yang tepat.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> saya menja<PERSON>an kunjungan properti melalui platform?"}, "searchPlaceholder": "<PERSON><PERSON>", "searchProperty": {"description": "Anda dapat mencari properti dengan menggunakan filter seperti lokasi, j<PERSON><PERSON> properti, dan kisaran harga. \nMasukkan preferensi Anda di bilah pencarian untuk menemukan daftar yang sesuai dengan kebutuhan <PERSON>a.", "title": "Bagaimana cara mencari properti di platform?"}, "subscriptionOffer": {"description": "<PERSON>mi saat ini hanya menawarkan langganan bulanan, dengan dua paket berbayar berbeda yang menampilkan berbagai manfaat. \nAnda dapat melihat paket yang tersedia di sini:", "extraContent": "Langganan Property Plaza.", "title": "<PERSON><PERSON> langganan apa yang <PERSON>a ta<PERSON>an?"}, "technicalIssue": {"description": "<PERSON><PERSON> <PERSON>a mengalami masalah apa pun, <PERSON><PERSON> dapat menghubungi dukungan pelanggan melalui fitur obrolan setelah masuk.", "title": "Bagaimana jika saya mengalami masalah teknis di platform?"}, "title": "FAQ untuk menemukan properti Bali Anda yang sempurna", "trustListingPhoto": {"description": "<PERSON><PERSON><PERSON> daftar melalui proses verifikasi. \n<PERSON><PERSON>, kami selalu mere<PERSON>n mengunjungi properti secara langsung sebelum membuat keputusan.", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON> saya mempercayai foto dan informasi dalam daftar?"}}, "seeker": {"faq": {"cancelSubscription": {"title": "<PERSON><PERSON><PERSON><PERSON> saya membatalkan langganan saya selama sebulan?"}}}, "termsOfUse": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "privacyPolicy": {"title": "<PERSON><PERSON><PERSON><PERSON>"}, "aboutUs": {"hero": {"title": "Membuat Pencarian Properti Men<PERSON> dan <PERSON>", "description": "Property Plaza berdedikasi untuk menghubungkan pencari properti dengan rumah dan ruang ideal mereka melalui platform intuitif yang mengutamakan transparansi, kualitas, dan pengalaman pengguna.", "browseProperties": "<PERSON><PERSON><PERSON><PERSON>", "contactUs": "<PERSON><PERSON><PERSON><PERSON>"}, "mission": {"title": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "description": "<PERSON><PERSON> memiliki misi untuk mengubah pengalaman pencarian properti dengan menciptakan platform yang mengutamakan pengguna dan membuat pencarian properti berikutnya menjadi perjalanan yang menyenangkan.", "values": {"global": {"title": "Perspektif Global", "description": "<PERSON><PERSON> membawa standar internasional ke pasar lokal, membantu Anda menemukan properti yang memenuhi tolok ukur kualitas global."}, "trust": {"title": "Dibangun atas Kepercayaan", "description": "<PERSON><PERSON> me<PERSON><PERSON><PERSON><PERSON> daftar dan pemilik untuk memastikan Anda dapat membuat keputusan dengan percaya diri dan ketenangan pikiran."}, "quality": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> meng<PERSON>si properti yang memenuhi standar tinggi kami, mengh<PERSON>t waktu Anda dan memastikan kepuasan."}, "community": {"title": "Berfokus pada Komunitas", "description": "<PERSON><PERSON> memba<PERSON> koneksi antara pencari properti dan pemilik, menciptakan ekosistem yang saling mendukung."}, "innovation": {"title": "<PERSON><PERSON><PERSON>", "description": "Kami terus meningkatkan platform dan layanan kami untuk memenuhi kebutuhan klien kami yang terus berkembang."}, "personalization": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> men<PERSON><PERSON>i bahwa kebutuhan setiap klien unik dan menyesuaikan pendekatan kami sesuai dengan itu."}}, "ourMission": {"title": "<PERSON><PERSON>", "additionalText": "<PERSON><PERSON> berkomitmen untuk menampilkan properti terbaik sambil mempertahankan standar integritas dan kepuasan pelanggan tertinggi."}, "ourVision": {"title": "<PERSON><PERSON>", "description": "Menjadi platform real estat yang paling dipercaya dan inovatif, menetapkan standar keunggulan dalam layanan properti dan pengalaman pelanggan.", "additionalText": "<PERSON><PERSON> memba<PERSON>kan masa depan di mana menemukan properti impian <PERSON><PERSON> dapat diakses oleh semua orang, didukung oleh teknologi canggih dan pemahaman mendalam tentang kebutuhan klien lokal dan internasional."}, "ourCoreValues": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "story": {"title": "<PERSON><PERSON>", "companyTitle": "G<PERSON>bang Anda untuk Pencarian Properti", "paragraph1": "Property Plaza dimulai pada tahun 2024 ketika pendiri kami mengalami sendiri tantangan menemukan properti berkualitas di pasar yang tidak familiar. Mereka membayangkan platform yang akan membuat pencarian properti transparan, e<PERSON><PERSON>n, dan menyenan<PERSON>.", "paragraph2": "<PERSON><PERSON><PERSON> dengan hanya beberapa daftar di <PERSON> (Bali), kami telah tumbuh menjadi platform tepercaya di beberapa negara, membantu ribuan orang menemukan properti yang cocok setiap bulan.", "paragraph3": "<PERSON><PERSON> <PERSON><PERSON>, kami terus berinovasi dan berk<PERSON><PERSON>, dipandu oleh komitmen kami ter<PERSON><PERSON> kualitas, transpar<PERSON>i, dan pengalaman pengguna yang luar biasa."}, "team": {"title": "<PERSON><PERSON>", "description": "Individu-individu bersemangat di balik Property Plaza berdedikasi untuk merevolusi cara orang menemukan dan mengamankan properti.", "roles": {"ceo": "Chief Executive Officer", "cto": "Chief Technology Officer", "marketing": "<PERSON><PERSON><PERSON>", "propertySpecialist": "Spes<PERSON><PERSON>", "frontend": "Pengembang Frontend", "backend": "Pengembang Backend", "backend2": "Pengembang Backend", "tester": "Penguji QA", "customerSuccess": "<PERSON><PERSON><PERSON>"}, "members": {"rt": {"name": "<PERSON>", "bio": "Sebagai pengusaha yang fokus pada inovasi dan optimasi proses, <PERSON> men<PERSON> bahwa Bali kekurangan pasar real estat yang transparan."}, "thijs": {"name": "<PERSON><PERSON><PERSON><PERSON>", "bio": "Memiliki pengalaman membangun beberapa proyek SaaS. Memimpin tim pengembangan dan memastikan infrastruktur teknis yang optimal."}, "joost": {"name": "<PERSON><PERSON>", "bio": "<PERSON><PERSON><PERSON> pemasaran yang bertanggung jawab atas strategi untuk membantu pencari menemukan properti ideal mereka di Bali."}, "aditya": {"name": "<PERSON><PERSON><PERSON>", "bio": "Dengan pengalaman dan dorong<PERSON>ya sebagai Pengembang Frontend, Aditya memastikan platform dikembangkan dengan cepat dengan fokus pada perjalanan pelanggan."}, "anjas": {"name": "<PERSON><PERSON><PERSON>", "bio": "Sebagai Pengembang Backend, <PERSON>jasmara memastikan semuanya terus ber<PERSON> lancar, di mana pun di dunia platform ini digunakan."}, "nuni": {"name": "<PERSON><PERSON>", "bio": "Pengembang Backend komunikatif kami bekerja dengan Anjasmara untuk memastikan semua sistem terus berfungsi secara optimal."}, "dennis": {"name": "<PERSON>", "bio": "Mengkhususkan diri dalam mengidentifikasi dan memverifikasi daftar untuk mempertahankan standar tinggi platform kami."}, "andrea": {"name": "<PERSON>", "bio": "Mengkhususkan diri dalam mengidentifikasi dan memverifikasi daftar untuk mempertahankan standar tinggi platform kami."}, "natha": {"name": "<PERSON><PERSON>", "bio": "Mengkhususkan diri dalam mengidentifikasi dan memverifikasi daftar untuk mempertahankan standar tinggi platform kami."}, "rizki": {"name": "<PERSON><PERSON><PERSON>", "bio": "Sebagai Penguji QA kami yang be<PERSON>, <PERSON><PERSON><PERSON> menguji setiap fitur dengan teliti untuk memastikan pengalaman pengguna yang mulus di semua perangkat dan platform."}}}, "cta": {"title": "Siap Menemukan Properti Sempurna Anda?", "description": "<PERSON><PERSON> per<PERSON>n pencarian properti Anda dengan Property Plaza hari ini dan temukan mengapa ribuan pengguna mempercayai kami untuk menemukan ruang ideal mereka.", "findProperty": "Temu<PERSON>", "getInTouch": "<PERSON><PERSON><PERSON><PERSON>"}, "contact": {"title": "<PERSON><PERSON><PERSON><PERSON>", "visitUs": {"title": "<PERSON><PERSON><PERSON><PERSON> (di Bali)", "address": "Jl. Sempol 17C\nPererenan - Canggu\nIndonesia 80351"}, "emailUs": {"title": "<PERSON><PERSON>", "general": "Untuk pertanyaan umum:", "generalEmail": "info (at) property-plaza.com", "listings": "Untuk daftar properti:", "listingsEmail": "listings (at) property-plaza.com"}, "callUs": {"title": "<PERSON><PERSON> den<PERSON>", "officeHours": "Jam Kerja: 9AM - 6PM (GMT+8)", "phone": "+62 ***********", "whatsapp": "WhatsApp:", "whatsappNumber": "+62 812 3456 7890"}}, "tabs": {"company": "<PERSON><PERSON><PERSON><PERSON>", "team": "<PERSON>", "mission": "Mi<PERSON> & Visi"}}, "plan": {"title": "<PERSON>kses eksklusif ke penawaran properti terbaik Bali!", "description": "Dapatkan akses eksklusif ke vila off-market, properti terbaik, dan peluang investasi menarik sebelum orang lain tahu. <PERSON>an lewatkan—berlangganan sekarang dan amankan properti terbaik sesuai kebutuhanmu!"}, "userDataDeletion": {"title": "Penghapusan Data Pengguna"}, "srOnly": {"FindBestPropertyOnBali": "Temukan properti terbaik di Bali"}}, "universal": {"otp": {"verifyHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {field} <PERSON><PERSON>", "changeRegisterData": "Ubah data pendaftaran Anda", "resendVerificationCode": "<PERSON><PERSON> kode Verifikasi"}, "form": {"utility": {"fieldRequired": "{field} wajib diisi", "invalidFormat": "{field} format tidak sesuai", "eitherFieldRequired": "Salah satu {fields} harus disediakan", "enterValidField": "<PERSON><PERSON><PERSON> masukkan {field} yang valid", "forgotField": "<PERSON><PERSON> {field} And<PERSON>?", "resetField": "<PERSON><PERSON> {field}", "minimumLength": "{field} memerlukan minimum {length} karakter", "fieldNotMatch": "{field} tidak sama", "optional": "(Opsional)", "coordinateRequired": "Anda perlu memasukkan longitude dan latitude yang dipisahkan dengan koma", "wrongFormatCoordinate": "Format koordinat salah, Anda perlu memasukkan longitude dan latitude yang dipisahkan dengan koma", "maximumLength": "Tidak dapat memasukkan {field} lebih dari {length} karakter", "passwordWeak": "Kata sandi terlalu lemah", "password": {"minimumLength": "Minimum 8 karakter", "numberRequired": "<PERSON>mal satu angka", "notCommonWord": "<PERSON><PERSON>n kata umum", "specialCharacter": "Minimal satu simbol ", "uppercaseRequired": "<PERSON><PERSON> satu huruf besar", "lowercaseRequired": "Minimal satu huruf kecil"}}, "field": {"email": "E-mail", "phoneNumber": "Nomor telepon", "password": "Password", "confirmPassword": "<PERSON>a sandi", "username": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "aboutYou": "<PERSON>tang anda", "language": "Bahasa", "message": "<PERSON><PERSON>", "typeProperty": "<PERSON><PERSON><PERSON> properti", "yearsOfBuild": "<PERSON><PERSON> p<PERSON>n", "propertyView": "<PERSON><PERSON><PERSON>", "landSize": "<PERSON><PERSON><PERSON> tanah", "buildingSize": "Ukuran bangunan", "gardenSize": "Ukuran taman", "typeBedroom": "<PERSON><PERSON>e kamar tidur", "totalBathroom": "<PERSON><PERSON><PERSON> ka<PERSON> mandi", "wifi": "Wi-Fi (Mb/s)", "cleaning": "<PERSON><PERSON><PERSON>", "cleaningTime": "Total bersih bersih", "review": "<PERSON><PERSON><PERSON>", "postalCode": "<PERSON><PERSON> pos", "coordinate": "Ko<PERSON>inat", "propertyLocation": "Lokasi <PERSON>", "roadSize": "Ukuran jalan masuk", "addressLocation": "<PERSON><PERSON><PERSON>", "distric": "Kecamatan", "city": "Kota atau kabupaten", "province": "<PERSON><PERSON><PERSON>", "typeContract": "<PERSON><PERSON>e kontrak", "minimumDuration": "<PERSON><PERSON><PERSON> minimal", "minimumPrice": "<PERSON><PERSON> per<PERSON>", "maximumDuration": "<PERSON><PERSON><PERSON> maksimum", "elictricity": "Kelistrikan (KW/h)", "furnishingStatus": "Status perabotan", "parking": "<PERSON><PERSON>", "poolAvailability": "<PERSON><PERSON><PERSON><PERSON> kolam renang", "livingStatus": "<PERSON><PERSON><PERSON><PERSON> di sekitar", "role": "<PERSON><PERSON>", "fullName": "<PERSON><PERSON> le<PERSON>", "name": "<PERSON><PERSON>", "otp": "OTP", "statusListing": "Status properti", "excerpt": "Ku<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "banjar": "Banjar"}, "title": {"login": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON> akun", "staffLogin": "Staf Autentikasi", "resetPassword": "<PERSON><PERSON> ulang kata sandi", "enterOtpCode": "Masukkan kode OTP", "createPassword": "Buat password"}, "label": {"emailOrPhone": "Email atau nomor telepon", "password": "<PERSON>a sandi", "username": "<PERSON><PERSON>", "confirmPassword": "Konfirmasi kata sandi", "firstName": "<PERSON><PERSON> de<PERSON>", "lastName": "<PERSON><PERSON>", "aboutYou": "<PERSON>tang anda", "email": "E-mail", "address": "<PERSON><PERSON><PERSON>", "phoneNumber": "Nomor telepon", "language": "Bahasa yang digunakan", "PropertyType": "<PERSON><PERSON>", "cascoProperty": "Properti casco", "yearsOfBuild": "<PERSON><PERSON> p<PERSON>n", "propertyView": "<PERSON><PERSON><PERSON>", "landSize": "<PERSON><PERSON><PERSON> tanah", "buildingSize": "Ukuran bangunan", "gardenSize": "Ukuran taman", "totalBedroom": "<PERSON><PERSON><PERSON> ka<PERSON> t<PERSON>ur", "totalBathroom": "<PERSON><PERSON><PERSON> ka<PERSON> mandi", "wifi": "Wi-Fi (kecepatan Wi-fi)", "cleaning": "<PERSON><PERSON><PERSON>", "coordinate": "Ko<PERSON>inat", "propertyLocation": "Lokasi <PERSON>", "roadSize": "Ukuran jalan masuk", "addressLocation": "<PERSON><PERSON><PERSON>", "postalCode": "kode Pos", "distric": "Kecamatan", "city": "Kota atau kabupaten", "province": "<PERSON><PERSON><PERSON>", "typeContract": "<PERSON><PERSON>e kontrak", "availableContract": "<PERSON><PERSON><PERSON> k<PERSON>", "minimumDuration": "<PERSON><PERSON><PERSON> minimal", "minimumPrice": "<PERSON><PERSON> per<PERSON>", "maximumDuration": "<PERSON><PERSON><PERSON> maksimum", "elictricity": "Kelistrikan", "furnishingStatus": "Status perabotan", "parkingStatus": "<PERSON><PERSON> parkir", "poolAvailability": "<PERSON><PERSON><PERSON><PERSON> kolam renang", "livingStatus": "<PERSON><PERSON><PERSON><PERSON> di sekitar", "fullName": "<PERSON><PERSON> le<PERSON>", "name": "<PERSON><PERSON>", "requestCreateListing": "Berikan sedikit informasi kepada manajer akun kami tentang properti anda", "propertyDescription": "<PERSON><PERSON><PERSON><PERSON>", "pricing": "<PERSON><PERSON>", "type": "Tipe", "amount": "<PERSON><PERSON><PERSON>", "price": "Biaya", "date": "Tanggal pembelian", "requestUpdateListing": "<PERSON>ri tahu kami tentang hal-hal yang perlu Anda ubah", "promoCode": "Kode promo", "status": "Status listing", "excerpt": "Ku<PERSON><PERSON>", "propertyTitle": "<PERSON><PERSON><PERSON>", "maximum": "<PERSON><PERSON><PERSON><PERSON>", "userId": "ID Pengguna", "banjar": "Banjar"}, "placeholder": {"basePlaceholder": "<PERSON><PERSON><PERSON><PERSON> {field} anda", "aboutYou": "Ceritakan sedikit mengenai dirimu", "pickLanguage": "<PERSON><PERSON><PERSON> bahasa", "baseSelectPlaceholder": "<PERSON><PERSON><PERSON> {field}", "baseNumberPlaceholder": "Tetap<PERSON> {field} anda", "availableContract": "Tetapkan kapan properti tersedia untuk dikontrak", "searchQuestion": "<PERSON><PERSON>", "example": {"requestHelpToCs": "<PERSON>a sudah isi kredi, namun kredit saya tidak bertambah", "email": "<EMAIL>", "phoneNumber": "+62 8123 4567 8901", "firtName": "I Ketut", "lastName": "<PERSON><PERSON><PERSON>", "requestCreateListing": "Villa 4 Kamar Tidur 3 Kamar Mandi dengan kolam renang pribadi berlokasi di canggu, Badung", "requestUpdateListing": "<PERSON>a ingin mengubah jumlah kamar mandi"}, "enterPromoCode": "Masukkan kode promo", "seekersFindPropertyLocation": "<PERSON><PERSON> t<PERSON>"}, "description": {"resetPassword": "<PERSON><PERSON> akan mengirim permintaan ganti password anda lewat email", "passwordRequirement": "Password minimal terdiri dari 8 <PERSON><PERSON><PERSON>, 1 <PERSON><PERSON><PERSON>, 1 <PERSON><PERSON><PERSON>, 1 Angka dan 1 Simbol", "userId": "Ini adalah pengidentifikasi unik Anda dalam sistem kami"}, "subtitle": {"resetPassword": "<PERSON><PERSON><PERSON> ulang kata sandi akun <PERSON>a"}}, "conjuntion": {"or": "atau", "and": "dan", "of": "dari", "for": "untuk"}, "cta": {"login": "<PERSON><PERSON><PERSON>", "createAccount": "<PERSON><PERSON><PERSON> akun", "continueWith": "<PERSON><PERSON><PERSON> {field}", "edit": "Ganti foto", "updatePassword": "Ganti", "verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seeTranslation": "<PERSON><PERSON>", "seeOriginal": "Lihat teks asli", "filter": "Filter", "chatCustomerService": "Butuh bantuan? \nchat <PERSON> kami", "requestCreateListing": "<PERSON><PERSON><PERSON><PERSON> daftar <PERSON>", "createListing": "Buat iklan properti", "contactAccountmanager": "<PERSON><PERSON><PERSON><PERSON> manajer akun", "disableListing": "Non aktifkan properti", "finishReview": "<PERSON><PERSON><PERSON><PERSON>", "saveAsDraft": "Simpan sebagai draf", "addReview": "Tambah<PERSON> ul<PERSON>n", "save": "Simpan", "back": "Kembali", "joinWaitngList": "<PERSON>kut daftar tunggu", "logout": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>", "signUpNow": "<PERSON><PERSON><PERSON>", "sendResetPassword": "Ubah kata sandi", "checkOut": "Check-out", "contactToWhatsapp": "<PERSON><PERSON><PERSON><PERSON> kami di <PERSON>", "createYourAccount": "Buat A<PERSON>n", "subscribe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "signUp": "Mendaftar", "goBack": "Kembali", "search": "<PERSON><PERSON>", "removeSearch": "Hapus", "disable": "Non-aktifkan", "enable": "Aktifkan", "sendRequest": "<PERSON><PERSON>", "closeChat": "<PERSON><PERSON><PERSON>", "sellPropertyFast": "<PERSON><PERSON> atau sewakan properti <PERSON>a dengan cepat", "next": "Selanjutnya", "activateListing": "Aktivasi iklan", "activate": "Aktivasi", "changeStatus": "Ubah status", "changePrice": "<PERSON><PERSON> harga", "pay": "Bayar", "topUp": "<PERSON><PERSON>", "viewAllProperty": "<PERSON><PERSON> semua <PERSON>", "readMore": "Selengkapnya ...", "changePassword": "Ubah kata sandi", "filters": "Filter", "clearAll": "<PERSON><PERSON>", "previous": "Sebelumnya", "maps": "Map", "list": "List", "contactOwner": "Hubungi Pemilik", "share": "Bagikan", "readLess": "Baca lebih sedikit", "copyLink": "<PERSON><PERSON>an", "close": "<PERSON><PERSON><PERSON>", "findOtherProperty": "<PERSON><PERSON>", "findOtherPackage": "<PERSON><PERSON> paket lainnya", "followUsOnInstagram": "<PERSON><PERSON><PERSON> kami di Instagram", "changePhoto": "Ubah foto", "saveChanges": "<PERSON><PERSON><PERSON>", "change": "Ubah", "confirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewAll": "<PERSON><PERSON>a", "signOutAll": "<PERSON><PERSON><PERSON> dari semua per<PERSON>", "signOutDevice": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "addPaymentMethod": "Tambahkan metode pembayaran", "requestChangePassword": "<PERSON>a ubah kata sandi"}, "error": {"resetPassword": {"title": "Ups, permintaan ubah kata sandi gagal"}, "requestForgetPassword": {"title": "<PERSON>s, permintaan lupakan kata sandi gagal"}, "foundError": "<PERSON><PERSON> terjadi k<PERSON>han"}, "success": {"requestForgotPassword": {"title": "<PERSON><PERSON><PERSON>an ganti password berhasil", "description": "<PERSON><PERSON><PERSON> per<PERSON> email Anda untuk melanjutkan prosesnya"}, "updateUser": "<PERSON><PERSON><PERSON>", "createPassword": {"title": "<PERSON><PERSON><PERSON><PERSON> membuat kata sandi baru", "description": "Harap masuk untuk melanjutkan"}}, "popup": {"followInstagram": {"title": "Mari terhubung", "description": "<PERSON><PERSON><PERSON> kami untuk mengetahui suasana <PERSON>, tips orang dalam, dan kabar terbaru eksklusif!"}}, "misc": {"enableSoundNotification": {"title": "Aktifkan pemberitahuan", "description": "Dapatkan pembaruan terbaru dari tim dan pemilik kami"}, "foundError": "Ups, tidak dapat mengirim email verifikasi", "ascendingOrder": "<PERSON><PERSON><PERSON> naik", "descendingOrder": "<PERSON><PERSON><PERSON> turun", "hide": "Sembunyikan", "userNotFound": "pengguna tidak ditemukan", "profileImageAlt": "Gambar Profil Pengguna Properti-Plaza", "temporaryBanner": {"title": "Properti Bali Ideal Anda sedang dalam perjalanan", "description": "<PERSON><PERSON> tahu betapa pentingnya menemukan properti yang tepat, apakah itu homestay, toko, atau tanah. Itu sebabnya kami meluangkan waktu untuk mendapatkan daftar properti yang memenuhi kebutuhan <PERSON>a."}}, "universal": {"success": {"createPassword": {"title": "<PERSON><PERSON><PERSON> Sandi Baru"}}}}, "ContactUs": {"pageTitle": "Hubungi Kami | Property Plaza", "pageDescription": "Hubungi Properti Plaza. \nHubungi tim kami untuk per<PERSON>aan, dukungan, atau kolaborasi. \n<PERSON><PERSON> siap membantu Anda dengan tanggapan yang cepat dan dapat diandalkan.", "title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON>ya pertanyaan atau butuh bantuan? Kami siap membantu. Hubungi kami menggunakan formulir di bawah ini.", "sendMessage": "<PERSON><PERSON>", "nameField": "<PERSON><PERSON>", "emailField": "<PERSON><PERSON>", "subjectField": "Subjek", "messageField": "<PERSON><PERSON>", "submitButton": "<PERSON><PERSON>", "messageSent": "<PERSON><PERSON> Anda telah ber<PERSON> di<PERSON>. Ka<PERSON> akan segera menghubungi Anda!"}, "owner": {"misc": {"rentalTerms": "<PERSON><PERSON><PERSON><PERSON> sewa", "ownerProperty": "Properti Pemilik"}, "listing": {"detail": {"contactCount": "{Count} orang sudah menghubungi pemiliknya"}}}, "verify": {"seo": {"title": "Layanan Inspeksi Villa Bali – Cegah Penipuan Sebelum Menyewa", "description": "<PERSON><PERSON><PERSON><PERSON> dengan penipuan saat menyewa villa di Bali? <PERSON><PERSON> villa, memverifikasi dokumen legal, dan melaporkan risiko tersembunyi sebelum Anda membayar. Pesan inspeksi villa profesional Bali Anda sekarang.", "keywords": "layanan inspeksi villa Bali, hindari penipuan sewa di Bali, cek villa sebelum menyewa, inspeksi properti Bali, pencegahan penipuan villa Bali, inspeksi villa bersertifikat"}, "hero": {"badge": "Inspeksi Villa Bali Terverifikasi", "title": "Inspeksi Villa Bali – Hindari Penipuan Sebelum Menyewa", "subtitle": "Mencari sewa jangka panjang atau properti investasi di Bali? Sayangnya, penipuan sewa villa dan listing properti palsu sangat umum. Property Plaza menawarkan layanan inspeksi villa terpercaya untuk melindungi investasi (sewa) Anda. <PERSON> kami mengun<PERSON> properti, memverif<PERSON>si dokumen legal (sertif<PERSON><PERSON> tanah, per<PERSON><PERSON><PERSON> sewa), men<PERSON>pe<PERSON>i struktur, dan menyoroti biaya tersembunyi atau risiko.", "benefits": ["Verifikasi Sertifikat Bersertifikat (BPN)", "Pemeriksaan Struktural & Keamanan Terperinci", "Video Walkthrough Profesional + <PERSON><PERSON><PERSON>", "Lebih dari 20% sewa villa Bali menyembunyikan risiko serius atau penipuan"], "cta": "Pesan inspeksi villa independen dan profesional di Bali sekarang - sebelum Anda membayar deposit", "warning": "<PERSON><PERSON> foto atau janji dari broker."}, "howItWorks": {"title": "Cara Kerja Inspeksi Villa Bali Kami – 3 Langkah Sederhana", "subtitle": "<PERSON><PERSON> villa Bali dan men<PERSON><PERSON><PERSON>n laporan personal <PERSON><PERSON> 24 jam – berdasarkan paket inspeksi yang Anda pilih.", "steps": {"book": {"title": "Pesan Inspeksi Online Anda", "description": "<PERSON><PERSON><PERSON> tingkat inspeksi <PERSON> (Basic, Standard, atau Premium), pilih tanggal inspeksi <PERSON>, dan kirimkan lokasi properti. \n \n <PERSON><PERSON><PERSON>, kami akan mengirimkan email konfirmasi dan daftar periksa singkat untuk membuat inspeksi seefisien mungkin.", "result": "Konfirmasi pemesanan + daftar persiapan villa"}, "inspect": {"title": "Kunjungan Villa di Lokasi", "description": "<PERSON>li kami mengunjungi properti untuk melakukan verifikasi, tergantung pada paket yang Anda pilih. Ini termasuk misalnya: menginspeksi struktur properti, mengidentifikasi potensi biaya tersembunyi, mengambil foto atau video walkthrough, memverifikasi identitas pemilik, dan men<PERSON><PERSON><PERSON> per<PERSON> sewa atau jual beli.", "result": {"basic": "Basic – Kunjungan villa + verifikasi pemilik + foto", "standard": "Standard – Inspek<PERSON> + laporan tertulis + video walkthrough", "premium": "Premium – Verifikasi kepemilikan BPN + prioritas pemesanan"}}, "report": {"title": "Terima Laporan And<PERSON> 24 Jam", "description": "<PERSON><PERSON><PERSON> ins<PERSON><PERSON><PERSON>, <PERSON><PERSON> akan men<PERSON> personal terperinci melalui email atau WhatsApp. Dengan gambaran menyelu<PERSON> ini, <PERSON><PERSON> akan diberdayakan untuk membuat keputusan sewa atau pembelian yang aman dan terinformasi dengan baik.", "result": {"basic": "Basic – Foto + ring<PERSON>an suara + draft per<PERSON><PERSON><PERSON> sewa", "standard": "Standard – Laporan inspeksi tertulis + video + konsultasi telepon", "premium": "Premium – Paket lengkap + verifikasi BPN + template sewa"}}}, "whyChoose": {"title": "Mengapa memilih inspeksi villa Bali Property Plaza?", "description": "Setiap paket memberi Anda transparansi dan ketenangan pikiran – sebelum Anda membayar deposit."}}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON> - <PERSON><PERSON><PERSON> tingkat inspeksi yang sesuai dengan kebutuhan <PERSON>", "tiers": {"basic": {"name": "Paket Basic", "subtitle": "Untuk sewa jangka pendek (1+ bulan)", "features": ["<PERSON><PERSON> temu & kunjungan villa", "Draft perjan<PERSON><PERSON> sewa", "Verifikasi identitas pemilik", "Foto-foto properti", "<PERSON><PERSON><PERSON> sing<PERSON>"]}, "standard": {"name": "Paket Standard", "subtitle": "Direkomendasikan untuk sewa jangka menengah (be<PERSON><PERSON> bulan atau lebih)", "popular": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "features": ["Termasuk Paket Basic, plus:", "Inspeksi properti umum", "Laporan inspeksi tertulis", "Video walkthrough properti", "Panggilan untuk membahas temuan kunci"]}, "premium": {"name": "Paket Premium", "subtitle": "Direkomendasikan untuk sewa jangka panjang (1+ tahun)", "features": ["Termasuk Paket Standard, plus:", "Verifikasi kepemilikan via BPN (Badan <PERSON>han Nasional)*", "Template <PERSON><PERSON><PERSON><PERSON> sewa jang<PERSON> panjang", "Prioritas pemesanan inspeksi", "*Verifikasi BPN memberikan kon<PERSON> kepem<PERSON>, bukan jaminan hukum"]}}, "cta": "<PERSON><PERSON><PERSON> {tierName}"}, "booking": {"title": "<PERSON>esan Inspeksi Anda", "subtitle": "<PERSON><PERSON> formulir di bawah ini dan kami akan menghubungi Anda dalam 24 jam", "form": {"firstName": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama depan <PERSON>", "required": "<PERSON><PERSON> de<PERSON> wajib diisi", "minLength": "<PERSON><PERSON> depan <PERSON> 2 karakter"}, "lastName": {"label": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> nama belakang <PERSON>", "required": "<PERSON><PERSON> be<PERSON> wajib diisi", "minLength": "<PERSON><PERSON> minimal 2 karakter"}, "email": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "<EMAIL>", "required": "<PERSON><PERSON><PERSON> email wajib diisi", "invalid": "<PERSON><PERSON><PERSON> ma<PERSON>kkan alamat email yang valid"}, "whatsappNumber": {"label": "Nomor WhatsApp", "placeholder": "+62 812 3456 7890", "required": "Nomor Whats<PERSON><PERSON> wajib diisi", "invalid": "<PERSON><PERSON>an masukkan nomor WhatsApp yang valid"}, "villaAddress": {"label": "Alamat <PERSON>", "placeholder": "Alamat villa lengkap di Bali", "required": "<PERSON><PERSON>t villa wajib diisi", "minLength": "<PERSON><PERSON>an berikan alamat villa yang lengkap"}, "preferredDate": {"label": "<PERSON>gal Inspeksi <PERSON>", "required": "Tanggal inspeksi pilihan wajib diisi"}, "tier": {"label": "Tingkat Inspeksi", "placeholder": "<PERSON><PERSON><PERSON> tingkat inspeksi", "required": "<PERSON><PERSON><PERSON> pilih tingkat inspeksi", "options": {"basic": "Basic - IDR 4,500,000", "smart": "Smart - IDR 6,000,000", "fullShield": "Full Shield - IDR 8,500,000"}}, "cta": "Reservasi Inspeksi", "submitting": "Mengirim...", "disclaimer": "<PERSON>gan mengirimkan formulir ini, <PERSON><PERSON> setuju untuk dihubungi melalui WhatsApp untuk koordinasi inspeksi.", "success": {"title": "<PERSON><PERSON>esanan <PERSON>!", "message": "<PERSON><PERSON> akan mengh<PERSON>i Anda dalam 24 jam untuk mengkonfirmasi inspeksi Anda."}, "error": {"title": "Error", "message": "<PERSON><PERSON> mengirim pemesanan. <PERSON>lakan coba lagi."}}}, "success": {"title": "Pembayaran Berhasil!", "subtitle": "Inspeksi villa Anda telah ber<PERSON>il dipesan.", "details": {"title": "Apa yang terjadi se<PERSON>?", "confirmation": "<PERSON><PERSON>", "confirmationDesc": "<PERSON><PERSON> akan men<PERSON>ma email konfirmasi terperinci dalam 5 menit.", "scheduling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "schedulingDesc": "<PERSON> kami akan mengh<PERSON><PERSON>i Anda dalam 24 jam untuk mengkonfirmasi waktu yang tepat.", "contact": "<PERSON><PERSON><PERSON><PERSON>", "contactDesc": "<PERSON><PERSON> akan mengh<PERSON>ungi melalui WhatsApp untuk update atau pertanyaan."}, "nextSteps": {"title": "<PERSON><PERSON><PERSON>", "step1": "Periksa email Anda untuk konfirmasi pemesanan dan daftar persiapan", "step2": "Pastikan ada seseorang di properti selama inspeksi", "step3": "Siapkan dokumen yang relevan (jika tersedia)", "step4": "Inspektur kami akan menghubungi Anda 1 hari sebelum kunjungan"}, "actions": {"backHome": "<PERSON><PERSON><PERSON> ke Beranda", "contactUs": "Hubungi <PERSON>"}}}}