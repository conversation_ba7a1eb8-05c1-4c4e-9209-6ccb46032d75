import { chainMiddleware } from "./middleware/chainMiddleware";
import { withAuth } from "./middleware/withAuth";
import { withCors } from "./middleware/withCors";
import { withDomain } from "./middleware/withDomain";
import { withLocale } from "./middleware/withLocale";

// always put withLocale middleware in the of execution chain
const executionChain = [withCors, withDomain, withAuth, withLocale];
export default chainMiddleware(executionChain);
export const config = {
  matcher: [
    "/",
    "/(en|id|nl)/:path*",
    "/((?!hooks|_next/static|_next/image|favicon.ico|icon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|mp3)$).*)",
    "/api/:path*",
  ],
};
