// This file is generated automatically by `scripts/build/indices.ts`. Please, don't change it.
export { default as af } from "./af/index.js";
export { default as ar } from "./ar/index.js";
export { default as arDZ } from "./ar-DZ/index.js";
export { default as arEG } from "./ar-EG/index.js";
export { default as arMA } from "./ar-MA/index.js";
export { default as arSA } from "./ar-SA/index.js";
export { default as arTN } from "./ar-TN/index.js";
export { default as az } from "./az/index.js";
export { default as be } from "./be/index.js";
export { default as beTarask } from "./be-tarask/index.js";
export { default as bg } from "./bg/index.js";
export { default as bn } from "./bn/index.js";
export { default as bs } from "./bs/index.js";
export { default as ca } from "./ca/index.js";
export { default as cs } from "./cs/index.js";
export { default as cy } from "./cy/index.js";
export { default as da } from "./da/index.js";
export { default as de } from "./de/index.js";
export { default as deAT } from "./de-AT/index.js";
export { default as el } from "./el/index.js";
export { default as enAU } from "./en-AU/index.js";
export { default as enCA } from "./en-CA/index.js";
export { default as enGB } from "./en-GB/index.js";
export { default as enIE } from "./en-IE/index.js";
export { default as enIN } from "./en-IN/index.js";
export { default as enNZ } from "./en-NZ/index.js";
export { default as enUS } from "./en-US/index.js";
export { default as enZA } from "./en-ZA/index.js";
export { default as eo } from "./eo/index.js";
export { default as es } from "./es/index.js";
export { default as et } from "./et/index.js";
export { default as eu } from "./eu/index.js";
export { default as faIR } from "./fa-IR/index.js";
export { default as fi } from "./fi/index.js";
export { default as fr } from "./fr/index.js";
export { default as frCA } from "./fr-CA/index.js";
export { default as frCH } from "./fr-CH/index.js";
export { default as fy } from "./fy/index.js";
export { default as gd } from "./gd/index.js";
export { default as gl } from "./gl/index.js";
export { default as gu } from "./gu/index.js";
export { default as he } from "./he/index.js";
export { default as hi } from "./hi/index.js";
export { default as hr } from "./hr/index.js";
export { default as ht } from "./ht/index.js";
export { default as hu } from "./hu/index.js";
export { default as hy } from "./hy/index.js";
export { default as id } from "./id/index.js";
export { default as is } from "./is/index.js";
export { default as it } from "./it/index.js";
export { default as itCH } from "./it-CH/index.js";
export { default as ja } from "./ja/index.js";
export { default as jaHira } from "./ja-Hira/index.js";
export { default as ka } from "./ka/index.js";
export { default as kk } from "./kk/index.js";
export { default as km } from "./km/index.js";
export { default as kn } from "./kn/index.js";
export { default as ko } from "./ko/index.js";
export { default as lb } from "./lb/index.js";
export { default as lt } from "./lt/index.js";
export { default as lv } from "./lv/index.js";
export { default as mk } from "./mk/index.js";
export { default as mn } from "./mn/index.js";
export { default as ms } from "./ms/index.js";
export { default as mt } from "./mt/index.js";
export { default as nb } from "./nb/index.js";
export { default as nl } from "./nl/index.js";
export { default as nlBE } from "./nl-BE/index.js";
export { default as nn } from "./nn/index.js";
export { default as oc } from "./oc/index.js";
export { default as pl } from "./pl/index.js";
export { default as pt } from "./pt/index.js";
export { default as ptBR } from "./pt-BR/index.js";
export { default as ro } from "./ro/index.js";
export { default as ru } from "./ru/index.js";
export { default as sk } from "./sk/index.js";
export { default as sl } from "./sl/index.js";
export { default as sq } from "./sq/index.js";
export { default as sr } from "./sr/index.js";
export { default as srLatn } from "./sr-Latn/index.js";
export { default as sv } from "./sv/index.js";
export { default as ta } from "./ta/index.js";
export { default as te } from "./te/index.js";
export { default as th } from "./th/index.js";
export { default as tr } from "./tr/index.js";
export { default as ug } from "./ug/index.js";
export { default as uk } from "./uk/index.js";
export { default as uz } from "./uz/index.js";
export { default as uzCyrl } from "./uz-Cyrl/index.js";
export { default as vi } from "./vi/index.js";
export { default as zhCN } from "./zh-CN/index.js";
export { default as zhHK } from "./zh-HK/index.js";
export { default as zhTW } from "./zh-TW/index.js";