"use client"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";
import { useSeekersSettingsStore } from "@/stores/seekers-settings.store";
import { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";

interface PricingTier {
  id: string;
  name: string;
  price: number; // in IDR
  popular?: boolean;
  features: string[];
}

interface VerifyPricingProps {
  conversions: { [key: string]: number };
  onSelectTier: (tier: PricingTier) => void;
}

export default function VerifyPricing({ conversions, onSelectTier }: VerifyPricingProps) {
  const { currency: currencyStored, isLoading } = useSeekersSettingsStore();
  const [currency, setCurrency] = useState("IDR");
  const locale = useLocale();
  const t = useTranslations("verify");

  const pricingTiers: PricingTier[] = [
    {
      id: "basic",
      name: t("pricing.tiers.basic.name"),
      price: 4500000, // IDR 4,500,000
      features: t.raw("pricing.tiers.basic.features") as string[]
    },
    {
      id: "smart",
      name: t("pricing.tiers.smart.name"),
      price: 6000000, // IDR 6,000,000
      popular: true,
      features: t.raw("pricing.tiers.smart.features") as string[]
    },
    {
      id: "full-shield",
      name: t("pricing.tiers.fullShield.name"),
      price: 8500000, // IDR 8,500,000
      features: t.raw("pricing.tiers.fullShield.features") as string[]
    }
  ];

  useEffect(() => {
    if (isLoading) return;
    setCurrency(currencyStored);
  }, [currencyStored, isLoading]);

  const formatPrice = (price: number) => {
    const convertedPrice = price * (conversions[currency] || 1);
    return formatCurrency(convertedPrice, currency, locale);
  };

  const getEuroPrice = (price: number) => {
    const euroPrice = price * (conversions["EUR"] || 0.000063);
    return formatCurrency(euroPrice, "EUR", locale);
  };

  return (
    <section className="py-16 bg-white" aria-labelledby="pricing-title">
      <MainContentLayout>
        <div className="text-center mb-12">
          <h2 id="pricing-title" className="text-3xl md:text-4xl font-bold text-seekers-text mb-4">
            {t("pricing.title")}
          </h2>
          <p className="text-lg text-seekers-text-light">
            {t("pricing.subtitle")}
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto">
          {pricingTiers.map((tier) => (
            <div
              key={tier.id}
              className={`relative rounded-lg border-2 p-6 ${
                tier.popular
                  ? "border-seekers-primary bg-seekers-primary/5"
                  : "border-neutral-200 bg-white"
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold">
                    {t("pricing.tiers.smart.popular")}
                  </span>
                </div>
              )}

              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-seekers-text mb-2">
                  {tier.name}
                </h3>
                <div className="space-y-1">
                  <div className="text-3xl font-bold text-seekers-primary">
                    {formatPrice(tier.price)}
                  </div>
                  <div className="text-sm text-seekers-text-light">
                    ({getEuroPrice(tier.price)})
                  </div>
                </div>
              </div>

              <ul className="space-y-3 mb-8">
                {tier.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-green-500 mt-0.5">✓</span>
                    <span className="text-sm text-seekers-text">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                className={`w-full ${
                  tier.popular
                    ? "bg-seekers-primary hover:bg-seekers-primary/90"
                    : "bg-neutral-800 hover:bg-neutral-700"
                } text-white`}
                onClick={() => onSelectTier(tier)}
              >
                {t("pricing.cta", { tierName: tier.name })}
              </Button>
            </div>
          ))}
        </div>
      </MainContentLayout>
    </section>
  );
}
